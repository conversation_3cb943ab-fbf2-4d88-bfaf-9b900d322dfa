from django.db import models
from django.contrib.auth.models import User
from definitions.models import BaseModel, Person, Currency
from decimal import Decimal
from datetime import datetime, date, time
from django.utils import timezone


class Department(BaseModel):
    """الأقسام"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود القسم")
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    manager = models.ForeignKey(
        'Employee', on_delete=models.SET_NULL, null=True, blank=True,
        related_name='managed_departments', verbose_name="مدير القسم"
    )
    parent_department = models.ForeignKey(
        'self', on_delete=models.CASCADE, null=True, blank=True,
        related_name='sub_departments', verbose_name="القسم الرئيسي"
    )

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Position(BaseModel):
    """المناصب والوظائف"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود المنصب")
    name = models.CharField(max_length=100, verbose_name="اسم المنصب")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    department = models.ForeignKey(
        Department, on_delete=models.CASCADE,
        related_name='positions', verbose_name="القسم"
    )

    class Meta:
        verbose_name = "منصب"
        verbose_name_plural = "المناصب"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.department.name}"


class Qualification(BaseModel):
    """المؤهلات الأكاديمية"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود المؤهل")
    name = models.CharField(max_length=100, verbose_name="اسم المؤهل")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    
    # مستوى المؤهل
    LEVEL_CHOICES = [
        ('PRIMARY', 'ابتدائي'),
        ('PREP', 'إعدادي'),
        ('SECONDARY', 'ثانوي'),
        ('DIPLOMA', 'دبلوم'),
        ('BACHELOR', 'بكالوريوس'),
        ('MASTER', 'ماجستير'),
        ('PHD', 'دكتوراه'),
    ]
    level = models.CharField(
        max_length=20, choices=LEVEL_CHOICES, blank=True,
        verbose_name="مستوى المؤهل"
    )
    
    # مدة الدراسة
    duration = models.CharField(max_length=50, blank=True, verbose_name="مدة الدراسة")
    
    # حالة المؤهل
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "مؤهل"
        verbose_name_plural = "المؤهلات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Shift(BaseModel):
    """الورديات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الوردية")
    name = models.CharField(max_length=100, verbose_name="اسم الوردية")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    
    # أوقات الوردية
    start_time = models.TimeField(verbose_name="وقت البداية")
    end_time = models.TimeField(verbose_name="وقت النهاية")
    
    # مدة الوردية
    duration_hours = models.DecimalField(
        max_digits=4, decimal_places=2, default=8.0,
        verbose_name="مدة الوردية (ساعات)"
    )
    
    # نوع الوردية
    SHIFT_TYPES = [
        ('MORNING', 'صباحية'),
        ('EVENING', 'مسائية'),
        ('NIGHT', 'ليلية'),
        ('FLEXIBLE', 'مرنة'),
        ('PART_TIME', 'دوام جزئي'),
    ]
    shift_type = models.CharField(
        max_length=20, choices=SHIFT_TYPES, default='MORNING',
        verbose_name="نوع الوردية"
    )
    
    # الإعدادات
    include_break = models.BooleanField(default=True, verbose_name="يشمل استراحة")
    break_duration = models.IntegerField(default=60, verbose_name="مدة الاستراحة (دقائق)")
    overtime_allowed = models.BooleanField(default=True, verbose_name="يسمح بالعمل الإضافي")
    
    # حالة الوردية
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "وردية"
        verbose_name_plural = "الورديات"
        ordering = ['start_time']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"
    
    @property
    def formatted_time(self):
        """تنسيق الوقت للعرض"""
        return f"{self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}"


class SalarySystem(BaseModel):
    """أنظمة صرف المرتبات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود النظام")
    name = models.CharField(max_length=100, verbose_name="اسم النظام")
    description = models.TextField(blank=True, verbose_name="الوصف")

    # نوع النظام
    SYSTEM_TYPES = [
        ('MONTHLY', 'شهري'),
        ('WEEKLY', 'أسبوعي'),
        ('DAILY', 'يومي'),
        ('HOURLY', 'بالساعة'),
        ('PIECE_RATE', 'بالقطعة'),
    ]
    system_type = models.CharField(
        max_length=20, choices=SYSTEM_TYPES, default='MONTHLY',
        verbose_name="نوع النظام"
    )

    # العملة
    currency = models.ForeignKey(
        Currency, on_delete=models.PROTECT,
        verbose_name="العملة"
    )

    # الراتب الأساسي
    basic_salary = models.DecimalField(
        max_digits=12, decimal_places=2, default=0,
        verbose_name="الراتب الأساسي"
    )

    # الإعدادات
    include_overtime = models.BooleanField(default=True, verbose_name="يشمل العمل الإضافي")
    overtime_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=1.5,
        verbose_name="معدل العمل الإضافي"
    )

    # التأمينات والضرائب
    social_insurance_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=14.0,
        verbose_name="نسبة التأمينات الاجتماعية (%)"
    )
    tax_exemption = models.DecimalField(
        max_digits=12, decimal_places=2, default=9000,
        verbose_name="الإعفاء الضريبي"
    )

    class Meta:
        verbose_name = "نظام صرف المرتب"
        verbose_name_plural = "أنظمة صرف المرتبات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Employee(BaseModel):
    """الموظفين"""
    # ربط بنموذج الشخص
    person = models.OneToOneField(
        Person, on_delete=models.CASCADE,
        related_name='employee_profile', verbose_name="بيانات الشخص"
    )

    # معلومات الوظيفة
    employee_number = models.CharField(max_length=20, unique=True, verbose_name="رقم الموظف")
    department = models.ForeignKey(
        Department, on_delete=models.PROTECT,
        related_name='employees', verbose_name="القسم"
    )
    position = models.ForeignKey(
        Position, on_delete=models.PROTECT,
        related_name='employees', verbose_name="المنصب"
    )
    salary_system = models.ForeignKey(
        SalarySystem, on_delete=models.PROTECT,
        related_name='employees', verbose_name="نظام المرتب"
    )

    # تواريخ مهمة
    hire_date = models.DateField(verbose_name="تاريخ التعيين")
    contract_start_date = models.DateField(null=True, blank=True, verbose_name="تاريخ بداية العقد")
    contract_end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ نهاية العقد")
    termination_date = models.DateField(null=True, blank=True, verbose_name="تاريخ انتهاء الخدمة")

    # حالة الموظف
    STATUS_CHOICES = [
        ('ACTIVE', 'نشط'),
        ('INACTIVE', 'غير نشط'),
        ('TERMINATED', 'منتهي الخدمة'),
        ('SUSPENDED', 'موقوف'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='ACTIVE',
        verbose_name="حالة الموظف"
    )

    # معلومات المرتب
    current_salary = models.DecimalField(
        max_digits=12, decimal_places=2, default=0,
        verbose_name="المرتب الحالي"
    )

    class Meta:
        verbose_name = "موظف"
        verbose_name_plural = "الموظفين"
        ordering = ['employee_number']

    def __str__(self):
        return f"{self.person.name} ({self.employee_number})"

    @property
    def full_name(self):
        return self.person.name


class EmployeeSalary(BaseModel):
    """تفاصيل المرتبات للموظفين"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='salary_details', verbose_name="الموظف"
    )
    
    # الراتب الأساسي
    basic_salary = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="الراتب الأساسي"
    )
    
    # الإضافات
    housing_allowance = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="بدل السكن"
    )
    transportation_allowance = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="بدل النقل"
    )
    food_allowance = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="بدل الطعام"
    )
    other_allowances = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="إضافات أخرى"
    )
    
    # الخصومات
    social_insurance = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="التأمينات الاجتماعية"
    )
    tax_deduction = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="الخصم الضريبي"
    )
    other_deductions = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="خصومات أخرى"
    )
    
    # العمل الإضافي
    overtime_hours = models.DecimalField(
        max_digits=6, decimal_places=2, default=Decimal('0.00'),
        verbose_name="ساعات العمل الإضافي"
    )
    overtime_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('1.50'),
        verbose_name="معدل العمل الإضافي"
    )
    overtime_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="مبلغ العمل الإضافي"
    )
    
    # المجاميع
    total_allowances = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="إجمالي الإضافات"
    )
    total_deductions = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="إجمالي الخصومات"
    )
    net_salary = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal('0.00'),
        verbose_name="صافي المرتب"
    )
    
    # تاريخ سريان المرتب
    effective_date = models.DateField(verbose_name="تاريخ سريان المرتب")
    is_current = models.BooleanField(default=True, verbose_name="المرتب الحالي")
    
    # ملاحظات
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "تفاصيل مرتب الموظف"
        verbose_name_plural = "تفاصيل مرتبات الموظفين"
        ordering = ['-effective_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.effective_date}"
    
    def save(self, *args, **kwargs):
        # حساب الإضافات
        self.total_allowances = (
            self.housing_allowance + 
            self.transportation_allowance + 
            self.food_allowance + 
            self.other_allowances
        )
        
        # حساب العمل الإضافي - تحويل إلى Decimal أولاً
        basic_salary_decimal = Decimal(str(self.basic_salary))
        overtime_hours_decimal = Decimal(str(self.overtime_hours))
        overtime_rate_decimal = Decimal(str(self.overtime_rate))
        
        # حساب سعر الساعة (افتراض 160 ساعة شهرياً)
        hourly_rate = basic_salary_decimal / Decimal('160')
        self.overtime_amount = overtime_hours_decimal * overtime_rate_decimal * hourly_rate
        
        # حساب الخصومات
        self.total_deductions = (
            self.social_insurance + 
            self.tax_deduction + 
            self.other_deductions
        )
        
        # حساب صافي المرتب
        self.net_salary = (
            self.basic_salary + 
            self.total_allowances + 
            self.overtime_amount - 
            self.total_deductions
        )
        
        # إذا كان هذا المرتب الحالي، إلغاء تفعيل المرتبات السابقة
        if self.is_current:
            EmployeeSalary.objects.filter(
                employee=self.employee, 
                is_current=True
            ).exclude(pk=self.pk).update(is_current=False)
        
        super().save(*args, **kwargs)
    
    @property
    def gross_salary(self):
        """إجمالي المرتب قبل الخصومات"""
        return self.basic_salary + self.total_allowances + self.overtime_amount


class Attendance(BaseModel):
    """سجل الحضور والانصراف"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE, related_name='attendances', verbose_name="الموظف"
    )
    date = models.DateField(verbose_name="التاريخ")
    check_in = models.TimeField(verbose_name="وقت الحضور")
    check_out = models.TimeField(null=True, blank=True, verbose_name="وقت الانصراف")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    ATTENDANCE_STATES = [
        ('REGULAR', 'منتظم'),
        ('LATE', 'متأخر'),
        ('ABSENT', 'غائب'),
        ('COMPLETE', 'مكتمل'),
    ]
    attendance_state = models.CharField(
        max_length=10,
        choices=ATTENDANCE_STATES,
        blank=True,
        null=True,
        verbose_name="الحالة (اختياري)"
    )

    class Meta:
        verbose_name = "حضور وانصراف"
        verbose_name_plural = "سجلات الحضور والانصراف"
        unique_together = ('employee', 'date')
        ordering = ['-date', 'employee']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"

    @property
    def duration(self):
        if self.check_in and self.check_out:
            today = date.today()
            in_dt = datetime.combine(today, self.check_in)
            out_dt = datetime.combine(today, self.check_out)
            diff = out_dt - in_dt
            hours = diff.seconds // 3600
            minutes = (diff.seconds % 3600) // 60
            return f"{hours} ساعة {minutes} دقيقة" if hours or minutes else "-"
        return "-"

    @property
    def status(self):
        # مثال: بداية الدوام 08:00
        start_time = time(8, 0)
        if self.check_in:
            if self.check_in > start_time:
                return "متأخر"
            elif self.check_out:
                return "مكتمل"
            else:
                return "حاضر"
        return "غائب"


class AllowanceDeduction(BaseModel):
    TYPE_CHOICES = [
        ('allowance', 'إضافة'),
        ('deduction', 'خصم'),
    ]
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, verbose_name="النوع")
    allowance_type = models.ForeignKey('AllowanceType', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="نوع الإضافة")
    deduction_type = models.ForeignKey('DeductionType', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="نوع الخصم")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="القيمة")
    date = models.DateField(verbose_name="تاريخ الخصم")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    employee = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='allowance_deductions', verbose_name='الموظف', null=True, blank=True)

    class Meta:
        verbose_name = "إضافة/خصم"
        verbose_name_plural = "الإضافات والخصومات"
        ordering = ['-date']

    def __str__(self):
        if self.type == 'allowance' and self.allowance_type:
            return f"إضافة - {self.allowance_type.name} ({self.amount})"
        elif self.type == 'deduction' and self.deduction_type:
            return f"خصم - {self.deduction_type.name} ({self.amount})"
        return f"{self.get_type_display()} - {self.amount}"


class Overtime(BaseModel):
    """سجل العمل الإضافي"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE, related_name='overtimes', verbose_name="الموظف"
    )
    date = models.DateField(verbose_name="التاريخ")
    hours = models.DecimalField(max_digits=4, decimal_places=2, verbose_name="عدد الساعات")
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="سعر الساعة")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ الإجمالي")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    
    STATUS_CHOICES = [
        ('PENDING', 'قيد المراجعة'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='PENDING', verbose_name="الحالة"
    )

    class Meta:
        verbose_name = "عمل إضافي"
        verbose_name_plural = "سجلات العمل الإضافي"
        ordering = ['-date', 'employee']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date} ({self.hours} ساعة)"

    def save(self, *args, **kwargs):
        # حساب المبلغ الإجمالي تلقائياً
        self.total_amount = self.hours * self.hourly_rate
        super().save(*args, **kwargs)


class LeaveType(BaseModel):
    """أنواع الإجازات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود نوع الإجازة")
    name = models.CharField(max_length=100, verbose_name="اسم نوع الإجازة")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    
    # عدد الأيام المسموحة
    allowed_days = models.IntegerField(default=0, verbose_name="عدد الأيام المسموحة سنوياً")
    
    # نوع الإجازة
    LEAVE_CATEGORIES = [
        ('ANNUAL', 'إجازة سنوية'),
        ('SICK', 'إجازة مرضية'),
        ('EMERGENCY', 'إجازة طارئة'),
        ('MATERNITY', 'إجازة أمومة'),
        ('PATERNITY', 'إجازة أبوة'),
        ('STUDY', 'إجازة دراسية'),
        ('PILGRIMAGE', 'إجازة حج'),
        ('OTHER', 'أخرى'),
    ]
    category = models.CharField(
        max_length=20, choices=LEAVE_CATEGORIES, default='OTHER',
        verbose_name="فئة الإجازة"
    )
    
    # إعدادات الإجازة
    is_paid = models.BooleanField(default=True, verbose_name="إجازة مدفوعة")
    requires_approval = models.BooleanField(default=True, verbose_name="تتطلب موافقة")
    can_carry_forward = models.BooleanField(default=False, verbose_name="يمكن ترحيلها للعام التالي")
    max_carry_forward_days = models.IntegerField(default=0, verbose_name="أقصى أيام للترحيل")
    
    # لون الإجازة في التقويم
    color = models.CharField(max_length=7, default='#007bff', verbose_name="لون الإجازة")
    
    # حالة النوع
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "نوع إجازة"
        verbose_name_plural = "أنواع الإجازات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class LeaveBalance(BaseModel):
    """رصيد الإجازات للموظفين"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='leave_balances', verbose_name="الموظف"
    )
    leave_type = models.ForeignKey(
        LeaveType, on_delete=models.CASCADE,
        related_name='employee_balances', verbose_name="نوع الإجازة"
    )
    
    # السنة
    year = models.IntegerField(verbose_name="السنة")
    
    # الأرصدة
    total_days = models.IntegerField(default=0, verbose_name="إجمالي الأيام المخصصة")
    used_days = models.IntegerField(default=0, verbose_name="الأيام المستخدمة")
    remaining_days = models.IntegerField(default=0, verbose_name="الأيام المتبقية")
    carried_forward_days = models.IntegerField(default=0, verbose_name="الأيام المحولة من السنة السابقة")
    
    # ملاحظات
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "رصيد إجازة"
        verbose_name_plural = "أرصدة الإجازات"
        unique_together = ('employee', 'leave_type', 'year')
        ordering = ['-year', 'employee']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type.name} ({self.year})"
    
    def save(self, *args, **kwargs):
        # حساب الأيام المتبقية
        self.remaining_days = self.total_days - self.used_days
        super().save(*args, **kwargs)


class LeaveRequest(BaseModel):
    """طلبات الإجازات"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='leave_requests', verbose_name="الموظف"
    )
    leave_type = models.ForeignKey(
        LeaveType, on_delete=models.CASCADE,
        related_name='requests', verbose_name="نوع الإجازة"
    )
    
    # تواريخ الإجازة
    from_date = models.DateField(verbose_name="من تاريخ")
    to_date = models.DateField(verbose_name="إلى تاريخ")
    
    # عدد الأيام
    days_count = models.IntegerField(verbose_name="عدد الأيام")
    
    # سبب الإجازة
    reason = models.TextField(verbose_name="سبب الإجازة")
    
    # حالة الطلب
    STATUS_CHOICES = [
        ('PENDING', 'قيد المراجعة'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
        ('CANCELLED', 'ملغي'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='PENDING',
        verbose_name="حالة الطلب"
    )
    
    # معلومات الموافقة
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='approved_leaves', verbose_name="تمت الموافقة بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    approval_notes = models.TextField(blank=True, verbose_name="ملاحظات الموافقة")
    
    # معلومات إضافية
    is_emergency = models.BooleanField(default=False, verbose_name="إجازة طارئة")
    attachment = models.FileField(
        upload_to='leave_attachments/', null=True, blank=True,
        verbose_name="مرفقات"
    )
    
    # معلومات النظام
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True,
        related_name='created_leaves', verbose_name="تم الإنشاء بواسطة"
    )

    class Meta:
        verbose_name = "طلب إجازة"
        verbose_name_plural = "طلبات الإجازات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type.name} ({self.from_date} إلى {self.to_date})"
    
    def save(self, *args, **kwargs):
        # حساب عدد الأيام تلقائياً إذا لم يتم تحديدها
        if not self.days_count:
            self.days_count = (self.to_date - self.from_date).days + 1
        
        # إذا تمت الموافقة، تحديث رصيد الإجازات
        if self.status == 'APPROVED' and not self.approved_at:
            self.approved_at = timezone.now()
            self._update_leave_balance()
        
        super().save(*args, **kwargs)
    
    def _update_leave_balance(self):
        """تحديث رصيد الإجازات"""
        year = self.from_date.year
        balance, created = LeaveBalance.objects.get_or_create(
            employee=self.employee,
            leave_type=self.leave_type,
            year=year,
            defaults={
                'total_days': self.leave_type.allowed_days,
                'used_days': 0,
                'remaining_days': self.leave_type.allowed_days,
            }
        )
        
        if not created:
            balance.used_days += self.days_count
            balance.save()
    
    @property
    def duration_display(self):
        """عرض مدة الإجازة"""
        if self.days_count == 1:
            return "يوم واحد"
        elif self.days_count == 2:
            return "يومان"
        elif self.days_count <= 10:
            return f"{self.days_count} أيام"
        else:
            return f"{self.days_count} يوم"
    
    @property
    def status_badge_class(self):
        """فئة CSS للحالة"""
        status_classes = {
            'PENDING': 'bg-warning',
            'APPROVED': 'bg-success',
            'REJECTED': 'bg-danger',
            'CANCELLED': 'bg-secondary',
        }
        return status_classes.get(self.status, 'bg-secondary')


class LeaveCalendar(BaseModel):
    """تقويم الإجازات"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='calendar_events', verbose_name="الموظف"
    )
    leave_request = models.ForeignKey(
        LeaveRequest, on_delete=models.CASCADE,
        related_name='calendar_events', verbose_name="طلب الإجازة"
    )
    
    # معلومات الحدث
    title = models.CharField(max_length=200, verbose_name="عنوان الحدث")
    description = models.TextField(blank=True, verbose_name="الوصف")
    
    # تاريخ الحدث
    event_date = models.DateField(verbose_name="تاريخ الحدث")
    
    # نوع الحدث
    EVENT_TYPES = [
        ('START', 'بداية الإجازة'),
        ('END', 'نهاية الإجازة'),
        ('FULL_DAY', 'يوم كامل'),
    ]
    event_type = models.CharField(
        max_length=20, choices=EVENT_TYPES, default='FULL_DAY',
        verbose_name="نوع الحدث"
    )
    
    # لون الحدث
    color = models.CharField(max_length=7, default='#007bff', verbose_name="لون الحدث")

    class Meta:
        verbose_name = "حدث تقويم الإجازات"
        verbose_name_plural = "أحداث تقويم الإجازات"
        ordering = ['event_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.title} ({self.event_date})"


class AbsenceRecord(BaseModel):
    """سجل الغياب والتأخير"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='absence_records', verbose_name="الموظف"
    )
    
    # نوع المخالفة
    ABSENCE_TYPES = [
        ('ABSENCE', 'غياب'),
        ('LATE', 'تأخير'),
        ('EARLY_LEAVE', 'انصراف مبكر'),
        ('ABSENCE_WITHOUT_PERMISSION', 'غياب بدون إذن'),
        ('LATE_WITHOUT_PERMISSION', 'تأخير بدون إذن'),
    ]
    absence_type = models.CharField(
        max_length=30, choices=ABSENCE_TYPES, verbose_name="نوع المخالفة"
    )
    
    # التاريخ والوقت
    date = models.DateField(verbose_name="التاريخ")
    time_from = models.TimeField(null=True, blank=True, verbose_name="من وقت")
    time_to = models.TimeField(null=True, blank=True, verbose_name="إلى وقت")
    
    # المدة
    duration_hours = models.DecimalField(
        max_digits=4, decimal_places=2, default=0,
        verbose_name="المدة بالساعات"
    )
    duration_minutes = models.IntegerField(default=0, verbose_name="المدة بالدقائق")
    
    # السبب والتفاصيل
    reason = models.TextField(verbose_name="السبب")
    details = models.TextField(blank=True, verbose_name="تفاصيل إضافية")
    
    # الخصم المالي
    deduction_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0,
        verbose_name="مبلغ الخصم"
    )
    currency = models.ForeignKey(
        Currency, on_delete=models.PROTECT,
        verbose_name="العملة"
    )
    
    # حالة السجل
    STATUS_CHOICES = [
        ('PENDING', 'قيد المراجعة'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
        ('CANCELLED', 'ملغي'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='PENDING',
        verbose_name="الحالة"
    )
    
    # معلومات الموافقة
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='approved_absences', verbose_name="تمت الموافقة بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    approval_notes = models.TextField(blank=True, verbose_name="ملاحظات الموافقة")
    
    # معلومات النظام
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_absences', verbose_name="تم الإنشاء بواسطة"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='updated_absences', verbose_name="تم التحديث بواسطة"
    )
    
    # مرفقات
    attachment = models.FileField(
        upload_to='absence_attachments/', null=True, blank=True,
        verbose_name="مرفقات"
    )
    
    # ملاحظات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "سجل غياب/تأخير"
        verbose_name_plural = "سجلات الغياب والتأخير"
        ordering = ['-date', '-created_at']
        unique_together = ('employee', 'date', 'absence_type')

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_absence_type_display()} - {self.date}"

    def save(self, *args, **kwargs):
        # حساب المدة تلقائياً إذا تم تحديد الأوقات
        if self.time_from and self.time_to:
            from datetime import datetime, timedelta
            today = date.today()
            start_dt = datetime.combine(today, self.time_from)
            end_dt = datetime.combine(today, self.time_to)
            duration = end_dt - start_dt
            self.duration_hours = duration.total_seconds() / 3600
            self.duration_minutes = int((duration.total_seconds() % 3600) / 60)
        
        # حساب مبلغ الخصم تلقائياً إذا لم يتم تحديده
        if self.deduction_amount == 0:
            self.deduction_amount = self._calculate_deduction()
        
        super().save(*args, **kwargs)

    def _calculate_deduction(self):
        """حساب مبلغ الخصم تلقائياً"""
        # يمكن تخصيص هذه القيم حسب سياسة الشركة
        deduction_rates = {
            'ABSENCE': 100,  # 100 ج.م للغياب
            'LATE': 25,      # 25 ج.م للتأخير
            'EARLY_LEAVE': 50,  # 50 ج.م للانصراف المبكر
            'ABSENCE_WITHOUT_PERMISSION': 200,  # 200 ج.م للغياب بدون إذن
            'LATE_WITHOUT_PERMISSION': 50,  # 50 ج.م للتأخير بدون إذن
        }
        return Decimal(deduction_rates.get(self.absence_type, 0))

    @property
    def duration_display(self):
        """عرض المدة بتنسيق مقروء"""
        if self.duration_hours > 0:
            hours = int(self.duration_hours)
            minutes = self.duration_minutes
            if hours > 0 and minutes > 0:
                return f"{hours} ساعة {minutes} دقيقة"
            elif hours > 0:
                return f"{hours} ساعة"
            else:
                return f"{minutes} دقيقة"
        elif self.absence_type == 'ABSENCE':
            return "يوم كامل"
        return "غير محدد"

    @property
    def status_badge_class(self):
        """فئة CSS للحالة"""
        status_classes = {
            'PENDING': 'bg-warning',
            'APPROVED': 'bg-success',
            'REJECTED': 'bg-danger',
            'CANCELLED': 'bg-secondary',
        }
        return status_classes.get(self.status, 'bg-secondary')

    @property
    def absence_type_badge_class(self):
        """فئة CSS لنوع الغياب"""
        type_classes = {
            'ABSENCE': 'bg-danger',
            'LATE': 'bg-warning',
            'EARLY_LEAVE': 'bg-info',
            'ABSENCE_WITHOUT_PERMISSION': 'bg-dark',
            'LATE_WITHOUT_PERMISSION': 'bg-warning',
        }
        return type_classes.get(self.absence_type, 'bg-secondary')


class Contract(models.Model):
    CONTRACT_TYPES = [
        ('PERMANENT', 'دائم'),
        ('TEMPORARY', 'مؤقت'),
        ('PART_TIME', 'دوام جزئي'),
        ('CONSULTANT', 'استشاري'),
        ('OTHER', 'أخرى'),
    ]
    employee = models.ForeignKey('Employee', on_delete=models.CASCADE, related_name='contracts', verbose_name="الموظف")
    contract_type = models.CharField(max_length=20, choices=CONTRACT_TYPES, verbose_name="نوع العقد")
    start_date = models.DateField(verbose_name="تاريخ بداية العقد")
    end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ نهاية العقد")
    salary = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="الراتب")
    is_active = models.BooleanField(default=True, verbose_name="عقد نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    contract_file = models.FileField(
        upload_to='contracts/',
        null=True,
        blank=True,
        verbose_name="ملف العقد (PDF أو صورة)",
        help_text="يسمح فقط بملفات PDF أو صور JPG/PNG"
    )
    archived = models.BooleanField(default=False, verbose_name="مؤرشف")

    class Meta:
        verbose_name = "عقد عمل"
        verbose_name_plural = "عقود العمل"
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_contract_type_display()} ({self.start_date})"


class Promotion(BaseModel):
    """سجل الترقيات"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='promotions', verbose_name="الموظف"
    )
    
    # المناصب
    from_position = models.ForeignKey(
        Position, on_delete=models.PROTECT,
        related_name='promotions_from', verbose_name="المنصب السابق"
    )
    to_position = models.ForeignKey(
        Position, on_delete=models.PROTECT,
        related_name='promotions_to', verbose_name="المنصب الجديد"
    )
    
    # المرتبات
    from_salary = models.DecimalField(
        max_digits=12, decimal_places=2, verbose_name="الراتب السابق"
    )
    to_salary = models.DecimalField(
        max_digits=12, decimal_places=2, verbose_name="الراتب الجديد"
    )
    
    # تواريخ الترقية
    promotion_date = models.DateField(verbose_name="تاريخ الترقية")
    effective_date = models.DateField(verbose_name="تاريخ سريان الترقية")
    
    # سبب الترقية
    reason = models.TextField(verbose_name="سبب الترقية")
    
    # حالة الترقية
    STATUS_CHOICES = [
        ('PENDING', 'قيد المراجعة'),
        ('APPROVED', 'معتمدة'),
        ('REJECTED', 'مرفوضة'),
        ('CANCELLED', 'ملغية'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='PENDING',
        verbose_name="حالة الترقية"
    )
    
    # معلومات الموافقة
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='approved_promotions', verbose_name="تمت الموافقة بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    approval_notes = models.TextField(blank=True, verbose_name="ملاحظات الموافقة")
    
    # معلومات النظام
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_promotions', verbose_name="تم الإنشاء بواسطة"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='updated_promotions', verbose_name="تم التحديث بواسطة"
    )
    
    # مرفقات
    attachment = models.FileField(
        upload_to='promotion_attachments/', null=True, blank=True,
        verbose_name="مرفقات"
    )
    
    # ملاحظات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    
    class Meta:
        verbose_name = "ترقية"
        verbose_name_plural = "الترقيات"
        ordering = ['-promotion_date', 'employee']
    
    def __str__(self):
        return f"{self.employee.full_name} - {self.from_position.name} إلى {self.to_position.name}"
    
    @property
    def salary_increase(self):
        """حساب زيادة الراتب"""
        return self.to_salary - self.from_salary
    
    @property
    def salary_increase_percentage(self):
        """حساب نسبة زيادة الراتب"""
        if self.from_salary > 0:
            return ((self.to_salary - self.from_salary) / self.from_salary) * 100
        return 0
    
    @property
    def status_badge_class(self):
        """فئة CSS للحالة"""
        status_classes = {
            'PENDING': 'bg-warning',
            'APPROVED': 'bg-success',
            'REJECTED': 'bg-danger',
            'CANCELLED': 'bg-secondary',
        }
        return status_classes.get(self.status, 'bg-secondary')
    
    def save(self, *args, **kwargs):
        # تحديث معلومات النظام
        if not self.pk:  # إنشاء جديد
            self.created_by = getattr(self, '_current_user', None)
        else:  # تحديث
            self.updated_by = getattr(self, '_current_user', None)
        
        super().save(*args, **kwargs)
        
        # إذا كانت الترقية معتمدة، تحديث بيانات الموظف
        if self.status == 'APPROVED':
            self.employee.position = self.to_position
            self.employee.current_salary = self.to_salary
            self.employee.save()


class Transfer(BaseModel):
    """سجل النقل والتحويل"""
    employee = models.ForeignKey(
        Employee, on_delete=models.CASCADE,
        related_name='transfers', verbose_name="الموظف"
    )
    
    # الأقسام
    from_department = models.ForeignKey(
        Department, on_delete=models.PROTECT,
        related_name='transfers_from', verbose_name="القسم السابق"
    )
    to_department = models.ForeignKey(
        Department, on_delete=models.PROTECT,
        related_name='transfers_to', verbose_name="القسم الجديد"
    )
    
    # المناصب
    from_position = models.ForeignKey(
        Position, on_delete=models.PROTECT,
        related_name='transfers_from', verbose_name="المنصب السابق"
    )
    to_position = models.ForeignKey(
        Position, on_delete=models.PROTECT,
        related_name='transfers_to', verbose_name="المنصب الجديد"
    )
    
    # نوع النقل
    TRANSFER_TYPES = [
        ('DEPARTMENT', 'نقل بين الأقسام'),
        ('POSITION', 'نقل منصب'),
        ('BRANCH', 'نقل فرع'),
        ('TEMPORARY', 'نقل مؤقت'),
        ('PERMANENT', 'نقل دائم'),
        ('SECONDMENT', 'إعارة'),
        ('OTHER', 'أخرى'),
    ]
    transfer_type = models.CharField(
        max_length=20, choices=TRANSFER_TYPES, default='DEPARTMENT',
        verbose_name="نوع النقل"
    )
    
    # تواريخ النقل
    transfer_date = models.DateField(verbose_name="تاريخ النقل")
    effective_date = models.DateField(verbose_name="تاريخ سريان النقل")
    end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ انتهاء النقل")
    
    # سبب النقل
    reason = models.TextField(verbose_name="سبب النقل")
    
    # حالة النقل
    STATUS_CHOICES = [
        ('PENDING', 'قيد المراجعة'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
        ('CANCELLED', 'ملغي'),
        ('COMPLETED', 'مكتمل'),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='PENDING',
        verbose_name="حالة النقل"
    )
    
    # معلومات الموافقة
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='approved_hr_transfers', verbose_name="تمت الموافقة بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    approval_notes = models.TextField(blank=True, verbose_name="ملاحظات الموافقة")
    
    # معلومات النظام
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_hr_transfers', verbose_name="تم الإنشاء بواسطة"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='updated_hr_transfers', verbose_name="تم التحديث بواسطة"
    )
    
    # مرفقات
    attachment = models.FileField(
        upload_to='transfer_attachments/', null=True, blank=True,
        verbose_name="مرفقات"
    )
    
    # ملاحظات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    
    # معلومات إضافية
    is_temporary = models.BooleanField(default=False, verbose_name="نقل مؤقت")
    return_date = models.DateField(null=True, blank=True, verbose_name="تاريخ العودة")
    replacement_employee = models.ForeignKey(
        Employee, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='replacement_transfers', verbose_name="الموظف البديل"
    )
    
    class Meta:
        verbose_name = "نقل/تحويل"
        verbose_name_plural = "النقل والتحويل"
        ordering = ['-transfer_date', 'employee']

    def __str__(self):
        return f"نقل {self.employee.full_name} من {self.from_department.name} إلى {self.to_department.name}"

    @property
    def status_badge_class(self):
        """فئة CSS للحالة"""
        status_classes = {
            'PENDING': 'badge-warning',
            'APPROVED': 'badge-success',
            'REJECTED': 'badge-danger',
            'CANCELLED': 'badge-secondary',
            'COMPLETED': 'badge-info',
        }
        return status_classes.get(self.status, 'badge-secondary')

    @property
    def transfer_type_display(self):
        """عرض نوع النقل بالعربية"""
        type_display = dict(self.TRANSFER_TYPES)
        return type_display.get(self.transfer_type, self.transfer_type)

    def save(self, *args, **kwargs):
        # تحديث معلومات النظام
        if not self.pk:  # إنشاء جديد
            self.created_by = getattr(self, '_current_user', None)
        else:  # تحديث
            self.updated_by = getattr(self, '_current_user', None)
        
        super().save(*args, **kwargs)
        
        # إذا تمت الموافقة، تحديث بيانات الموظف
        if self.status == 'APPROVED' and not self._state.adding:
            self._update_employee_data()

    def _update_employee_data(self):
        """تحديث بيانات الموظف بعد الموافقة على النقل"""
        try:
            employee = self.employee
            employee.department = self.to_department
            employee.position = self.to_position
            employee.save()
        except Exception as e:
            # تسجيل الخطأ في السجلات
            print(f"Error updating employee data: {e}")


class Termination(models.Model):
    """نموذج إنهاء الخدمة"""
    
    TERMINATION_TYPES = [
        ('RESIGNATION', 'استقالة'),
        ('DISMISSAL', 'فصل من الخدمة'),
        ('RETIREMENT', 'تقاعد'),
        ('CONTRACT_END', 'انتهاء العقد'),
        ('DEATH', 'وفاة'),
        ('OTHER', 'أخرى'),
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'قيد الانتظار'),
        ('APPROVED', 'موافق عليه'),
        ('REJECTED', 'مرفوض'),
        ('COMPLETED', 'مكتمل'),
    ]
    
    employee = models.ForeignKey('Employee', on_delete=models.CASCADE, verbose_name='الموظف')
    termination_type = models.CharField(max_length=20, choices=TERMINATION_TYPES, verbose_name='نوع إنهاء الخدمة')
    reason = models.TextField(verbose_name='سبب إنهاء الخدمة')
    notice_period = models.IntegerField(default=30, verbose_name='فترة الإشعار (بالأيام)')
    last_working_day = models.DateField(verbose_name='آخر يوم عمل')
    termination_date = models.DateField(verbose_name='تاريخ إنهاء الخدمة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', verbose_name='الحالة')
    
    # معلومات الموافقة
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تمت الموافقة من')
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الموافقة')
    approval_notes = models.TextField(blank=True, verbose_name='ملاحظات الموافقة')
    
    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='terminations_created', verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    class Meta:
        verbose_name = 'إنهاء خدمة'
        verbose_name_plural = 'إنهاء الخدمة'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"إنهاء خدمة - {self.employee} - {self.get_termination_type_display()}"
    
    def get_service_duration(self):
        """حساب مدة الخدمة"""
        if self.employee.hire_date:
            from datetime import date
            today = date.today()
            duration = today - self.employee.hire_date
            years = duration.days // 365
            months = (duration.days % 365) // 30
            return f"{years} سنة و {months} شهر"
        return "غير محدد"
    
    def can_be_approved(self):
        """التحقق من إمكانية الموافقة"""
        return self.status == 'PENDING'
    
    def can_be_rejected(self):
        """التحقق من إمكانية الرفض"""
        return self.status == 'PENDING'


class SalaryAddition(models.Model):
    """نموذج إضافات المرتبات"""
    ADDITION_TYPES = [
        ('HOUSING', 'بدل سكن'),
        ('TRANSPORTATION', 'بدل نقل'),
        ('FOOD', 'بدل طعام'),
        ('SPECIAL', 'بدل خاص'),
        ('PERFORMANCE', 'بدل أداء'),
        ('OVERTIME', 'بدل عمل إضافي'),
        ('OTHER', 'إضافات أخرى'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, verbose_name='الموظف')
    addition_type = models.CharField(max_length=20, choices=ADDITION_TYPES, verbose_name='نوع الإضافة')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='أنشئ بواسطة')
    
    class Meta:
        verbose_name = 'إضافة مرتب'
        verbose_name_plural = 'إضافات المرتبات'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.employee.full_name} - {self.get_addition_type_display()} - {self.amount}"
    
    @property
    def is_current(self):
        """هل الإضافة سارية المفعول حالياً"""
        today = timezone.now().date()
        return (self.is_active and 
                self.start_date <= today and 
                (self.end_date is None or self.end_date >= today))


class AllowanceType(BaseModel):
    """أنواع الإضافات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود نوع الإضافة")
    name = models.CharField(max_length=100, verbose_name="اسم نوع الإضافة")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_taxable = models.BooleanField(default=False, verbose_name="خاضعة للضريبة؟")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "نوع إضافة"
        verbose_name_plural = "أنواع الإضافات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class DeductionType(BaseModel):
    """أنواع الخصومات"""
    code = models.CharField(max_length=20, unique=True, verbose_name="كود نوع الخصم")
    name = models.CharField(max_length=100, verbose_name="اسم نوع الخصم")
    name_english = models.CharField(max_length=100, blank=True, verbose_name="الاسم بالإنجليزية")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_statutory = models.BooleanField(default=False, verbose_name="خصم إلزامي (قانوني)؟")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "نوع خصم"
        verbose_name_plural = "أنواع الخصومات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"
