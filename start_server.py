#!/usr/bin/env python
"""
Django Development Server Starter
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
    
    # Setup Django
    django.setup()
    
    print("=" * 60)
    print("Starting Django Development Server")
    print("=" * 60)
    print()
    print("Server URL: http://127.0.0.1:8000")
    print("Admin Panel: http://127.0.0.1:8000/admin/")
    print("Manufacturing Orders: http://127.0.0.1:8000/inventory/manufacturing-orders/")
    print()
    print("Manufacturing Order System Features:")
    print("   - Create and manage manufacturing orders")
    print("   - Automatic cost calculation")
    print("   - Material management with real-time pricing")
    print("   - Production tracking and status management")
    print("   - Comprehensive reporting and analytics")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    print()
    
    try:
        # Start the development server
        execute_from_command_line([
            'manage.py', 
            'runserver', 
            '127.0.0.1:8000',
            '--verbosity=1'
        ])
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\nError starting server: {e}")
        sys.exit(1)