@echo off
REM ===================================================
REM سكريبت تشغيل الخادم الذكي
REM Smart Server Startup Script
REM ===================================================

title الخادم الذكي - نظام المحاسبة - Smart Server

echo.
echo ========================================
echo    🧠 الخادم الذكي - نظام المحاسبة
echo    Smart Server - Accounting System
echo ========================================
echo.

REM تحديد مجلد المشروع
cd /d "%~dp0"

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المتطلبات...
pip install django psutil >nul 2>&1

echo ✅ المتطلبات جاهزة

echo.
echo 🎯 خيارات الخادم الذكي:
echo ==========================
echo 1. تشغيل الخادم الذكي (يحل جميع المشاكل تلقائياً)
echo 2. إصلاح الأخطاء أولاً ثم تشغيل الخادم
echo 3. تشخيص المشاكل فقط
echo 4. عرض سجل الخادم الذكي
echo 5. عرض معلومات النظام
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" goto smart_server
if "%choice%"=="2" goto fix_then_run
if "%choice%"=="3" goto diagnose_only
if "%choice%"=="4" goto show_log
if "%choice%"=="5" goto system_info
if "%choice%"=="0" goto exit
goto invalid_choice

:smart_server
echo.
echo 🧠 تشغيل الخادم الذكي...
echo =========================
echo.
echo 🔥 مميزات الخادم الذكي:
echo • يجد منفذ متاح تلقائياً
echo • يحل مشاكل المنفذ المستخدم
echo • يحضر البيئة تلقائياً
echo • مراقبة مستمرة للصحة
echo • إعادة تشغيل تلقائية
echo • وصول من أي جهاز في الشبكة
echo.
echo 🌐 سيتم عرض معلومات الوصول بعد التشغيل
echo ⌨️ اضغط Ctrl+C لإيقاف الخادم بأمان
echo.
pause
python smart_server.py
goto end

:fix_then_run
echo.
echo 🔧 إصلاح الأخطاء أولاً...
python fix_errors.py
echo.
echo 🧠 تشغيل الخادم الذكي...
python smart_server.py
goto end

:diagnose_only
echo.
echo 🔍 تشخيص المشاكل...
python diagnose_server.py
echo.
pause
goto start

:show_log
echo.
echo 📊 سجل الخادم الذكي:
echo ====================
if exist "smart_server.log" (
    echo آخر 20 سطر من سجل الخادم الذكي:
    echo.
    powershell "Get-Content smart_server.log | Select-Object -Last 20"
) else (
    echo لا يوجد سجل للخادم الذكي حتى الآن
    echo قم بتشغيل الخادم الذكي أولاً
)
echo.
pause
goto start

:system_info
echo.
echo 💻 معلومات النظام:
echo ==================
python -c "
import platform
import sys
import socket
try:
    import psutil
    print(f'نظام التشغيل: {platform.system()} {platform.release()}')
    print(f'Python: {platform.python_version()}')
    try:
        import django
        print(f'Django: {django.get_version()}')
    except:
        print('Django: غير مثبت')
    print(f'اسم الجهاز: {socket.gethostname()}')
    
    # الحصول على IP المحلي
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(('*******', 80))
    local_ip = s.getsockname()[0]
    s.close()
    print(f'عنوان IP المحلي: {local_ip}')
    
    print(f'المعالج: {psutil.cpu_count()} cores')
    print(f'الذاكرة: {psutil.virtual_memory().total // (1024**3)} GB')
    print(f'مساحة القرص: {psutil.disk_usage('.').total // (1024**3)} GB')
    print(f'استخدام المعالج: {psutil.cpu_percent()}%%')
    print(f'استخدام الذاكرة: {psutil.virtual_memory().percent}%%')
    
    # فحص المنافذ المتاحة
    available_ports = []
    for port in [8000, 8001, 8002, 8003, 8080]:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            if result != 0:
                available_ports.append(port)
        except:
            available_ports.append(port)
    
    print(f'المنافذ المتاحة: {available_ports}')
    
except ImportError:
    print('نظام التشغيل:', platform.system(), platform.release())
    print('Python:', platform.python_version())
    try:
        import django
        print('Django:', django.get_version())
    except:
        print('Django: غير مثبت')
    print('psutil غير مثبت - لا يمكن عرض معلومات النظام الكاملة')
"
echo.
pause
goto start

:invalid_choice
echo ❌ خيار غير صحيح
pause
goto start

:exit
echo 👋 شكراً لاستخدام الخادم الذكي!
exit /b 0

:end
echo.
echo 🎉 تم إنهاء العملية
pause

:start
cls
goto start
