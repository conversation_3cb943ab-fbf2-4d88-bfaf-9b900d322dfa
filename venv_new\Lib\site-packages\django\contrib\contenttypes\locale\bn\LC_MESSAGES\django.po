# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# Ta<PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Bengali (http://www.transifex.com/django/django/language/"
"bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "কনটেন্ট টাইপসমূহ"

msgid "python model class name"
msgstr "পাইথন মডেল ক্লাসের নাম"

msgid "content type"
msgstr "কনটেন্ট টাইপ"

msgid "content types"
msgstr "কনটেন্ট টাইপ সমূহ"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "কনটেন্ট টাইপ %(ct_id)s অবজেক্টের সাথে সংযুক্ত কোনো মডেল নেই"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s অবজেক্টের কোনো get_absolute_url() মেথড নেই"
