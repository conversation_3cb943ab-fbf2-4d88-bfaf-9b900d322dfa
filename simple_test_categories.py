#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.forms import ManufacturingOrderForm
from definitions.models import ItemCategory

def simple_test():
    print("=== Testing Category Selection ===")
    
    # Test form creation
    form = ManufacturingOrderForm()
    print("Form created successfully")
    
    # Check categories
    categories = ItemCategory.objects.filter(is_active=True)
    print(f"Total categories available: {categories.count()}")
    
    # Check form field
    form_categories = form.fields['finished_product_category'].queryset
    print(f"Categories in form: {form_categories.count()}")
    
    print("\nCategories list:")
    for i, cat in enumerate(categories, 1):
        print(f"{i}. {cat.code}: {cat.name}")
    
    print("\nSUCCESS!")
    print("Manufacturing Order now uses Item Categories")
    print("Field: finished_product_category")
    print("Shows all active categories from definitions")

if __name__ == "__main__":
    simple_test()