{% extends 'base/base.html' %}
{% block title %}طباعة الخصم{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-primary">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fas fa-print me-2"></i>بيان خصم من المرتب</h4>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th>الموظف</th>
                            <td>
                                {% if object.employee %}
                                    {% if object.employee.full_name %}
                                        {{ object.employee.full_name }}
                                    {% elif object.employee.person and object.employee.person.name %}
                                        {{ object.employee.person.name }}
                                    {% elif object.employee.person %}
                                        {{ object.employee.person }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>نوع الخصم</th>
                            <td>{{ object.name }}</td>
                        </tr>
                        <tr>
                            <th>المبلغ</th>
                            <td><strong class="text-danger">{{ object.amount|floatformat:2 }} ج.م</strong></td>
                        </tr>
                        <tr>
                            <th>تاريخ الخصم</th>
                            <td>{{ object.date|date:'Y-m-d' }}</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>{% if object.is_active %}<span class="badge bg-success">مطبق</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ object.notes|default:'-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="card-footer text-end d-print-none">
                    <button onclick="window.print()" class="btn btn-info"><i class="fas fa-print me-2"></i>طباعة</button>
                    <a href="{% url 'hr:salary_deduction_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 