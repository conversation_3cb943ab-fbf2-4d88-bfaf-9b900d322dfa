{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-list text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة أنواع الإجازات المختلفة</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:leave_type_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة نوع إجازة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Types Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة أنواع الإجازات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>نوع الإجازة</th>
                                    <th>عدد الأيام المسموح</th>
                                    <th>مدفوعة الأجر</th>
                                    <th>قابلة للتراكم</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave_type in leave_types %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                        {{ leave_type.name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ leave_type.allowed_days }} يوم</span>
                                    </td>
                                    <td>
                                        {% if leave_type.is_paid %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if leave_type.can_carry_forward %}
                                            <span class="badge bg-info">قابلة للتراكم</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير قابلة للتراكم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if leave_type.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:leave_type_edit' leave_type.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="post" action="{% url 'hr:leave_type_delete' leave_type.pk %}" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا النوع؟')">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-alt fa-3x mb-3 d-block"></i>
                                        لا توجد أنواع إجازات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
