from django.db import models, transaction
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
from django.core.exceptions import ValidationError
from definitions.models import BaseModel, Item, Warehouse, Currency
from decimal import Decimal


class StockMovementType(models.TextChoices):
    """أنواع حركات المخزون"""
    IN = 'IN', 'إدخال'
    OUT = 'OUT', 'إخراج'
    TRANSFER = 'TRANSFER', 'تحويل'
    ADJUSTMENT = 'ADJUSTMENT', 'تسوية'
    PRODUCTION = 'PRODUCTION', 'إنتاج'
    OPENING = 'OPENING', 'جرد افتتاحي'


class StockMovement(BaseModel):
    """حركات المخزون"""
    movement_type = models.CharField(max_length=20, choices=StockMovementType.choices,
                                     verbose_name="نوع الحركة")
    reference_number = models.CharField(max_length=50, unique=True, verbose_name="رقم المرجع")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                       verbose_name="إجمالي المبلغ")

    class Meta:
        verbose_name = "حركة مخزون"
        verbose_name_plural = "حركات المخزون"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.reference_number} - {self.get_movement_type_display()}"


class StockMovementItem(BaseModel):
    """أصناف حركات المخزون"""
    movement = models.ForeignKey(StockMovement, on_delete=models.CASCADE,
                                 related_name="items", verbose_name="الحركة")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")

    class Meta:
        verbose_name = "صنف حركة مخزون"
        verbose_name_plural = "أصناف حركات المخزون"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity}"


class Stock(BaseModel):
    """أرصدة المخزون"""
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المخزن")
    item = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                   verbose_name="الكمية")
    reserved_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                            verbose_name="الكمية المحجوزة")
    available_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                             verbose_name="الكمية المتاحة")
    average_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                       verbose_name="متوسط التكلفة")
    last_movement_date = models.DateTimeField(null=True, blank=True,
                                              verbose_name="تاريخ آخر حركة")

    class Meta:
        verbose_name = "رصيد مخزون"
        verbose_name_plural = "أرصدة المخزون"
        unique_together = ['warehouse', 'item']

    def save(self, *args, **kwargs):
        self.available_quantity = self.quantity - self.reserved_quantity
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.warehouse.name} - {self.item.name}: {self.quantity}"


class StockTransfer(BaseModel):
    """تحويلات المخزون"""
    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    date = models.DateField(verbose_name="التاريخ")
    from_warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT,
                                       related_name="transfers_out",
                                       verbose_name="من مخزن")
    to_warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT,
                                     related_name="transfers_in",
                                     verbose_name="إلى مخزن")
    status = models.CharField(max_length=20, choices=[
        ('PENDING', 'في الانتظار'),
        ('APPROVED', 'معتمد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ], default='PENDING', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "تحويل مخزون"
        verbose_name_plural = "تحويلات المخزون"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.transfer_number} - {self.from_warehouse} إلى {self.to_warehouse}"


class StockTransferItem(BaseModel):
    """أصناف تحويلات المخزون"""
    transfer = models.ForeignKey(StockTransfer, on_delete=models.CASCADE,
                                 related_name="items", verbose_name="التحويل")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")

    class Meta:
        verbose_name = "صنف تحويل مخزون"
        verbose_name_plural = "أصناف تحويلات المخزون"

    def __str__(self):
        return f"{self.item.name} - {self.quantity}"


class StockAdjustment(BaseModel):
    """تسويات المخزون"""
    adjustment_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التسوية")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    reason = models.CharField(max_length=200, verbose_name="السبب")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "تسوية مخزون"
        verbose_name_plural = "تسويات المخزون"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.adjustment_number} - {self.warehouse}"


class StockAdjustmentItem(BaseModel):
    """أصناف تسويات المخزون"""
    adjustment = models.ForeignKey(StockAdjustment, on_delete=models.CASCADE,
                                   related_name="items", verbose_name="التسوية")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    book_quantity = models.DecimalField(max_digits=15, decimal_places=3,
                                        verbose_name="الكمية الدفترية")
    actual_quantity = models.DecimalField(max_digits=15, decimal_places=3,
                                          verbose_name="الكمية الفعلية")
    difference = models.DecimalField(max_digits=15, decimal_places=3,
                                     verbose_name="الفرق")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")

    class Meta:
        verbose_name = "صنف تسوية مخزون"
        verbose_name_plural = "أصناف تسويات المخزون"

    def save(self, *args, **kwargs):
        self.difference = self.actual_quantity - self.book_quantity
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - فرق: {self.difference}"


class StockIncrease(BaseModel):
    """إذن إضافة الزيادات"""
    increase_number = models.CharField(max_length=50, unique=True, verbose_name="رقم إذن الزيادة")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    reason = models.CharField(max_length=200, verbose_name="سبب الزيادة")
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'مسودة'),
        ('APPROVED', 'معتمد'),
        ('APPLIED', 'مطبق'),
        ('CANCELLED', 'ملغي'),
    ], default='DRAFT', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                       verbose_name="إجمالي المبلغ")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_increases", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")
    applied_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name="applied_increases", verbose_name="مطبق من")
    applied_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ التطبيق")

    class Meta:
        verbose_name = "إذن إضافة زيادة"
        verbose_name_plural = "أذون إضافة الزيادات"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.increase_number} - {self.warehouse}"


class StockIncreaseItem(BaseModel):
    """أصناف إذن إضافة الزيادات"""
    increase = models.ForeignKey(StockIncrease, on_delete=models.CASCADE,
                                 related_name="items", verbose_name="إذن الزيادة")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية المضافة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف إذن زيادة"
        verbose_name_plural = "أصناف أذون الزيادات"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity}"


class StockDecrease(BaseModel):
    """إذن صرف النواقص"""
    decrease_number = models.CharField(max_length=50, unique=True, verbose_name="رقم إذن النقص")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    reason = models.CharField(max_length=200, verbose_name="سبب النقص")
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'مسودة'),
        ('APPROVED', 'معتمد'),
        ('APPLIED', 'مطبق'),
        ('CANCELLED', 'ملغي'),
    ], default='DRAFT', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                       verbose_name="إجمالي المبلغ")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_decreases", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "إذن صرف نقص"
        verbose_name_plural = "أذون صرف النواقص"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.decrease_number} - {self.warehouse}"


class StockDecreaseItem(BaseModel):
    """أصناف إذن صرف النواقص"""
    decrease = models.ForeignKey(StockDecrease, on_delete=models.CASCADE,
                                 related_name="items", verbose_name="إذن النقص")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية المنقصة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف إذن نقص"
        verbose_name_plural = "أصناف أذون النواقص"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity}"


class GoodsReceivedOnLoan(BaseModel):
    """بضاعة مضافة سلفة من الغير"""
    loan_number = models.CharField(max_length=50, unique=True, verbose_name="رقم السلفة")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    lender_name = models.CharField(max_length=200, verbose_name="اسم المُقرض")
    lender_phone = models.CharField(max_length=20, blank=True, verbose_name="هاتف المُقرض")
    lender_address = models.TextField(blank=True, verbose_name="عنوان المُقرض")
    loan_reason = models.CharField(max_length=200, verbose_name="سبب السلفة")
    expected_return_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الإرجاع المتوقع")
    status = models.CharField(max_length=20, choices=[
        ('RECEIVED', 'مستلمة'),
        ('PARTIAL_RETURNED', 'مرتجعة جزئياً'),
        ('RETURNED', 'مرتجعة بالكامل'),
        ('OVERDUE', 'متأخرة'),
        ('CANCELLED', 'ملغاة'),
    ], default='RECEIVED', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_estimated_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                                verbose_name="إجمالي القيمة التقديرية")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_loans_received", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "بضاعة مضافة سلفة من الغير"
        verbose_name_plural = "بضائع مضافة سلفة من الغير"
        ordering = ['-date', '-id']

    @property
    def is_overdue(self):
        """التحقق من تأخر الإرجاع"""
        if self.expected_return_date and self.status in ['RECEIVED', 'PARTIAL_RETURNED']:
            from datetime import date
            return date.today() > self.expected_return_date
        return False

    @property
    def days_until_return(self):
        """عدد الأيام المتبقية للإرجاع"""
        if self.expected_return_date and self.status in ['RECEIVED', 'PARTIAL_RETURNED']:
            from datetime import date
            delta = self.expected_return_date - date.today()
            return delta.days
        return None

    def __str__(self):
        return f"{self.loan_number} - {self.lender_name}"


class GoodsReceivedOnLoanItem(BaseModel):
    """أصناف البضاعة المضافة سلفة من الغير"""
    loan = models.ForeignKey(GoodsReceivedOnLoan, on_delete=models.CASCADE,
                             related_name="items", verbose_name="السلفة")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity_received = models.DecimalField(max_digits=15, decimal_places=3,
                                            verbose_name="الكمية المستلمة")
    quantity_returned = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                            verbose_name="الكمية المرتجعة")
    estimated_unit_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                               verbose_name="القيمة التقديرية للوحدة")
    total_estimated_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                                verbose_name="إجمالي القيمة التقديرية")
    condition_received = models.CharField(max_length=100, blank=True,
                                          verbose_name="حالة البضاعة عند الاستلام")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف بضاعة سلفة"
        verbose_name_plural = "أصناف بضائع السلف"

    @property
    def quantity_remaining(self):
        """الكمية المتبقية"""
        return self.quantity_received - self.quantity_returned

    @property
    def is_fully_returned(self):
        """هل تم إرجاع الكمية بالكامل"""
        return self.quantity_returned >= self.quantity_received

    def save(self, *args, **kwargs):
        self.total_estimated_value = self.quantity_received * self.estimated_unit_value
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity_received}"


class GoodsIssuedOnLoan(BaseModel):
    """بضاعة منصرفة سلفة لدى الغير"""
    loan_number = models.CharField(max_length=50, unique=True, verbose_name="رقم السلفة")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    borrower_name = models.CharField(max_length=200, verbose_name="اسم المُستعير")
    borrower_phone = models.CharField(max_length=20, blank=True, verbose_name="هاتف المُستعير")
    borrower_address = models.TextField(blank=True, verbose_name="عنوان المُستعير")
    loan_reason = models.CharField(max_length=200, verbose_name="سبب السلفة")
    expected_return_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الإرجاع المتوقع")
    status = models.CharField(max_length=20, choices=[
        ('ISSUED', 'منصرفة'),
        ('PARTIAL_RETURNED', 'مرتجعة جزئياً'),
        ('RETURNED', 'مرتجعة بالكامل'),
        ('OVERDUE', 'متأخرة'),
        ('CANCELLED', 'ملغاة'),
    ], default='ISSUED', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_estimated_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                                verbose_name="إجمالي القيمة التقديرية")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_loans_issued", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "بضاعة منصرفة سلفة لدى الغير"
        verbose_name_plural = "بضائع منصرفة سلفة لدى الغير"
        ordering = ['-date', '-id']

    @property
    def is_overdue(self):
        """التحقق من تأخر الإرجاع"""
        if self.expected_return_date and self.status in ['ISSUED', 'PARTIAL_RETURNED']:
            from datetime import date
            return date.today() > self.expected_return_date
        return False

    @property
    def days_until_return(self):
        """عدد الأيام المتبقية للإرجاع"""
        if self.expected_return_date and self.status in ['ISSUED', 'PARTIAL_RETURNED']:
            from datetime import date
            delta = self.expected_return_date - date.today()
            return delta.days
        return None

    def __str__(self):
        return f"{self.loan_number} - {self.borrower_name}"


class GoodsIssuedOnLoanItem(BaseModel):
    """أصناف البضاعة المنصرفة سلفة لدى الغير"""
    loan = models.ForeignKey(GoodsIssuedOnLoan, on_delete=models.CASCADE,
                             related_name="items", verbose_name="السلفة")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity_issued = models.DecimalField(max_digits=15, decimal_places=3,
                                          verbose_name="الكمية المنصرفة")
    quantity_returned = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                            verbose_name="الكمية المرتجعة")
    estimated_unit_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                               verbose_name="القيمة التقديرية للوحدة")
    total_estimated_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                                verbose_name="إجمالي القيمة التقديرية")
    condition_issued = models.CharField(max_length=100, blank=True,
                                        verbose_name="حالة البضاعة عند الصرف")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف بضاعة سلفة منصرفة"
        verbose_name_plural = "أصناف بضائع السلف المنصرفة"

    @property
    def quantity_remaining(self):
        """الكمية المتبقية"""
        return self.quantity_issued - self.quantity_returned

    @property
    def is_fully_returned(self):
        """هل تم إرجاع الكمية بالكامل"""
        return self.quantity_returned >= self.quantity_issued

    def save(self, *args, **kwargs):
        self.total_estimated_value = self.quantity_issued * self.estimated_unit_value
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity_issued}"


class WarehouseTransfer(BaseModel):
    """تحويل بين المخازن"""
    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    date = models.DateField(verbose_name="التاريخ")
    from_warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT,
                                       related_name="warehouse_transfers_out", verbose_name="من مخزن")
    to_warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT,
                                     related_name="warehouse_transfers_in", verbose_name="إلى مخزن")
    transfer_reason = models.CharField(max_length=200, verbose_name="سبب التحويل")
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'مسودة'),
        ('APPROVED', 'معتمد'),
        ('IN_TRANSIT', 'في الطريق'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ], default='DRAFT', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_estimated_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                                verbose_name="إجمالي القيمة التقديرية")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_transfers", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")
    shipped_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name="shipped_transfers", verbose_name="شحن بواسطة")
    shipped_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الشحن")
    received_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="received_transfers", verbose_name="استلم بواسطة")
    received_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاستلام")

    class Meta:
        verbose_name = "تحويل بين المخازن"
        verbose_name_plural = "تحويلات بين المخازن"
        ordering = ['-date', '-id']

    def clean(self):
        if self.from_warehouse == self.to_warehouse:
            from django.core.exceptions import ValidationError
            raise ValidationError('لا يمكن التحويل من وإلى نفس المخزن')

    @property
    def can_be_edited(self):
        """هل يمكن تعديل التحويل"""
        return self.status in ['DRAFT']

    @property
    def can_be_approved(self):
        """هل يمكن اعتماد التحويل"""
        return self.status == 'DRAFT'

    @property
    def can_be_shipped(self):
        """هل يمكن شحن التحويل"""
        return self.status == 'APPROVED'

    @property
    def can_be_received(self):
        """هل يمكن استلام التحويل"""
        return self.status == 'IN_TRANSIT'

    @property
    def can_be_cancelled(self):
        """هل يمكن إلغاء التحويل"""
        return self.status in ['DRAFT', 'APPROVED']

    def __str__(self):
        return f"{self.transfer_number} - {self.from_warehouse} → {self.to_warehouse}"


class WarehouseTransferItem(BaseModel):
    """أصناف تحويل بين المخازن"""
    transfer = models.ForeignKey(WarehouseTransfer, on_delete=models.CASCADE,
                                 related_name="items", verbose_name="التحويل")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف")
    quantity_requested = models.DecimalField(max_digits=15, decimal_places=3,
                                             verbose_name="الكمية المطلوبة")
    quantity_shipped = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                           verbose_name="الكمية المشحونة")
    quantity_received = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                            verbose_name="الكمية المستلمة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف تحويل مخزن"
        verbose_name_plural = "أصناف تحويلات المخازن"

    @property
    def quantity_pending(self):
        """الكمية المعلقة (المطلوبة - المشحونة)"""
        return self.quantity_requested - self.quantity_shipped

    @property
    def quantity_in_transit(self):
        """الكمية في الطريق (المشحونة - المستلمة)"""
        return self.quantity_shipped - self.quantity_received

    @property
    def is_fully_shipped(self):
        """هل تم شحن الكمية بالكامل"""
        return self.quantity_shipped >= self.quantity_requested

    @property
    def is_fully_received(self):
        """هل تم استلام الكمية بالكامل"""
        return self.quantity_received >= self.quantity_shipped

    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية بناءً على الكمية المطلوبة
        self.total_cost = self.quantity_requested * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity_requested}"


class ItemTransformation(BaseModel):
    """التحويل من صنف إلى صنف"""
    transformation_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    date = models.DateField(verbose_name="التاريخ")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name="المخزن")
    transformation_reason = models.CharField(max_length=200, verbose_name="سبب التحويل")
    transformation_type = models.CharField(max_length=20, choices=[
        ('ASSEMBLY', 'تجميع'),
        ('DISASSEMBLY', 'تفكيك'),
        ('CONVERSION', 'تحويل'),
        ('REPACKAGING', 'إعادة تعبئة'),
        ('QUALITY_CHANGE', 'تغيير جودة'),
    ], verbose_name="نوع التحويل")
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'مسودة'),
        ('APPROVED', 'معتمد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ], default='DRAFT', verbose_name="الحالة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    total_input_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                            verbose_name="إجمالي قيمة المدخلات")
    total_output_value = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                             verbose_name="إجمالي قيمة المخرجات")
    transformation_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                              verbose_name="تكلفة التحويل")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name="approved_transformations", verbose_name="معتمد من")
    approved_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "تحويل من صنف إلى صنف"
        verbose_name_plural = "تحويلات من صنف إلى صنف"
        ordering = ['-date', '-id']

    @property
    def can_be_edited(self):
        """هل يمكن تعديل التحويل"""
        return self.status in ['DRAFT']

    @property
    def can_be_approved(self):
        """هل يمكن اعتماد التحويل"""
        return self.status == 'DRAFT'

    @property
    def can_be_completed(self):
        """هل يمكن إكمال التحويل"""
        return self.status == 'APPROVED'

    @property
    def can_be_cancelled(self):
        """هل يمكن إلغاء التحويل"""
        return self.status in ['DRAFT', 'APPROVED']

    @property
    def net_value_change(self):
        """صافي التغيير في القيمة"""
        return self.total_output_value - self.total_input_value - self.transformation_cost

    def __str__(self):
        return f"{self.transformation_number} - {self.get_transformation_type_display()}"


class ItemTransformationInput(BaseModel):
    """المدخلات (الأصناف المستهلكة) في التحويل"""
    transformation = models.ForeignKey(ItemTransformation, on_delete=models.CASCADE,
                                       related_name="inputs", verbose_name="التحويل")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف المستهلك")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية المستهلكة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "مدخل تحويل صنف"
        verbose_name_plural = "مدخلات تحويل الأصناف"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity} (مدخل)"


class ItemTransformationOutput(BaseModel):
    """المخرجات (الأصناف المنتجة) في التحويل"""
    transformation = models.ForeignKey(ItemTransformation, on_delete=models.CASCADE,
                                       related_name="outputs", verbose_name="التحويل")
    item = models.ForeignKey(Item, on_delete=models.PROTECT, verbose_name="الصنف المنتج")
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية المنتجة")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                     verbose_name="إجمالي التكلفة")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء")
    batch_number = models.CharField(max_length=50, blank=True, verbose_name="رقم الدفعة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "مخرج تحويل صنف"
        verbose_name_plural = "مخرجات تحويل الأصناف"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.item.name} - {self.quantity} (مخرج)"


# ========== نماذج التصنيع الجديدة ==========

class ManufacturingOrder(BaseModel):
    """أوامر التصنيع الاحترافية"""
    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم أمر التصنيع")
    order_date = models.DateField(verbose_name="تاريخ الأمر", default=timezone.now)
    
    # المنتج النهائي
    finished_product = models.ForeignKey(Item, on_delete=models.PROTECT,
                                       limit_choices_to={'item_type__in': ['FINISHED_PRODUCT', 'SEMI_FINISHED']},
                                       related_name='manufacturing_orders',
                                       verbose_name="المنتج النهائي")
    quantity_to_produce = models.DecimalField(max_digits=15, decimal_places=3,
                                            verbose_name="الكمية المطلوبة")
    
    # المخازن
    finished_goods_warehouse = models.ForeignKey(Warehouse, on_delete=models.PROTECT,
                                               related_name='finished_goods_orders',
                                               verbose_name="مخزن المنتجات النهائية")
    
    # التواريخ
    expected_start_date = models.DateField(verbose_name="تاريخ البدء المتوقع", default=timezone.now)
    expected_completion_date = models.DateField(verbose_name="تاريخ الانتهاء المتوقع", default=timezone.now)
    actual_start_date = models.DateField(null=True, blank=True, verbose_name="تاريخ البدء الفعلي")
    actual_completion_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء الفعلي")
    
    # معلومات الإنتاج
    production_line = models.CharField(max_length=100, blank=True, verbose_name="خط الإنتاج")
    shift = models.CharField(max_length=50, blank=True, verbose_name="الوردية")
    priority = models.CharField(max_length=20, choices=[
        ('LOW', 'منخفض'),
        ('MEDIUM', 'متوسط'),
        ('HIGH', 'عالي'),
        ('URGENT', 'عاجل')
    ], default='MEDIUM', verbose_name="الأولوية")
    
    # الحالة
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'مسودة'),
        ('PLANNED', 'مخطط'),
        ('IN_PROGRESS', 'قيد التنفيذ'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي')
    ], default='DRAFT', verbose_name="الحالة")
    
    # التكاليف
    labor_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                   verbose_name="تكلفة العمالة")
    overhead_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                      verbose_name="التكاليف العامة")
    packaging_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                       verbose_name="تكلفة التغليف")
    quality_control_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                             verbose_name="تكلفة مراقبة الجودة")
    operating_expenses = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                           verbose_name="مصروفات التشغيل")
    electricity_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                         verbose_name="تكلفة الكهرباء")
    transportation_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                            verbose_name="تكلفة النقل")
    other_costs = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                    verbose_name="تكاليف أخرى")
    
    # الحسابات التلقائية
    total_material_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                            verbose_name="إجمالي تكلفة المواد")
    total_operating_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                             verbose_name="إجمالي تكاليف التشغيل")
    total_production_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                              verbose_name="إجمالي تكلفة الإنتاج")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                  verbose_name="تكلفة الوحدة")
    
    # الجودة
    quality_standard = models.CharField(max_length=100, blank=True, verbose_name="معيار الجودة")
    quality_notes = models.TextField(blank=True, verbose_name="ملاحظات الجودة")
    
    # إضافي
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='supervised_orders', verbose_name="المشرف")
    internal_notes = models.TextField(blank=True, verbose_name="ملاحظات داخلية")
    notes = models.TextField(blank=True, verbose_name="ملاحظات عامة")
    
    class Meta:
        verbose_name = "أمر تصنيع"
        verbose_name_plural = "أوامر التصنيع"
        ordering = ['-order_date', '-id']

    def __str__(self):
        return f"{self.order_number} - {self.finished_product.name}"

    def save(self, *args, **kwargs):
        # إنشاء رقم الأمر تلقائياً إذا لم يكن موجوداً
        if not self.order_number:
            last_order = ManufacturingOrder.objects.order_by('-id').first()
            if last_order:
                last_number = int(last_order.order_number.split('-')[-1])
                self.order_number = f"MFG-{timezone.now().strftime('%Y%m')}-{last_number + 1:04d}"
            else:
                self.order_number = f"MFG-{timezone.now().strftime('%Y%m')}-0001"
        
        # حساب التكاليف - فقط إذا كان النموذج محفوظاً
        if self.pk:
            self.calculate_total_material_cost()
            self.calculate_total_operating_cost()
            self.calculate_total_production_cost()
            self.calculate_unit_cost()
        
        super().save(*args, **kwargs)

    def calculate_total_material_cost(self):
        """حساب إجمالي تكلفة المواد"""
        total = sum(item.total_cost for item in self.materials.all())
        self.total_material_cost = total

    def calculate_total_operating_cost(self):
        """حساب إجمالي تكاليف التشغيل"""
        self.total_operating_cost = (
            self.labor_cost + self.overhead_cost + self.packaging_cost +
            self.quality_control_cost + self.operating_expenses +
            self.electricity_cost + self.transportation_cost + self.other_costs
        )

    def calculate_total_production_cost(self):
        """حساب إجمالي تكلفة الإنتاج"""
        self.total_production_cost = self.total_material_cost + self.total_operating_cost

    def calculate_unit_cost(self):
        """حساب تكلفة الوحدة"""
        if self.quantity_to_produce > 0:
            self.unit_cost = self.total_production_cost / self.quantity_to_produce
        else:
            self.unit_cost = 0

    @property
    def can_be_edited(self):
        """هل يمكن تعديل الأمر"""
        return self.status in ['DRAFT', 'PLANNED']

    @property
    def can_start_production(self):
        """هل يمكن بدء الإنتاج"""
        return self.status in ['PLANNED'] and self.materials.exists()

    @property
    def can_complete_production(self):
        """هل يمكن إكمال الإنتاج"""
        return self.status == 'IN_PROGRESS'

    @property
    def progress_percentage(self):
        """نسبة التقدم في الإنتاج"""
        if self.status == 'COMPLETED':
            return 100
        elif self.status == 'IN_PROGRESS':
            return 50
        elif self.status == 'PLANNED':
            return 25
        else:
            return 0

    @property
    def materials_sufficient(self):
        """هل المواد الخام كافية للإنتاج"""
        # تحديث الكميات المتاحة لجميع المواد
        self.update_materials_availability()
        for item in self.materials.all():
            if not item.is_sufficient:
                return False
        return True

    def update_materials_availability(self):
        """تحديث الكميات المتاحة لجميع المواد في الأمر"""
        for material in self.materials.all():
            material.update_available_quantity()

    def start_production(self, user):
        """بدء الإنتاج"""
        if self.can_start_production and self.materials_sufficient:
            self.status = 'IN_PROGRESS'
            self.actual_start_date = timezone.now().date()
            self.save()
            # خصم المواد الخام من المخزن
            for material_item in self.materials.all():
                material_item.consume_materials()
            return True
        return False

    def complete_production(self, user):
        """إكمال الإنتاج"""
        if self.can_complete_production:
            self.status = 'COMPLETED'
            self.actual_completion_date = timezone.now().date()
            self.save()
            
            # إضافة المنتج النهائي إلى مخزن المنتجات النهائية
            self.add_finished_product_to_warehouse()
            
            return True
        return False

    def add_finished_product_to_warehouse(self):
        """إضافة المنتج النهائي إلى المخزن"""
        from .utils import add_stock_to_warehouse
        
        add_stock_to_warehouse(
            warehouse=self.finished_goods_warehouse,
            item=self.finished_product,
            quantity=self.quantity_to_produce,
            unit_cost=self.unit_cost,
            reference=f"إنتاج - {self.order_number}",
            movement_type='PRODUCTION'
        )


class ManufacturingMaterial(BaseModel):
    """المواد الخام المطلوبة لأمر التصنيع"""
    order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                            related_name="materials", verbose_name="أمر التصنيع")
    
    # حقل الصنف - قائمة منسدلة بالأصناف الموجودة في المخزن المختار
    material = models.ForeignKey(Item, on_delete=models.PROTECT,
                               limit_choices_to={'item_type': 'RAW_MATERIAL'},
                               verbose_name="المادة الخام")
    
    # حقل الكمية المتوفرة - يعرض تلقائياً كمية الصنف المختار
    available_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                           verbose_name="الكمية المتوفرة")
    
    # حقل الكمية المطلوبة - لإدخال الكمية المطلوبة
    quantity_required = models.DecimalField(max_digits=15, decimal_places=3,
                                          verbose_name="الكمية المطلوبة")
    
    # حقل الكمية المستهلكة
    quantity_consumed = models.DecimalField(max_digits=15, decimal_places=3, default=0,
                                          verbose_name="الكمية المستهلكة")
    
    # حقل سعر الوحدة - يتم اختياره تلقائياً من سعر الصنف
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                  verbose_name="سعر الوحدة")
    
    # حقل إجمالي التكلفة - يحسب تلقائياً (الكمية × سعر الوحدة)
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0,
                                   verbose_name="إجمالي التكلفة")
    
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "مادة خام للتصنيع"
        verbose_name_plural = "المواد الخام للتصنيع"
        unique_together = ['order', 'material']

    def save(self, *args, **kwargs):
        # حفظ سعر الوحدة من المادة الخام إذا لم تكن محددة
        if not self.unit_cost:
            self.unit_cost = self.material.cost_price
        
        # حساب إجمالي التكلفة تلقائياً
        self.total_cost = self.quantity_required * self.unit_cost
        
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.material.name} - {self.quantity_required}"

    @property
    def is_sufficient(self):
        """هل الكمية المتاحة كافية"""
        # تحديث الكمية المتاحة من المخزن الفعلي
        self.update_available_quantity()
        return self.available_quantity >= self.quantity_required

    def update_available_quantity(self):
        """تحديث الكمية المتاحة من المخزن الفعلي"""
        from .utils import get_stock_quantity
        # نحتاج لتحديد المخزن - سنستخدم مخزن المواد الخام من الأمر
        if hasattr(self.order, 'raw_materials_warehouse') and self.order.raw_materials_warehouse:
            warehouse = self.order.raw_materials_warehouse
        else:
            # إذا لم يكن هناك مخزن محدد، نبحث في جميع المخازن
            from .models import Stock
            stock = Stock.objects.filter(item=self.material, quantity__gt=0).first()
            if stock:
                warehouse = stock.warehouse
            else:
                self.available_quantity = 0
                return
        
        # الحصول على الكمية المتاحة من المخزن
        available = get_stock_quantity(warehouse, self.material)
        if self.available_quantity != available:
            self.available_quantity = available
            self.save(update_fields=['available_quantity'])

    @property
    def shortage_quantity(self):
        """كمية النقص"""
        if self.is_sufficient:
            return 0
        return self.quantity_required - self.available_quantity

    def consume_materials(self):
        """استهلاك المواد الخام"""
        from .utils import reduce_stock_from_warehouse
        
        if self.is_sufficient:
            # تحديد المخزن - نبحث في جميع المخازن عن المادة
            from .models import Stock
            stock = Stock.objects.filter(item=self.material, quantity__gt=0).first()
            
            if stock:
                try:
                    # خصم المواد من المخزن
                    reduce_stock_from_warehouse(
                        warehouse=stock.warehouse,
                        item=self.material,
                        quantity=self.quantity_required,
                        reference=f"إنتاج - {self.order.order_number}",
                        movement_type='OUT'
                    )
                    
                    # تحديث الكمية المستهلكة
                    self.quantity_consumed = self.quantity_required
                    self.save()
                    
                    print(f"✅ تم خصم {self.quantity_required} من {self.material.name} من مخزن {stock.warehouse.name}")
                    
                except Exception as e:
                    print(f"❌ خطأ في خصم المواد: {e}")
                    raise e
            else:
                print(f"❌ لم يتم العثور على مخزون للمادة: {self.material.name}")
                raise ValueError(f"لا يوجد مخزون كافي للمادة: {self.material.name}")
        else:
            print(f"❌ الكمية غير كافية للمادة: {self.material.name}")
            raise ValueError(f"الكمية غير كافية للمادة: {self.material.name}")


class ManufacturingStep(BaseModel):
    """خطوات التصنيع"""
    order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                            related_name="steps", verbose_name="أمر التصنيع")
    step_number = models.IntegerField(verbose_name="رقم الخطوة")
    step_name = models.CharField(max_length=200, verbose_name="اسم الخطوة")
    description = models.TextField(blank=True, verbose_name="وصف الخطوة")
    estimated_duration = models.DurationField(null=True, blank=True, verbose_name="المدة المتوقعة")
    actual_duration = models.DurationField(null=True, blank=True, verbose_name="المدة الفعلية")
    status = models.CharField(max_length=20, choices=[
        ('PENDING', 'في الانتظار'),
        ('IN_PROGRESS', 'قيد التنفيذ'),
        ('COMPLETED', 'مكتمل'),
        ('SKIPPED', 'تم تخطيه')
    ], default='PENDING', verbose_name="الحالة")
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  verbose_name="مكلف إلى")
    started_at = models.DateTimeField(null=True, blank=True, verbose_name="وقت البدء")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="وقت الإكمال")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "خطوة تصنيع"
        verbose_name_plural = "خطوات التصنيع"
        ordering = ['step_number']
        unique_together = ['order', 'step_number']

    def __str__(self):
        return f"{self.order.order_number} - خطوة {self.step_number}: {self.step_name}"

    def start_step(self, user):
        """بدء الخطوة"""
        if self.status == 'PENDING':
            self.status = 'IN_PROGRESS'
            self.assigned_to = user
            self.started_at = timezone.now()
            self.save()

    def complete_step(self, user):
        """إكمال الخطوة"""
        if self.status == 'IN_PROGRESS':
            self.status = 'COMPLETED'
            self.completed_at = timezone.now()
            if self.actual_duration is None and self.started_at:
                self.actual_duration = self.completed_at - self.started_at
            self.save()


class QualityControl(BaseModel):
    """مراقبة الجودة"""
    order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                            related_name="quality_checks", verbose_name="أمر التصنيع")
    check_number = models.IntegerField(verbose_name="رقم الفحص")
    check_name = models.CharField(max_length=200, verbose_name="اسم الفحص")
    standard_value = models.CharField(max_length=100, blank=True, verbose_name="القيمة المعيارية")
    actual_value = models.CharField(max_length=100, blank=True, verbose_name="القيمة الفعلية")
    tolerance = models.CharField(max_length=100, blank=True, verbose_name="الحد المسموح")
    status = models.CharField(max_length=20, choices=[
        ('PENDING', 'في الانتظار'),
        ('PASSED', 'مقبول'),
        ('FAILED', 'مرفوض'),
        ('REWORK', 'إعادة عمل')
    ], default='PENDING', verbose_name="الحالة")
    checked_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 verbose_name="فحص بواسطة")
    checked_at = models.DateTimeField(null=True, blank=True, verbose_name="وقت الفحص")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "فحص جودة"
        verbose_name_plural = "فحوصات الجودة"
        ordering = ['check_number']
        unique_together = ['order', 'check_number']

    def __str__(self):
        return f"{self.order.order_number} - فحص {self.check_number}: {self.check_name}"

    @property
    def is_within_tolerance(self):
        """هل القيمة الفعلية ضمن الحد المسموح"""
        if not self.standard_value or not self.actual_value or not self.tolerance:
            return True
        # يمكن إضافة منطق أكثر تعقيداً هنا
        return True


# حذف النماذج القديمة
# class RawMaterial(BaseModel):
# class ManufacturingOrder(BaseModel):
# class ManufacturingItem(BaseModel):
