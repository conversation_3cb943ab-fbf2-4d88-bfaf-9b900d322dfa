{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/print-design.css' %}">
<link rel="stylesheet" href="{% static 'css/design-editor.css' %}">
<link rel="stylesheet" href="{% static 'css/advanced-editor.css' %}">
<style>
    .design-editor-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        height: 80vh;
    }

    .editor-toolbar {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
    }

    .toolbar-section {
        margin-bottom: 25px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .toolbar-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e9ecef;
    }

    .toolbar-actions {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 15px;
        border-radius: 8px;
        border: 2px solid #007bff;
        margin-top: 20px;
    }

    .toolbar-actions .btn {
        margin: 5px;
        border-radius: 6px;
    }

    .editor-preview {
        background: white;
        border-radius: 12px;
        border: 1px solid #dee2e6;
        display: flex;
        flex-direction: column;
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px 12px 0 0;
    }

    .preview-header h6 {
        margin: 0;
        font-weight: 600;
    }

    .preview-controls .btn {
        margin-left: 5px;
        border-color: rgba(255,255,255,0.3);
        color: white;
    }

    .preview-controls .btn:hover {
        background: rgba(255,255,255,0.2);
        border-color: white;
    }

    .preview-content {
        flex: 1;
        padding: 20px;
        overflow: auto;
        background: #f8f9fa;
    }

    #designPreview {
        transition: transform 0.3s ease;
        transform-origin: top right;
    }

    @media (max-width: 1200px) {
        .design-editor-container {
            grid-template-columns: 1fr;
            height: auto;
        }

        .editor-preview {
            min-height: 500px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-0">
                <i class="fas fa-print text-primary me-2"></i>
                {{ title }}
            </h2>
            <p class="text-muted mb-0">تصميم وتخصيص نماذج الطباعة</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        نماذج الطباعة المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- فاتورة مبيعات -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-primary print-template-card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-invoice me-2"></i>
                                        فاتورة مبيعات
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-file-invoice fa-3x text-primary mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة فاتورة المبيعات</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-primary btn-sm" onclick="designSalesInvoice()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="previewSalesInvoice()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="printSalesInvoice()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- فاتورة مشتريات -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-success print-template-card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        فاتورة مشتريات
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-shopping-cart fa-3x text-success mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة فاتورة المشتريات</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-success btn-sm" onclick="designPurchaseInvoice()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="previewPurchaseInvoice()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="printPurchaseInvoice()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير مخزون -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-warning print-template-card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-boxes me-2"></i>
                                        تقرير مخزون
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-3x text-warning mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة تقرير المخزون</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-warning btn-sm" onclick="designInventoryReport()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="previewInventoryReport()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-warning btn-sm" onclick="printInventoryReport()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- كشف حساب عميل -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-info print-template-card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-tie me-2"></i>
                                        كشف حساب عميل
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-user-tie fa-3x text-info mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة كشف حساب العميل</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-info btn-sm" onclick="designCustomerStatement()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="previewCustomerStatement()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-info btn-sm" onclick="printCustomerStatement()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- سند قبض -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-secondary print-template-card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-receipt me-2"></i>
                                        سند قبض
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-receipt fa-3x text-secondary mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة سند القبض</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-secondary btn-sm" onclick="designReceiptVoucher()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="previewReceiptVoucher()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="printReceiptVoucher()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- سند صرف -->
                        <div class="col-lg-4 mb-3">
                            <div class="card border-danger print-template-card">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        سند صرف
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fas fa-money-bill-wave fa-3x text-danger mb-3"></i>
                                    <p class="text-muted small mb-3">تصميم ومعاينة سند الصرف</p>
                                    <div class="btn-group-vertical w-100 gap-2">
                                        <button class="btn btn-danger btn-sm" onclick="designPaymentVoucher()">
                                            <i class="fas fa-edit me-1"></i>
                                            تصميم
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="previewPaymentVoucher()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="printPaymentVoucher()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتصميم النماذج -->
<div class="modal fade" id="designModal" tabindex="-1" aria-labelledby="designModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="designModalLabel">تصميم النموذج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="designContent">
                    <!-- سيتم تحميل محتوى التصميم هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="saveDesign()">حفظ التصميم</button>
                <button type="button" class="btn btn-success" onclick="previewFromDesign()">معاينة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للمعاينة -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">معاينة النموذج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- سيتم تحميل محتوى المعاينة هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printFromPreview()">طباعة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let currentTemplate = '';
let currentTemplateData = {};

// بيانات تجريبية للنماذج
const sampleData = {
    salesInvoice: {
        invoiceNumber: 'INV-2024-001',
        date: '2024-01-15',
        customer: {
            name: 'شركة الأمل للتجارة',
            address: 'القاهرة - مصر الجديدة',
            phone: '0*********0',
            taxNumber: '*********'
        },
        items: [
            { name: 'منتج أ', quantity: 10, price: 100, total: 1000 },
            { name: 'منتج ب', quantity: 5, price: 200, total: 1000 },
            { name: 'منتج ج', quantity: 3, price: 150, total: 450 }
        ],
        subtotal: 2450,
        tax: 367.5,
        total: 2817.5
    },
    purchaseInvoice: {
        invoiceNumber: 'PUR-2024-001',
        date: '2024-01-15',
        supplier: {
            name: 'شركة النور للتوريدات',
            address: 'الجيزة - الدقي',
            phone: '01*********',
            taxNumber: '*********'
        },
        items: [
            { name: 'مادة خام أ', quantity: 20, price: 50, total: 1000 },
            { name: 'مادة خام ب', quantity: 15, price: 80, total: 1200 },
            { name: 'مادة خام ج', quantity: 10, price: 120, total: 1200 }
        ],
        subtotal: 3400,
        tax: 510,
        total: 3910
    },
    inventoryReport: {
        reportDate: '2024-01-15',
        warehouse: 'المخزن الرئيسي',
        items: [
            { code: 'ITM001', name: 'منتج أ', unit: 'قطعة', quantity: 150, price: 100, value: 15000 },
            { code: 'ITM002', name: 'منتج ب', unit: 'كيلو', quantity: 80, price: 200, value: 16000 },
            { code: 'ITM003', name: 'منتج ج', unit: 'متر', quantity: 200, price: 75, value: 15000 },
            { code: 'ITM004', name: 'منتج د', unit: 'لتر', quantity: 120, price: 150, value: 18000 }
        ],
        totalValue: 64000
    }
};

// دوال فاتورة المبيعات
function designSalesInvoice() {
    currentTemplate = 'salesInvoice';
    currentTemplateData = sampleData.salesInvoice;

    // إنشاء المحرر المتقدم
    const designContent = designEditor.createEditorInterface();

    document.getElementById('designContent').innerHTML = designContent;
    document.getElementById('designModalLabel').textContent = 'تصميم فاتورة المبيعات - محرر متقدم';

    // تهيئة المحرر
    setTimeout(() => {
        designEditor.bindEvents();
        designEditor.updatePreview();
    }, 100);

    new bootstrap.Modal(document.getElementById('designModal')).show();
}

function previewSalesInvoice() {
    currentTemplate = 'salesInvoice';
    currentTemplateData = sampleData.salesInvoice;

    const content = designEditor ? generateAdvancedInvoiceHTML('فاتورة مبيعات') : generateSalesInvoiceHTML();
    document.getElementById('previewContent').innerHTML = content;
    document.getElementById('previewModalLabel').textContent = 'معاينة فاتورة المبيعات';

    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function printSalesInvoice() {
    currentTemplate = 'salesInvoice';
    currentTemplateData = sampleData.salesInvoice;

    const printWindow = window.open('', '_blank');
    const content = generateAdvancedInvoiceHTML('فاتورة مبيعات');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                ${designEditor ? designEditor.getDesignCSS() : getSalesInvoiceCSS()}
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}

// دوال فاتورة المشتريات
function designPurchaseInvoice() {
    currentTemplate = 'purchaseInvoice';
    currentTemplateData = sampleData.purchaseInvoice;

    // تعديل الإعدادات الافتراضية للمشتريات
    designEditor.designSettings.headerColor = '#28a745';

    const designContent = designEditor.createEditorInterface();

    document.getElementById('designContent').innerHTML = designContent;
    document.getElementById('designModalLabel').textContent = 'تصميم فاتورة المشتريات - محرر متقدم';

    setTimeout(() => {
        designEditor.bindEvents();
        designEditor.updatePreview();
    }, 100);

    new bootstrap.Modal(document.getElementById('designModal')).show();
}

function previewPurchaseInvoice() {
    currentTemplate = 'purchaseInvoice';
    currentTemplateData = sampleData.purchaseInvoice;

    const content = designEditor ? generateAdvancedInvoiceHTML('فاتورة مشتريات') : generatePurchaseInvoiceHTML();
    document.getElementById('previewContent').innerHTML = content;
    document.getElementById('previewModalLabel').textContent = 'معاينة فاتورة المشتريات';

    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function printPurchaseInvoice() {
    currentTemplate = 'purchaseInvoice';
    currentTemplateData = sampleData.purchaseInvoice;

    const printWindow = window.open('', '_blank');
    const content = generatePurchaseInvoiceHTML();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مشتريات</title>
            <style>
                ${getPurchaseInvoiceCSS()}
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}

// دوال تقرير المخزون
function designInventoryReport() {
    currentTemplate = 'inventoryReport';
    currentTemplateData = sampleData.inventoryReport;

    // تعديل الإعدادات الافتراضية للمخزون
    designEditor.designSettings.headerColor = '#ffc107';
    designEditor.designSettings.headerTextColor = '#000000';

    const designContent = designEditor.createEditorInterface();

    document.getElementById('designContent').innerHTML = designContent;
    document.getElementById('designModalLabel').textContent = 'تصميم تقرير المخزون - محرر متقدم';

    setTimeout(() => {
        designEditor.bindEvents();
        designEditor.updatePreview();
    }, 100);

    new bootstrap.Modal(document.getElementById('designModal')).show();
}

function previewInventoryReport() {
    currentTemplate = 'inventoryReport';
    currentTemplateData = sampleData.inventoryReport;

    const content = designEditor ? generateAdvancedInvoiceHTML('تقرير المخزون') : generateInventoryReportHTML();
    document.getElementById('previewContent').innerHTML = content;
    document.getElementById('previewModalLabel').textContent = 'معاينة تقرير المخزون';

    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function printInventoryReport() {
    currentTemplate = 'inventoryReport';
    currentTemplateData = sampleData.inventoryReport;

    const printWindow = window.open('', '_blank');
    const content = generateInventoryReportHTML();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخزون</title>
            <style>
                ${getInventoryReportCSS()}
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}

// دوال كشف حساب العميل
function designCustomerStatement() {
    alert('تصميم كشف حساب العميل - قريباً');
}

function previewCustomerStatement() {
    alert('معاينة كشف حساب العميل - قريباً');
}

function printCustomerStatement() {
    alert('طباعة كشف حساب العميل - قريباً');
}

// دوال سند القبض
function designReceiptVoucher() {
    alert('تصميم سند القبض - قريباً');
}

function previewReceiptVoucher() {
    alert('معاينة سند القبض - قريباً');
}

function printReceiptVoucher() {
    alert('طباعة سند القبض - قريباً');
}

// دوال سند الصرف
function designPaymentVoucher() {
    alert('تصميم سند الصرف - قريباً');
}

function previewPaymentVoucher() {
    alert('معاينة سند الصرف - قريباً');
}

function printPaymentVoucher() {
    alert('طباعة سند الصرف - قريباً');
}

// دالة إنتاج HTML متقدم باستخدام إعدادات المحرر
function generateAdvancedInvoiceHTML(title) {
    if (!designEditor) {
        return generateSalesInvoiceHTML(); // fallback
    }

    const settings = designEditor.designSettings;
    const data = currentTemplateData;
    const logoSizeMap = {
        small: '60px',
        medium: '80px',
        large: '100px'
    };

    return `
        <div class="invoice-container">
            ${settings.showLogo ? `
                <div class="invoice-header">
                    ${settings.companyLogo ? `
                        <div class="company-logo" style="order: ${settings.logoPosition === 'left' ? '2' : '1'};">
                            <img src="${settings.companyLogo}" alt="شعار الشركة" style="max-height: ${logoSizeMap[settings.logoSize]};">
                        </div>
                    ` : ''}
                    <div class="company-info" style="order: ${settings.logoPosition === 'left' ? '1' : '2'};">
                        <h1>${settings.companyName}</h1>
                        <p>${settings.companyAddress}</p>
                        <p>الهاتف: ${settings.companyPhone}</p>
                        <p>البريد: ${settings.companyEmail}</p>
                    </div>
                </div>
            ` : ''}

            <div class="invoice-title">
                <h2>${title}</h2>
            </div>

            <div class="invoice-details">
                <div class="invoice-info">
                    <div class="info-section">
                        <h4>بيانات ${title.includes('مبيعات') ? 'الفاتورة' : title.includes('مشتريات') ? 'الفاتورة' : 'التقرير'}</h4>
                        <p><strong>رقم ${title.includes('تقرير') ? 'التقرير' : 'الفاتورة'}:</strong> ${data.invoiceNumber || data.reportDate}</p>
                        <p><strong>التاريخ:</strong> ${data.date || data.reportDate}</p>
                        ${data.warehouse ? `<p><strong>المخزن:</strong> ${data.warehouse}</p>` : ''}
                    </div>
                    ${data.customer ? `
                        <div class="customer-info">
                            <h4>بيانات العميل</h4>
                            <p><strong>اسم العميل:</strong> ${data.customer.name}</p>
                            <p><strong>العنوان:</strong> ${data.customer.address}</p>
                            <p><strong>الهاتف:</strong> ${data.customer.phone}</p>
                            <p><strong>الرقم الضريبي:</strong> ${data.customer.taxNumber}</p>
                        </div>
                    ` : ''}
                    ${data.supplier ? `
                        <div class="supplier-info">
                            <h4>بيانات المورد</h4>
                            <p><strong>اسم المورد:</strong> ${data.supplier.name}</p>
                            <p><strong>العنوان:</strong> ${data.supplier.address}</p>
                            <p><strong>الهاتف:</strong> ${data.supplier.phone}</p>
                            <p><strong>الرقم الضريبي:</strong> ${data.supplier.taxNumber}</p>
                        </div>
                    ` : ''}
                </div>
            </div>

            <div class="invoice-items">
                <table class="items-table">
                    <thead>
                        <tr>
                            ${title.includes('تقرير') ? `
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>الوحدة</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>القيمة</th>
                            ` : `
                                <th>الصنف</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                            `}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.items.map(item => `
                            <tr>
                                ${title.includes('تقرير') ? `
                                    <td>${item.code}</td>
                                    <td>${item.name}</td>
                                    <td>${item.unit}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price.toFixed(2)}</td>
                                    <td>${item.value.toFixed(2)}</td>
                                ` : `
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price.toFixed(2)}</td>
                                    <td>${item.total.toFixed(2)}</td>
                                `}
                            </tr>
                        `).join('')}
                    </tbody>
                    ${title.includes('تقرير') ? `
                        <tfoot>
                            <tr class="total-row">
                                <td colspan="5"><strong>الإجمالي</strong></td>
                                <td><strong>${data.totalValue.toFixed(2)} ج.م</strong></td>
                            </tr>
                        </tfoot>
                    ` : ''}
                </table>
            </div>

            ${!title.includes('تقرير') ? `
                <div class="invoice-totals">
                    <div class="totals-section">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span>${data.subtotal.toFixed(2)} ج.م</span>
                        </div>
                        <div class="total-row">
                            <span>الضريبة (15%):</span>
                            <span>${data.tax.toFixed(2)} ج.م</span>
                        </div>
                        <div class="total-row final-total">
                            <span>الإجمالي النهائي:</span>
                            <span>${data.total.toFixed(2)} ج.م</span>
                        </div>
                    </div>
                </div>
            ` : ''}

            ${settings.showFooter ? `
                <div class="invoice-footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>هذا النموذج مُنشأ إلكترونياً ولا يحتاج إلى توقيع</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                </div>
            ` : ''}
        </div>
    `;
}

// تحديث دوال الطباعة الأخرى
function printPurchaseInvoice() {
    currentTemplate = 'purchaseInvoice';
    currentTemplateData = sampleData.purchaseInvoice;

    const printWindow = window.open('', '_blank');
    const content = generateAdvancedInvoiceHTML('فاتورة مشتريات');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مشتريات</title>
            <style>
                ${designEditor ? designEditor.getDesignCSS() : getPurchaseInvoiceCSS()}
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}

function printInventoryReport() {
    currentTemplate = 'inventoryReport';
    currentTemplateData = sampleData.inventoryReport;

    const printWindow = window.open('', '_blank');
    const content = generateAdvancedInvoiceHTML('تقرير المخزون');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخزون</title>
            <style>
                ${designEditor ? designEditor.getDesignCSS() : getInventoryReportCSS()}
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}

// تحميل التصميمات المحفوظة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل التصميمات المحفوظة من localStorage
    const savedTemplates = ['salesInvoice', 'purchaseInvoice', 'inventoryReport'];

    savedTemplates.forEach(template => {
        const saved = localStorage.getItem(`template_${template}`);
        if (saved) {
            console.log(`تم تحميل تصميم ${template} المحفوظ`);
        }
    });

    // إضافة رسالة ترحيب
    console.log('تم تحميل نظام تصميم نماذج الطباعة المتقدم بنجاح');
});
</script>
{% endblock %}
