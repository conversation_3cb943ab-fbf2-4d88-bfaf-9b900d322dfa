{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0"><i class="fas fa-minus text-danger me-2"></i>{{ title }}</h2>
                <p class="text-muted mb-0">إدارة الخصومات والاستقطاعات من المرتبات</p>
            </div>
            <div>
                <a href="/hr/" class="btn btn-secondary me-2"><i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم</a>
                <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addDeductionModal"><i class="fas fa-minus me-2"></i>خصم جديد</button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white text-center"><div class="card-body"><h3>{{ total_deductions|floatformat:2 }} ج.م</h3><p class="mb-0">إجمالي الخصومات</p></div></div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white text-center"><div class="card-body"><h3>{{ count_deductions }}</h3><p class="mb-0">عدد الخصومات</p></div></div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white text-center"><div class="card-body"><h3>{{ avg_deduction|floatformat:2 }} ج.م</h3><p class="mb-0">متوسط الخصم</p></div></div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white text-center"><div class="card-body"><h3>{{ current_month }}</h3><p class="mb-0">الشهر الحالي</p></div></div>
        </div>
    </div>

    <!-- Alerts -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">{{ message }}<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
        {% endfor %}
    {% endif %}

    <!-- Deductions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><h5 class="mb-0"><i class="fas fa-table me-2"></i>قائمة الخصومات من المرتبات</h5></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الخصم</th>
                                    <th>المبلغ</th>
                                    <th>الشهر</th>
                                    <th>تاريخ الخصم</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for deduction in deductions %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {% if deduction.employee %}
                                            {% if deduction.employee.full_name %}{{ deduction.employee.full_name }}
                                            {% else %}{{ deduction.employee.person.full_name }}{% endif %}
                                        {% else %}<span class="text-danger">غير محدد</span>{% endif %}
                                    </td>
                                    <td><span class="badge bg-secondary">{{ deduction.name }}</span></td>
                                    <td><strong class="text-danger">{{ deduction.amount|floatformat:2 }} ج.م</strong></td>
                                    <td>{{ deduction.date|date:'F Y' }}</td>
                                    <td>{{ deduction.date|date:'Y-m-d' }}</td>
                                    <td>{% if deduction.is_active %}<span class="badge bg-success">مطبق</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:salary_deduction_detail' deduction.id %}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل"><i class="fas fa-eye"></i></a>
                                            <a href="{% url 'hr:salary_deduction_edit' deduction.id %}" class="btn btn-sm btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></a>
                                            <a href="{% url 'hr:allowance_deduction_delete' deduction.id %}" class="btn btn-sm btn-outline-danger" title="حذف"><i class="fas fa-trash"></i></a>
                                            <a href="{% url 'hr:salary_deduction_print' deduction.id %}" class="btn btn-sm btn-outline-info" title="طباعة" target="_blank"><i class="fas fa-print"></i></a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr><td colspan="8" class="text-center text-muted py-4"><i class="fas fa-minus fa-3x mb-3 d-block"></i>لا توجد خصومات مسجلة</td></tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Deduction Types Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white"><h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>ملخص أنواع الخصومات</h6></div>
                <div class="card-body">
                    <div class="row">
                        {% for summary in deduction_types_summary %}
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-primary">{{ summary.total|floatformat:2 }} ج.م</h5>
                                <p class="mb-0">{{ summary.name }}</p>
                                <span class="badge bg-secondary">عدد: {{ summary.count }}</span>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12 text-center text-muted py-3">لا يوجد ملخص أنواع خصومات</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Deduction Modal -->
<div class="modal fade" id="addDeductionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خصم جديد من المرتب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" novalidate>
                {% csrf_token %}
                <div class="modal-body">
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                    {% endif %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.employee.label_tag }}
                                {{ form.employee }}
                                {% if form.employee.errors %}<div class="text-danger small">{{ form.employee.errors }}</div>{% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label_tag }}
                                {{ form.name }}
                                {% if form.name.errors %}<div class="text-danger small">{{ form.name.errors }}</div>{% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.amount.label_tag }}
                                {{ form.amount }}
                                {% if form.amount.errors %}<div class="text-danger small">{{ form.amount.errors }}</div>{% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.date.label_tag }}
                                {{ form.date }}
                                {% if form.date.errors %}<div class="text-danger small">{{ form.date.errors }}</div>{% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.is_active }} {{ form.is_active.label_tag }}
                                {% if form.is_active.errors %}<div class="text-danger small">{{ form.is_active.errors }}</div>{% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        {{ form.notes.label_tag }}
                        {{ form.notes }}
                        {% if form.notes.errors %}<div class="text-danger small">{{ form.notes.errors }}</div>{% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حفظ الخصم</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- لا يوجد كود إعادة تحميل تلقائي، الرسائل تظهر بعد إعادة التوجيه فقط -->
{% endblock %}
