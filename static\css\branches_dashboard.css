/* 
CSS للفروع والمركز الرئيسي
Branches and Headquarters CSS
*/

/* الألوان الأساسية */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* البطاقات الرئيسية */
.dashboard-card {
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
    background: white;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-left: 5px solid #fff;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transform: rotate(45deg);
    transition: all 0.3s ease;
}

.stats-card:hover::before {
    right: -30%;
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* بطاقات العمليات */
.operation-card {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    background: white;
    border-radius: 10px;
}

.operation-card:hover {
    border-left-color: var(--success-color);
    transform: translateX(5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.operation-card.cash {
    border-left-color: var(--success-color);
}

.operation-card.cash:hover {
    border-left-color: #20c997;
}

.operation-card.bank {
    border-left-color: var(--info-color);
}

.operation-card.bank:hover {
    border-left-color: #138496;
}

.operation-card.revenue {
    border-left-color: var(--warning-color);
}

.operation-card.revenue:hover {
    border-left-color: #e0a800;
}

.operation-card.expense {
    border-left-color: var(--danger-color);
}

.operation-card.expense:hover {
    border-left-color: #c82333;
}

/* الأزرار السريعة */
.quick-action-btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.quick-action-btn:hover::before {
    width: 300px;
    height: 300px;
}

.quick-action-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* النشاط الأخير */
.recent-activity {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
}

.recent-activity::-webkit-scrollbar {
    width: 6px;
}

.recent-activity::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.recent-activity::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.recent-activity::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.activity-item {
    border-left: 3px solid var(--primary-color);
    padding: 15px;
    margin-bottom: 10px;
    background: var(--light-color);
    border-radius: 0 10px 10px 0;
    transition: all 0.3s ease;
    position: relative;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(0,123,255,0.5), transparent);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.activity-item:hover::before {
    background: linear-gradient(to bottom, transparent, rgba(40,167,69,0.8), transparent);
}

.activity-item.cash {
    border-left-color: var(--success-color);
}

.activity-item.revenue {
    border-left-color: var(--warning-color);
}

.activity-item.expense {
    border-left-color: var(--danger-color);
}

.activity-item.bank {
    border-left-color: var(--info-color);
}

/* الشارات */
.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
    border-radius: 15px;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.1);
    transform: scale(1.01);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.dashboard-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:hover {
    animation: pulse 1s infinite;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: 20px;
    }
    
    .stats-card h3 {
        font-size: 1.5rem;
    }
    
    .operation-card {
        margin-bottom: 15px;
    }
    
    .quick-action-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .recent-activity {
        max-height: 300px;
    }
    
    .activity-item {
        padding: 10px;
    }
}

@media (max-width: 576px) {
    .dashboard-card {
        border-radius: 10px;
    }
    
    .stats-card {
        text-align: center;
    }
    
    .operation-card .btn-group-vertical .btn {
        font-size: 0.8rem;
        padding: 5px 10px;
    }
}

/* تأثيرات إضافية */
.card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.btn-group-vertical .btn {
    border-radius: 5px !important;
    margin-bottom: 5px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

/* تحسينات الطباعة */
@media print {
    .dashboard-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .stats-card {
        background: white !important;
        color: black !important;
        border: 1px solid #ddd;
    }
    
    .quick-action-btn,
    .modal {
        display: none !important;
    }
}
