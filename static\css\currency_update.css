/**
 * تحديث العملة إلى الجنيه المصري
 * Currency Update to Egyptian Pound
 */

/* إخفاء أي رموز للريال السعودي وإظهار الجنيه المصري */
.currency-sar {
    display: none !important;
}

.currency-egp {
    display: inline !important;
}

/* تنسيق عرض العملة المصرية */
.currency-symbol {
    font-weight: bold;
    color: #28a745;
}

.currency-amount {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: right;
}

/* تنسيق المبالغ الكبيرة */
.amount-large {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}

.amount-medium {
    font-size: 1.1em;
    font-weight: 600;
    color: #6c757d;
}

.amount-small {
    font-size: 1em;
    color: #495057;
}

/* تنسيق المبالغ الإيجابية والسلبية */
.amount-positive {
    color: #28a745 !important;
}

.amount-negative {
    color: #dc3545 !important;
}

.amount-zero {
    color: #6c757d !important;
}

/* تنسيق جداول المبالغ */
.currency-table .amount-cell {
    text-align: left;
    font-family: 'Cairo', monospace;
    font-weight: 500;
}

.currency-table .currency-header {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
}

/* تنسيق النماذج */
.currency-input {
    text-align: left;
    direction: ltr;
    font-family: 'Cairo', monospace;
}

.currency-input::after {
    content: " ج.م";
    color: #6c757d;
    font-weight: normal;
}

/* تنسيق البطاقات المالية */
.financial-card {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.financial-card .amount {
    font-size: 1.5em;
    font-weight: bold;
    color: #28a745;
}

.financial-card .currency {
    font-size: 0.9em;
    color: #6c757d;
    margin-right: 5px;
}

/* تنسيق الإحصائيات */
.stats-widget .stat-amount {
    font-size: 2em;
    font-weight: bold;
    line-height: 1;
}

.stats-widget .stat-currency {
    font-size: 0.8em;
    color: #6c757d;
    margin-right: 5px;
}

/* تنسيق التقارير */
.report-table .amount-column {
    text-align: left;
    font-family: 'Cairo', monospace;
    font-weight: 500;
    min-width: 120px;
}

.report-summary .total-amount {
    font-size: 1.3em;
    font-weight: bold;
    color: #007bff;
    border-top: 2px solid #007bff;
    padding-top: 10px;
}

/* تنسيق الفواتير */
.invoice-amount {
    font-size: 1.2em;
    font-weight: bold;
}

.invoice-total {
    font-size: 1.4em;
    font-weight: bold;
    color: #007bff;
    border-top: 2px solid #dee2e6;
    padding-top: 10px;
}

.invoice-subtotal,
.invoice-discount,
.invoice-tax {
    font-weight: 500;
    color: #6c757d;
}

/* تنسيق أسعار العملات */
.currency-rates-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.currency-rate-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f1f1f1;
}

.currency-rate-item:last-child {
    border-bottom: none;
}

.currency-code {
    font-weight: bold;
    color: #495057;
}

.currency-name {
    color: #6c757d;
    font-size: 0.9em;
}

.currency-value {
    font-weight: bold;
    color: #28a745;
    font-family: 'Cairo', monospace;
}

/* تنسيق الخزينة والبنوك */
.treasury-balance,
.bank-balance {
    font-size: 1.3em;
    font-weight: bold;
    color: #17a2b8;
}

.balance-card {
    background: linear-gradient(135deg, #17a2b8 0%, #**********%);
    color: white;
    border-radius: 10px;
    padding: 20px;
}

.balance-card .balance-amount {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.balance-card .balance-label {
    font-size: 0.9em;
    opacity: 0.8;
}

/* تنسيق المبيعات والمشتريات */
.sales-amount,
.purchase-amount {
    font-weight: bold;
}

.sales-amount {
    color: #28a745;
}

.purchase-amount {
    color: #dc3545;
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .currency-amount {
        font-size: 0.9em;
    }
    
    .amount-large {
        font-size: 1.1em;
    }
    
    .stats-widget .stat-amount {
        font-size: 1.5em;
    }
    
    .balance-card .balance-amount {
        font-size: 1.5em;
    }
}

/* تنسيق الطباعة */
@media print {
    .currency-symbol,
    .currency-amount {
        color: #000 !important;
    }
    
    .amount-positive,
    .amount-negative {
        color: #000 !important;
    }
    
    .financial-card,
    .balance-card {
        background: #fff !important;
        color: #000 !important;
    }
}

/* تنسيق إضافي للجنيه المصري */
.egp-symbol::before {
    content: "ج.م ";
    font-weight: bold;
    color: #28a745;
}

.currency-egp-highlight {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
}

/* تحديث ألوان العلم المصري */
.egypt-flag-colors {
    background: linear-gradient(to right, #ce1126 33%, #ffffff 33%, #ffffff 66%, #000000 66%);
    height: 4px;
    width: 100%;
    margin-bottom: 10px;
}
