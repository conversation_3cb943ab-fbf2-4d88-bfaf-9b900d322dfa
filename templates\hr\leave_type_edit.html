{% extends 'base/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تعديل نوع الإجازة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        تعديل نوع الإجازة
                    </h2>
                    <p class="text-muted mb-0">يمكنك تعديل بيانات نوع الإجازة هنا</p>
                </div>
                <div>
                    <a href="{% url 'hr:leave_type_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-8 col-lg-6 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات نوع الإجازة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label class="form-label">كود نوع الإجازة <span class="text-danger">*</span></label>
                            <input type="text" name="code" class="form-control" value="{{ leave_type.code }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم نوع الإجازة <span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" value="{{ leave_type.name }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الاسم بالإنجليزية</label>
                            <input type="text" name="name_english" class="form-control" value="{{ leave_type.name_english }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea name="description" class="form-control" rows="2">{{ leave_type.description }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">فئة الإجازة <span class="text-danger">*</span></label>
                            <select name="category" class="form-select" required>
                                {% for value, label in leave_type.LEAVE_CATEGORIES %}
                                    <option value="{{ value }}" {% if leave_type.category == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">عدد الأيام المسموحة سنوياً</label>
                            <input type="number" name="allowed_days" class="form-control" value="{{ leave_type.allowed_days }}" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">لون الإجازة في التقويم</label>
                            <input type="color" name="color" class="form-control form-control-color" value="{{ leave_type.color|default:'#007bff' }}">
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="is_paid" id="is_paid" {% if leave_type.is_paid %}checked{% endif %}>
                            <label class="form-check-label" for="is_paid">إجازة مدفوعة</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="requires_approval" id="requires_approval" {% if leave_type.requires_approval %}checked{% endif %}>
                            <label class="form-check-label" for="requires_approval">تتطلب موافقة</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="can_carry_forward" id="can_carry_forward" {% if leave_type.can_carry_forward %}checked{% endif %}>
                            <label class="form-check-label" for="can_carry_forward">يمكن ترحيلها للعام التالي</label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">أقصى أيام للترحيل</label>
                            <input type="number" name="max_carry_forward_days" class="form-control" value="{{ leave_type.max_carry_forward_days }}" min="0">
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" {% if leave_type.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">نشط</label>
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <a href="{% url 'hr:leave_type_list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 