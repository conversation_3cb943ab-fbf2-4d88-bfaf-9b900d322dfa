@echo off
REM ملف batch لتشغيل خدمة أوساريك بشكل دائم
REM يعيد تشغيل الخدمة تلقائياً في حالة التوقف

title خدمة أوساريك - Osaric Service
color 0A

echo ========================================
echo        خدمة أوساريك - Osaric Service
echo ========================================
echo.

REM الانتقال لمجلد المشروع
cd /d "D:\"

REM التحقق من وجود ملف الخدمة
if not exist "osaric_service.py" (
    echo ❌ ملف الخدمة غير موجود: osaric_service.py
    pause
    exit /b 1
)

REM التحقق من وجود manage.py
if not exist "manage.py" (
    echo ❌ ملف manage.py غير موجود
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات المشروع
echo 🚀 بدء تشغيل خدمة أوساريك...
echo.

REM تفعيل البيئة الافتراضية إذا كانت موجودة
if exist "venv_new\Scripts\activate.bat" (
    call venv_new\Scripts\activate.bat
    echo ✅ تم تفعيل البيئة الافتراضية
)

REM حلقة لا نهائية لإعادة التشغيل التلقائي
:restart_loop

echo [%date% %time%] بدء تشغيل الخدمة...

REM تشغيل خدمة أوساريك
python osaric_service.py

REM إذا توقفت الخدمة، انتظر 5 ثوان ثم أعد التشغيل
echo.
echo ⚠️ توقفت الخدمة! إعادة التشغيل خلال 5 ثوان...
echo [%date% %time%] إعادة تشغيل تلقائي...
timeout /t 5 /nobreak >nul

goto restart_loop
