{% extends 'base/base.html' %}
{% block title %}تفاصيل الخصم{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0"><i class="fas fa-minus me-2"></i>تفاصيل الخصم من المرتب</h4>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">الموظف</dt>
                        <dd class="col-sm-8">
                            {% if object.employee %}
                                {% if object.employee.full_name %}
                                    {{ object.employee.full_name }}
                                {% elif object.employee.person and object.employee.person.name %}
                                    {{ object.employee.person.name }}
                                {% elif object.employee.person %}
                                    {{ object.employee.person }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </dd>
                        <dt class="col-sm-4">نوع الخصم</dt>
                        <dd class="col-sm-8">{{ object.name }}</dd>
                        <dt class="col-sm-4">المبلغ</dt>
                        <dd class="col-sm-8"><strong class="text-danger">{{ object.amount|floatformat:2 }} ج.م</strong></dd>
                        <dt class="col-sm-4">تاريخ الخصم</dt>
                        <dd class="col-sm-8">{{ object.date|date:'Y-m-d' }}</dd>
                        <dt class="col-sm-4">الحالة</dt>
                        <dd class="col-sm-8">{% if object.is_active %}<span class="badge bg-success">مطبق</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</dd>
                        <dt class="col-sm-4">ملاحظات</dt>
                        <dd class="col-sm-8">{{ object.notes|default:'-' }}</dd>
                    </dl>
                </div>
                <div class="card-footer text-end">
                    <a href="{% url 'hr:salary_deduction_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 