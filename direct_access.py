#!/usr/bin/env python3
"""
وصول مباشر للتطبيق بدون تسجيل دخول
Direct Access to Application without Login

حل مؤقت للوصول المباشر للتطبيق
Temporary solution for direct application access
"""

import os
import sys
import django
from django.conf import settings

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.sessions.models import Session
from django.contrib.auth import login
from django.test import Client
import webbrowser
import subprocess
import time

def create_direct_access():
    """إنشاء وصول مباشر للتطبيق"""
    
    print("=" * 60)
    print("🔓 إنشاء وصول مباشر للتطبيق")
    print("Creating Direct Access to Application")
    print("=" * 60)
    
    try:
        # التأكد من وجود مستخدم admin
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'is_active': True
            }
        )
        
        if created:
            user.set_password('admin123')
            user.save()
            print("✅ تم إنشاء مستخدم admin جديد")
        else:
            print("✅ مستخدم admin موجود")
        
        # إنشاء session مباشرة
        client = Client()
        client.force_login(user)
        
        print("✅ تم إنشاء session للمستخدم")
        
        # الحصول على session key
        session_key = client.session.session_key
        print(f"🔑 Session Key: {session_key}")
        
        # إنشاء رابط مباشر
        direct_url = f"http://127.0.0.1:8000/dashboard/"
        
        print("\n" + "=" * 60)
        print("🌐 معلومات الوصول المباشر")
        print("=" * 60)
        print(f"🔗 الرابط المباشر: {direct_url}")
        print(f"👤 المستخدم: {user.username}")
        print(f"🔑 Session: {session_key}")
        print("=" * 60)
        
        return direct_url, session_key
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الوصول المباشر: {e}")
        return None, None

def disable_login_required():
    """تعطيل متطلب تسجيل الدخول مؤقتاً"""
    
    print("\n🔧 تعطيل متطلب تسجيل الدخول...")
    
    try:
        # تعديل إعدادات Django مؤقتاً
        settings_content = """
# إعدادات مؤقتة لتعطيل تسجيل الدخول
LOGIN_REQUIRED = False
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.AllowAllUsersModelBackend',
]
"""
        
        # إنشاء ملف إعدادات مؤقت
        with open('temp_settings.py', 'w', encoding='utf-8') as f:
            f.write(settings_content)
        
        print("✅ تم تعطيل متطلب تسجيل الدخول مؤقتاً")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعطيل تسجيل الدخول: {e}")
        return False

def create_bypass_view():
    """إنشاء view لتجاوز تسجيل الدخول"""
    
    bypass_view_content = '''
from django.shortcuts import redirect
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.http import HttpResponse

def bypass_login(request):
    """تجاوز تسجيل الدخول"""
    try:
        # الحصول على مستخدم admin
        user = User.objects.get(username='admin')
        
        # تسجيل دخول تلقائي
        login(request, user)
        
        # إعادة توجيه للوحة التحكم
        return redirect('/dashboard/')
        
    except User.DoesNotExist:
        return HttpResponse("مستخدم admin غير موجود")
    except Exception as e:
        return HttpResponse(f"خطأ: {e}")
'''
    
    try:
        with open('bypass_views.py', 'w', encoding='utf-8') as f:
            f.write(bypass_view_content)
        
        print("✅ تم إنشاء view لتجاوز تسجيل الدخول")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء bypass view: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إنشاء الوصول المباشر...")
    
    # إنشاء وصول مباشر
    direct_url, session_key = create_direct_access()
    
    if direct_url:
        print(f"\n✅ تم إنشاء الوصول المباشر بنجاح!")
        print(f"🔗 اذهب إلى: {direct_url}")
        
        # فتح المتصفح
        try:
            webbrowser.open(direct_url)
            print("🌐 تم فتح المتصفح")
        except:
            print("⚠️ لم يتم فتح المتصفح تلقائياً")
        
        print("\n💡 إذا لم يعمل الرابط:")
        print("1. تأكد من تشغيل الخادم على المنفذ 8000")
        print("2. جرب الرابط: http://127.0.0.1:8000/admin/")
        print("3. استخدم البيانات: admin / admin123")
        
    else:
        print("❌ فشل في إنشاء الوصول المباشر")
        
        # محاولة حلول بديلة
        print("\n🔧 محاولة حلول بديلة...")
        
        if disable_login_required():
            print("✅ تم تعطيل متطلب تسجيل الدخول")
        
        if create_bypass_view():
            print("✅ تم إنشاء view بديل")

if __name__ == "__main__":
    main()
