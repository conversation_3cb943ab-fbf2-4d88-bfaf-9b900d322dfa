{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-balance-scale text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">متابعة أرصدة الإجازات للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:leave_balance_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة رصيد جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ employees|length }}</h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ total_days_sum }}</h3>
                    <p class="mb-0">إجمالي الأيام المخصصة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ used_days_sum }}</h3>
                    <p class="mb-0">الأيام المستخدمة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ remaining_days_sum }}</h3>
                    <p class="mb-0">الأيام المتبقية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Balances Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        أرصدة الإجازات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>السنة</th>
                                    <th>إجمالي الأيام</th>
                                    <th>المستخدمة</th>
                                    <th>المتبقية</th>
                                    <th>المحولة</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for balance in leave_balances %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ balance.employee }}</td>
                                    <td>{{ balance.leave_type }}</td>
                                    <td>{{ balance.year }}</td>
                                    <td>{{ balance.total_days }}</td>
                                    <td>{{ balance.used_days }}</td>
                                    <td>{{ balance.remaining_days }}</td>
                                    <td>{{ balance.carried_forward_days }}</td>
                                    <td>{{ balance.notes|default_if_none:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:leave_balance_edit' balance.id %}" class="btn btn-sm btn-outline-warning" title="تعديل الرصيد">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="post" action="{% url 'hr:leave_balance_delete' balance.id %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف الرصيد" onclick="return confirm('هل أنت متأكد من حذف هذا الرصيد؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-balance-scale fa-3x mb-3 d-block"></i>
                                        لا توجد أرصدة إجازات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Types Legend -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">دليل أنواع الإجازات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <span class="badge bg-success me-2">الإجازة السنوية</span>
                            <small class="text-muted">21 يوم سنوياً</small>
                        </div>
                        <div class="col-md-4">
                            <span class="badge bg-info me-2">الإجازة المرضية</span>
                            <small class="text-muted">30 يوم سنوياً</small>
                        </div>
                        <div class="col-md-4">
                            <span class="badge bg-warning me-2">الإجازة الطارئة</span>
                            <small class="text-muted">3 أيام سنوياً</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
