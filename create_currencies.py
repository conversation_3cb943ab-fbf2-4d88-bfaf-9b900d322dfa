#!/usr/bin/env python
"""
إنشاء العملات المطلوبة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Currency

def create_currencies():
    """إنشاء العملات المطلوبة"""
    
    print("🔧 إنشاء العملات المطلوبة...")
    
    try:
        # العملات المطلوبة
        currencies = [
            {'code': 'EUR', 'name': 'يورو', 'symbol': '€'},
            {'code': 'GBP', 'name': 'جنيه إسترليني', 'symbol': '£'},
            {'code': 'SAR', 'name': 'ريال سعودي', 'symbol': 'ر.س'},
            {'code': 'AED', 'name': 'درهم إماراتي', 'symbol': 'د.إ'},
            {'code': 'EGP', 'name': 'جنيه مصري', 'symbol': 'ج.م'},
            {'code': 'USD', 'name': 'دولار أمريكي', 'symbol': '$'},
        ]

        for curr_data in currencies:
            currency, created = Currency.objects.get_or_create(
                code=curr_data['code'],
                defaults={
                    'name': curr_data['name'],
                    'symbol': curr_data['symbol'],
                    'exchange_rate': 1.0,
                    'is_active': True
                }
            )
            if created:
                print(f'✅ تم إنشاء العملة: {currency.name} ({currency.code})')
            else:
                print(f'✓ العملة موجودة: {currency.name} ({currency.code})')

        print('🎉 تم إنشاء جميع العملات بنجاح!')
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء العملات: {e}")

if __name__ == "__main__":
    create_currencies()
