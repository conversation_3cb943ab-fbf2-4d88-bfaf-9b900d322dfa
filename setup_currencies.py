#!/usr/bin/env python
"""
إعداد العملات الافتراضية في النظام
"""

import os
import sys
import django

# إعداد Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Currency

def setup_currencies():
    """إنشاء العملات الافتراضية"""
    print("🏦 إعداد العملات الافتراضية...")
    print("=" * 50)
    
    # العملات الافتراضية
    currencies = [
        {'code': 'EGP', 'name': 'الجنيه المصري', 'symbol': 'ج.م', 'is_base': True},
        {'code': 'USD', 'name': 'الدولار الأمريكي', 'symbol': '$'},
        {'code': 'EUR', 'name': 'اليورو', 'symbol': '€'},
        {'code': 'GBP', 'name': 'الجنيه الإسترليني', 'symbol': '£'},
        {'code': 'SAR', 'name': 'الريال السعودي', 'symbol': 'ر.س'},
        {'code': 'AED', 'name': 'الدرهم الإماراتي', 'symbol': 'د.إ'},
        {'code': 'KWD', 'name': 'الدينار الكويتي', 'symbol': 'د.ك'},
        {'code': 'QAR', 'name': 'الريال القطري', 'symbol': 'ر.ق'},
        {'code': 'OMR', 'name': 'الريال العماني', 'symbol': 'ر.ع'},
        {'code': 'BHD', 'name': 'الدينار البحريني', 'symbol': 'د.ب'},
        {'code': 'JOD', 'name': 'الدينار الأردني', 'symbol': 'د.أ'},
    ]
    
    created_count = 0
    updated_count = 0
    
    for currency_data in currencies:
        currency, created = Currency.objects.get_or_create(
            code=currency_data['code'],
            defaults={
                'name': currency_data['name'],
                'symbol': currency_data['symbol'],
                'is_base_currency': currency_data.get('is_base', False),
                'is_active': True,
            }
        )
        
        if created:
            created_count += 1
            print(f'✅ تم إنشاء العملة: {currency.name} ({currency.code})')
        else:
            # تحديث البيانات إذا كانت مختلفة
            updated = False
            if currency.name != currency_data['name']:
                currency.name = currency_data['name']
                updated = True
            if currency.symbol != currency_data['symbol']:
                currency.symbol = currency_data['symbol']
                updated = True
            if currency.is_base_currency != currency_data.get('is_base', False):
                currency.is_base_currency = currency_data.get('is_base', False)
                updated = True
            if not currency.is_active:
                currency.is_active = True
                updated = True
            
            if updated:
                currency.save()
                updated_count += 1
                print(f'🔄 تم تحديث العملة: {currency.name} ({currency.code})')
            else:
                print(f'⚠️ العملة موجودة: {currency.name} ({currency.code})')
    
    print("\n" + "=" * 50)
    print(f'📝 تم إنشاء {created_count} عملة جديدة')
    print(f'🔄 تم تحديث {updated_count} عملة')
    print(f'📊 إجمالي العملات: {Currency.objects.count()}')
    print(f'💰 العملات المفعلة: {Currency.objects.filter(is_active=True).count()}')
    
    # عرض العملة الأساسية
    base_currency = Currency.objects.filter(is_base_currency=True).first()
    if base_currency:
        print(f'🏛️ العملة الأساسية: {base_currency.name} ({base_currency.code})')
    
    print("✅ تم إعداد العملات بنجاح!")

def main():
    """الدالة الرئيسية"""
    try:
        setup_currencies()
    except Exception as e:
        print(f"❌ خطأ في إعداد العملات: {str(e)}")
        return False
    return True

if __name__ == "__main__":
    main()
