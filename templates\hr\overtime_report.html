{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-clock text-secondary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل عن ساعات العمل الإضافي والتكاليف</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-secondary" onclick="generateOvertimeReport()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Overtime Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-secondary text-white overtime-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3>{{ total_overtime_hours }}</h3>
                    <p class="mb-0">إجمالي ساعات إضافية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white overtime-card">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h3>{{ total_overtime_cost }}</h3>
                    <p class="mb-0">إجمالي التكلفة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white overtime-card">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x mb-2"></i>
                    <h3>{{ avg_overtime_per_employee }}</h3>
                    <p class="mb-0">متوسط الساعات/موظف</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white overtime-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3>{{ employees_with_overtime }}</h3>
                    <p class="mb-0">موظفين بعمل إضافي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Overtime Types -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع أنواع العمل الإضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="overtime-circle bg-primary text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>120h</strong>
                                </div>
                                <h6 class="text-primary">عمل إضافي عادي</h6>
                                <small class="text-muted">65%</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="overtime-circle bg-warning text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>45h</strong>
                                </div>
                                <h6 class="text-warning">عمل إضافي ليلي</h6>
                                <small class="text-muted">25%</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="overtime-circle bg-danger text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>15h</strong>
                                </div>
                                <h6 class="text-danger">عمل إضافي عطلة</h6>
                                <small class="text-muted">8%</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="overtime-circle bg-success text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>5h</strong>
                                </div>
                                <h6 class="text-success">عمل إضافي طارئ</h6>
                                <small class="text-muted">2%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">نوع العمل الإضافي:</label>
                            <select class="form-select" onchange="filterByOvertimeType()">
                                <option value="">جميع الأنواع</option>
                                <option value="عادي">عمل إضافي عادي</option>
                                <option value="ليلي">عمل إضافي ليلي</option>
                                <option value="عطلة">عمل إضافي عطلة</option>
                                <option value="طارئ">عمل إضافي طارئ</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="الإنتاج">الإنتاج</option>
                                <option value="المبيعات">المبيعات</option>
                                <option value="تقنية المعلومات">تقنية المعلومات</option>
                                <option value="المالية">المالية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفترة:</label>
                            <select class="form-select" onchange="filterByPeriod()">
                                <option value="month" selected>هذا الشهر</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportOvertime()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Excel
                                </button>
                                <button class="btn btn-info" onclick="printOvertime()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overtime Details Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل العمل الإضافي للموظفين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>ساعات عادية</th>
                                    <th>ساعات ليلية</th>
                                    <th>ساعات عطلة</th>
                                    <th>إجمالي الساعات</th>
                                    <th>معدل الساعة</th>
                                    <th>إجمالي المستحق</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for overtime in overtime_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-secondary text-white rounded-circle me-2" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                {{ overtime.employee_name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ overtime.employee_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ overtime.employee_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ overtime.department }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ overtime.regular_hours }}h</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ overtime.night_hours }}h</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ overtime.holiday_hours }}h</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ overtime.total_hours }}h</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">{{ overtime.hourly_rate }} ج.م</span>
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ overtime.total_amount }} ج.م</strong>
                                        <br>
                                        <small class="text-muted">
                                            {% if overtime.total_hours > 30 %}
                                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>ساعات عالية
                                            {% elif overtime.total_hours > 15 %}
                                                <i class="fas fa-info-circle text-info me-1"></i>ساعات متوسطة
                                            {% else %}
                                                <i class="fas fa-check-circle text-success me-1"></i>ساعات عادية
                                            {% endif %}
                                        </small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-clock fa-3x mb-3 d-block"></i>
                                        لا توجد بيانات عمل إضافي للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Overtime Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        ملخص العمل الإضافي حسب الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dept in department_overtime %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title text-secondary">{{ dept.name }}</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي الساعات:</span>
                                        <strong class="text-primary">{{ dept.total_hours }}h</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>عدد الموظفين:</span>
                                        <strong>{{ dept.employee_count }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي التكلفة:</span>
                                        <strong class="text-success">{{ dept.total_cost }} ج.م</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>متوسط/موظف:</span>
                                        <strong class="text-info">{{ dept.avg_hours }}h</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overtime Analysis by Type -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        تحليل العمل الإضافي حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-placeholder" style="height: 300px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center">
                                    <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">رسم بياني دائري</h5>
                                    <p class="text-muted">توزيع أنواع العمل الإضافي</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="overtime-type-stats">
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="overtime-type-indicator bg-primary me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">عمل إضافي عادي</h6>
                                            <small class="text-muted">ساعات العمل العادية الإضافية</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-primary">120 ساعة</strong>
                                        <br>
                                        <small class="text-muted">65%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="overtime-type-indicator bg-warning me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">عمل إضافي ليلي</h6>
                                            <small class="text-muted">ساعات العمل الليلية</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-warning">45 ساعة</strong>
                                        <br>
                                        <small class="text-muted">25%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="overtime-type-indicator bg-danger me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">عمل إضافي عطلة</h6>
                                            <small class="text-muted">ساعات العمل في العطل</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-danger">15 ساعة</strong>
                                        <br>
                                        <small class="text-muted">8%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="overtime-type-indicator bg-success me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">عمل إضافي طارئ</h6>
                                            <small class="text-muted">ساعات العمل الطارئة</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success">5 ساعات</strong>
                                        <br>
                                        <small class="text-muted">2%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Overtime Trend -->
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه العمل الإضافي الشهري
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder" style="height: 250px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">رسم بياني للعمل الإضافي</h5>
                            <p class="text-muted">يظهر توزيع ساعات العمل الإضافي عبر الأشهر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات العمل الإضافي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <strong>تحذير!</strong>
                        <br>
                        قسم الإنتاج تجاوز الحد المسموح للعمل الإضافي هذا الشهر
                    </div>
                    <div class="alert alert-info" role="alert">
                        <strong>ملاحظة:</strong>
                        <br>
                        3 موظفين لديهم أكثر من 20 ساعة إضافية
                    </div>
                    <div class="alert alert-success" role="alert">
                        <strong>جيد:</strong>
                        <br>
                        انخفاض العمل الإضافي بنسبة 15% عن الشهر الماضي
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Overtime Employees -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        الموظفين الأكثر عملاً إضافياً هذا الشهر
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3 p-3 border rounded">
                                <div class="avatar bg-warning text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">أحمد محمد</h6>
                                    <p class="mb-1 text-muted">قسم الإنتاج</p>
                                    <small class="text-warning">
                                        <i class="fas fa-clock me-1"></i>
                                        35 ساعة إضافية
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-warning">2,800 ج.م</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3 p-3 border rounded">
                                <div class="avatar bg-secondary text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">فاطمة علي</h6>
                                    <p class="mb-1 text-muted">قسم المبيعات</p>
                                    <small class="text-secondary">
                                        <i class="fas fa-clock me-1"></i>
                                        28 ساعة إضافية
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">2,240 ج.م</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3 p-3 border rounded">
                                <div class="avatar bg-info text-white rounded-circle me-3" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">محمد أحمد</h6>
                                    <p class="mb-1 text-muted">قسم تقنية المعلومات</p>
                                    <small class="text-info">
                                        <i class="fas fa-clock me-1"></i>
                                        22 ساعة إضافية
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-info">1,980 ج.م</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterByOvertimeType() {
    const overtimeType = event.target.value;
    console.log('تم تطبيق فلتر نوع العمل الإضافي:', overtimeType);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (overtimeType === '' || row.textContent.includes(overtimeType)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر نوع العمل الإضافي: ${overtimeType || 'جميع الأنواع'}`);
}

function filterByDepartment() {
    const department = event.target.value;
    console.log('تم تطبيق فلتر القسم:', department);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (department === '' || row.textContent.includes(department)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر القسم: ${department || 'جميع الأقسام'}`);
}

function filterByPeriod() {
    const period = event.target.value;
    console.log('تم تطبيق فلتر الفترة:', period);

    // إظهار رسالة للمستخدم
    const periodText = {
        'month': 'هذا الشهر',
        'week': 'هذا الأسبوع',
        'quarter': 'هذا الربع',
        'year': 'هذا العام'
    };

    showToast(`تم تطبيق فلتر الفترة: ${periodText[period] || period}`);
}

function exportOvertime() {
    // محاكاة تصدير البيانات
    const data = [
        ['الموظف', 'القسم', 'ساعات عادية', 'ساعات ليلية', 'ساعات عطلة', 'إجمالي الساعات', 'إجمالي المستحق'],
        ['أحمد محمد', 'الإنتاج', '25', '8', '2', '35', '2800'],
        ['فاطمة علي', 'المبيعات', '20', '6', '2', '28', '2240'],
        // يمكن إضافة المزيد من البيانات هنا
    ];

    console.log('بيانات التصدير:', data);
    showToast('تم تصدير تقرير العمل الإضافي بنجاح!', 'success');
}

function printOvertime() {
    // جمع بيانات العمل الإضافي من الجدول
    const overtimeData = [];
    const rows = document.querySelectorAll('#overtimeTable tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            overtimeData.push({
                employee_name: cells[1]?.textContent?.trim() || 'غير محدد',
                date: cells[2]?.textContent?.trim() || 'غير محدد',
                start_time: cells[3]?.textContent?.trim() || 'غير محدد',
                end_time: cells[4]?.textContent?.trim() || 'غير محدد',
                hours: cells[5]?.textContent?.trim() || '0',
                rate: cells[6]?.textContent?.trim() || '0',
                amount: cells[7]?.textContent?.trim() || '0'
            });
        }
    });

    // استخدام القالب المخصص للطباعة
    printReport('تقرير العمل الإضافي', `
        .overtime-table th {
            background: linear-gradient(135deg, #f39c12, #e67e22) !important;
            color: white !important;
        }
        .overtime-summary {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
            border: 2px solid #f39c12 !important;
            padding: 20px !important;
            border-radius: 8px !important;
            margin-top: 30px !important;
        }
    `);
}

function generateOvertimeReport() {
    // محاكاة إنشاء تقرير مخصص
    showToast('جاري إنشاء تقرير العمل الإضافي المخصص...', 'info');

    setTimeout(() => {
        showToast('تم إنشاء تقرير العمل الإضافي المخصص بنجاح!', 'success');
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    toast.innerHTML = `
        <strong>${type === 'success' ? 'نجح!' : type === 'warning' ? 'تحذير!' : 'معلومة:'}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 4 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

// إضافة وظائف تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للجدول
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // إضافة إمكانية البحث السريع
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'البحث السريع في الجدول...';
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // إدراج حقل البحث قبل الجدول
    const tableCard = document.querySelector('.table-responsive').parentNode;
    tableCard.insertBefore(searchInput, tableCard.querySelector('.table-responsive'));

    // إضافة تأثيرات للبطاقات
    const overtimeCards = document.querySelectorAll('.overtime-card');
    overtimeCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.15)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // إضافة تأثيرات لإحصائيات أنواع العمل الإضافي
    const typeStats = document.querySelectorAll('.overtime-type-stats .border');
    typeStats.forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transform = 'translateX(5px)';
        });
        stat.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'translateX(0)';
        });
    });
});
</script>

<style>
.overtime-card {
    transition: all 0.3s ease-in-out;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

.progress-bar {
    transition: width 0.3s ease-in-out;
}

.alert {
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-success {
    border-left-color: #198754;
}

.card-header {
    font-weight: 600;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.overtime-type-stats .border {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.overtime-type-indicator {
    transition: all 0.2s ease-in-out;
}

.chart-placeholder {
    transition: all 0.3s ease-in-out;
}

.chart-placeholder:hover {
    background: linear-gradient(45deg, #e9ecef, #f8f9fa);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .overtime-type-stats .d-flex {
        flex-direction: column;
        text-align: center;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}
