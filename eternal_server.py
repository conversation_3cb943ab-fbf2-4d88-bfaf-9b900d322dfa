#!/usr/bin/env python3
"""
خادم أبدي لا يتوقف أبداً
Eternal Server - Never Stops

خادم متقدم يعمل بشكل دائم ولا يتوقف تحت أي ظرف
Advanced server that runs permanently and never stops under any circumstances
"""

import os
import sys
import time
import signal
import socket
import subprocess
import threading
import logging
import atexit
import psutil
from datetime import datetime
from pathlib import Path

# إعداد اللوجز المتقدم
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'eternal_server.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class EternalServer:
    """خادم أبدي لا يتوقف أبداً"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = float('inf')  # لا نهاية
        self.restart_delay = 2
        self.preferred_ports = [8000, 8001, 8002, 8003, 8080, 8888, 9000, 3000, 5000]
        self.current_port = None
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.health_check_interval = 10  # فحص كل 10 ثوانٍ
        self.last_health_check = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5
        self.pid_file = Path('eternal_server.pid')
        self.lock_file = Path('eternal_server.lock')
        self.use_https = False
        self.ssl_cert = None
        self.ssl_key = None

        # فحص وإعداد SSL
        self.setup_ssl()

        # تسجيل معالجات الإغلاق
        atexit.register(self.cleanup)
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ✅ {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"

    def setup_ssl(self):
        """إعداد SSL"""
        try:
            ssl_dir = Path('ssl')
            cert_file = ssl_dir / 'server.crt'
            key_file = ssl_dir / 'server.key'

            if cert_file.exists() and key_file.exists():
                self.ssl_cert = cert_file
                self.ssl_key = key_file
                self.use_https = True
                self.log_success("تم العثور على شهادة SSL - سيتم استخدام HTTPS")
            else:
                self.log_info("لا توجد شهادة SSL - إنشاء شهادة جديدة...")
                if self.create_ssl_certificate():
                    self.use_https = True
                    self.log_success("تم إنشاء شهادة SSL - سيتم استخدام HTTPS")
                else:
                    self.log_info("سيتم استخدام HTTP العادي")
                    self.use_https = False
        except Exception as e:
            self.log_error(f"خطأ في إعداد SSL: {e}")
            self.use_https = False

    def create_ssl_certificate(self):
        """إنشاء شهادة SSL"""
        try:
            from simple_ssl import create_ssl_certificate
            cert_file, key_file, pem_file = create_ssl_certificate()

            if cert_file and key_file:
                self.ssl_cert = cert_file
                self.ssl_key = key_file
                return True
            else:
                return False

        except Exception as e:
            self.log_error(f"خطأ في إنشاء شهادة SSL: {e}")
            return False
    
    def create_pid_file(self):
        """إنشاء ملف PID"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
            self.log_info(f"تم إنشاء ملف PID: {self.pid_file}")
        except Exception as e:
            self.log_error(f"خطأ في إنشاء ملف PID: {e}")
    
    def create_lock_file(self):
        """إنشاء ملف القفل"""
        try:
            with open(self.lock_file, 'w') as f:
                f.write(f"{os.getpid()}\n{datetime.now().isoformat()}")
            self.log_info(f"تم إنشاء ملف القفل: {self.lock_file}")
        except Exception as e:
            self.log_error(f"خطأ في إنشاء ملف القفل: {e}")
    
    def check_if_already_running(self):
        """فحص إذا كان الخادم يعمل بالفعل"""
        if self.pid_file.exists():
            try:
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                # فحص إذا كانت العملية ما زالت تعمل
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    if 'python' in process.name().lower():
                        self.log_error(f"الخادم الأبدي يعمل بالفعل! PID: {pid}")
                        return True
                else:
                    # العملية لا تعمل، احذف ملف PID
                    self.pid_file.unlink()
                    
            except Exception as e:
                self.log_error(f"خطأ في فحص ملف PID: {e}")
                
        return False
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self):
        """البحث عن منفذ متاح"""
        self.log_info("البحث عن منفذ متاح...")
        
        for port in self.preferred_ports:
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        # البحث في نطاق أوسع
        for port in range(8000, 9000):
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        # البحث في نطاق أوسع جداً
        for port in range(3000, 10000):
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        self.log_error("لم يتم العثور على منفذ متاح")
        return None
    
    def kill_processes_on_port(self, port):
        """قتل العمليات المستخدمة للمنفذ"""
        try:
            killed = False
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    if proc.info['connections']:
                        for conn in proc.info['connections']:
                            if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                                proc.terminate()
                                killed = True
                                self.log_success(f"تم إنهاء العملية {proc.info['name']} (PID: {proc.info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if killed:
                time.sleep(2)
            return killed
        except Exception as e:
            self.log_error(f"خطأ في قتل العمليات: {e}")
            return False
    
    def prepare_environment(self):
        """تحضير البيئة"""
        try:
            # إنشاء المجلدات المطلوبة
            dirs = ['logs', 'static', 'staticfiles', 'media', 'backup', 'temp']
            for dir_name in dirs:
                Path(dir_name).mkdir(exist_ok=True)
            
            # تشغيل الترحيلات
            self.run_migrations()
            
            # جمع الملفات الثابتة
            self.collect_static()
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في تحضير البيئة: {e}")
            return False
    
    def run_migrations(self):
        """تشغيل الترحيلات"""
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'migrate', '--noinput'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                self.log_success("تم تشغيل الترحيلات بنجاح")
            else:
                self.log_info("تحذير في الترحيلات (سنستمر)")
                
        except Exception as e:
            self.log_error(f"خطأ في الترحيلات: {e}")
    
    def collect_static(self):
        """جمع الملفات الثابتة"""
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_success("تم جمع الملفات الثابتة")
            else:
                self.log_info("تحذير في جمع الملفات الثابتة (سنستمر)")
                
        except Exception as e:
            self.log_info(f"تحذير في جمع الملفات الثابتة: {e}")
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة {signum} - لكن الخادم الأبدي لن يتوقف!")
        # لا نتوقف! نتجاهل الإشارة
        pass
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        # نتجاهل جميع إشارات الإيقاف
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            # البحث عن منفذ متاح
            port = self.find_available_port()
            if not port:
                # محاولة تحرير منفذ
                for preferred_port in self.preferred_ports:
                    if self.kill_processes_on_port(preferred_port):
                        if self.is_port_available(preferred_port):
                            port = preferred_port
                            break
                
                if not port:
                    self.log_error("لا يمكن العثور على منفذ متاح")
                    return False
            
            self.current_port = port
            self.log_info(f"بدء تشغيل الخادم الأبدي على {self.host}:{port}")
            
            # إنشاء عملية الخادم مع حماية من الإغلاق
            # استخدام HTTP عادي دائماً للاستقرار
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{port}',
                '--insecure', '--noreload'
            ]
            self.log_info(f"استخدام HTTP على المنفذ {port}")

            # إضافة ملاحظة حول SSL
            if self.use_https and self.ssl_cert and self.ssl_key:
                self.log_info("شهادة SSL متوفرة - يمكن تفعيل HTTPS لاحقاً")
            
            # تشغيل الخادم في عملية منفصلة ومحمية
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0,
                preexec_fn=None if os.name == 'nt' else os.setsid
            )
            
            self.is_running = True
            self.log_success(f"تم بدء الخادم الأبدي! PID: {self.server_process.pid}")
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(3)
            
            if self.server_process.poll() is None:
                self.log_success("الخادم الأبدي يعمل بنجاح")
                self.display_access_info()
                self.consecutive_failures = 0
                return True
            else:
                self.log_error(f"الخادم توقف فوراً بكود: {self.server_process.returncode}")
                return False
            
        except Exception as e:
            self.log_error(f"خطأ في بدء الخادم: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🌟 الخادم الأبدي - لا يتوقف أبداً")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 المنفذ: {self.current_port}")
        print(f"🔐 البروتوكول: HTTP")

        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   http://{self.local_ip}:{self.current_port}/")
        print("\n💻 للوصول من نفس الجهاز:")
        print(f"   http://localhost:{self.current_port}/")
        print(f"   http://127.0.0.1:{self.current_port}/")

        if self.ssl_cert and self.ssl_key:
            print("\n🔒 شهادة SSL متوفرة:")
            print(f"   📄 الشهادة: {self.ssl_cert}")
            print(f"   🔑 المفتاح: {self.ssl_key}")
            print("   💡 يمكن تفعيل HTTPS لاحقاً")

        print("\n🔥 مميزات الخادم الأبدي:")
        print("   • لا يتوقف أبداً")
        print("   • إعادة تشغيل تلقائية فورية")
        print("   • مقاوم لجميع أنواع الأخطاء")
        print("   • فحص صحة كل 10 ثوانٍ")
        print("   • يجد منفذ متاح تلقائياً")
        print("   • شهادة SSL جاهزة للاستخدام")
        print("=" * 60)
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            if not self.server_process or self.server_process.poll() is not None:
                return False

            import urllib.request
            import urllib.error

            url = f"http://127.0.0.1:{self.current_port}/"

            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'EternalServer/1.0')
                response = urllib.request.urlopen(request, timeout=5)

                if response.getcode() == 200:
                    return True
                else:
                    self.log_error(f"الخادم يرد بكود خطأ: {response.getcode()}")
                    return False

            except urllib.error.URLError as e:
                self.log_error(f"خطأ في الاتصال بالخادم: {e}")
                return False

        except Exception as e:
            self.log_error(f"خطأ في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.restart_count += 1
        self.log_info(f"إعادة تشغيل الخادم الأبدي (المحاولة #{self.restart_count})...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        # تحضير البيئة مرة أخرى
        self.prepare_environment()
        
        return self.start_server()
    
    def stop_server(self):
        """إيقاف الخادم (مؤقت فقط)"""
        if self.server_process:
            try:
                self.log_info("إيقاف مؤقت للخادم...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.server_process.kill()
                    self.server_process.wait()
                
            except Exception as e:
                self.log_error(f"خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
    
    def eternal_monitor_loop(self):
        """حلقة المراقبة الأبدية"""
        while True:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    self.log_info("إعادة تشغيل الخادم الأبدي...")
                    if self.restart_server():
                        continue
                
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("عملية الخادم توقفت! إعادة تشغيل فورية...")
                    if self.restart_server():
                        continue
                
                if self.check_server_health():
                    if self.consecutive_failures > 0:
                        self.log_success("الخادم الأبدي عاد للعمل بنجاح")
                    self.consecutive_failures = 0
                    self.log_info("الخادم الأبدي يعمل بصحة ممتازة")
                else:
                    self.consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({self.consecutive_failures}/{self.max_consecutive_failures})")
                    
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        self.log_error("فشل متكرر - إعادة تشغيل فورية")
                        if self.restart_server():
                            continue
                
                self.last_health_check = time.time()
                
            except Exception as e:
                self.log_error(f"خطأ في حلقة المراقبة: {e}")
                time.sleep(5)
                # نستمر في المحاولة
                continue
    
    def cleanup(self):
        """تنظيف الملفات عند الإغلاق"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
            if self.lock_file.exists():
                self.lock_file.unlink()
        except Exception:
            pass
    
    def run(self):
        """تشغيل الخادم الأبدي"""
        print("=" * 60)
        print("🌟 الخادم الأبدي - لا يتوقف أبداً")
        print("Eternal Server - Never Stops")
        print("=" * 60)
        
        # فحص إذا كان يعمل بالفعل
        if self.check_if_already_running():
            return False
        
        # إنشاء ملفات التحكم
        self.create_pid_file()
        self.create_lock_file()
        
        self.log_info("بدء تشغيل الخادم الأبدي...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # تحضير البيئة
        if not self.prepare_environment():
            self.log_error("فشل في تحضير البيئة - لكن سنحاول المتابعة")
        
        # بدء الخادم
        if not self.start_server():
            self.log_error("فشل في بدء الخادم - سنحاول مرة أخرى")
            time.sleep(5)
            if not self.restart_server():
                self.log_error("فشل نهائي في بدء الخادم")
                return False
        
        self.log_success("الخادم الأبدي يعمل بأقصى استقرار!")
        self.log_info("الخادم محمي من جميع أنواع الأخطاء والإيقاف")
        self.log_info("سيعمل إلى الأبد ولن يتوقف تحت أي ظرف")
        
        # بدء المراقبة الأبدية
        try:
            self.eternal_monitor_loop()
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C - لكن الخادم الأبدي لن يتوقف!")
            # نتجاهل Ctrl+C ونستمر
            self.eternal_monitor_loop()
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
            # نعيد تشغيل الحلقة
            time.sleep(5)
            self.eternal_monitor_loop()

def main():
    """الدالة الرئيسية"""
    server = EternalServer()
    server.run()

if __name__ == "__main__":
    main()
