#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import connection

cursor = connection.cursor()

# Complete list of ALL possible missing columns for ManufacturingOrder
all_columns = [
    ("actual_start_date", "DATE"),
    ("actual_completion_date", "DATE"),
    ("approved_at", "DATETIME"),
    ("completed_at", "DATETIME"),
    ("materials_reserved", "BOOLEAN DEFAULT 0"),
    ("materials_consumed", "BOOLEAN DEFAULT 0"),
    ("production_completed", "BOOLEAN DEFAULT 0"),
    ("quality_approved", "BOOLEAN DEFAULT 0"),
    ("stock_updated", "BOOLEAN DEFAULT 0"),
    ("approved_by_id", "INTEGER"),
    ("completed_by_id", "INTEGER"),
    ("total_material_cost", "DECIMAL(15,2) DEFAULT 0.00 NOT NULL"),
    ("total_production_cost", "DECIMAL(15,2) DEFAULT 0.00 NOT NULL"),
    ("production_cost_per_unit", "DECIMAL(15,2) DEFAULT 0.00 NOT NULL"),
]

print("Complete database fix...")
print("=" * 50)

for column_name, column_type in all_columns:
    try:
        cursor.execute(f"ALTER TABLE inventory_manufacturingorder ADD COLUMN {column_name} {column_type}")
        print(f"+ Added {column_name}")
    except Exception as e:
        if "duplicate column name" in str(e):
            print(f"- {column_name} already exists")
        else:
            print(f"x Error in {column_name}: {e}")

print("=" * 50)
print("Complete fix finished!")
