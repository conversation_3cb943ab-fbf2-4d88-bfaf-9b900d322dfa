{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-lg">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-tools fa-5x text-primary mb-3"></i>
                        <h2 class="text-primary">{{ title }}</h2>
                        <p class="lead text-muted">{{ message }}</p>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        نحن نعمل بجد لإنجاز هذا التقرير. سيكون متاحاً قريباً!
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-chart-bar fa-2x text-success mb-2"></i>
                                <h6>تقارير تفاعلية</h6>
                                <small class="text-muted">رسوم بيانية ومخططات متقدمة</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-download fa-2x text-info mb-2"></i>
                                <h6>تصدير متعدد</h6>
                                <small class="text-muted">Excel, PDF, CSV</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-filter fa-2x text-warning mb-2"></i>
                                <h6>فلاتر متقدمة</h6>
                                <small class="text-muted">تخصيص التقارير حسب الحاجة</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="/hr/" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للوحة التحكم
                        </a>
                        <button class="btn btn-outline-secondary btn-lg" onclick="notifyWhenReady()">
                            <i class="fas fa-bell me-2"></i>
                            أعلمني عند الانتهاء
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function notifyWhenReady() {
    alert('سيتم إعلامك عند توفر هذا التقرير');
}
</script>

<style>
.feature-item {
    padding: 20px;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    background-color: #f8f9fa;
}
</style>
{% endblock %}
