/**
 * Enhanced Form Validation for Manufacturing Order
 * تحسينات التحقق من صحة البيانات لنموذج أمر الإنتاج
 */

$(document).ready(function() {
    
    // قواعد التحقق المخصصة
    const validationRules = {
        finished_product: {
            required: true,
            message: 'يرجى اختيار المنتج النهائي'
        },
        quantity_to_produce: {
            required: true,
            min: 0.01,
            message: 'يرجى إدخال كمية صحيحة أكبر من صفر'
        },
        raw_materials_warehouse: {
            required: true,
            message: 'يرجى اختيار مخزن المواد الخام'
        },
        finished_goods_warehouse: {
            required: true,
            message: 'يرجى اختيار مخزن المنتج النهائي'
        },
        expected_start_date: {
            required: false,
            dateValidation: true,
            message: 'يرجى إدخال تاريخ صحيح'
        },
        expected_completion_date: {
            required: false,
            dateValidation: true,
            afterStartDate: true,
            message: 'يرجى إدخال تاريخ انتهاء صحيح بعد تاريخ البدء'
        }
    };

    // التحقق من صحة حقل واحد
    function validateField(fieldName, value) {
        const rule = validationRules[fieldName];
        if (!rule) return { valid: true };

        const $field = $(`#id_${fieldName}`);
        
        // التحقق من الحقول المطلوبة
        if (rule.required && (!value || value.trim() === '')) {
            return {
                valid: false,
                message: rule.message
            };
        }

        // التحقق من القيم الرقمية
        if (rule.min !== undefined) {
            const numValue = parseFloat(value);
            if (isNaN(numValue) || numValue < rule.min) {
                return {
                    valid: false,
                    message: rule.message
                };
            }
        }

        // التحقق من التواريخ
        if (rule.dateValidation && value) {
            const date = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (isNaN(date.getTime())) {
                return {
                    valid: false,
                    message: 'تاريخ غير صحيح'
                };
            }
            
            if (date < today) {
                return {
                    valid: false,
                    message: 'لا يمكن أن يكون التاريخ في الماضي'
                };
            }
        }

        // التحقق من تاريخ الانتهاء بعد تاريخ البدء
        if (rule.afterStartDate && value) {
            const startDate = $('#id_expected_start_date').val();
            if (startDate) {
                const start = new Date(startDate);
                const end = new Date(value);
                
                if (end <= start) {
                    return {
                        valid: false,
                        message: 'تاريخ الانتهاء يجب أن يكون بعد تاريخ البدء'
                    };
                }
            }
        }

        return { valid: true };
    }

    // عرض رسالة خطأ للحقل
    function showFieldError($field, message) {
        clearFieldError($field);
        
        $field.addClass('is-invalid');
        $field.after(`
            <div class="invalid-feedback d-block">
                <i class="fas fa-exclamation-triangle me-1"></i>
                ${message}
            </div>
        `);
        
        // إضافة تأثير بصري
        $field.parent().addClass('shake');
        setTimeout(() => {
            $field.parent().removeClass('shake');
        }, 500);
    }

    // مسح رسالة الخطأ
    function clearFieldError($field) {
        $field.removeClass('is-invalid');
        $field.siblings('.invalid-feedback').remove();
    }

    // التحقق من صحة جميع الحقول
    function validateAllFields() {
        let isValid = true;
        const errors = [];

        // التحقق من الحقول الأساسية
        Object.keys(validationRules).forEach(fieldName => {
            const $field = $(`#id_${fieldName}`);
            const value = $field.val();
            const validation = validateField(fieldName, value);
            
            if (!validation.valid) {
                showFieldError($field, validation.message);
                errors.push({
                    field: fieldName,
                    message: validation.message
                });
                isValid = false;
            } else {
                clearFieldError($field);
            }
        });

        // التحقق من صفوف المواد الخام
        const materialErrors = validateMaterialRows();
        if (materialErrors.length > 0) {
            isValid = false;
            errors.push(...materialErrors);
        }

        return {
            valid: isValid,
            errors: errors
        };
    }

    // التحقق من صفوف المواد الخام
    function validateMaterialRows() {
        const errors = [];
        let hasValidRow = false;

        $('.material-row').each(function(index) {
            const $row = $(this);
            const material = $row.find('.material-select').val();
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            
            let rowValid = true;

            // التحقق من المادة
            if (!material) {
                showFieldError($row.find('.material-select'), 'يرجى اختيار المادة الخام');
                errors.push({
                    field: `material_row_${index}`,
                    message: 'مادة خام غير محددة'
                });
                rowValid = false;
            } else {
                clearFieldError($row.find('.material-select'));
            }

            // التحقق من الكمية
            if (quantity <= 0) {
                showFieldError($row.find('.quantity-input'), 'يرجى إدخال كمية صحيحة');
                errors.push({
                    field: `quantity_row_${index}`,
                    message: 'كمية غير صحيحة'
                });
                rowValid = false;
            } else {
                clearFieldError($row.find('.quantity-input'));
            }

            if (rowValid && material && quantity > 0) {
                hasValidRow = true;
            }
        });

        // التحقق من وجود مادة واحدة على الأقل
        if (!hasValidRow) {
            errors.push({
                field: 'materials',
                message: 'يجب إضافة مادة خام واحدة صحيحة على الأقل'
            });
        }

        return errors;
    }

    // التحقق الفوري عند تغيير الحقول
    Object.keys(validationRules).forEach(fieldName => {
        $(`#id_${fieldName}`).on('blur change', function() {
            const value = $(this).val();
            const validation = validateField(fieldName, value);
            
            if (!validation.valid) {
                showFieldError($(this), validation.message);
            } else {
                clearFieldError($(this));
                $(this).addClass('is-valid');
                setTimeout(() => {
                    $(this).removeClass('is-valid');
                }, 2000);
            }
        });
    });

    // التحقق من المواد الخام عند التغيير
    $(document).on('blur change', '.material-select, .quantity-input', function() {
        const $row = $(this).closest('.material-row');
        const material = $row.find('.material-select').val();
        const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
        
        if ($(this).hasClass('material-select')) {
            if (!material) {
                showFieldError($(this), 'يرجى اختيار المادة الخام');
            } else {
                clearFieldError($(this));
            }
        }
        
        if ($(this).hasClass('quantity-input')) {
            if (quantity <= 0) {
                showFieldError($(this), 'يرجى إدخال كمية صحيحة');
            } else {
                clearFieldError($(this));
            }
        }
    });

    // التحقق عند إرسال النموذج
    $('#digitalManufacturingForm').on('submit', function(e) {
        e.preventDefault();
        
        const validation = validateAllFields();
        
        if (validation.valid) {
            // إظهار مؤشر التحميل
            const $submitBtn = $('#submitBtn');
            $submitBtn.prop('disabled', true);
            $submitBtn.find('.btn-text').hide();
            $submitBtn.find('.loading-spinner').show();
            
            // محاكاة الإرسال (يمكن استبدالها بـ AJAX)
            setTimeout(() => {
                // إرسال النموذج فعلياً
                this.submit();
            }, 1000);
            
        } else {
            // عرض ملخص الأخطاء
            showValidationSummary(validation.errors);
            
            // التمرير إلى أول خطأ
            const $firstError = $('.is-invalid').first();
            if ($firstError.length) {
                $('html, body').animate({
                    scrollTop: $firstError.offset().top - 100
                }, 500);
                $firstError.focus();
            }
        }
    });

    // عرض ملخص الأخطاء
    function showValidationSummary(errors) {
        const errorList = errors.map(error => `<li>${error.message}</li>`).join('');
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h6 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    يرجى تصحيح الأخطاء التالية:
                </h6>
                <ul class="mb-0">${errorList}</ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إزالة التنبيهات السابقة
        $('.alert-danger').remove();
        
        // إضافة التنبيه الجديد
        $('.form-section').first().prepend(alertHtml);
    }

    // إضافة تأثيرات CSS للتحقق
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .shake {
                animation: shake 0.5s ease-in-out;
            }
            
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            
            .is-valid {
                border-color: #28a745 !important;
                box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
            }
            
            .is-invalid {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
            }
            
            .invalid-feedback {
                animation: slideDown 0.3s ease;
            }
            
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `)
        .appendTo('head');
});
