#!/usr/bin/env python3
"""
أداة تشخيص مشاكل الخادم
Server Diagnostics Tool

تحليل وحل جميع مشاكل عدم الاستقرار
Analyze and fix all stability issues
"""

import os
import sys
import subprocess
import platform
from datetime import datetime

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n--- {title} ---")

def run_command(cmd, description):
    """تشغيل أمر وطباعة النتيجة"""
    print(f"\n{description}:")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ نجح: {result.stdout.strip()}")
            return True, result.stdout.strip()
        else:
            print(f"❌ فشل: {result.stderr.strip()}")
            return False, result.stderr.strip()
    except subprocess.TimeoutExpired:
        print("❌ انتهت المهلة الزمنية")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False, str(e)

def check_python_environment():
    """فحص بيئة Python"""
    print_section("فحص بيئة Python")
    
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    
    # فحص المكتبات المطلوبة
    required_packages = ['django', 'psutil']
    
    for package in required_packages:
        try:
            __import__(package)
            if package == 'django':
                import django
                print(f"✅ {package}: {django.get_version()}")
            else:
                print(f"✅ {package}: مثبت")
        except ImportError:
            print(f"❌ {package}: غير مثبت")

def check_django_setup():
    """فحص إعدادات Django"""
    print_section("فحص إعدادات Django")
    
    # فحص ملف manage.py
    if os.path.exists('manage.py'):
        print("✅ ملف manage.py موجود")
    else:
        print("❌ ملف manage.py غير موجود")
        return False
    
    # فحص إعدادات Django
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
        import django
        django.setup()
        print("✅ إعدادات Django صحيحة")
        
        # فحص قاعدة البيانات
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ اتصال قاعدة البيانات يعمل")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في إعدادات Django: {e}")
        return False

def check_port_availability():
    """فحص توفر المنفذ"""
    print_section("فحص توفر المنفذ 8000")
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 8000))
        sock.close()
        
        if result == 0:
            print("❌ المنفذ 8000 مستخدم بالفعل")
            
            # محاولة العثور على العملية
            if platform.system() == "Windows":
                run_command("netstat -ano | findstr :8000", "البحث عن العملية المستخدمة للمنفذ")
            else:
                run_command("lsof -i :8000", "البحث عن العملية المستخدمة للمنفذ")
            
            return False
        else:
            print("✅ المنفذ 8000 متاح")
            return True
    except Exception as e:
        print(f"❌ خطأ في فحص المنفذ: {e}")
        return False

def check_file_permissions():
    """فحص صلاحيات الملفات"""
    print_section("فحص صلاحيات الملفات")
    
    important_files = [
        'manage.py',
        'db.sqlite3',
        'osaric_accounts/settings.py'
    ]
    
    all_good = True
    
    for file_path in important_files:
        if os.path.exists(file_path):
            if os.access(file_path, os.R_OK):
                print(f"✅ {file_path}: قابل للقراءة")
            else:
                print(f"❌ {file_path}: غير قابل للقراءة")
                all_good = False
                
            if file_path == 'db.sqlite3':
                if os.access(file_path, os.W_OK):
                    print(f"✅ {file_path}: قابل للكتابة")
                else:
                    print(f"❌ {file_path}: غير قابل للكتابة")
                    all_good = False
        else:
            print(f"❌ {file_path}: غير موجود")
            if file_path != 'db.sqlite3':  # قاعدة البيانات قد لا تكون موجودة في البداية
                all_good = False
    
    return all_good

def check_memory_and_resources():
    """فحص الذاكرة والموارد"""
    print_section("فحص الذاكرة والموارد")
    
    try:
        import psutil
        
        # فحص الذاكرة
        memory = psutil.virtual_memory()
        print(f"الذاكرة المتاحة: {memory.available // (1024*1024)} MB")
        print(f"استخدام الذاكرة: {memory.percent}%")
        
        if memory.percent > 90:
            print("⚠️ استخدام الذاكرة عالي جداً")
        elif memory.percent > 80:
            print("⚠️ استخدام الذاكرة عالي")
        else:
            print("✅ استخدام الذاكرة طبيعي")
        
        # فحص المعالج
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"استخدام المعالج: {cpu_percent}%")
        
        if cpu_percent > 90:
            print("⚠️ استخدام المعالج عالي جداً")
        elif cpu_percent > 80:
            print("⚠️ استخدام المعالج عالي")
        else:
            print("✅ استخدام المعالج طبيعي")
        
        # فحص مساحة القرص
        disk = psutil.disk_usage('.')
        free_gb = disk.free // (1024*1024*1024)
        print(f"مساحة القرص المتاحة: {free_gb} GB")
        
        if free_gb < 1:
            print("❌ مساحة القرص ممتلئة تقريباً")
            return False
        elif free_gb < 5:
            print("⚠️ مساحة القرص قليلة")
        else:
            print("✅ مساحة القرص كافية")
        
        return True
        
    except ImportError:
        print("❌ psutil غير مثبت - لا يمكن فحص الموارد")
        return True  # لا نعتبرها مشكلة حرجة

def test_basic_server():
    """اختبار تشغيل الخادم الأساسي"""
    print_section("اختبار تشغيل الخادم الأساسي")
    
    try:
        print("محاولة تشغيل الخادم لمدة 10 ثوانٍ...")
        
        cmd = [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000', '--noreload']
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # انتظار 5 ثوانٍ
        import time
        time.sleep(5)
        
        # فحص إذا كان الخادم ما زال يعمل
        if process.poll() is None:
            print("✅ الخادم يعمل بنجاح")
            
            # اختبار الاتصال
            try:
                import urllib.request
                response = urllib.request.urlopen('http://127.0.0.1:8000/', timeout=5)
                if response.getcode() == 200:
                    print("✅ الخادم يستجيب للطلبات")
                    success = True
                else:
                    print(f"⚠️ الخادم يرد بكود: {response.getcode()}")
                    success = False
            except Exception as e:
                print(f"❌ الخادم لا يستجيب: {e}")
                success = False
            
            # إيقاف الخادم
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return success
        else:
            # الخادم توقف
            stdout, stderr = process.communicate()
            print(f"❌ الخادم توقف فوراً")
            print(f"المخرجات: {stdout}")
            print(f"الأخطاء: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الخادم: {e}")
        return False

def provide_solutions():
    """تقديم الحلول للمشاكل الشائعة"""
    print_section("الحلول المقترحة")
    
    print("""
الحلول للمشاكل الشائعة:

1. إذا كان Django غير مثبت:
   pip install django

2. إذا كان المنفذ 8000 مستخدم:
   - أغلق التطبيقات الأخرى التي تستخدم المنفذ
   - أو استخدم منفذ آخر: python manage.py runserver 127.0.0.1:8001

3. إذا كانت قاعدة البيانات تحتاج ترحيل:
   python manage.py migrate

4. إذا كانت هناك مشاكل في الصلاحيات:
   - تأكد من تشغيل الأمر كمدير (Administrator)
   - تحقق من صلاحيات المجلد

5. إذا كانت الذاكرة ممتلئة:
   - أغلق التطبيقات غير الضرورية
   - أعد تشغيل الكمبيوتر

6. لتشغيل الخادم المستقر:
   python ultra_stable_server.py
""")

def main():
    """الدالة الرئيسية للتشخيص"""
    print_header("أداة تشخيص مشاكل الخادم")
    print(f"وقت التشخيص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    issues_found = []
    
    # فحص بيئة Python
    check_python_environment()
    
    # فحص إعدادات Django
    if not check_django_setup():
        issues_found.append("مشكلة في إعدادات Django")
    
    # فحص توفر المنفذ
    if not check_port_availability():
        issues_found.append("المنفذ 8000 غير متاح")
    
    # فحص صلاحيات الملفات
    if not check_file_permissions():
        issues_found.append("مشكلة في صلاحيات الملفات")
    
    # فحص الموارد
    if not check_memory_and_resources():
        issues_found.append("مشكلة في الموارد")
    
    # اختبار الخادم
    if not test_basic_server():
        issues_found.append("فشل في تشغيل الخادم")
    
    # النتائج النهائية
    print_header("نتائج التشخيص")
    
    if issues_found:
        print("❌ تم العثور على المشاكل التالية:")
        for issue in issues_found:
            print(f"  - {issue}")
        provide_solutions()
    else:
        print("✅ لم يتم العثور على مشاكل!")
        print("الخادم يجب أن يعمل بشكل طبيعي.")
        print("\nلتشغيل الخادم المستقر:")
        print("python ultra_stable_server.py")

if __name__ == "__main__":
    main()
