#!/usr/bin/env python3
"""
النظام الآمن الشامل - بدون أخطاء
Complete Secure System - Error Free

نظام شامل يجمع الخادم الأبدي مع HTTPS مستقر بدون أخطاء
Complete system combining eternal server with stable HTTPS without errors
"""

import os
import sys
import time
import socket
import ssl
import threading
import subprocess
import http.server
import socketserver
import urllib.request
from pathlib import Path
from datetime import datetime

class CompleteSecureSystem:
    """النظام الآمن الشامل"""
    
    def __init__(self):
        self.django_port = 8000
        self.https_port = 8443
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.ssl_cert = None
        self.ssl_key = None
        self.django_process = None
        self.https_server = None
        self.is_running = False
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_error("لا توجد شهادة SSL")
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def start_django_server(self):
        """بدء خادم Django"""
        try:
            # البحث عن منفذ متاح
            port = self.find_available_port(8000)
            if not port:
                self.log_error("لا يمكن العثور على منفذ متاح لـ Django")
                return False
            
            self.django_port = port
            self.log_info(f"بدء خادم Django على المنفذ {port}")
            
            # تشغيل Django
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'127.0.0.1:{port}',
                '--insecure', '--noreload'
            ]
            
            self.django_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # انتظار بدء Django
            time.sleep(5)
            
            if self.django_process.poll() is None:
                self.log_success(f"خادم Django يعمل على المنفذ {port}")
                return True
            else:
                self.log_error("فشل في بدء خادم Django")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في بدء Django: {e}")
            return False
    
    def check_django_health(self):
        """فحص صحة Django"""
        try:
            response = urllib.request.urlopen(f"http://127.0.0.1:{self.django_port}/", timeout=3)
            return response.getcode() == 200
        except Exception:
            return False
    
    def create_https_handler(self):
        """إنشاء معالج HTTPS محسن"""
        
        class SecureHandler(http.server.BaseHTTPRequestHandler):
            def __init__(self, django_port, *args, **kwargs):
                self.django_port = django_port
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                self.handle_request()
            
            def do_POST(self):
                self.handle_request()
            
            def do_PUT(self):
                self.handle_request()
            
            def do_DELETE(self):
                self.handle_request()
            
            def handle_request(self):
                try:
                    # إنشاء URL
                    url = f"http://127.0.0.1:{self.django_port}{self.path}"
                    
                    # إنشاء الطلب
                    req = urllib.request.Request(url, method=self.command)
                    
                    # نسخ الهيدرز المهمة
                    for header, value in self.headers.items():
                        if header.lower() not in ['host', 'connection']:
                            req.add_header(header, value)
                    
                    # إضافة البيانات للطلبات POST
                    if self.command in ['POST', 'PUT']:
                        try:
                            content_length = int(self.headers.get('Content-Length', 0))
                            if 0 < content_length < 1048576:  # حد أقصى 1MB
                                req.data = self.rfile.read(content_length)
                        except:
                            pass
                    
                    # إرسال الطلب
                    try:
                        response = urllib.request.urlopen(req, timeout=5)
                        
                        # إرسال الاستجابة
                        self.send_response(response.getcode())
                        
                        # نسخ الهيدرز
                        for header, value in response.headers.items():
                            if header.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(header, value)
                        
                        self.end_headers()
                        
                        # نسخ المحتوى
                        content = response.read()
                        if content:
                            self.wfile.write(content)
                            
                    except urllib.error.HTTPError as e:
                        self.send_response(e.code)
                        self.end_headers()
                        if hasattr(e, 'read'):
                            self.wfile.write(e.read())
                    except Exception:
                        self.send_error(502, "Bad Gateway")
                        
                except Exception:
                    self.send_error(500, "Internal Server Error")
            
            def log_message(self, format, *args):
                pass  # تجاهل اللوج
        
        def handler_factory(*args, **kwargs):
            return SecureHandler(self.django_port, *args, **kwargs)
        
        return handler_factory
    
    def start_https_server(self):
        """بدء خادم HTTPS"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # فحص Django
            if not self.check_django_health():
                self.log_error("Django لا يعمل")
                return False
            
            # البحث عن منفذ HTTPS
            port = self.find_available_port(8443)
            if not port:
                self.log_error("لا يمكن العثور على منفذ HTTPS")
                return False
            
            self.https_port = port
            
            # إنشاء الخادم
            handler = self.create_https_handler()
            self.https_server = socketserver.ThreadingTCPServer(('0.0.0.0', port), handler)
            self.https_server.allow_reuse_address = True
            
            # إعداد SSL
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain(str(self.ssl_cert), str(self.ssl_key))
            
            self.https_server.socket = context.wrap_socket(
                self.https_server.socket,
                server_side=True
            )
            
            self.log_success(f"خادم HTTPS يعمل على المنفذ {port}")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في بدء HTTPS: {e}")
            return False
    
    def display_info(self):
        """عرض معلومات النظام"""
        print("\n" + "=" * 60)
        print("🔒 النظام الآمن الشامل")
        print("=" * 60)
        print(f"🖥️  الجهاز: {socket.gethostname()}")
        print(f"🌍 IP المحلي: {self.local_ip}")
        print(f"🔌 منفذ Django: {self.django_port}")
        print(f"🔒 منفذ HTTPS: {self.https_port}")
        print("\n🔒 الروابط الآمنة:")
        print(f"   https://{self.local_ip}:{self.https_port}/dashboard/")
        print(f"   https://localhost:{self.https_port}/dashboard/")
        print("\n✅ النظام يعمل بشكل مثالي!")
        print("=" * 60)
    
    def monitor_system(self):
        """مراقبة النظام"""
        while self.is_running:
            try:
                time.sleep(10)
                
                # فحص Django
                if not self.django_process or self.django_process.poll() is not None:
                    self.log_error("Django توقف - إعادة تشغيل...")
                    self.start_django_server()
                    time.sleep(5)
                    if self.https_server:
                        self.https_server.shutdown()
                        self.start_https_server()
                
                # فحص صحة النظام
                if not self.check_django_health():
                    self.log_error("مشكلة في صحة النظام")
                
            except Exception as e:
                self.log_error(f"خطأ في المراقبة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل النظام الشامل"""
        print("=" * 60)
        print("🔒 النظام الآمن الشامل")
        print("Complete Secure System")
        print("=" * 60)
        
        # بدء Django
        if not self.start_django_server():
            return False
        
        # بدء HTTPS
        if not self.start_https_server():
            return False
        
        # عرض المعلومات
        self.display_info()
        
        self.is_running = True
        
        # بدء المراقبة
        monitor_thread = threading.Thread(target=self.monitor_system)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # تشغيل HTTPS
        try:
            self.https_server.serve_forever()
        except KeyboardInterrupt:
            self.log_info("إيقاف النظام...")
        finally:
            self.is_running = False
            if self.https_server:
                self.https_server.shutdown()
            if self.django_process:
                self.django_process.terminate()
        
        return True

def main():
    """الدالة الرئيسية"""
    system = CompleteSecureSystem()
    success = system.run()
    
    if success:
        print("✅ النظام الآمن الشامل يعمل بنجاح!")
    else:
        print("❌ فشل في تشغيل النظام!")

if __name__ == "__main__":
    main()
