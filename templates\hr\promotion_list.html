{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-arrow-up text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة ترقيات الموظفين ومراجعة الأداء</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:promotion_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        ترقية جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ total_promotions|default:0 }}</h3>
                    <p class="mb-0">إجمالي الترقيات</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ pending_reviews|default:0 }}</h3>
                    <p class="mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ approved_promotions|default:0 }}</h3>
                    <p class="mb-0">موافق عليها</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ rejected_promotions|default:0 }}</h3>
                    <p class="mb-0">مرفوضة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ eligible_count|default:0 }}</h3>
                    <p class="mb-0">مؤهلين للترقية</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ promotion_rate|default:0 }}%</h3>
                    <p class="mb-0">معدل الترقية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        سجل الترقيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>المنصب السابق</th>
                                    <th>المنصب الجديد</th>
                                    <th>تاريخ الترقية</th>
                                    <th>زيادة الراتب</th>
                                    <th>سبب الترقية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for promotion in promotions %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ promotion.employee }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ promotion.from_position }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ promotion.to_position }}</span>
                                    </td>
                                    <td>{{ promotion.date|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if promotion.salary_increase %}
                                            <span class="text-success">
                                                <strong>+{{ promotion.salary_increase }} ج.م</strong>
                                                {% if promotion.salary_increase_percentage %}
                                                    <br><small>({{ promotion.salary_increase_percentage }}%)</small>
                                                {% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ promotion.reason|default:"-" }}</small>
                                    </td>
                                    <td>
                                        {% if promotion.status == 'PENDING' %}
                                            <span class="badge bg-warning">قيد المراجعة</span>
                                        {% elif promotion.status == 'APPROVED' %}
                                            <span class="badge bg-success">موافق عليها</span>
                                        {% elif promotion.status == 'REJECTED' %}
                                            <span class="badge bg-danger">مرفوضة</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ promotion.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:promotion_detail' promotion.id %}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:promotion_edit' promotion.id %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if promotion.status == 'PENDING' %}
                                                <button class="btn btn-sm btn-outline-success" title="موافقة" onclick="approvePromotion({{ promotion.id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="رفض" onclick="rejectPromotion({{ promotion.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-info" title="طباعة قرار الترقية">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-arrow-up fa-3x mb-3 d-block"></i>
                                        لا توجد ترقيات مسجلة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Eligible for Promotion -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        الموظفين المؤهلين للترقية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>المنصب الحالي</th>
                                    <th>سنوات الخبرة</th>
                                    <th>تقييم الأداء</th>
                                    <th>آخر ترقية</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in eligible_employees %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user text-primary me-2"></i>
                                            <div>
                                                <div class="fw-bold">{{ employee.name }}</div>
                                                <small class="text-muted">{{ employee.department }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ employee.current_position }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ employee.years_experience }} سنوات</span>
                                    </td>
                                    <td>
                                        {% if employee.performance_rating == 'ممتاز' %}
                                            <span class="badge bg-success">{{ employee.performance_rating }}</span>
                                        {% elif employee.performance_rating == 'جيد جداً' %}
                                            <span class="badge bg-warning">{{ employee.performance_rating }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ employee.performance_rating }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ employee.last_promotion }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-success" title="ترقية"
                                                    onclick="promoteEmployee({{ employee.id }}, '{{ employee.name }}')">
                                                <i class="fas fa-arrow-up"></i> ترقية
                                            </button>
                                            <!-- Debug: Employee ID = {{ employee.id }} -->
                                            <a href="{% url 'hr:employee_detail' employee.id %}"
                                               class="btn btn-sm btn-outline-info" title="تفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:employee_edit' employee.id %}"
                                               class="btn btn-sm btn-outline-warning ms-1" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-star fa-3x mb-3 d-block"></i>
                                        لا يوجد موظفين مؤهلين للترقية حالياً
                                        <br>
                                        <small class="text-muted">يحتاج الموظف على الأقل سنة ونصف خبرة للترقية</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Promotion Modal -->
<div class="modal fade" id="addPromotionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ترقية موظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <option value="1">محمد علي</option>
                                    <option value="2">سارة أحمد</option>
                                    <option value="3">علي محمود</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الترقية</label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب الحالي</label>
                                <input type="text" class="form-control" readonly value="محاسب">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب الجديد</label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الراتب الحالي (ج.م)</label>
                                <input type="number" class="form-control" readonly value="3000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الراتب الجديد (ج.م)</label>
                                <input type="number" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب الترقية</label>
                        <textarea class="form-control" rows="3" required placeholder="اذكر أسباب استحقاق الموظف للترقية"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">حفظ الترقية</button>
            </div>
        </div>
    </div>
</div>

<script>
function promoteEmployee(employeeId, employeeName) {
    if (confirm(`هل أنت متأكد من ترقية الموظف "${employeeName}"؟`)) {
        // يمكن إضافة منطق الترقية هنا
        alert(`تم إرسال طلب ترقية للموظف ${employeeName}`);
        // يمكن إضافة AJAX call هنا لإرسال الطلب للخادم
    }
}

function approvePromotion(promotionId) {
    if (confirm('هل أنت متأكد من الموافقة على هذه الترقية؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/hr/promotions/${promotionId}/approve/`;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectPromotion(promotionId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/hr/promotions/${promotionId}/reject/`;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'rejection_reason';
        reasonInput.value = reason;
        
        form.appendChild(csrfInput);
        form.appendChild(reasonInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
