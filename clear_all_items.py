#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item

def clear_all_items():
    items = Item.objects.filter(is_active=True)
    count = items.count()
    
    # Mark all as inactive
    items.update(is_active=False)
    
    print(f"Cleared {count} items")
    
    # Verify
    remaining = Item.objects.filter(is_active=True).count()
    print(f"Remaining active items: {remaining}")

if __name__ == "__main__":
    clear_all_items()