#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemType

def simple_cleanup():
    print("=== Simple Cleanup ===")
    
    # First, let's see what we have
    all_items = Item.objects.all()
    print(f"Total items in database: {all_items.count()}")
    
    # Show test items
    test_items = Item.objects.filter(code__startswith='FP')
    print(f"Test items (FP*): {test_items.count()}")
    
    # Delete test items one by one
    for item in test_items:
        print(f"Deleting: {item.code}")
        try:
            item.delete()
        except Exception as e:
            print(f"Error deleting {item.code}: {e}")
            # Try to deactivate instead
            item.is_active = False
            item.save()
            print(f"Deactivated {item.code} instead")
    
    print("Cleanup completed")

def show_remaining_items():
    print("\n=== Remaining Active Items ===")
    
    active_items = Item.objects.filter(is_active=True)
    print(f"Active items: {active_items.count()}")
    
    for item in active_items:
        print(f"- {item.code}: {item.name} (Type: {item.item_type})")

def convert_to_finished_products():
    print("\n=== Converting Items to Finished Products ===")
    
    # Get non-test active items
    candidate_items = Item.objects.filter(
        is_active=True
    ).exclude(code__startswith='FP').exclude(code__startswith='TEST')
    
    print(f"Candidate items: {candidate_items.count()}")
    
    # Convert first 3 to finished products
    converted = 0
    for item in candidate_items[:3]:
        print(f"Converting {item.code} to FINISHED_PRODUCT")
        item.item_type = ItemType.FINISHED_PRODUCT
        item.save()
        converted += 1
    
    print(f"Converted {converted} items")

if __name__ == "__main__":
    simple_cleanup()
    show_remaining_items()
    convert_to_finished_products()
    
    print("\n=== Final Check ===")
    finished_products = Item.objects.filter(
        item_type=ItemType.FINISHED_PRODUCT,
        is_active=True
    )
    print(f"Finished products available: {finished_products.count()}")
    for item in finished_products:
        print(f"- {item.code}: {item.name}")