#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit, Currency, Warehouse
from django.contrib.auth.models import User

def create_basic_data():
    print("Creating basic system data...")
    
    # Get or create admin user
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    
    # Create basic currencies if not exist
    currencies_data = [
        {'code': 'EGP', 'name': 'Egyptian Pound', 'symbol': 'ج.م', 'is_default': True},
        {'code': 'USD', 'name': 'US Dollar', 'symbol': '$', 'is_default': False},
        {'code': 'EUR', 'name': 'Euro', 'symbol': '€', 'is_default': False},
    ]
    
    for curr_data in currencies_data:
        currency, created = Currency.objects.get_or_create(
            code=curr_data['code'],
            defaults={
                'name': curr_data['name'],
                'symbol': curr_data['symbol'],
                'is_default': curr_data['is_default'],
                'is_active': True,
                'created_by': admin_user
            }
        )
        if created:
            print(f"Created currency: {currency.code}")
    
    # Create basic units
    units_data = [
        {'code': 'PCS', 'name': 'Pieces', 'symbol': 'pcs'},
        {'code': 'KG', 'name': 'Kilogram', 'symbol': 'kg'},
        {'code': 'L', 'name': 'Liter', 'symbol': 'L'},
        {'code': 'M', 'name': 'Meter', 'symbol': 'm'},
        {'code': 'BOX', 'name': 'Box', 'symbol': 'box'},
    ]
    
    for unit_data in units_data:
        unit, created = Unit.objects.get_or_create(
            code=unit_data['code'],
            defaults={
                'name': unit_data['name'],
                'symbol': unit_data['symbol'],
                'is_active': True,
                'created_by': admin_user
            }
        )
        if created:
            print(f"Created unit: {unit.code}")
    
    # Create basic categories
    categories_data = [
        {'code': 'GEN', 'name': 'General Items'},
        {'code': 'RAW', 'name': 'Raw Materials'},
        {'code': 'FIN', 'name': 'Finished Products'},
    ]
    
    for cat_data in categories_data:
        category, created = ItemCategory.objects.get_or_create(
            code=cat_data['code'],
            defaults={
                'name': cat_data['name'],
                'is_active': True,
                'created_by': admin_user
            }
        )
        if created:
            print(f"Created category: {category.code}")
    
    # Create basic warehouse
    warehouse, created = Warehouse.objects.get_or_create(
        code='MAIN',
        defaults={
            'name': 'Main Warehouse',
            'location': 'Main Location',
            'is_active': True,
            'created_by': admin_user
        }
    )
    if created:
        print(f"Created warehouse: {warehouse.code}")
    
    print("Basic data creation completed!")

if __name__ == "__main__":
    create_basic_data()