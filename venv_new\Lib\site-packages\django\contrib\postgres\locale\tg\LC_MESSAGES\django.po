# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-15 00:32+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "Расширения PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Элементи %(nth)s -и массив санҷишро нагузашт:"

msgid "Nested arrays must have the same length."
msgstr "Массивҳои воридкардашуда бояд дарозиҳои якхела дошта бошанд."

msgid "Map of strings to strings/nulls"
msgstr "Массивҳои strings ба strings/nulls"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "Боркунии JSON-додаҳо муяссар нашуд."

msgid "Input must be a JSON dictionary."
msgstr "Қимматҳо бояд дар шакли JSON-луғатҳо ворид карда шаванд."

msgid "Enter two valid values."
msgstr "Дуто қимати дуруст ворид созед."

msgid "The start of the range must not exceed the end of the range."
msgstr "Оғози фосила наметавонад қимати аз ҳудуди худ калонро қабул кунад."

msgid "Enter two whole numbers."
msgstr "Дуто адади яклухтро ворид созед."

msgid "Enter two numbers."
msgstr "Ду ададро ворид созед."

msgid "Enter two valid date/times."
msgstr "Санаро бо вақт дуруст ворид кунед."

msgid "Enter two valid dates."
msgstr "Дуто санаи дурустро ворид созед."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Баъзе аз калидҳо истисно карда шудаанд: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""
"Баъзе аз калидҳои овардашуда ба руйхати калидҳои маъмул дохил нестанд: "
"%(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Мутмаин гардед, ки ҳамаи қиматҳои мутаалиқ ба ин фосила, кам ё баробаранд ба:"
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "Мутмаин шавед ки ин фосила зиёд ё баробаранд ба: %(limit_value)s."
