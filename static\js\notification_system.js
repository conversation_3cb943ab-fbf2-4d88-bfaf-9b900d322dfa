/**
 * Advanced Notification System
 * نظام الإشعارات المتقدم
 */

class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.init();
    }

    init() {
        // إنشاء حاوي الإشعارات
        this.container = $(`
            <div id="notificationContainer" class="notification-container">
            </div>
        `);
        $('body').append(this.container);

        // إضافة الأنماط
        this.addStyles();
    }

    addStyles() {
        const styles = `
            <style>
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 400px;
                }

                .notification {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                    margin-bottom: 12px;
                    padding: 16px 20px;
                    border-left: 4px solid;
                    backdrop-filter: blur(20px);
                    transform: translateX(100%);
                    opacity: 0;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                }

                .notification.show {
                    transform: translateX(0);
                    opacity: 1;
                }

                .notification.success {
                    border-left-color: #28a745;
                    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                }

                .notification.error {
                    border-left-color: #dc3545;
                    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
                }

                .notification.warning {
                    border-left-color: #ffc107;
                    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                }

                .notification.info {
                    border-left-color: #17a2b8;
                    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
                }

                .notification-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;
                }

                .notification-title {
                    font-weight: 600;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .notification-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    opacity: 0.6;
                    transition: opacity 0.2s;
                }

                .notification-close:hover {
                    opacity: 1;
                }

                .notification-message {
                    font-size: 13px;
                    line-height: 1.4;
                    color: #495057;
                }

                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background: currentColor;
                    opacity: 0.3;
                    transition: width linear;
                }

                @media (max-width: 768px) {
                    .notification-container {
                        right: 10px;
                        left: 10px;
                        max-width: none;
                    }
                }
            </style>
        `;
        $('head').append(styles);
    }

    show(message, type = 'info', title = null, duration = 5000) {
        const id = 'notification_' + Date.now();
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };

        const notification = $(`
            <div class="notification ${type}" id="${id}">
                <div class="notification-header">
                    <div class="notification-title">
                        <i class="${icons[type]}"></i>
                        ${title || titles[type]}
                    </div>
                    <button class="notification-close" onclick="notificationSystem.hide('${id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-message">${message}</div>
                ${duration > 0 ? `<div class="notification-progress"></div>` : ''}
            </div>
        `);

        this.container.append(notification);
        this.notifications.push(id);

        // إظهار الإشعار
        setTimeout(() => {
            notification.addClass('show');
        }, 100);

        // شريط التقدم
        if (duration > 0) {
            const progress = notification.find('.notification-progress');
            progress.css('width', '100%');
            setTimeout(() => {
                progress.css({
                    'width': '0%',
                    'transition': `width ${duration}ms linear`
                });
            }, 200);

            // إخفاء تلقائي
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    hide(id) {
        const notification = $(`#${id}`);
        if (notification.length) {
            notification.removeClass('show');
            setTimeout(() => {
                notification.remove();
                this.notifications = this.notifications.filter(n => n !== id);
            }, 400);
        }
    }

    hideAll() {
        this.notifications.forEach(id => this.hide(id));
    }

    success(message, title = null, duration = 4000) {
        return this.show(message, 'success', title, duration);
    }

    error(message, title = null, duration = 6000) {
        return this.show(message, 'error', title, duration);
    }

    warning(message, title = null, duration = 5000) {
        return this.show(message, 'warning', title, duration);
    }

    info(message, title = null, duration = 4000) {
        return this.show(message, 'info', title, duration);
    }

    // إشعارات خاصة بالنموذج
    formSaved() {
        return this.success('تم حفظ النموذج بنجاح', 'تم الحفظ');
    }

    formError(errors) {
        const errorList = Array.isArray(errors) ? errors.join('<br>') : errors;
        return this.error(`يرجى تصحيح الأخطاء التالية:<br>${errorList}`, 'خطأ في النموذج', 8000);
    }

    materialAdded() {
        return this.success('تم إضافة مادة خام جديدة', 'تمت الإضافة', 3000);
    }

    materialRemoved() {
        return this.warning('تم حذف المادة الخام', 'تم الحذف', 3000);
    }

    autoSaved() {
        return this.info('تم الحفظ التلقائي', 'حفظ تلقائي', 2000);
    }

    stockWarning(material, available, required) {
        return this.warning(
            `المادة "${material}" غير متوفرة بالكمية المطلوبة<br>متوفر: ${available} | مطلوب: ${required}`,
            'تحذير المخزون',
            6000
        );
    }

    calculationUpdated() {
        return this.info('تم تحديث حسابات التكلفة', 'تحديث التكلفة', 2000);
    }
}

// إنشاء نسخة عامة من النظام
let notificationSystem;

$(document).ready(function() {
    notificationSystem = new NotificationSystem();
    
    // ربط النظام مع النموذج
    window.showNotification = function(message, type, title, duration) {
        return notificationSystem.show(message, type, title, duration);
    };
    
    window.hideNotification = function(id) {
        return notificationSystem.hide(id);
    };
    
    window.hideAllNotifications = function() {
        return notificationSystem.hideAll();
    };
});
