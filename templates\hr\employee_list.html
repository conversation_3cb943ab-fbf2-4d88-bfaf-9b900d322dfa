{% extends 'base/base.html' %}
{% load static %}

{% block title %}قائمة العاملين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <h2 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        قائمة العاملين
                    </h2>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة موظف جديد
                    </a>
                    <button class="btn btn-outline-success" onclick="exportTableToCSV('employees.csv')" title="تصدير إلى CSV">
                        <i class="fas fa-file-csv"></i>
                    </button>
                    <button class="btn btn-outline-dark" onclick="window.print()" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4 g-3">
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ total_employees }}</div>
                        <div>إجمالي العاملين</div>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ active_employees }}</div>
                        <div>العاملين النشطين</div>
                    </div>
                    <i class="fas fa-user-check fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ inactive_employees }}</div>
                        <div>غير النشطين</div>
                    </div>
                    <i class="fas fa-user-times fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ terminated_employees }}</div>
                        <div>المنتهية خدمتهم</div>
                    </div>
                    <i class="fas fa-user-slash fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" class="row g-2 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">بحث بالاسم أو الرقم الوظيفي</label>
                    <input type="text" name="q" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">القسم</label>
                    <select name="department" class="form-select">
                        <option value="">كل الأقسام</option>
                        {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:'s' %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="ACTIVE" {% if status_filter == 'ACTIVE' %}selected{% endif %}>نشط</option>
                        <option value="INACTIVE" {% if status_filter == 'INACTIVE' %}selected{% endif %}>غير نشط</option>
                        <option value="TERMINATED" {% if status_filter == 'TERMINATED' %}selected{% endif %}>منتهي</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex gap-2">
                    <button class="btn btn-outline-primary w-100" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="?" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="employeesTable">
                    <thead class="table-primary">
                        <tr>
                            <th>#</th>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>الحالة</th>
                            <th>تاريخ التعيين</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in page_obj %}
                        <tr>
                            <td class="fw-bold">{{ forloop.counter }}</td>
                            <td class="fw-bold">{{ employee.employee_number }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-circle text-info me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ employee.full_name }}</div>
                                        {% if employee.person.phone %}
                                            <small class="text-muted">{{ employee.person.phone }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ employee.department.name }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ employee.position.name }}</span>
                            </td>
                            <td>
                                {% if employee.status == 'ACTIVE' %}
                                    <span class="badge bg-success">نشط</span>
                                {% elif employee.status == 'INACTIVE' %}
                                    <span class="badge bg-warning text-dark">غير نشط</span>
                                {% elif employee.status == 'TERMINATED' %}
                                    <span class="badge bg-danger">منتهي</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ employee.hire_date|date:"Y/m/d" }}</small>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:employee_edit' employee.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" title="حذف" onclick="confirmDelete({{ employee.pk }}, '{{ employee.full_name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center text-muted py-5">
                                <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                                <h5 class="text-muted">لا يوجد عاملين</h5>
                                <p class="text-muted mb-3">ابدأ بإضافة أول موظف في النظام</p>
                                <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة موظف جديد
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="صفحات الموظفين">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&q={{ search_query }}&department={{ department_filter }}&status={{ status_filter }}">
                            <i class="fas fa-chevron-right"></i> السابق
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-right"></i> السابق</span>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}&q={{ search_query }}&department={{ department_filter }}&status={{ status_filter }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&q={{ search_query }}&department={{ department_filter }}&status={{ status_filter }}">
                            التالي <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">التالي <i class="fas fa-chevron-left"></i></span>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    <div class="text-center text-muted mt-2">
        <small>عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من {{ page_obj.paginator.count }} موظف</small>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد حذف العامل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل: <strong id="deleteEmployeeName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>

<script>
// تصدير الجدول إلى CSV
function exportTableToCSV(filename) {
    var csv = [];
    var rows = document.querySelectorAll("#employeesTable tr");
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll("td, th");
        for (var j = 0; j < cols.length; j++)
            row.push('"' + cols[j].innerText.replace(/\n/g, ' ').replace(/"/g, '""') + '"');
        csv.push(row.join(","));
    }
    var csvFile = new Blob([csv.join("\n")], { type: "text/csv" });
    var downloadLink = document.createElement("a");
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
}

// حذف عامل مع تأكيد
let deleteEmployeeId = null;
function confirmDelete(id, name) {
    deleteEmployeeId = id;
    document.getElementById('deleteEmployeeName').textContent = name;
    var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDeleteBtn').onclick = function() {
    if (deleteEmployeeId) {
        // الحصول على CSRF token من الكوكيز
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        const csrftoken = getCookie('csrftoken');
        
        fetch(`/hr/employees/${deleteEmployeeId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrftoken,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
};
</script>
{% endblock %} 