import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, ItemType, Unit
from decimal import Decimal

# الحصول على أول فئة متاحة
category = ItemCategory.objects.first()
unit = Unit.objects.first()  # الحصول على أول وحدة متاحة

if category and unit:
    # إنشاء 5 منتجات نهائية
    for i in range(1, 6):
        Item.objects.get_or_create(
            code=f'FP{i:03d}',
            defaults={
                'name': f'Product {i}',
                'item_type': 'FINISHED_PRODUCT',
                'category': category,
                'selling_price': Decimal('100.00'),
                'unit': unit,
                'is_active': True
            }
        )

# عرض العدد
count = Item.objects.filter(item_type='FINISHED_PRODUCT', is_active=True).count()
print(f"Finished products count: {count}")
