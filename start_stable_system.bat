@echo off
REM ===================================================
REM سكريبت بدء النظام المستقر
REM Stable System Startup Script
REM ===================================================

echo.
echo ========================================
echo    🚀 نظام المحاسبة المستقر
echo    Stable Accounting System
echo ========================================
echo.

REM تحديد مجلد المشروع
cd /d "%~dp0"

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "static" mkdir static
if not exist "media" mkdir media
if not exist "backup" mkdir backup

echo ✅ تم إنشاء المجلدات المطلوبة

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM فحص pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

echo ✅ pip متوفر

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المتطلبات...
pip install django psutil urllib3 requests

REM فحص Django
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django غير مثبت بشكل صحيح
    echo جاري تثبيت Django...
    pip install django
)

echo ✅ Django جاهز

REM إنشاء ملف requirements.txt إذا لم يكن موجوداً
if not exist "requirements.txt" (
    echo django>=4.0 > requirements.txt
    echo psutil >> requirements.txt
    echo urllib3 >> requirements.txt
    echo requests >> requirements.txt
    echo ✅ تم إنشاء ملف requirements.txt
)

REM تشغيل الترحيلات
echo.
echo 🔄 تشغيل ترحيلات قاعدة البيانات...
python manage.py migrate --noinput
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في الترحيلات، لكن سنستمر...
)

REM جمع الملفات الثابتة
echo.
echo 📁 جمع الملفات الثابتة...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في جمع الملفات الثابتة، لكن سنستمر...
)

REM إنشاء مستخدم إداري إذا لم يكن موجوداً
echo.
echo 👤 فحص المستخدم الإداري...
python -c "
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
django.setup()
from django.contrib.auth.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ تم إنشاء مستخدم إداري: admin/admin123')
else:
    print('✅ المستخدم الإداري موجود')
"

echo.
echo 🎯 بدء تشغيل النظام المستقر...
echo.
echo ⚡ الخيارات المتاحة:
echo 1. تشغيل الخادم المستقر فقط
echo 2. تشغيل الخادم + مراقب النظام
echo 3. تشغيل مراقب النظام فقط
echo 4. إنشاء نسخة احتياطية
echo 5. استعادة نسخة احتياطية
echo 6. إعادة تعيين قاعدة البيانات
echo 7. عرض معلومات النظام
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-7): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto start_both
if "%choice%"=="3" goto start_monitor
if "%choice%"=="4" goto backup
if "%choice%"=="5" goto restore
if "%choice%"=="6" goto reset_db
if "%choice%"=="7" goto system_info
if "%choice%"=="0" goto exit
goto invalid_choice

:start_server
echo.
echo 🚀 تشغيل الخادم المستقر...
python run_stable_server.py
goto end

:start_both
echo.
echo 🚀 تشغيل الخادم المستقر + مراقب النظام...
start "مراقب النظام" python system_monitor.py
timeout /t 3 /nobreak >nul
python run_stable_server.py
goto end

:start_monitor
echo.
echo 🔍 تشغيل مراقب النظام...
python system_monitor.py
goto end

:backup
echo.
echo 💾 إنشاء نسخة احتياطية...
set backup_name=backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_name=%backup_name: =0%
mkdir "backup\%backup_name%" 2>nul
copy "db.sqlite3" "backup\%backup_name%\db.sqlite3" >nul 2>&1
xcopy "media" "backup\%backup_name%\media" /E /I /Q >nul 2>&1
xcopy "static" "backup\%backup_name%\static" /E /I /Q >nul 2>&1
echo ✅ تم إنشاء النسخة الاحتياطية: backup\%backup_name%
pause
goto start

:restore
echo.
echo 🔄 استعادة نسخة احتياطية...
echo النسخ المتاحة:
dir /b backup
echo.
set /p backup_folder="اسم مجلد النسخة الاحتياطية: "
if exist "backup\%backup_folder%\db.sqlite3" (
    copy "backup\%backup_folder%\db.sqlite3" "db.sqlite3" >nul
    echo ✅ تم استعادة قاعدة البيانات
) else (
    echo ❌ النسخة الاحتياطية غير موجودة
)
pause
goto start

:reset_db
echo.
echo ⚠️ تحذير: سيتم حذف جميع البيانات!
set /p confirm="هل أنت متأكد؟ (y/N): "
if /i "%confirm%"=="y" (
    del "db.sqlite3" 2>nul
    python manage.py migrate
    echo ✅ تم إعادة تعيين قاعدة البيانات
) else (
    echo ❌ تم إلغاء العملية
)
pause
goto start

:system_info
echo.
echo 💻 معلومات النظام:
echo ==================
python -c "
import platform
import psutil
import django
print(f'نظام التشغيل: {platform.system()} {platform.release()}')
print(f'Python: {platform.python_version()}')
print(f'Django: {django.get_version()}')
print(f'المعالج: {psutil.cpu_count()} cores')
print(f'الذاكرة: {psutil.virtual_memory().total // (1024**3)} GB')
print(f'مساحة القرص: {psutil.disk_usage('.').total // (1024**3)} GB')
"
echo.
pause
goto start

:invalid_choice
echo ❌ خيار غير صحيح
pause
goto start

:exit
echo 👋 شكراً لاستخدام النظام!
exit /b 0

:end
echo.
echo 🎉 تم إنهاء العملية
pause
