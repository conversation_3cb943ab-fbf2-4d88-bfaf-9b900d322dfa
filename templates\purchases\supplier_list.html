{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-truck text-success me-2"></i>
                    أوامر التوريد
                </h2>
                <a href="{% url 'purchases:supplier_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    إضافة أمر توريد جديد
                </a>
            </div>

            <!-- Search -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ search_query }}" placeholder="البحث في رقم الأمر أو اسم المورد أو الوصف">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <a href="{% url 'purchases:supplier_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة أوامر التوريد
                        <span class="badge bg-success ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الأمر</th>
                                        <th>التاريخ</th>
                                        <th>المورد</th>
                                        <th>الوصف</th>
                                        <th>إجمالي المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ order.order_number|default:"TO-001" }}</strong>
                                            </td>
                                            <td>{{ order.date|default:"2025-01-01"|date:"d/m/Y" }}</td>
                                            <td>
                                                <strong>{{ order.supplier_name|default:"مورد افتراضي" }}</strong>
                                                {% if order.contact_person %}
                                                    <br><small class="text-muted">{{ order.contact_person }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ order.description|default:"أمر توريد مواد خام" }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-success">{{ order.total_amount|default:"15000"|floatformat:2 }} ج.م</strong>
                                            </td>
                                            <td>
                                                {% if order.status == 'PENDING' %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% elif order.status == 'APPROVED' %}
                                                    <span class="badge bg-success">معتمد</span>
                                                {% elif order.status == 'IN_PROGRESS' %}
                                                    <span class="badge bg-info">قيد التنفيذ</span>
                                                {% elif order.status == 'COMPLETED' %}
                                                    <span class="badge bg-primary">مكتمل</span>
                                                {% elif order.status == 'CANCELLED' %}
                                                    <span class="badge bg-danger">ملغي</span>
                                                {% else %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'purchases:supplier_edit' order.pk|default:1 %}"
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="printOrder({{ order.pk|default:1 }})" title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteOrder({{ order.pk|default:1 }}, '{{ order.order_number|default:"TO-001" }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أوامر توريد</h5>
                            <p class="text-muted">ابدأ بإضافة أمر توريد جديد</p>
                            <a href="{% url 'purchases:supplier_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                إضافة أمر توريد جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printOrder(orderId) {
    alert('سيتم إضافة وظيفة الطباعة قريباً');
}

function deleteOrder(orderId, orderNumber) {
    if (confirm(`هل أنت متأكد من حذف أمر التوريد "${orderNumber}"؟`)) {
        fetch(`/purchases/suppliers/${orderId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
