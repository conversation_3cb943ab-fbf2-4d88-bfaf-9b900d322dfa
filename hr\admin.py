from django.contrib import admin
from .models import Department, Position, SalarySystem, Employee, Qualification, Shift, EmployeeSalary, Attendance, AllowanceDeduction, Overtime, LeaveType, LeaveBalance, LeaveRequest, LeaveCalendar, AbsenceRecord, Promotion, Transfer, Termination, SalaryAddition, AllowanceType, DeductionType


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'manager', 'parent_department', 'is_active']
    list_filter = ['parent_department', 'is_active']
    search_fields = ['code', 'name', 'name_english']
    ordering = ['name']
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('code', 'name', 'name_english', 'description')
        }),
        ('الهيكل التنظيمي', {
            'fields': ('parent_department', 'manager')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'department', 'is_active']
    list_filter = ['department', 'is_active']
    search_fields = ['code', 'name', 'name_english']
    ordering = ['name']
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('code', 'name', 'name_english', 'description')
        }),
        ('القسم', {
            'fields': ('department',)
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Qualification)
class QualificationAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'level', 'is_active', 'created_at']
    list_filter = ['level', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(SalarySystem)
class SalarySystemAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'system_type', 'basic_salary', 'currency', 'is_active']
    list_filter = ['system_type', 'currency', 'is_active']
    search_fields = ['code', 'name']
    ordering = ['name']
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('code', 'name', 'description', 'system_type', 'currency')
        }),
        ('الراتب الأساسي', {
            'fields': ('basic_salary',)
        }),
        ('العمل الإضافي', {
            'fields': ('include_overtime', 'overtime_rate')
        }),
        ('التأمينات والضرائب', {
            'fields': ('social_insurance_rate', 'tax_exemption')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_number', 'full_name', 'department', 'position', 'status', 'hire_date']
    list_filter = ['department', 'position', 'status', 'salary_system']
    search_fields = ['employee_number', 'person__name', 'person__name_english']
    ordering = ['employee_number']
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('person', 'employee_number')
        }),
        ('معلومات الوظيفة', {
            'fields': ('department', 'position', 'salary_system', 'current_salary')
        }),
        ('التواريخ', {
            'fields': ('hire_date', 'contract_start_date', 'contract_end_date', 'termination_date')
        }),
        ('الحالة', {
            'fields': ('status', 'is_active')
        }),
    )
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Shift)
class ShiftAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'shift_type', 'start_time', 'end_time', 'duration_hours', 'is_active']
    list_filter = ['shift_type', 'is_active', 'include_break', 'overtime_allowed']
    search_fields = ['name', 'code', 'description']
    list_editable = ['is_active']
    ordering = ['start_time']
    
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('code', 'name', 'name_english', 'description', 'shift_type', 'is_active')
        }),
        ('أوقات الوردية', {
            'fields': ('start_time', 'end_time', 'duration_hours')
        }),
        ('الإعدادات', {
            'fields': ('include_break', 'break_duration', 'overtime_allowed')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(EmployeeSalary)
class EmployeeSalaryAdmin(admin.ModelAdmin):
    list_display = ['employee', 'basic_salary', 'net_salary', 'effective_date', 'is_current']
    list_filter = ['is_current', 'effective_date', 'employee__department']
    search_fields = ['employee__person__name', 'employee__employee_number']
    list_editable = ['is_current']
    ordering = ['-effective_date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee', 'effective_date', 'is_current')
        }),
        ('الراتب الأساسي والإضافات', {
            'fields': ('basic_salary', 'housing_allowance', 'transportation_allowance', 'food_allowance', 'other_allowances')
        }),
        ('الخصومات', {
            'fields': ('social_insurance', 'tax_deduction', 'other_deductions')
        }),
        ('العمل الإضافي', {
            'fields': ('overtime_hours', 'overtime_rate', 'overtime_amount')
        }),
        ('المجاميع', {
            'fields': ('total_allowances', 'total_deductions', 'net_salary'),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )
    
    readonly_fields = ['total_allowances', 'total_deductions', 'overtime_amount', 'net_salary']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee__person', 'employee__department')


@admin.register(Overtime)
class OvertimeAdmin(admin.ModelAdmin):
    list_display = ['employee', 'date', 'hours', 'hourly_rate', 'total_amount', 'status']
    list_filter = ['status', 'date', 'employee']
    search_fields = ['employee__person__name', 'notes']
    date_hierarchy = 'date'
    ordering = ['-date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee', 'date')
        }),
        ('تفاصيل العمل الإضافي', {
            'fields': ('hours', 'hourly_rate', 'total_amount')
        }),
        ('معلومات إضافية', {
            'fields': ('status', 'notes')
        }),
    )
    
    readonly_fields = ['total_amount']


@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'category', 'allowed_days', 'is_paid', 'requires_approval', 'is_active']
    list_filter = ['category', 'is_paid', 'requires_approval', 'can_carry_forward', 'is_active']
    search_fields = ['code', 'name', 'name_english', 'description']
    list_editable = ['is_active']
    ordering = ['name']
    
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('code', 'name', 'name_english', 'description', 'category')
        }),
        ('إعدادات الإجازة', {
            'fields': ('allowed_days', 'is_paid', 'requires_approval')
        }),
        ('الترحيل', {
            'fields': ('can_carry_forward', 'max_carry_forward_days')
        }),
        ('التنسيق', {
            'fields': ('color',)
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'year', 'total_days', 'used_days', 'remaining_days', 'carried_forward_days']
    list_filter = ['leave_type', 'year', 'employee__department']
    search_fields = ['employee__person__name', 'employee__employee_number']
    ordering = ['-year', 'employee']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee', 'leave_type', 'year')
        }),
        ('الأرصدة', {
            'fields': ('total_days', 'used_days', 'remaining_days', 'carried_forward_days')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )
    
    readonly_fields = ['remaining_days']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee__person', 'leave_type')


@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'from_date', 'to_date', 'days_count', 'status', 'is_emergency']
    list_filter = ['status', 'leave_type', 'is_emergency', 'from_date', 'to_date', 'employee__department']
    search_fields = ['employee__person__name', 'employee__employee_number', 'reason']
    date_hierarchy = 'from_date'
    ordering = ['-created_at']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee', 'leave_type', 'is_emergency')
        }),
        ('تفاصيل الإجازة', {
            'fields': ('from_date', 'to_date', 'days_count', 'reason')
        }),
        ('حالة الطلب', {
            'fields': ('status', 'approved_by', 'approved_at', 'approval_notes')
        }),
        ('مرفقات', {
            'fields': ('attachment',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at', 'days_count']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'employee__person', 'leave_type', 'approved_by', 'created_by'
        )
    
    def save_model(self, request, obj, form, change):
        if not obj.created_by:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(LeaveCalendar)
class LeaveCalendarAdmin(admin.ModelAdmin):
    list_display = ['employee', 'title', 'event_date', 'event_type', 'leave_request']
    list_filter = ['event_type', 'event_date', 'employee__department']
    search_fields = ['employee__person__name', 'title', 'description']
    date_hierarchy = 'event_date'
    ordering = ['event_date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee', 'leave_request')
        }),
        ('تفاصيل الحدث', {
            'fields': ('title', 'description', 'event_date', 'event_type')
        }),
        ('التنسيق', {
            'fields': ('color',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee__person', 'leave_request')


@admin.register(Promotion)
class PromotionAdmin(admin.ModelAdmin):
    list_display = ['employee', 'from_position', 'to_position', 'from_salary', 'to_salary', 'promotion_date', 'status']
    list_filter = ['status', 'promotion_date', 'effective_date', 'employee__department']
    search_fields = ['employee__person__name', 'employee__employee_number', 'reason']
    date_hierarchy = 'promotion_date'
    ordering = ['-promotion_date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('تفاصيل الترقية', {
            'fields': ('from_position', 'to_position', 'from_salary', 'to_salary')
        }),
        ('التواريخ', {
            'fields': ('promotion_date', 'effective_date')
        }),
        ('السبب والملاحظات', {
            'fields': ('reason', 'notes')
        }),
        ('حالة الترقية', {
            'fields': ('status', 'approved_by', 'approved_at', 'approval_notes')
        }),
        ('مرفقات', {
            'fields': ('attachment',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at', 'salary_increase', 'salary_increase_percentage']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'employee__person', 'from_position', 'to_position', 'approved_by', 'created_by'
        )
    
    def save_model(self, request, obj, form, change):
        if not obj.created_by:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Transfer)
class TransferAdmin(admin.ModelAdmin):
    list_display = ['employee', 'from_department', 'to_department', 'transfer_type', 'transfer_date', 'status']
    list_filter = ['status', 'transfer_type', 'transfer_date', 'effective_date', 'employee__department']
    search_fields = ['employee__person__name', 'employee__employee_number', 'reason']
    date_hierarchy = 'transfer_date'
    ordering = ['-transfer_date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('تفاصيل النقل', {
            'fields': ('from_department', 'to_department', 'from_position', 'to_position', 'transfer_type')
        }),
        ('التواريخ', {
            'fields': ('transfer_date', 'effective_date', 'end_date', 'is_temporary', 'return_date')
        }),
        ('السبب والملاحظات', {
            'fields': ('reason', 'notes')
        }),
        ('حالة النقل', {
            'fields': ('status', 'approved_by', 'approved_at', 'approval_notes')
        }),
        ('معلومات إضافية', {
            'fields': ('replacement_employee', 'attachment')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'employee__person', 'employee__department', 'employee__position',
            'from_department', 'to_department', 'from_position', 'to_position',
            'approved_by', 'created_by', 'updated_by'
        )
    
    def save_model(self, request, obj, form, change):
        obj._current_user = request.user
        super().save_model(request, obj, form, change)


@admin.register(Termination)
class TerminationAdmin(admin.ModelAdmin):
    list_display = ['employee', 'termination_type', 'termination_date', 'status', 'created_at']
    list_filter = ['status', 'termination_type', 'created_at']
    search_fields = ['employee__first_name', 'employee__last_name', 'reason']
    readonly_fields = ['created_at', 'updated_at', 'approved_date']
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('تفاصيل إنهاء الخدمة', {
            'fields': ('termination_type', 'reason', 'notice_period', 'last_working_day', 'termination_date')
        }),
        ('الحالة والموافقة', {
            'fields': ('status', 'approved_by', 'approved_date', 'approval_notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SalaryAddition)
class SalaryAdditionAdmin(admin.ModelAdmin):
    list_display = ['employee', 'addition_type', 'amount', 'start_date', 'end_date', 'is_active', 'is_current']
    list_filter = ['addition_type', 'is_active', 'start_date', 'end_date']
    search_fields = ['employee__person__name', 'employee__employee_number', 'description']
    readonly_fields = ['created_at', 'updated_at', 'created_by']
    date_hierarchy = 'start_date'
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('تفاصيل الإضافة', {
            'fields': ('addition_type', 'amount', 'description')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date', 'is_active')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(AllowanceType)
class AllowanceTypeAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'name_english', 'is_taxable', 'is_active')
    search_fields = ('code', 'name', 'name_english')
    list_filter = ('is_taxable', 'is_active')
    ordering = ('name',)

@admin.register(DeductionType)
class DeductionTypeAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'name_english', 'is_statutory', 'is_active')
    search_fields = ('code', 'name', 'name_english')
    list_filter = ('is_statutory', 'is_active')
    ordering = ('name',)
