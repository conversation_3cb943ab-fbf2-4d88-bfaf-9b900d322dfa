{% extends 'base/base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-warehouse text-primary me-2"></i>
                        {{ warehouse.name }}
                    </h2>
                    <p class="text-muted mb-0">{{ warehouse.location }}</p>
                </div>
                <div>
                    <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </a>
                    <!-- <a href="{% url 'inventory:physical_inventory_create' %}?warehouse={{ warehouse.id }}" class="btn btn-primary">
                        <i class="fas fa-clipboard-list me-2"></i>
                        جرد المخزن
                    </a> -->
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المخزن -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <h4 class="mb-0">{{ total_items }}</h4>
                    <p class="mb-0">إجمالي الأصناف</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <h4 class="mb-0">{{ total_value|currency }}</h4>
                    <p class="mb-0">قيمة المخزون</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <h4 class="mb-0">{{ movements_summary.total_in }}</h4>
                    <p class="mb-0">إجمالي الوارد</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <h4 class="mb-0">{{ movements_summary.total_out }}</h4>
                    <p class="mb-0">إجمالي الصادر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الصف الثاني -->
    <div class="row mb-4">
        <!-- الأصناف حسب الفئات -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        الأصناف حسب الفئات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>الأصناف</th>
                                    <th>الكمية</th>
                                    <th>القيمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories_stats %}
                                <tr>
                                    <td>{{ category.item__category__name|default:"غير محدد" }}</td>
                                    <td>{{ category.items_count }}</td>
                                    <td>{{ category.total_quantity }}</td>
                                    <td>{{ category.total_value|currency }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد أصناف</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأصناف عالية القيمة -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-gem text-warning me-2"></i>
                        الأصناف عالية القيمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>متوسط التكلفة</th>
                                    <th>القيمة الإجمالية</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in high_value_items %}
                                <tr>
                                    <td>{{ stock.item.name }}</td>
                                    <td>{{ stock.quantity }}</td>
                                    <td>{{ stock.average_cost|currency }}</td>
                                    <td>{{ stock.total_value|currency }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد أصناف</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الصف الثالث -->
    <div class="row mb-4">
        <!-- الأصناف منخفضة المخزون -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        أصناف منخفضة المخزون
                    </h6>
                </div>
                <div class="card-body">
                    {% if low_stock %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in low_stock %}
                                <tr>
                                    <td>{{ stock.item.name }}</td>
                                    <td class="text-danger">{{ stock.quantity }}</td>
                                    <td>{{ stock.item.min_stock }}</td>
                                    <td>
                                        <span class="badge bg-danger">نقص</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center text-success">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>جميع الأصناف في المستوى الطبيعي</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- آخر جرد -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        آخر جرد
                    </h6>
                </div>
                <div class="card-body">
                    {% if last_inventory %}
                    <div class="row">
                        <div class="col-6">
                            <strong>رقم الجرد:</strong><br>
                            <span class="text-primary">{{ last_inventory.inventory_number }}</span>
                        </div>
                        <div class="col-6">
                            <strong>التاريخ:</strong><br>
                            {{ last_inventory.date|date:"d/m/Y" }}
                        </div>
                        <div class="col-6 mt-3">
                            <strong>النوع:</strong><br>
                            {{ last_inventory.get_inventory_type_display }}
                        </div>
                        <div class="col-6 mt-3">
                            <strong>الحالة:</strong><br>
                            <span class="badge bg-{% if last_inventory.status == 'APPROVED' %}success{% elif last_inventory.status == 'COMPLETED' %}info{% else %}warning{% endif %}">
                                {{ last_inventory.get_status_display }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <!-- <a href="{% url 'inventory:physical_inventory_detail' last_inventory.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>
                            عرض آخر جرد
                        </a> -->
                    </div>
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-clipboard fa-3x mb-3"></i>
                        <p>لم يتم إجراء جرد لهذا المخزن بعد</p>
                        <!-- <a href="{% url 'inventory:physical_inventory_create' %}?warehouse={{ warehouse.id }}" class="btn btn-primary">
                            إجراء جرد جديد
                        </a> -->
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- حركات المخزن الأخيرة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر حركات المخزن (آخر 30 يوم)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>رقم المرجع</th>
                                    <th>نوع الحركة</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in recent_movements %}
                                <tr>
                                    <td>{{ movement.reference_number }}</td>
                                    <td>
                                        <span class="badge bg-{% if movement.movement_type == 'INCREASE' %}success{% elif movement.movement_type == 'DECREASE' %}danger{% else %}info{% endif %}">
                                            {{ movement.get_movement_type_display }}
                                        </span>
                                    </td>
                                    <td>{{ movement.date|date:"d/m/Y" }}</td>
                                    <td>{{ movement.total_amount|currency }}</td>
                                    <td>{{ movement.notes|truncatechars:50 }}</td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">لا توجد حركات في الفترة المحددة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
