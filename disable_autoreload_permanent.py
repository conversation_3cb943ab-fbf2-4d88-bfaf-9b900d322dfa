#!/usr/bin/env python
"""
تعطيل Django autoreload نهائياً
"""

import os
import sys
import shutil
from pathlib import Path

def disable_autoreload_permanently():
    """تعطيل Django autoreload نهائياً"""
    
    print("🔧 بدء تعطيل Django autoreload نهائياً...")
    
    # 1. تعطيل autoreload في متغيرات البيئة
    os.environ['DJANGO_AUTORELOAD'] = 'False'
    os.environ['RUN_MAIN'] = 'true'
    
    # 2. إنشاء ملف .env لتعطيل autoreload
    env_content = """
# تعطيل Django autoreload
DJANGO_AUTORELOAD=False
RUN_MAIN=true
USE_RELOADER=False
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env لتعطيل autoreload")
    
    # 3. تعديل manage.py لتعطيل autoreload
    manage_py_path = Path('manage.py')
    if manage_py_path.exists():
        with open(manage_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة كود تعطيل autoreload
        autoreload_code = '''
# تعطيل Django autoreload
import os
os.environ['DJANGO_AUTORELOAD'] = 'False'
os.environ['RUN_MAIN'] = 'true'

try:
    from django.utils import autoreload
    autoreload.USE_INOTIFY = False
    autoreload.RUN_RELOADER = False
except ImportError:
    pass
'''
        
        if 'DJANGO_AUTORELOAD' not in content:
            # إضافة الكود بعد السطر الأول
            lines = content.split('\n')
            lines.insert(3, autoreload_code)
            new_content = '\n'.join(lines)
            
            with open(manage_py_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ تم تعديل manage.py لتعطيل autoreload")
    
    # 4. إنشاء ملف تشغيل مخصص
    run_script = '''@echo off
set DJANGO_AUTORELOAD=False
set RUN_MAIN=true
python manage.py runserver 127.0.0.1:8000 --noreload --insecure
'''
    
    with open('run_no_reload.bat', 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    print("✅ تم إنشاء ملف run_no_reload.bat")
    
    print("\n🎉 تم تعطيل Django autoreload نهائياً!")
    print("\n📋 الملفات المُنشأة:")
    print("   - .env (متغيرات البيئة)")
    print("   - run_no_reload.bat (ملف تشغيل)")
    print("   - manage.py (محدث)")
    
    print("\n🚀 لتشغيل الخادم بدون autoreload:")
    print("   python manage.py runserver --noreload")
    print("   أو")
    print("   run_no_reload.bat")

if __name__ == "__main__":
    disable_autoreload_permanently()
