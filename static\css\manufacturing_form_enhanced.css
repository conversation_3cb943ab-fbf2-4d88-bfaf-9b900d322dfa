/* ===== تنسيقات محسنة لنموذج التصنيع ===== */

/* تنسيق عام للنموذج */
.form-section {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.form-section:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.form-section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
    overflow: hidden;
}

.form-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.form-section:hover .form-section-header::before {
    left: 100%;
}

.form-section-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.form-section-body {
    padding: 25px;
    background-color: #fff;
}

/* تنسيق أرقام الأقسام */
.section-number {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-weight: bold;
    font-size: 1.1rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

/* تنسيق الأزرار */
.add-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.add-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.add-btn:hover::before {
    left: 100%;
}

/* تنسيق صف إدخال المواد */
.material-input-row {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.material-input-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #17a2b8, #ffc107, #dc3545);
    background-size: 400% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.material-input-row .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.material-input-row .form-label i {
    margin-left: 8px;
    color: #667eea;
}

/* تنسيق جدول المواد */
.materials-table-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
}

.materials-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #17a2b8, #ffc107, #dc3545);
    background-size: 400% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

#materials-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: none;
}

#materials-table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 18px 12px;
    border: none;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#materials-table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

#materials-table tbody tr {
    transition: all 0.3s ease;
    position: relative;
}

#materials-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    z-index: 1;
}

/* تنسيق البادجات */
.badge {
    font-size: 0.75rem;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.badge.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* تنسيق صفوف الخطوات والجودة */
.step-row, .quality-row {
    border: 2px solid #e3e6f0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.step-row {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.step-row:hover {
    border-color: #36b9cc;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.quality-row {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.quality-row:hover {
    border-color: #f6c23e;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.step-header, .quality-header {
    border-bottom: 2px solid rgba(0,0,0,0.1);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

/* تنسيق ملخصات الأقسام */
.steps-summary, .quality-summary, .cost-summary {
    margin-bottom: 25px;
}

.steps-summary .card, .quality-summary .card, .cost-summary .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.steps-summary .card:hover, .quality-summary .card:hover, .cost-summary .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.steps-summary .card-body, .quality-summary .card-body, .cost-summary .card-body {
    padding: 20px;
    text-align: center;
}

.steps-summary .card-title, .quality-summary .card-title, .cost-summary .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.steps-summary h4, .quality-summary h4, .cost-summary h4 {
    font-weight: 700;
    margin: 0;
    font-size: 1.5rem;
}

/* تنسيق التكاليف */
.total-cost-card {
    margin-top: 30px;
}

.total-cost-card .card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    overflow: hidden;
}

.total-cost-card .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.total-cost-card .card:hover::before {
    left: 100%;
}

.total-cost-card .card-body {
    padding: 30px;
    text-align: center;
}

.total-cost-card h4 {
    font-weight: 700;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.total-cost-card h6 {
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.9;
}

.total-cost-card h5 {
    font-weight: 700;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.total-cost-card h3 {
    font-weight: 800;
    font-size: 2rem;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* تنسيق حقول الإدخال */
.form-control, .form-select {
    border: 2px solid #d1d3e2;
    border-radius: 8px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-control[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}

/* تنسيق العملة */
.currency-input {
    position: relative;
    direction: ltr;
    text-align: left;
    font-family: 'Cairo', monospace;
    font-weight: 500;
}

.currency-input::after {
    content: " ج.م";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #28a745;
    font-weight: bold;
    pointer-events: none;
    z-index: 10;
}

.cost-input {
    background-color: #f8f9fc;
    font-weight: 600;
    border-left: 4px solid #28a745;
}

.cost-input:focus {
    background-color: white;
    border-left-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تنسيق الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* تنسيق الأزرار الإضافية */
.btn-outline-info, .btn-outline-warning, .btn-outline-danger {
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-info:hover, .btn-outline-warning:hover, .btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .form-section {
        margin-bottom: 20px;
    }
    
    .form-section-header {
        padding: 15px 20px;
    }
    
    .form-section-body {
        padding: 20px;
    }
    
    .material-input-row {
        padding: 20px;
    }
    
    .materials-table-container {
        padding: 20px;
    }
    
    #materials-table {
        font-size: 12px;
    }
    
    #materials-table thead th,
    #materials-table tbody td {
        padding: 10px 8px;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 6px 10px;
    }
    
    .total-cost-card .card-body {
        padding: 20px;
    }
    
    .total-cost-card h3 {
        font-size: 1.5rem;
    }
}

/* تنسيقات إضافية للتفاعل */
.form-control:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تنسيق مؤشر التحميل */
.loading-indicator {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تنسيق Select2 */
.select2-container--default .select2-selection--single {
    border: 2px solid #d1d3e2;
    border-radius: 8px;
    height: 42px;
    line-height: 40px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 40px;
    padding-right: 15px;
    padding-left: 15px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px;
}

.select2-dropdown {
    border: 2px solid #d1d3e2;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.select2-results__option {
    padding: 10px 15px;
}

.select2-results__option--highlighted[aria-selected] {
    background-color: #667eea;
}

/* تنسيق النص التوضيحي */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

.form-text i {
    color: #667eea;
    margin-left: 5px;
}

/* تنسيق الحقول المطلوبة */
.required-field {
    border-left: 4px solid #dc3545;
}

.required-field:focus {
    border-left-color: #667eea;
}

/* تنسيق رسائل الخطأ */
.text-danger {
    color: #dc3545 !important;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* تنسيق التقدم */
.progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #28a745, #17a2b8, #ffc107);
    background-size: 400% 100%;
    animation: progress 2s ease-in-out infinite;
    z-index: 9999;
}

@keyframes progress {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
} 