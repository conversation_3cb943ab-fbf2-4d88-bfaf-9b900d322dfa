/**
 * Help and Shortcuts System
 * نظام المساعدة والاختصارات
 */

class HelpShortcutsSystem {
    constructor() {
        this.shortcuts = {};
        this.helpVisible = false;
        this.init();
    }

    init() {
        this.setupShortcuts();
        this.addHelpButton();
        this.addShortcutListeners();
        this.addTooltips();
    }

    setupShortcuts() {
        this.shortcuts = {
            'ctrl+s': {
                action: () => this.saveForm(),
                description: 'حفظ النموذج كمسودة'
            },
            'ctrl+p': {
                action: () => exportSystem.printPreview(),
                description: 'طباعة النموذج'
            },
            'ctrl+r': {
                action: () => this.resetForm(),
                description: 'إعادة تعيين النموذج'
            },
            'ctrl+h': {
                action: () => this.toggleHelp(),
                description: 'إظهار/إخفاء المساعدة'
            },
            'ctrl+f': {
                action: () => advancedSearch.toggleSearchPanel(),
                description: 'فتح البحث المتقدم'
            },
            'ctrl+enter': {
                action: () => this.submitForm(),
                description: 'إرسال النموذج'
            },
            'ctrl+n': {
                action: () => this.addNewMaterial(),
                description: 'إضافة مادة خام جديدة'
            },
            'escape': {
                action: () => this.closeModals(),
                description: 'إغلاق النوافذ المنبثقة'
            },
            'f1': {
                action: () => this.showQuickHelp(),
                description: 'مساعدة سريعة'
            }
        };
    }

    addHelpButton() {
        const helpButton = `
            <div class="help-system">
                <button type="button" class="btn btn-info btn-floating" 
                        onclick="helpSystem.toggleHelp()" 
                        title="المساعدة والاختصارات (Ctrl+H)"
                        style="position: fixed; bottom: 20px; left: 20px; z-index: 1000; border-radius: 50%; width: 60px; height: 60px;">
                    <i class="fas fa-question-circle fa-lg"></i>
                </button>
                
                <div id="helpPanel" class="help-panel" style="display: none;">
                    <div class="card shadow-lg">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                المساعدة والاختصارات
                                <button type="button" class="btn-close btn-close-white float-end" 
                                        onclick="helpSystem.toggleHelp()"></button>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">اختصارات لوحة المفاتيح</h6>
                                    <div id="shortcutsList"></div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">نصائح سريعة</h6>
                                    <div id="quickTips"></div>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <button type="button" class="btn btn-outline-primary btn-sm me-2" 
                                        onclick="helpSystem.showTour()">
                                    <i class="fas fa-route me-1"></i>جولة إرشادية
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" 
                                        onclick="helpSystem.showVideoHelp()">
                                    <i class="fas fa-play me-1"></i>فيديو تعليمي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(helpButton);
        this.populateHelpContent();
    }

    populateHelpContent() {
        // ملء قائمة الاختصارات
        const shortcutsList = Object.entries(this.shortcuts).map(([key, shortcut]) => {
            const keyDisplay = key.replace('ctrl', 'Ctrl').replace('enter', 'Enter').toUpperCase();
            return `
                <div class="shortcut-item mb-2">
                    <kbd class="me-2">${keyDisplay}</kbd>
                    <small>${shortcut.description}</small>
                </div>
            `;
        }).join('');

        $('#shortcutsList').html(shortcutsList);

        // ملء النصائح السريعة
        const quickTips = `
            <div class="tip-item mb-2">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <small>استخدم Tab للتنقل بين الحقول</small>
            </div>
            <div class="tip-item mb-2">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <small>يتم الحفظ التلقائي كل 30 ثانية</small>
            </div>
            <div class="tip-item mb-2">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <small>انقر مرتين على المادة لإضافتها للمفضلة</small>
            </div>
            <div class="tip-item mb-2">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <small>استخدم البحث المتقدم للعثور على المواد بسرعة</small>
            </div>
            <div class="tip-item mb-2">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <small>يمكن طباعة النموذج أو تصديره بصيغ مختلفة</small>
            </div>
        `;

        $('#quickTips').html(quickTips);
    }

    addShortcutListeners() {
        $(document).on('keydown', (e) => {
            const key = this.getKeyCombo(e);
            
            if (this.shortcuts[key]) {
                e.preventDefault();
                this.shortcuts[key].action();
                
                // إظهار إشعار الاختصار
                this.showShortcutNotification(key);
            }
        });
    }

    getKeyCombo(e) {
        const keys = [];
        
        if (e.ctrlKey) keys.push('ctrl');
        if (e.altKey) keys.push('alt');
        if (e.shiftKey) keys.push('shift');
        
        const keyName = e.key.toLowerCase();
        if (keyName !== 'control' && keyName !== 'alt' && keyName !== 'shift') {
            keys.push(keyName === ' ' ? 'space' : keyName);
        }
        
        return keys.join('+');
    }

    showShortcutNotification(key) {
        const shortcut = this.shortcuts[key];
        if (shortcut) {
            notificationSystem.info(
                `تم تنفيذ: ${shortcut.description}`,
                'اختصار لوحة المفاتيح',
                2000
            );
        }
    }

    addTooltips() {
        // إضافة tooltips للعناصر المهمة
        const tooltips = [
            { selector: '#id_finished_product', text: 'اختر المنتج الذي تريد إنتاجه' },
            { selector: '#id_quantity_to_produce', text: 'أدخل الكمية المطلوب إنتاجها' },
            { selector: '.material-select', text: 'اختر المادة الخام المطلوبة' },
            { selector: '.quantity-input', text: 'أدخل الكمية المطلوبة من هذه المادة' },
            { selector: '#totalProductionCost', text: 'إجمالي تكلفة إنتاج الكمية المحددة' }
        ];

        tooltips.forEach(tooltip => {
            $(tooltip.selector).attr('title', tooltip.text);
        });

        // تفعيل Bootstrap tooltips
        $('[title]').tooltip();
    }

    toggleHelp() {
        const $panel = $('#helpPanel');
        
        if (this.helpVisible) {
            $panel.fadeOut(300);
            this.helpVisible = false;
        } else {
            $panel.css({
                position: 'fixed',
                bottom: '90px',
                left: '20px',
                zIndex: 1001,
                maxWidth: '600px',
                width: '90vw'
            }).fadeIn(300);
            this.helpVisible = true;
        }
    }

    showQuickHelp() {
        const quickHelpModal = `
            <div class="modal fade" id="quickHelpModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle me-2"></i>
                                مساعدة سريعة - نموذج أمر الإنتاج
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">خطوات إنشاء أمر الإنتاج:</h6>
                                    <ol class="list-group list-group-numbered">
                                        <li class="list-group-item">اختر المنتج النهائي المراد إنتاجه</li>
                                        <li class="list-group-item">حدد الكمية المطلوبة</li>
                                        <li class="list-group-item">اختر مخازن المواد الخام والمنتج النهائي</li>
                                        <li class="list-group-item">أضف المواد الخام المطلوبة</li>
                                        <li class="list-group-item">أدخل التكاليف الإضافية</li>
                                        <li class="list-group-item">راجع التكاليف في الحاسبة</li>
                                        <li class="list-group-item">أضف الملاحظات والتواريخ</li>
                                        <li class="list-group-item">اضغط "إنشاء أمر الإنتاج"</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">ميزات متقدمة:</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <i class="fas fa-search text-info me-2"></i>
                                            البحث المتقدم في المواد
                                        </li>
                                        <li class="list-group-item">
                                            <i class="fas fa-save text-warning me-2"></i>
                                            الحفظ التلقائي للمسودات
                                        </li>
                                        <li class="list-group-item">
                                            <i class="fas fa-calculator text-primary me-2"></i>
                                            حاسبة التكلفة التلقائية
                                        </li>
                                        <li class="list-group-item">
                                            <i class="fas fa-print text-secondary me-2"></i>
                                            طباعة وتصدير متقدم
                                        </li>
                                        <li class="list-group-item">
                                            <i class="fas fa-keyboard text-dark me-2"></i>
                                            اختصارات لوحة المفاتيح
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="helpSystem.showTour()">
                                بدء الجولة الإرشادية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة إذا كانت موجودة
        $('#quickHelpModal').remove();
        
        // إضافة النافذة الجديدة
        $('body').append(quickHelpModal);
        
        // إظهار النافذة
        $('#quickHelpModal').modal('show');
    }

    showTour() {
        // إغلاق نافذة المساعدة السريعة
        $('#quickHelpModal').modal('hide');
        
        // بدء الجولة الإرشادية
        this.startGuidedTour();
    }

    startGuidedTour() {
        const tourSteps = [
            {
                element: '.digital-header',
                title: 'مرحباً بك في نموذج أمر الإنتاج',
                content: 'هذا هو النموذج المتطور لإنشاء أوامر الإنتاج مع حاسبة التكلفة الذكية'
            },
            {
                element: '#id_finished_product',
                title: 'اختيار المنتج النهائي',
                content: 'ابدأ باختيار المنتج الذي تريد إنتاجه من هذه القائمة'
            },
            {
                element: '#id_quantity_to_produce',
                title: 'تحديد الكمية',
                content: 'أدخل الكمية المطلوب إنتاجها. ستؤثر على حسابات التكلفة تلقائياً'
            },
            {
                element: '.materials-table',
                title: 'إدارة المواد الخام',
                content: 'أضف المواد الخام المطلوبة للإنتاج. يمكنك إضافة عدة مواد'
            },
            {
                element: '.cost-calculator',
                title: 'حاسبة التكلفة الذكية',
                content: 'تحسب التكاليف تلقائياً وتعرض التفاصيل في الوقت الفعلي'
            },
            {
                element: '#submitBtn',
                title: 'إنهاء الأمر',
                content: 'بعد ملء جميع البيانات، اضغط هنا لإنشاء أمر الإنتاج'
            }
        ];

        this.runTour(tourSteps);
    }

    runTour(steps) {
        let currentStep = 0;
        
        const showStep = (stepIndex) => {
            if (stepIndex >= steps.length) {
                this.endTour();
                return;
            }

            const step = steps[stepIndex];
            const $element = $(step.element);
            
            if ($element.length === 0) {
                showStep(stepIndex + 1);
                return;
            }

            // إنشاء نافذة الجولة
            const tourModal = `
                <div class="tour-overlay" id="tourOverlay">
                    <div class="tour-spotlight" id="tourSpotlight"></div>
                    <div class="tour-popup" id="tourPopup">
                        <div class="tour-header">
                            <h6>${step.title}</h6>
                            <span class="tour-counter">${stepIndex + 1} من ${steps.length}</span>
                        </div>
                        <div class="tour-content">
                            ${step.content}
                        </div>
                        <div class="tour-footer">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="helpSystem.endTour()">
                                تخطي الجولة
                            </button>
                            <div class="ms-auto">
                                ${stepIndex > 0 ? '<button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="helpSystem.previousStep()">السابق</button>' : ''}
                                <button type="button" class="btn btn-primary btn-sm" onclick="helpSystem.nextStep()">
                                    ${stepIndex === steps.length - 1 ? 'إنهاء' : 'التالي'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة الجولة السابقة
            $('#tourOverlay').remove();
            
            // إضافة الجولة الجديدة
            $('body').append(tourModal);
            
            // تحديد موقع العنصر
            this.highlightElement($element);
            
            // حفظ الخطوة الحالية
            this.currentTourStep = stepIndex;
        };

        // بدء الجولة
        this.tourSteps = steps;
        showStep(0);
        
        // إضافة أنماط الجولة
        this.addTourStyles();
    }

    highlightElement($element) {
        const offset = $element.offset();
        const width = $element.outerWidth();
        const height = $element.outerHeight();
        
        $('#tourSpotlight').css({
            top: offset.top - 10,
            left: offset.left - 10,
            width: width + 20,
            height: height + 20
        });
        
        // تحديد موقع النافذة المنبثقة
        const popupTop = offset.top + height + 20;
        const popupLeft = Math.max(20, offset.left - 150);
        
        $('#tourPopup').css({
            top: popupTop,
            left: popupLeft
        });
        
        // التمرير إلى العنصر
        $('html, body').animate({
            scrollTop: offset.top - 100
        }, 500);
    }

    addTourStyles() {
        const tourStyles = `
            <style id="tourStyles">
                .tour-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    z-index: 10000;
                }
                
                .tour-spotlight {
                    position: absolute;
                    background: transparent;
                    border: 3px solid #007bff;
                    border-radius: 8px;
                    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
                    transition: all 0.3s ease;
                }
                
                .tour-popup {
                    position: absolute;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    max-width: 350px;
                    z-index: 10001;
                }
                
                .tour-header {
                    padding: 1rem;
                    border-bottom: 1px solid #eee;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .tour-header h6 {
                    margin: 0;
                    color: #007bff;
                    font-weight: 600;
                }
                
                .tour-counter {
                    font-size: 0.8rem;
                    color: #6c757d;
                    background: #f8f9fa;
                    padding: 0.2rem 0.5rem;
                    border-radius: 12px;
                }
                
                .tour-content {
                    padding: 1rem;
                    color: #495057;
                    line-height: 1.5;
                }
                
                .tour-footer {
                    padding: 1rem;
                    border-top: 1px solid #eee;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            </style>
        `;
        
        $('#tourStyles').remove();
        $('head').append(tourStyles);
    }

    nextStep() {
        if (this.currentTourStep < this.tourSteps.length - 1) {
            this.runTour(this.tourSteps.slice(this.currentTourStep + 1));
        } else {
            this.endTour();
        }
    }

    previousStep() {
        if (this.currentTourStep > 0) {
            this.currentTourStep -= 2;
            this.nextStep();
        }
    }

    endTour() {
        $('#tourOverlay').fadeOut(300, function() {
            $(this).remove();
        });
        $('#tourStyles').remove();
        
        notificationSystem.success('تم إنهاء الجولة الإرشادية', 'جولة مكتملة');
    }

    showVideoHelp() {
        notificationSystem.info('سيتم فتح الفيديو التعليمي قريباً', 'فيديو تعليمي');
    }

    // وظائف الاختصارات
    saveForm() {
        if (typeof saveAsDraft === 'function') {
            saveAsDraft();
        }
    }

    resetForm() {
        if (typeof resetForm === 'function') {
            resetForm();
        }
    }

    submitForm() {
        $('#digitalManufacturingForm').submit();
    }

    addNewMaterial() {
        if (typeof addMaterialRow === 'function') {
            addMaterialRow();
        }
    }

    closeModals() {
        $('.modal').modal('hide');
        $('#helpPanel').fadeOut();
        $('#advancedSearchPanel').slideUp();
        this.helpVisible = false;
    }
}

// إنشاء نسخة عامة من النظام
let helpSystem;

$(document).ready(function() {
    helpSystem = new HelpShortcutsSystem();
});
