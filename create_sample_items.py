#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.contrib.auth.models import User
from decimal import Decimal

def create_sample_items():
    # Get required objects
    admin_user = User.objects.get(username='admin')
    
    # Get categories and units
    general_category = ItemCategory.objects.get(code='GEN')
    raw_category = ItemCategory.objects.get(code='RAW')
    finished_category = ItemCategory.objects.get(code='FIN')
    
    pcs_unit = Unit.objects.get(code='PCS')
    kg_unit = Unit.objects.get(code='KG')
    box_unit = Unit.objects.get(code='BOX')
    
    # Sample items data
    items_data = [
        {
            'code': 'ITEM001',
            'name': 'قلم حبر جاف أزرق',
            'category': general_category,
            'unit': pcs_unit,
            'cost_price': Decimal('2.50'),
            'selling_price': Decimal('3.00'),
            'is_active': True
        },
        {
            'code': 'ITEM002',
            'name': 'دفتر 100 ورقة',
            'category': general_category,
            'unit': pcs_unit,
            'cost_price': Decimal('15.00'),
            'selling_price': Decimal('20.00'),
            'is_active': True
        },
        {
            'code': 'RAW001',
            'name': 'دقيق أبيض',
            'category': raw_category,
            'unit': kg_unit,
            'cost_price': Decimal('8.50'),
            'selling_price': Decimal('10.00'),
            'is_active': True
        },
        {
            'code': 'RAW002',
            'name': 'سكر أبيض',
            'category': raw_category,
            'unit': kg_unit,
            'cost_price': Decimal('12.00'),
            'selling_price': Decimal('15.00'),
            'is_active': True
        },
        {
            'code': 'FIN001',
            'name': 'خبز أبيض',
            'category': finished_category,
            'unit': pcs_unit,
            'cost_price': Decimal('1.50'),
            'selling_price': Decimal('2.00'),
            'is_active': True
        },
        {
            'code': 'FIN002',
            'name': 'كعك محلى',
            'category': finished_category,
            'unit': box_unit,
            'cost_price': Decimal('25.00'),
            'selling_price': Decimal('35.00'),
            'is_active': True
        },
        {
            'code': 'OLD001',
            'name': 'صنف قديم محذوف',
            'category': general_category,
            'unit': pcs_unit,
            'cost_price': Decimal('5.00'),
            'selling_price': Decimal('7.00'),
            'is_active': False  # This item is deleted
        },
        {
            'code': 'OLD002',
            'name': 'مادة خام قديمة',
            'category': raw_category,
            'unit': kg_unit,
            'cost_price': Decimal('20.00'),
            'selling_price': Decimal('25.00'),
            'is_active': False  # This item is deleted
        }
    ]
    
    created_count = 0
    for item_data in items_data:
        item, created = Item.objects.get_or_create(
            code=item_data['code'],
            defaults={
                'name': item_data['name'],
                'category': item_data['category'],
                'unit': item_data['unit'],
                'cost_price': item_data['cost_price'],
                'selling_price': item_data['selling_price'],
                'is_active': item_data['is_active'],
                'created_by': admin_user
            }
        )
        if created:
            print(f"Created item: {item.code} - {item.name} ({'Active' if item.is_active else 'Deleted'})")
            created_count += 1
        else:
            print(f"Item already exists: {item.code}")
    
    print(f"\nTotal items created: {created_count}")
    
    # Show summary
    active_items = Item.objects.filter(is_active=True).count()
    inactive_items = Item.objects.filter(is_active=False).count()
    total_items = Item.objects.count()
    
    print(f"Active items: {active_items}")
    print(f"Deleted items: {inactive_items}")
    print(f"Total items: {total_items}")

if __name__ == "__main__":
    create_sample_items()