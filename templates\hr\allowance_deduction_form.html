{% extends 'base/base.html' %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header {% if 'خصم' in title %}bg-danger text-white{% else %}bg-success text-white{% endif %}">
                    <h4 class="mb-0">
                        {% if 'خصم' in title %}<i class="fas fa-minus me-2"></i>{% else %}<i class="fas fa-plus me-2"></i>{% endif %}
                        {{ title }}
                    </h4>
                </div>
                <form method="post" novalidate>
                    {% csrf_token %}
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                        {% endif %}
                        <div class="mb-3">
                            {{ form.employee.label_tag }}
                            {{ form.employee }}
                            {% if form.employee.errors %}<div class="text-danger small">{{ form.employee.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {% if 'خصم' in title %}
                                {{ form.deduction_type.label_tag }}
                                {{ form.deduction_type }}
                                {% if form.deduction_type.errors %}<div class="text-danger small">{{ form.deduction_type.errors }}</div>{% endif %}
                            {% else %}
                                {{ form.allowance_type.label_tag }}
                                {{ form.allowance_type }}
                                {% if form.allowance_type.errors %}<div class="text-danger small">{{ form.allowance_type.errors }}</div>{% endif %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.amount.label_tag }}
                            {{ form.amount }}
                            {% if form.amount.errors %}<div class="text-danger small">{{ form.amount.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.date.label_tag }}
                            {{ form.date }}
                            {% if form.date.errors %}<div class="text-danger small">{{ form.date.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.is_active }} {{ form.is_active.label_tag }}
                            {% if form.is_active.errors %}<div class="text-danger small">{{ form.is_active.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.notes.label_tag }}
                            {{ form.notes }}
                            {% if form.notes.errors %}<div class="text-danger small">{{ form.notes.errors }}</div>{% endif %}
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <a href="{% url 'hr:allowance_deduction_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
                        <button type="submit" class="btn {% if 'خصم' in title %}btn-danger{% else %}btn-success{% endif %}">
                            <i class="fas fa-save me-2"></i>{% if 'خصم' in title %}حفظ الخصم{% else %}حفظ الإضافة{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 