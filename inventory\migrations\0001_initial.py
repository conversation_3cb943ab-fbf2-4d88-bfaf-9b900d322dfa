# Generated by Django 5.2.2 on 2025-06-29 15:19

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsIssuedOnLoan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('loan_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السلفة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('borrower_name', models.CharField(max_length=200, verbose_name='اسم المُستعير')),
                ('borrower_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف المُستعير')),
                ('borrower_address', models.TextField(blank=True, verbose_name='عنوان المُستعير')),
                ('loan_reason', models.CharField(max_length=200, verbose_name='سبب السلفة')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ISSUED', 'منصرفة'), ('PARTIAL_RETURNED', 'مرتجعة جزئياً'), ('RETURNED', 'مرتجعة بالكامل'), ('OVERDUE', 'متأخرة'), ('CANCELLED', 'ملغاة')], default='ISSUED', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_loans_issued', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'بضاعة منصرفة سلفة لدى الغير',
                'verbose_name_plural': 'بضائع منصرفة سلفة لدى الغير',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='GoodsIssuedOnLoanItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_issued', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المنصرفة')),
                ('quantity_returned', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('estimated_unit_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة التقديرية للوحدة')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('condition_issued', models.CharField(blank=True, max_length=100, verbose_name='حالة البضاعة عند الصرف')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.goodsissuedonloan', verbose_name='السلفة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف بضاعة سلفة منصرفة',
                'verbose_name_plural': 'أصناف بضائع السلف المنصرفة',
            },
        ),
        migrations.CreateModel(
            name='GoodsReceivedOnLoan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('loan_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السلفة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('lender_name', models.CharField(max_length=200, verbose_name='اسم المُقرض')),
                ('lender_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف المُقرض')),
                ('lender_address', models.TextField(blank=True, verbose_name='عنوان المُقرض')),
                ('loan_reason', models.CharField(max_length=200, verbose_name='سبب السلفة')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('RECEIVED', 'مستلمة'), ('PARTIAL_RETURNED', 'مرتجعة جزئياً'), ('RETURNED', 'مرتجعة بالكامل'), ('OVERDUE', 'متأخرة'), ('CANCELLED', 'ملغاة')], default='RECEIVED', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_loans_received', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'بضاعة مضافة سلفة من الغير',
                'verbose_name_plural': 'بضائع مضافة سلفة من الغير',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='GoodsReceivedOnLoanItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_received', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المستلمة')),
                ('quantity_returned', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('estimated_unit_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة التقديرية للوحدة')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('condition_received', models.CharField(blank=True, max_length=100, verbose_name='حالة البضاعة عند الاستلام')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.goodsreceivedonloan', verbose_name='السلفة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف بضاعة سلفة',
                'verbose_name_plural': 'أصناف بضائع السلف',
            },
        ),
        migrations.CreateModel(
            name='ItemTransformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transformation_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transformation_reason', models.CharField(max_length=200, verbose_name='سبب التحويل')),
                ('transformation_type', models.CharField(choices=[('ASSEMBLY', 'تجميع'), ('DISASSEMBLY', 'تفكيك'), ('CONVERSION', 'تحويل'), ('REPACKAGING', 'إعادة تعبئة'), ('QUALITY_CHANGE', 'تغيير جودة')], max_length=20, verbose_name='نوع التحويل')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_input_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي قيمة المدخلات')),
                ('total_output_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي قيمة المخرجات')),
                ('transformation_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة التحويل')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transformations', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تحويل من صنف إلى صنف',
                'verbose_name_plural': 'تحويلات من صنف إلى صنف',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ItemTransformationInput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المستهلكة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف المستهلك')),
                ('transformation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inputs', to='inventory.itemtransformation', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مدخل تحويل صنف',
                'verbose_name_plural': 'مدخلات تحويل الأصناف',
            },
        ),
        migrations.CreateModel(
            name='ItemTransformationOutput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المنتجة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف المنتج')),
                ('transformation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outputs', to='inventory.itemtransformation', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مخرج تحويل صنف',
                'verbose_name_plural': 'مخرجات تحويل الأصناف',
            },
        ),
        migrations.CreateModel(
            name='ManufacturingOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر التصنيع')),
                ('order_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الأمر')),
                ('quantity_to_produce', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('expected_start_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ البدء المتوقع')),
                ('expected_completion_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الانتهاء المتوقع')),
                ('actual_start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ البدء الفعلي')),
                ('actual_completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء الفعلي')),
                ('production_line', models.CharField(blank=True, max_length=100, verbose_name='خط الإنتاج')),
                ('shift', models.CharField(blank=True, max_length=50, verbose_name='الوردية')),
                ('priority', models.CharField(choices=[('LOW', 'منخفض'), ('MEDIUM', 'متوسط'), ('HIGH', 'عالي'), ('URGENT', 'عاجل')], default='MEDIUM', max_length=20, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('PLANNED', 'مخطط'), ('IN_PROGRESS', 'قيد التنفيذ'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف العامة')),
                ('packaging_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة التغليف')),
                ('quality_control_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة مراقبة الجودة')),
                ('operating_expenses', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مصروفات التشغيل')),
                ('electricity_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الكهرباء')),
                ('transportation_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة النقل')),
                ('other_costs', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكاليف أخرى')),
                ('total_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة المواد')),
                ('total_operating_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكاليف التشغيل')),
                ('total_production_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي تكلفة الإنتاج')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('quality_standard', models.CharField(blank=True, max_length=100, verbose_name='معيار الجودة')),
                ('quality_notes', models.TextField(blank=True, verbose_name='ملاحظات الجودة')),
                ('internal_notes', models.TextField(blank=True, verbose_name='ملاحظات داخلية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات عامة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('finished_goods_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='finished_goods_orders', to='definitions.warehouse', verbose_name='مخزن المنتجات النهائية')),
                ('finished_product', models.ForeignKey(limit_choices_to={'item_type__in': ['FINISHED_PRODUCT', 'SEMI_FINISHED']}, on_delete=django.db.models.deletion.PROTECT, related_name='manufacturing_orders', to='definitions.item', verbose_name='المنتج النهائي')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_orders', to=settings.AUTH_USER_MODEL, verbose_name='المشرف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'أمر تصنيع',
                'verbose_name_plural': 'أوامر التصنيع',
                'ordering': ['-order_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('adjustment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسوية')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('reason', models.CharField(max_length=200, verbose_name='السبب')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('book_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الدفترية')),
                ('actual_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الفعلية')),
                ('difference', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الفرق')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockadjustment', verbose_name='التسوية')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف تسوية مخزون',
                'verbose_name_plural': 'أصناف تسويات المخزون',
            },
        ),
        migrations.CreateModel(
            name='StockDecrease',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('decrease_number', models.CharField(max_length=50, unique=True, verbose_name='رقم إذن النقص')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب النقص')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('APPLIED', 'مطبق'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_decreases', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'إذن صرف نقص',
                'verbose_name_plural': 'أذون صرف النواقص',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockDecreaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المنقصة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('decrease', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockdecrease', verbose_name='إذن النقص')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف إذن نقص',
                'verbose_name_plural': 'أصناف أذون النواقص',
            },
        ),
        migrations.CreateModel(
            name='StockIncrease',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('increase_number', models.CharField(max_length=50, unique=True, verbose_name='رقم إذن الزيادة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب الزيادة')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('APPLIED', 'مطبق'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('applied_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التطبيق')),
                ('applied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applied_increases', to=settings.AUTH_USER_MODEL, verbose_name='مطبق من')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_increases', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'إذن إضافة زيادة',
                'verbose_name_plural': 'أذون إضافة الزيادات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockIncreaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المضافة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('increase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockincrease', verbose_name='إذن الزيادة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف إذن زيادة',
                'verbose_name_plural': 'أصناف أذون الزيادات',
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('movement_type', models.CharField(choices=[('IN', 'إدخال'), ('OUT', 'إخراج'), ('TRANSFER', 'تحويل'), ('ADJUSTMENT', 'تسوية'), ('PRODUCTION', 'إنتاج'), ('OPENING', 'جرد افتتاحي')], max_length=20, verbose_name='نوع الحركة')),
                ('reference_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المرجع')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockMovementItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('movement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockmovement', verbose_name='الحركة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف حركة مخزون',
                'verbose_name_plural': 'أصناف حركات المخزون',
            },
        ),
        migrations.CreateModel(
            name='StockTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_out', to='definitions.warehouse', verbose_name='من مخزن')),
                ('to_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_in', to='definitions.warehouse', verbose_name='إلى مخزن')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل مخزون',
                'verbose_name_plural': 'تحويلات المخزون',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='StockTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stocktransfer', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف تحويل مخزون',
                'verbose_name_plural': 'أصناف تحويلات المخزون',
            },
        ),
        migrations.CreateModel(
            name='WarehouseTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transfer_reason', models.CharField(max_length=200, verbose_name='سبب التحويل')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('IN_TRANSIT', 'في الطريق'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('total_estimated_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة التقديرية')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('shipped_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')),
                ('received_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستلام')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouse_transfers_out', to='definitions.warehouse', verbose_name='من مخزن')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_transfers', to=settings.AUTH_USER_MODEL, verbose_name='استلم بواسطة')),
                ('shipped_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shipped_transfers', to=settings.AUTH_USER_MODEL, verbose_name='شحن بواسطة')),
                ('to_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouse_transfers_in', to='definitions.warehouse', verbose_name='إلى مخزن')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل بين المخازن',
                'verbose_name_plural': 'تحويلات بين المخازن',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity_requested', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('quantity_shipped', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المشحونة')),
                ('quantity_received', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستلمة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.warehousetransfer', verbose_name='التحويل')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف تحويل مخزن',
                'verbose_name_plural': 'أصناف تحويلات المخازن',
            },
        ),
        migrations.CreateModel(
            name='ManufacturingMaterial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('available_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المتوفرة')),
                ('quantity_required', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('quantity_consumed', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستهلكة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('material', models.ForeignKey(limit_choices_to={'item_type': 'RAW_MATERIAL'}, on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='المادة الخام')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='inventory.manufacturingorder', verbose_name='أمر التصنيع')),
            ],
            options={
                'verbose_name': 'مادة خام للتصنيع',
                'verbose_name_plural': 'المواد الخام للتصنيع',
                'unique_together': {('order', 'material')},
            },
        ),
        migrations.CreateModel(
            name='ManufacturingStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('step_number', models.IntegerField(verbose_name='رقم الخطوة')),
                ('step_name', models.CharField(max_length=200, verbose_name='اسم الخطوة')),
                ('description', models.TextField(blank=True, verbose_name='وصف الخطوة')),
                ('estimated_duration', models.DurationField(blank=True, null=True, verbose_name='المدة المتوقعة')),
                ('actual_duration', models.DurationField(blank=True, null=True, verbose_name='المدة الفعلية')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('IN_PROGRESS', 'قيد التنفيذ'), ('COMPLETED', 'مكتمل'), ('SKIPPED', 'تم تخطيه')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت البدء')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإكمال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='مكلف إلى')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='inventory.manufacturingorder', verbose_name='أمر التصنيع')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'خطوة تصنيع',
                'verbose_name_plural': 'خطوات التصنيع',
                'ordering': ['step_number'],
                'unique_together': {('order', 'step_number')},
            },
        ),
        migrations.CreateModel(
            name='QualityControl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('check_number', models.IntegerField(verbose_name='رقم الفحص')),
                ('check_name', models.CharField(max_length=200, verbose_name='اسم الفحص')),
                ('standard_value', models.CharField(blank=True, max_length=100, verbose_name='القيمة المعيارية')),
                ('actual_value', models.CharField(blank=True, max_length=100, verbose_name='القيمة الفعلية')),
                ('tolerance', models.CharField(blank=True, max_length=100, verbose_name='الحد المسموح')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('PASSED', 'مقبول'), ('FAILED', 'مرفوض'), ('REWORK', 'إعادة عمل')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('checked_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الفحص')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='فحص بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='inventory.manufacturingorder', verbose_name='أمر التصنيع')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['check_number'],
                'unique_together': {('order', 'check_number')},
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية')),
                ('reserved_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المحجوزة')),
                ('available_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المتاحة')),
                ('average_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='متوسط التكلفة')),
                ('last_movement_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر حركة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'رصيد مخزون',
                'verbose_name_plural': 'أرصدة المخزون',
                'unique_together': {('warehouse', 'item')},
            },
        ),
    ]
