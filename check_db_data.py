import sqlite3

# الاتصال بقاعدة البيانات
conn = sqlite3.connect('db.sqlite3')
cursor = conn.cursor()

# جلب بعض البيانات من جداول الأصناف والفئات ووحدات القياس
try:
    print('--- فئات الأصناف ---')
    for row in cursor.execute('SELECT * FROM categories LIMIT 10'):
        print(row)
    print('\n--- الأصناف ---')
    for row in cursor.execute('SELECT * FROM items LIMIT 10'):
        print(row)
    print('\n--- وحدات القياس ---')
    for row in cursor.execute('SELECT * FROM units LIMIT 10'):
        print(row)
except Exception as e:
    print('حدث خطأ:', e)
finally:
    conn.close()
