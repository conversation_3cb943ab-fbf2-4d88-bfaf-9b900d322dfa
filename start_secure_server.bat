@echo off
REM بدء النظام الآمن - الخادم الأبدي + HTTPS
REM Start Secure System - Eternal Server + HTTPS

title الخادم الأبدي الآمن - Secure Eternal Server
color 0A

echo ============================================================
echo 🔒 النظام الآمن للمحاسبة - الخادم الأبدي
echo Secure Accounting System - Eternal Server
echo ============================================================
echo.

echo 🔍 فحص النظام...
echo Checking system...
echo.

REM فحص Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python غير مثبت
    echo Please install Python first
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM فحص Django
python -c "import django" >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Django غير مثبت
    echo Installing Django...
    pip install django
)
echo ✅ Django متوفر

REM فحص وإنشاء شهادة SSL
if not exist "ssl\server.crt" (
    echo 🔒 إنشاء شهادة SSL...
    echo Creating SSL certificate...
    python simple_ssl.py
    if not exist "ssl\server.crt" (
        echo ❌ فشل في إنشاء الشهادة
        pause
        exit /b 1
    )
)
echo ✅ شهادة SSL متوفرة

echo.
echo ============================================================
echo 🚀 بدء تشغيل النظام الآمن
echo Starting Secure System
echo ============================================================
echo.

echo 📋 خيارات التشغيل:
echo 1. تشغيل الخادم الأبدي فقط (HTTP)
echo 2. تشغيل النظام الآمن (HTTP + HTTPS)
echo 3. تثبيت شهادة SSL في النظام
echo 4. عرض معلومات الوصول
echo 5. إيقاف جميع الخوادم
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" goto start_eternal
if "%choice%"=="2" goto start_secure
if "%choice%"=="3" goto install_cert
if "%choice%"=="4" goto show_info
if "%choice%"=="5" goto stop_servers
if "%choice%"=="0" goto exit
goto menu

:start_eternal
echo.
echo 🌟 بدء تشغيل الخادم الأبدي...
echo Starting Eternal Server...
start "الخادم الأبدي" python eternal_server.py
timeout /t 5 /nobreak >nul
goto show_info

:start_secure
echo.
echo 🔒 بدء تشغيل النظام الآمن...
echo Starting Secure System...

REM بدء الخادم الأبدي
echo 🌟 بدء الخادم الأبدي (HTTP)...
start "الخادم الأبدي - HTTP" python eternal_server.py

REM انتظار بدء الخادم الأبدي
echo ⏳ انتظار بدء الخادم الأبدي...
timeout /t 10 /nobreak >nul

REM بدء بروكسي HTTPS
echo 🔒 بدء بروكسي HTTPS...
start "بروكسي HTTPS" python https_proxy.py

REM انتظار بدء البروكسي
timeout /t 5 /nobreak >nul

goto show_info

:install_cert
echo.
echo 🔧 تثبيت شهادة SSL...
echo Installing SSL Certificate...
call install_certificate.bat
pause
goto menu

:show_info
echo.
echo ============================================================
echo 🌐 معلومات الوصول للنظام
echo System Access Information
echo ============================================================
echo.

REM فحص الخوادم
netstat -an | findstr ":8000" >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ الخادم الأبدي يعمل على المنفذ 8000
    echo 🌍 HTTP Access:
    echo    http://***************:8000/
    echo    http://localhost:8000/
    echo.
) else (
    echo ❌ الخادم الأبدي لا يعمل
)

netstat -an | findstr ":8443" >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ بروكسي HTTPS يعمل على المنفذ 8443
    echo 🔒 HTTPS Access (Secure):
    echo    https://***************:8443/
    echo    https://localhost:8443/
    echo.
    echo ⚠️ ملاحظة: قد تظهر تحذيرات أمان في المتصفح
    echo    اضغط "Advanced" ثم "Proceed to localhost"
    echo    أو استخدم الخيار 3 لتثبيت الشهادة
) else (
    echo ❌ بروكسي HTTPS لا يعمل
)

echo.
echo 💡 نصائح:
echo • للوصول الآمن استخدم HTTPS
echo • لتجنب التحذيرات ثبت الشهادة (الخيار 3)
echo • الخادم الأبدي لا يتوقف أبداً
echo • يمكن الوصول من أي جهاز في الشبكة
echo.

pause
goto menu

:stop_servers
echo.
echo 🛑 إيقاف جميع الخوادم...
echo Stopping all servers...

REM إيقاف جميع عمليات Python
taskkill /F /IM python.exe >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم إيقاف جميع الخوادم
) else (
    echo ℹ️ لا توجد خوادم تعمل
)

timeout /t 3 /nobreak >nul
goto menu

:menu
echo.
echo ============================================================
goto start

:exit
echo.
echo 👋 شكراً لاستخدام النظام الآمن
echo Thank you for using the Secure System
echo.
pause
exit /b 0
