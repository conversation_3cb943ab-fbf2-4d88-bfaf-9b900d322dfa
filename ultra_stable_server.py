#!/usr/bin/env python3
"""
خادم Django فائق الاستقرار
Ultra Stable Django Server

خادم لا يتوقف أبداً مع حلول جميع المشاكل
Never-stopping server with all issues resolved
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
from datetime import datetime

# إعداد اللوجز بدون رموز Unicode
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultra_stable.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class UltraStableServer:
    """خادم Django فائق الاستقرار"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 10000  # عدد كبير جداً
        self.restart_delay = 2  # تأخير قصير
        self.port = 8000
        self.host = '127.0.0.1'
        self.health_check_interval = 15  # فحص كل 15 ثانية
        
    def log_info(self, message):
        """تسجيل معلومات بدون رموز Unicode"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء بدون رموز Unicode"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة الإيقاف {signum}")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def check_django_available(self):
        """فحص توفر Django"""
        try:
            import django
            self.log_info(f"Django متوفر - الإصدار: {django.get_version()}")
            return True
        except ImportError:
            self.log_error("Django غير مثبت!")
            return False
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            self.log_info(f"بدء تشغيل الخادم على {self.host}:{self.port}")
            
            # أوامر بسيطة بدون خيارات معقدة
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}'
            ]
            
            # تشغيل الخادم مع إعادة توجيه المخرجات
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.is_running = True
            self.log_info(f"تم بدء الخادم بنجاح! PID: {self.server_process.pid}")
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(3)
            
            # فحص إذا كان الخادم ما زال يعمل
            if self.server_process.poll() is None:
                self.log_info("الخادم يعمل بنجاح")
                return True
            else:
                self.log_error(f"الخادم توقف فوراً بكود: {self.server_process.returncode}")
                return False
            
        except Exception as e:
            self.log_error(f"خطأ في بدء الخادم: {e}")
            return False
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            # فحص العملية أولاً
            if not self.server_process or self.server_process.poll() is not None:
                return False
            
            # فحص HTTP
            import urllib.request
            import urllib.error
            
            url = f"http://{self.host}:{self.port}/"
            
            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'UltraStableServer/1.0')
                response = urllib.request.urlopen(request, timeout=10)
                
                if response.getcode() == 200:
                    return True
                else:
                    self.log_error(f"الخادم يرد بكود خطأ: {response.getcode()}")
                    return False
                    
            except urllib.error.URLError as e:
                self.log_error(f"خطأ في الاتصال بالخادم: {e}")
                return False
            except Exception as e:
                self.log_error(f"خطأ في فحص الصحة: {e}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ عام في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.log_info("بدء إعادة تشغيل الخادم...")
        
        # إيقاف الخادم الحالي
        self.stop_server()
        
        # انتظار قصير
        time.sleep(self.restart_delay)
        
        # زيادة عداد إعادة التشغيل
        self.restart_count += 1
        
        if self.restart_count <= self.max_restarts:
            self.log_info(f"محاولة إعادة التشغيل رقم {self.restart_count}")
            return self.start_server()
        else:
            self.log_error(f"تم الوصول للحد الأقصى من إعادة التشغيل ({self.max_restarts})")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                self.log_info("إيقاف الخادم...")
                
                # إنهاء العملية بلطف
                self.server_process.terminate()
                
                # انتظار الإنهاء
                try:
                    self.server_process.wait(timeout=10)
                    self.log_info("تم إيقاف الخادم بنجاح")
                except subprocess.TimeoutExpired:
                    self.log_info("إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                    self.log_info("تم إجبار إيقاف الخادم")
                
            except Exception as e:
                self.log_error(f"خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def monitor_server_output(self):
        """مراقبة مخرجات الخادم"""
        if not self.server_process:
            return
            
        try:
            # قراءة المخرجات
            while self.is_running and self.server_process:
                if self.server_process.poll() is not None:
                    break
                
                # قراءة الأخطاء
                if self.server_process.stderr:
                    try:
                        line = self.server_process.stderr.readline()
                        if line:
                            line = line.strip()
                            if line and 'ERROR' in line.upper():
                                self.log_error(f"خطأ من الخادم: {line}")
                    except:
                        pass
                
                time.sleep(1)
                
        except Exception as e:
            self.log_error(f"خطأ في مراقبة المخرجات: {e}")
    
    def health_monitor_loop(self):
        """حلقة مراقبة الصحة"""
        consecutive_failures = 0
        max_failures = 3
        
        while self.is_running:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                # فحص حالة العملية
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("عملية الخادم توقفت!")
                    if not self.restart_server():
                        break
                    consecutive_failures = 0
                    continue
                
                # فحص صحة HTTP
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_info("الخادم عاد للعمل بنجاح")
                    consecutive_failures = 0
                    self.log_info("الخادم يعمل بصحة جيدة")
                else:
                    consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.log_error("فشل متكرر في فحص الصحة - إعادة تشغيل الخادم")
                        if not self.restart_server():
                            break
                        consecutive_failures = 0
                
            except Exception as e:
                self.log_error(f"خطأ في حلقة مراقبة الصحة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل الخادم فائق الاستقرار"""
        print("=" * 60)
        print("خادم Django فائق الاستقرار")
        print("Ultra Stable Django Server")
        print("=" * 60)
        
        self.log_info("بدء تشغيل النظام...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # فحص Django
        if not self.check_django_available():
            self.log_error("لا يمكن المتابعة بدون Django")
            return False
        
        # بدء الخادم
        if not self.start_server():
            self.log_error("فشل في بدء الخادم!")
            return False
        
        # بدء مراقبة المخرجات
        output_thread = threading.Thread(target=self.monitor_server_output)
        output_thread.daemon = True
        output_thread.start()
        
        # بدء مراقبة الصحة
        health_thread = threading.Thread(target=self.health_monitor_loop)
        health_thread.daemon = True
        health_thread.start()
        
        self.log_info("الخادم يعمل بأقصى استقرار!")
        self.log_info(f"الموقع متاح على: http://{self.host}:{self.port}/")
        self.log_info("مراقبة مستمرة للصحة والاستقرار")
        self.log_info("اضغط Ctrl+C للإيقاف الآمن")
        
        try:
            # الحلقة الرئيسية
            while self.is_running:
                time.sleep(1)
                
                # فحص حالة الخادم
                if self.server_process and self.server_process.poll() is not None:
                    return_code = self.server_process.returncode
                    if return_code != 0:
                        self.log_error(f"الخادم توقف بكود خطأ: {return_code}")
                        if not self.restart_server():
                            break
                    else:
                        self.log_info("الخادم توقف بشكل طبيعي")
                        break
                        
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
        finally:
            self.stop_server()
            self.log_info("تم إنهاء الخادم فائق الاستقرار")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = UltraStableServer()
    success = server.run()
    
    if success:
        print("تم تشغيل الخادم بنجاح!")
    else:
        print("فشل في تشغيل الخادم!")
        sys.exit(1)

if __name__ == "__main__":
    main()
