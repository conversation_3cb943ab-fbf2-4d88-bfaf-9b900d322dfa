#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit, Currency, Warehouse

def check_data():
    print("=== Checking existing data ===")
    
    # Check items
    items = Item.objects.filter(is_active=True)
    print(f"Active items count: {items.count()}")
    
    if items.exists():
        print("\nExisting items:")
        for item in items[:10]:  # First 10 items
            print(f"- {item.code}: {item.name}")
        
        if items.count() > 10:
            print(f"... and {items.count() - 10} more items")
    
    # Check categories
    categories = ItemCategory.objects.filter(is_active=True)
    print(f"\nItem categories count: {categories.count()}")
    
    # Check units
    units = Unit.objects.filter(is_active=True)
    print(f"Units count: {units.count()}")
    
    # Check currencies
    currencies = Currency.objects.filter(is_active=True)
    print(f"Currencies count: {currencies.count()}")
    
    # Check warehouses
    warehouses = Warehouse.objects.filter(is_active=True)
    print(f"Warehouses count: {warehouses.count()}")

def delete_test_data():
    print("\n=== Deleting test data ===")
    
    # Delete test items (you can modify criteria as needed)
    test_items = Item.objects.filter(
        is_active=True,
        name__icontains='test'  # or any other criteria for test data
    )
    
    if test_items.exists():
        print(f"Will delete {test_items.count()} test items")
        for item in test_items:
            print(f"- Deleting: {item.code}: {item.name}")
            item.is_active = False
            item.save()
        print("Test data deleted")
    else:
        print("No test data found to delete")

if __name__ == "__main__":
    check_data()
    
    # If you want to delete test data, uncomment the next line
    # delete_test_data()