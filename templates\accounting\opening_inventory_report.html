<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير جرد بضاعة أول المدة - {{ opening_inventory.inventory_number }}</title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-column {
            flex: 1;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            min-width: 120px;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .status-draft { background-color: #ffc107; }
        .status-approved { background-color: #17a2b8; }
        .status-posted { background-color: #28a745; }
        .status-cancelled { background-color: #dc3545; }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 14px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        
        .items-table th {
            background-color: #e9ecef;
            font-weight: bold;
            color: #495057;
        }
        
        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background-color: #e3f2fd;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #007bff !important;
            color: white;
        }
        
        .summary-section {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }
        
        .summary-card {
            text-align: center;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            min-width: 150px;
        }
        
        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
        
        .condition-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        
        .condition-new { background-color: #007bff; }
        .condition-good { background-color: #28a745; }
        .condition-fair { background-color: #ffc107; color: #212529; }
        .condition-poor { background-color: #dc3545; }
        .condition-damaged { background-color: #6c757d; }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">
        🖨️ طباعة التقرير
    </button>

    <div class="header">
        <div class="company-name">نظام إدارة المخازن والحسابات</div>
        <div class="report-title">تقرير جرد بضاعة أول المدة</div>
        <div class="report-subtitle">{{ opening_inventory.inventory_number }}</div>
    </div>

    <div class="info-section">
        <div class="info-column">
            <div class="info-row">
                <span class="info-label">رقم الجرد:</span>
                <span class="info-value">{{ opening_inventory.inventory_number }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">التاريخ:</span>
                <span class="info-value">{{ opening_inventory.date|date:"Y-m-d" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">المخزن:</span>
                <span class="info-value">{{ opening_inventory.warehouse.name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">السنة المالية:</span>
                <span class="info-value">{{ opening_inventory.fiscal_year }}</span>
            </div>
        </div>
        <div class="info-column">
            <div class="info-row">
                <span class="info-label">اسم الفترة:</span>
                <span class="info-value">{{ opening_inventory.period_name|default:"غير محدد" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">العملة:</span>
                <span class="info-value">{{ opening_inventory.currency.name|default:"غير محدد" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">سعر الصرف:</span>
                <span class="info-value">{{ opening_inventory.exchange_rate }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الحالة:</span>
                <span class="info-value">
                    {% if opening_inventory.status == 'DRAFT' %}
                        <span class="status-badge status-draft">مسودة</span>
                    {% elif opening_inventory.status == 'APPROVED' %}
                        <span class="status-badge status-approved">معتمد</span>
                    {% elif opening_inventory.status == 'POSTED' %}
                        <span class="status-badge status-posted">مرحل</span>
                    {% elif opening_inventory.status == 'CANCELLED' %}
                        <span class="status-badge status-cancelled">ملغي</span>
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <div class="summary-section">
        <div class="summary-card">
            <div class="summary-label">عدد الأصناف</div>
            <div class="summary-value">{{ items.count }}</div>
        </div>
        <div class="summary-card">
            <div class="summary-label">إجمالي الكميات</div>
            <div class="summary-value">{{ total_quantity }}</div>
        </div>
        <div class="summary-card">
            <div class="summary-label">إجمالي القيمة</div>
            <div class="summary-value">{{ total_value }} ج.م</div>
        </div>
    </div>

    {% if items %}
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 20%;">الصنف</th>
                    <th style="width: 10%;">الكود</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 10%;">الوحدة</th>
                    <th style="width: 10%;">تكلفة الوحدة</th>
                    <th style="width: 10%;">إجمالي القيمة</th>
                    <th style="width: 10%;">رقم الدفعة</th>
                    <th style="width: 8%;">الحالة</th>
                    <th style="width: 7%;">تاريخ الانتهاء</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.item.name }}</td>
                        <td>{{ item.item.code }}</td>
                        <td>{{ item.opening_quantity }}</td>
                        <td>{{ item.item.unit.name|default:"-" }}</td>
                        <td>{{ item.unit_cost }} ج.م</td>
                        <td>{{ item.total_value }} ج.م</td>
                        <td>{{ item.batch_number|default:"-" }}</td>
                        <td>
                            {% if item.condition == 'NEW' %}
                                <span class="condition-badge condition-new">جديد</span>
                            {% elif item.condition == 'GOOD' %}
                                <span class="condition-badge condition-good">جيد</span>
                            {% elif item.condition == 'FAIR' %}
                                <span class="condition-badge condition-fair">مقبول</span>
                            {% elif item.condition == 'POOR' %}
                                <span class="condition-badge condition-poor">ضعيف</span>
                            {% elif item.condition == 'DAMAGED' %}
                                <span class="condition-badge condition-damaged">تالف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.expiry_date %}
                                {{ item.expiry_date|date:"Y-m-d" }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
                <tr class="total-row">
                    <td colspan="3">الإجمالي</td>
                    <td>{{ total_quantity }}</td>
                    <td>-</td>
                    <td>-</td>
                    <td>{{ total_value }} ج.م</td>
                    <td colspan="3">-</td>
                </tr>
            </tbody>
        </table>
    {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>لا توجد أصناف في هذا الجرد</h3>
        </div>
    {% endif %}

    {% if opening_inventory.notes %}
        <div style="margin-bottom: 30px;">
            <h4 style="color: #007bff; margin-bottom: 10px;">ملاحظات:</h4>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-right: 4px solid #007bff;">
                {{ opening_inventory.notes }}
            </div>
        </div>
    {% endif %}

    {% if opening_inventory.approved_by or opening_inventory.posted_by %}
        <div style="margin-bottom: 30px;">
            <h4 style="color: #007bff; margin-bottom: 15px;">معلومات الاعتماد والترحيل:</h4>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                {% if opening_inventory.approved_by %}
                    <div style="margin-bottom: 10px;">
                        <strong>معتمد من:</strong> {{ opening_inventory.approved_by.get_full_name|default:opening_inventory.approved_by.username }}
                        <br>
                        <strong>تاريخ الاعتماد:</strong> {{ opening_inventory.approved_date|date:"Y-m-d h:i A" }}
                    </div>
                {% endif %}
                
                {% if opening_inventory.posted_by %}
                    <div>
                        <strong>رحل بواسطة:</strong> {{ opening_inventory.posted_by.get_full_name|default:opening_inventory.posted_by.username }}
                        <br>
                        <strong>تاريخ الترحيل:</strong> {{ opening_inventory.posted_date|date:"Y-m-d h:i A" }}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}

    <div class="footer">
        <div>تم إنشاء هذا التقرير في: {{ "now"|date:"Y-m-d h:i:s A" }}</div>
        <div>نظام إدارة المخازن والحسابات - جرد بضاعة أول المدة</div>
    </div>
</body>
</html>
