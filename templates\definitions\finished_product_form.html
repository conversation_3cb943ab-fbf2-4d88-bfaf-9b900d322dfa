{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-box text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'definitions:finished_product_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات المنتج التام
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <!-- معلومات أساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }}</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name_english.id_for_label }}" class="form-label">{{ form.name_english.label }}</label>
                                {{ form.name_english }}
                                {% if form.name_english.errors %}
                                    <div class="text-danger small">{{ form.name_english.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.category.id_for_label }}" class="form-label">{{ form.category.label }}</label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <div class="text-danger small">{{ form.category.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.unit.id_for_label }}" class="form-label">{{ form.unit.label }}</label>
                                {{ form.unit }}
                                {% if form.unit.errors %}
                                    <div class="text-danger small">{{ form.unit.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات المنتج -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-tag me-2"></i>
                                    معلومات المنتج
                                </h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.brand.id_for_label }}" class="form-label">{{ form.brand.label }}</label>
                                {{ form.brand }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.model.id_for_label }}" class="form-label">{{ form.model.label }}</label>
                                {{ form.model }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.version.id_for_label }}" class="form-label">{{ form.version.label }}</label>
                                {{ form.version }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.barcode.id_for_label }}" class="form-label">{{ form.barcode.label }}</label>
                                {{ form.barcode }}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.sku.id_for_label }}" class="form-label">{{ form.sku.label }}</label>
                                {{ form.sku }}
                            </div>
                        </div>

                        <!-- التكاليف والأسعار -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    التكاليف والأسعار
                                </h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.material_cost.id_for_label }}" class="form-label">{{ form.material_cost.label }}</label>
                                {{ form.material_cost }}
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.labor_cost.id_for_label }}" class="form-label">{{ form.labor_cost.label }}</label>
                                {{ form.labor_cost }}
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.overhead_cost.id_for_label }}" class="form-label">{{ form.overhead_cost.label }}</label>
                                {{ form.overhead_cost }}
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.standard_cost.id_for_label }}" class="form-label">{{ form.standard_cost.label }}</label>
                                {{ form.standard_cost }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.selling_price.id_for_label }}" class="form-label">{{ form.selling_price.label }}</label>
                                {{ form.selling_price }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.wholesale_price.id_for_label }}" class="form-label">{{ form.wholesale_price.label }}</label>
                                {{ form.wholesale_price }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.retail_price.id_for_label }}" class="form-label">{{ form.retail_price.label }}</label>
                                {{ form.retail_price }}
                            </div>
                        </div>

                        <!-- حدود المخزون -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-warehouse me-2"></i>
                                    حدود المخزون
                                </h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.min_stock.id_for_label }}" class="form-label">{{ form.min_stock.label }}</label>
                                {{ form.min_stock }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.max_stock.id_for_label }}" class="form-label">{{ form.max_stock.label }}</label>
                                {{ form.max_stock }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.reorder_point.id_for_label }}" class="form-label">{{ form.reorder_point.label }}</label>
                                {{ form.reorder_point }}
                            </div>
                        </div>

                        <!-- معلومات الإنتاج -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-cogs me-2"></i>
                                    معلومات الإنتاج
                                </h6>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.production_lead_time.id_for_label }}" class="form-label">{{ form.production_lead_time.label }}</label>
                                {{ form.production_lead_time }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.batch_size.id_for_label }}" class="form-label">{{ form.batch_size.label }}</label>
                                {{ form.batch_size }}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.quality_grade.id_for_label }}" class="form-label">{{ form.quality_grade.label }}</label>
                                {{ form.quality_grade }}
                            </div>
                        </div>

                        <!-- الإعدادات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-cog me-2"></i>
                                    الإعدادات
                                </h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    {{ form.is_manufactured }}
                                    <label class="form-check-label" for="{{ form.is_manufactured.id_for_label }}">
                                        {{ form.is_manufactured.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    {{ form.is_sellable }}
                                    <label class="form-check-label" for="{{ form.is_sellable.id_for_label }}">
                                        {{ form.is_sellable.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    {{ form.is_purchasable }}
                                    <label class="form-check-label" for="{{ form.is_purchasable.id_for_label }}">
                                        {{ form.is_purchasable.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    {{ form.track_serial_numbers }}
                                    <label class="form-check-label" for="{{ form.track_serial_numbers.id_for_label }}">
                                        {{ form.track_serial_numbers.label }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'definitions:finished_product_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
