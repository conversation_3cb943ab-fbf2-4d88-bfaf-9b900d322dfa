{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-bar text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل عن حضور وانصراف الموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printAttendanceReport()">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقرير
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" value="2024-01-01">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" value="2024-01-31">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الموظف</label>
                            <select class="form-select">
                                <option value="">جميع الموظفين</option>
                                <option value="1">أحمد محمد</option>
                                <option value="2">فاطمة علي</option>
                                <option value="3">محمد أحمد</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>
                                عرض التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ total_present }}</h3>
                    <p class="mb-0">أيام الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ total_absent }}</h3>
                    <p class="mb-0">أيام الغياب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ total_late }}</h3>
                    <p class="mb-0">أيام التأخير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ attendance_percent }}%</h3>
                    <p class="mb-0">نسبة الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>أيام الحضور</th>
                                    <th>أيام الغياب</th>
                                    <th>أيام التأخير</th>
                                    <th>نسبة الحضور</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in report_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ report.employee }}
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ report.present_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ report.absent_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ report.late_days }}</span>
                                    </td>
                                    <td>
                                        {% widthratio report.present_days 25 100 %}%
                                    </td>
                                    <td>
                                        {% if report.present_days >= 23 %}
                                            <span class="badge bg-success">ممتاز</span>
                                        {% elif report.present_days >= 20 %}
                                            <span class="badge bg-warning">جيد</span>
                                        {% else %}
                                            <span class="badge bg-danger">ضعيف</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-chart-bar fa-3x mb-3 d-block"></i>
                                        لا توجد بيانات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printAttendanceReport() {
    // جمع بيانات الحضور من الجدول
    const attendanceData = [];
    const rows = document.querySelectorAll('table tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            attendanceData.push({
                employee_name: cells[1]?.textContent?.trim() || 'غير محدد',
                present_days: cells[2]?.textContent?.trim() || '0',
                absent_days: cells[3]?.textContent?.trim() || '0',
                late_days: cells[4]?.textContent?.trim() || '0',
                attendance_rate: cells[5]?.textContent?.trim() || '0%',
                evaluation: cells[6]?.textContent?.trim() || 'غير محدد'
            });
        }
    });

    // استخدام القالب المخصص للطباعة
    if (typeof printAttendanceReport !== 'undefined') {
        printAttendanceReport(attendanceData, 'تقرير الحضور والانصراف');
    } else {
        // استخدام الطباعة العادية كبديل
        printReport('تقرير الحضور والانصراف');
    }
}
</script>
{% endblock %}
