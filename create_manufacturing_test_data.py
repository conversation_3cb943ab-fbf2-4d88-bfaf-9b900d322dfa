#!/usr/bin/env python
"""
سكريبت لإنشاء بيانات تجريبية للتصنيع
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.contrib.auth.models import User
from definitions.models import Item, ItemType, Warehouse, Unit
from inventory.models import Stock
from decimal import Decimal

def create_test_data():
    """إنشاء بيانات تجريبية للتصنيع"""
    
    # إنشاء مستخدم تجريبي إذا لم يكن موجوداً
    user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('admin123')
        user.save()
        print("تم إنشاء المستخدم التجريبي")
    
    # إنشاء الوحدات إذا لم تكن موجودة
    unit_kg, created = Unit.objects.get_or_create(
        name='كيلوجرام',
        defaults={'abbreviation': 'كجم'}
    )
    unit_piece, created = Unit.objects.get_or_create(
        name='قطعة',
        defaults={'abbreviation': 'قطعة'}
    )
    
    # إنشاء المخازن إذا لم تكن موجودة
    raw_materials_warehouse, created = Warehouse.objects.get_or_create(
        name='مخزن المواد الخام',
        defaults={
            'code': 'RM001',
            'location': 'الطابق الأول',
            'manager_name': 'مدير المواد الخام'
        }
    )
    
    finished_goods_warehouse, created = Warehouse.objects.get_or_create(
        name='مخزن المنتجات النهائية',
        defaults={
            'code': 'FG001',
            'location': 'الطابق الثاني',
            'manager_name': 'مدير المنتجات النهائية'
        }
    )
    
    # إنشاء المواد الخام
    flour, created = Item.objects.get_or_create(
        name='دقيق القمح',
        defaults={
            'item_type': 'RAW_MATERIAL',
            'unit': unit_kg,
            'cost_price': Decimal('5.00'),
            'selling_price': Decimal('6.00'),
            'min_stock': Decimal('100'),
            'max_stock': Decimal('1000'),
            'is_active': True
        }
    )
    
    sugar, created = Item.objects.get_or_create(
        name='سكر أبيض',
        defaults={
            'item_type': 'RAW_MATERIAL',
            'unit': unit_kg,
            'cost_price': Decimal('8.00'),
            'selling_price': Decimal('10.00'),
            'min_stock': Decimal('50'),
            'max_stock': Decimal('500'),
            'is_active': True
        }
    )
    
    eggs, created = Item.objects.get_or_create(
        name='بيض',
        defaults={
            'item_type': 'RAW_MATERIAL',
            'unit': unit_piece,
            'cost_price': Decimal('1.50'),
            'selling_price': Decimal('2.00'),
            'min_stock': Decimal('100'),
            'max_stock': Decimal('1000'),
            'is_active': True
        }
    )
    
    oil, created = Item.objects.get_or_create(
        name='زيت نباتي',
        defaults={
            'item_type': 'RAW_MATERIAL',
            'unit': unit_kg,
            'cost_price': Decimal('12.00'),
            'selling_price': Decimal('15.00'),
            'min_stock': Decimal('20'),
            'max_stock': Decimal('200'),
            'is_active': True
        }
    )
    
    # إنشاء المنتج النهائي
    cake, created = Item.objects.get_or_create(
        name='كيك الشوكولاتة',
        defaults={
            'item_type': 'FINISHED_PRODUCT',
            'unit': unit_piece,
            'cost_price': Decimal('25.00'),
            'selling_price': Decimal('35.00'),
            'min_stock': Decimal('10'),
            'max_stock': Decimal('100'),
            'is_active': True
        }
    )
    
    # إضافة مخزون للمواد الخام
    materials = [flour, sugar, eggs, oil]
    quantities = [Decimal('500'), Decimal('200'), Decimal('300'), Decimal('100')]
    
    for material, quantity in zip(materials, quantities):
        stock, created = Stock.objects.get_or_create(
            warehouse=raw_materials_warehouse,
            item=material,
            defaults={
                'quantity': quantity,
                'available_quantity': quantity,
                'average_cost': material.cost_price,
                'last_movement_date': django.utils.timezone.now()
            }
        )
        if not created:
            stock.quantity = quantity
            stock.available_quantity = quantity
            stock.save()
    
    # إضافة مخزون للمنتج النهائي
    finished_stock, created = Stock.objects.get_or_create(
        warehouse=finished_goods_warehouse,
        item=cake,
        defaults={
            'quantity': Decimal('50'),
            'available_quantity': Decimal('50'),
            'average_cost': cake.cost_price,
            'last_movement_date': django.utils.timezone.now()
        }
    )
    
    print("تم إنشاء البيانات التجريبية بنجاح!")
    print(f"المواد الخام: {', '.join([m.name for m in materials])}")
    print(f"المنتج النهائي: {cake.name}")
    print(f"مخزن المواد الخام: {raw_materials_warehouse.name}")
    print(f"مخزن المنتجات النهائية: {finished_goods_warehouse.name}")
    print("\nيمكنك الآن اختبار نظام التصنيع!")

if __name__ == '__main__':
    create_test_data() 