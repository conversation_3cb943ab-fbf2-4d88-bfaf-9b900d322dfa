@echo off
REM تحميل ngrok للحصول على HTTPS معتمد مجاني
REM Download ngrok for free certified HTTPS

title تحميل ngrok - Download ngrok
color 0A

echo ============================================================
echo 🔒 تحميل ngrok للحصول على HTTPS معتمد مجاني
echo Download ngrok for Free Certified HTTPS
echo ============================================================
echo.

echo 🌐 فتح موقع ngrok للتحميل...
echo Opening ngrok website for download...
echo.

echo 📋 الخطوات:
echo 1. اضغط "Download for Windows"
echo 2. حمل الملف ngrok-v3-stable-windows-amd64.zip
echo 3. فك الضغط في مجلد ngrok
echo 4. شغل: ngrok\ngrok http 8000
echo.

echo 🚀 فتح الموقع...
start https://ngrok.com/download

echo.
echo ⏳ بعد التحميل:
echo 1. فك الضغط في مجلد ngrok
echo 2. شغل start_ngrok_simple.bat
echo.

REM إنشاء سكريپت تشغيل بسيط
echo @echo off > start_ngrok_simple.bat
echo title ngrok - HTTPS معتمد مجاني >> start_ngrok_simple.bat
echo color 0A >> start_ngrok_simple.bat
echo echo ============================================================ >> start_ngrok_simple.bat
echo echo 🔒 ngrok - HTTPS معتمد مجاني >> start_ngrok_simple.bat
echo echo ============================================================ >> start_ngrok_simple.bat
echo echo. >> start_ngrok_simple.bat
echo echo 🚀 بدء ngrok... >> start_ngrok_simple.bat
echo echo. >> start_ngrok_simple.bat
echo ngrok\ngrok http 8000 >> start_ngrok_simple.bat
echo pause >> start_ngrok_simple.bat

echo ✅ تم إنشاء سكريپت التشغيل: start_ngrok_simple.bat
echo.

pause
