{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-check text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل عن حضور وغياب الموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-warning" onclick="generateAttendanceReport()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3>{{ total_present }}</h3>
                    <p class="mb-0">إجمالي الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h3>{{ total_absent }}</h3>
                    <p class="mb-0">إجمالي الغياب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3>{{ total_late }}</h3>
                    <p class="mb-0">التأخير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white attendance-card">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h3>{{ attendance_rate }}%</h3>
                    <p class="mb-0">معدل الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">الفترة:</label>
                            <select class="form-select" onchange="filterByPeriod()">
                                <option value="today">اليوم</option>
                                <option value="week" selected>هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="sales">المبيعات</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التقرير:</label>
                            <select class="form-select" onchange="changeReportType()">
                                <option value="summary">ملخص</option>
                                <option value="detailed">تفصيلي</option>
                                <option value="daily">يومي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportAttendance()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Excel
                                </button>
                                <button class="btn btn-info" onclick="printAttendance()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تقرير الحضور التفصيلي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>أيام الحضور</th>
                                    <th>أيام الغياب</th>
                                    <th>ساعات التأخير</th>
                                    <th>ساعات العمل</th>
                                    <th>معدل الحضور</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in attendance_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                {{ attendance.employee_name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ attendance.employee_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ attendance.employee_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ attendance.department }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ attendance.present_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ attendance.absent_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ attendance.late_hours }}h</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ attendance.total_hours }}h</strong>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if attendance.attendance_rate >= 95 %}bg-success
                                                {% elif attendance.attendance_rate >= 85 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ attendance.attendance_rate }}%">
                                                {{ attendance.attendance_rate }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if attendance.attendance_rate >= 95 %}
                                            <span class="badge bg-success">ممتاز</span>
                                        {% elif attendance.attendance_rate >= 85 %}
                                            <span class="badge bg-warning">جيد</span>
                                        {% elif attendance.attendance_rate >= 70 %}
                                            <span class="badge bg-info">مقبول</span>
                                        {% else %}
                                            <span class="badge bg-danger">ضعيف</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-check fa-3x mb-3 d-block"></i>
                                        لا توجد بيانات حضور للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Attendance Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        ملخص الحضور حسب الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dept in department_attendance %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary department-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-building fa-2x text-primary me-3"></i>
                                        <h6 class="card-title text-primary mb-0">{{ dept.name }}</h6>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>معدل الحضور:</span>
                                        <strong class="text-success">{{ dept.attendance_rate }}%</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>أيام الحضور:</span>
                                        <strong>{{ dept.present_days }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>أيام الغياب:</span>
                                        <strong class="text-danger">{{ dept.absent_days }}</strong>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar
                                            {% if dept.attendance_rate >= 95 %}bg-success
                                            {% elif dept.attendance_rate >= 85 %}bg-warning
                                            {% else %}bg-danger{% endif %}"
                                            style="width: {{ dept.attendance_rate }}%">
                                        </div>
                                    </div>
                                    <small class="text-muted mt-1">
                                        {% if dept.attendance_rate >= 95 %}
                                            <i class="fas fa-thumbs-up text-success me-1"></i>ممتاز
                                        {% elif dept.attendance_rate >= 85 %}
                                            <i class="fas fa-exclamation-triangle text-warning me-1"></i>جيد
                                        {% else %}
                                            <i class="fas fa-exclamation-circle text-danger me-1"></i>يحتاج تحسين
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Attendance Chart -->
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه الحضور اليومي (آخر 7 أيام)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder" style="height: 250px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">رسم بياني للحضور اليومي</h5>
                            <p class="text-muted">يظهر معدل الحضور خلال الأسبوع الماضي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات الحضور
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <strong>تحذير!</strong>
                        <br>
                        3 موظفين لديهم معدل حضور أقل من 85%
                    </div>
                    <div class="alert alert-warning" role="alert">
                        <strong>ملاحظة:</strong>
                        <br>
                        زيادة في التأخير بنسبة 12% هذا الأسبوع
                    </div>
                    <div class="alert alert-info" role="alert">
                        <strong>معلومة:</strong>
                        <br>
                        قسم المالية لديه أعلى معدل حضور (98%)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Trends -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>
                        تفاصيل الحضور الأسبوعي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اليوم</th>
                                    <th>الحضور</th>
                                    <th>الغياب</th>
                                    <th>التأخير</th>
                                    <th>معدل الحضور</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>السبت</strong></td>
                                    <td><span class="badge bg-success">58</span></td>
                                    <td><span class="badge bg-danger">2</span></td>
                                    <td><span class="badge bg-warning">5</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-success" style="width: 97%">97%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">ممتاز</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الأحد</strong></td>
                                    <td><span class="badge bg-success">56</span></td>
                                    <td><span class="badge bg-danger">4</span></td>
                                    <td><span class="badge bg-warning">3</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-success" style="width: 93%">93%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">جيد</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الاثنين</strong></td>
                                    <td><span class="badge bg-success">59</span></td>
                                    <td><span class="badge bg-danger">1</span></td>
                                    <td><span class="badge bg-warning">2</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-success" style="width: 98%">98%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">ممتاز</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الثلاثاء</strong></td>
                                    <td><span class="badge bg-success">57</span></td>
                                    <td><span class="badge bg-danger">3</span></td>
                                    <td><span class="badge bg-warning">4</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-warning" style="width: 95%">95%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-warning">جيد</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الأربعاء</strong></td>
                                    <td><span class="badge bg-success">55</span></td>
                                    <td><span class="badge bg-danger">5</span></td>
                                    <td><span class="badge bg-warning">6</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-warning" style="width: 92%">92%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-warning">مقبول</span></td>
                                </tr>
                                <tr>
                                    <td><strong>الخميس</strong></td>
                                    <td><span class="badge bg-success">60</span></td>
                                    <td><span class="badge bg-danger">0</span></td>
                                    <td><span class="badge bg-warning">1</span></td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-success" style="width: 100%">100%</div>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-success">ممتاز</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterByPeriod() {
    const period = event.target.value;
    console.log('تم تطبيق فلتر الفترة:', period);

    // إظهار رسالة للمستخدم
    const toast = document.createElement('div');
    toast.className = 'alert alert-info alert-dismissible fade show position-fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <strong>تم تطبيق الفلتر!</strong> عرض بيانات ${period === 'today' ? 'اليوم' : period === 'week' ? 'هذا الأسبوع' : period === 'month' ? 'هذا الشهر' : 'الفترة المخصصة'}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function filterByDepartment() {
    const department = event.target.value;
    console.log('تم تطبيق فلتر القسم:', department);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (department === '' || row.textContent.includes(department)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // إظهار رسالة
    showToast(`تم تطبيق فلتر القسم: ${department || 'جميع الأقسام'}`);
}

function changeReportType() {
    const reportType = event.target.value;
    console.log('تم تغيير نوع التقرير:', reportType);
    showToast(`تم تغيير نوع التقرير إلى: ${reportType === 'summary' ? 'ملخص' : reportType === 'detailed' ? 'تفصيلي' : 'يومي'}`);
}

function exportAttendance() {
    // محاكاة تصدير البيانات
    const data = [
        ['الموظف', 'القسم', 'أيام الحضور', 'أيام الغياب', 'معدل الحضور'],
        ['أحمد محمد', 'تقنية المعلومات', '22', '2', '92%'],
        ['فاطمة علي', 'المالية', '24', '0', '100%'],
        // يمكن إضافة المزيد من البيانات هنا
    ];

    console.log('بيانات التصدير:', data);
    showToast('تم تصدير تقرير الحضور بنجاح!', 'success');
}

function printAttendance() {
    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.btn, .alert, .card-header .btn-group');
    elementsToHide.forEach(el => el.style.display = 'none');

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(el => el.style.display = '');
    }, 1000);
}

function generateAttendanceReport() {
    // محاكاة إنشاء تقرير مخصص
    showToast('جاري إنشاء تقرير الحضور المخصص...', 'info');

    setTimeout(() => {
        showToast('تم إنشاء تقرير الحضور المخصص بنجاح!', 'success');
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    toast.innerHTML = `
        <strong>${type === 'success' ? 'نجح!' : type === 'warning' ? 'تحذير!' : 'معلومة:'}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 4 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

// إضافة وظائف تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للجدول
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // إضافة إمكانية البحث السريع
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'البحث السريع في الجدول...';
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // إدراج حقل البحث قبل الجدول
    const tableCard = document.querySelector('.table-responsive').parentNode;
    tableCard.insertBefore(searchInput, tableCard.querySelector('.table-responsive'));
});
</script>
<style>
.attendance-card {
    transition: transform 0.2s ease-in-out;
}

.attendance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.progress-bar {
    transition: width 0.3s ease-in-out;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

.avatar {
    transition: all 0.2s ease-in-out;
}

.avatar:hover {
    transform: scale(1.1);
}

.chart-placeholder {
    transition: all 0.3s ease-in-out;
}

.chart-placeholder:hover {
    background: linear-gradient(45deg, #e9ecef, #f8f9fa);
}

.alert {
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.card-header {
    font-weight: 600;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.department-card {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.department-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

{% endblock %}
