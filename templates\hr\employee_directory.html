{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-address-book text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">دليل شامل لجميع الموظفين ومعلومات الاتصال</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-info" onclick="printDirectory()">
                        <i class="fas fa-print me-2"></i>
                        طباعة الدليل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label">البحث:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="ابحث بالاسم أو المنصب أو القسم..." onkeyup="searchEmployees()">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="sales">المبيعات</option>
                                <option value="production">الإنتاج</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">العرض:</label>
                            <select class="form-select" id="viewSelect" onchange="changeView()">
                                <option value="list" selected>قائمة</option>
                                <option value="cards">بطاقات</option>
                                <option value="grid">شبكة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الترتيب:</label>
                            <select class="form-select" onchange="sortEmployees()">
                                <option value="name">الاسم</option>
                                <option value="department">القسم</option>
                                <option value="position">المنصب</option>
                                <option value="hire_date">تاريخ التوظيف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-1">
                                <button class="btn btn-primary" onclick="exportDirectory()">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-success" onclick="exportVCards()">
                                    <i class="fas fa-id-card"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Directory -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        دليل الموظفين
                    </h5>
                </div>
                <div class="card-body">
                    <!-- List View -->
                    <div id="listView" class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الموظف</th>
                                    <th>المنصب</th>
                                    <th>القسم</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>التحويلة</th>
                                    <th>المكتب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-3" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                                                {{ employee.name|first }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ employee.name }}</h6>
                                                <small class="text-muted">{{ employee.employee_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ employee.position }}</strong>
                                        <br>
                                        <small class="text-muted">{{ employee.level }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ employee.department }}</span>
                                    </td>
                                    <td>
                                        <a href="tel:{{ employee.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone text-success me-1"></i>
                                            {{ employee.phone }}
                                        </a>
                                        {% if employee.mobile %}
                                        <br>
                                        <a href="tel:{{ employee.mobile }}" class="text-decoration-none">
                                            <i class="fas fa-mobile-alt text-primary me-1"></i>
                                            {{ employee.mobile }}
                                        </a>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="mailto:{{ employee.email }}" class="text-decoration-none">
                                            <i class="fas fa-envelope text-info me-1"></i>
                                            {{ employee.email }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if employee.extension %}
                                            <span class="badge bg-secondary">{{ employee.extension }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-warning me-1"></i>
                                        {{ employee.office_location }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" title="عرض الملف الشخصي">
                                                <i class="fas fa-user"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="إرسال رسالة">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="طباعة بطاقة">
                                                <i class="fas fa-id-card"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-address-book fa-3x mb-3 d-block"></i>
                                        لا توجد موظفين للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Cards View -->
                    <div id="cardsView" class="row" style="display: none;">
                        {% for employee in employees %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card employee-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar bg-primary text-white rounded-circle me-3" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                                            {{ employee.name|first }}
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-1">{{ employee.name }}</h5>
                                            <p class="text-muted mb-0">{{ employee.employee_id }}</p>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-primary">{{ employee.position }}</h6>
                                        <small class="text-muted">{{ employee.level }}</small>
                                    </div>

                                    <div class="mb-2">
                                        <span class="badge bg-info">{{ employee.department }}</span>
                                    </div>

                                    <div class="contact-info">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-phone text-success me-2"></i>
                                            <a href="tel:{{ employee.phone }}" class="text-decoration-none">{{ employee.phone }}</a>
                                        </div>
                                        {% if employee.mobile %}
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-mobile-alt text-primary me-2"></i>
                                            <a href="tel:{{ employee.mobile }}" class="text-decoration-none">{{ employee.mobile }}</a>
                                        </div>
                                        {% endif %}
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-envelope text-info me-2"></i>
                                            <a href="mailto:{{ employee.email }}" class="text-decoration-none small">{{ employee.email }}</a>
                                        </div>
                                        {% if employee.extension %}
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-phone-square text-secondary me-2"></i>
                                            <span>تحويلة: {{ employee.extension }}</span>
                                        </div>
                                        {% endif %}
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-map-marker-alt text-warning me-2"></i>
                                            <small>{{ employee.office_location }}</small>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button class="btn btn-sm btn-outline-primary" title="عرض الملف الشخصي">
                                            <i class="fas fa-user me-1"></i>
                                            الملف الشخصي
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" title="إرسال رسالة">
                                            <i class="fas fa-envelope me-1"></i>
                                            رسالة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Grid View -->
                    <div id="gridView" class="row" style="display: none;">
                        {% for employee in employees %}
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="card employee-grid-card h-100 text-center">
                                <div class="card-body">
                                    <div class="avatar bg-primary text-white rounded-circle mx-auto mb-3" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                                        {{ employee.name|first }}
                                    </div>
                                    <h6 class="card-title">{{ employee.name }}</h6>
                                    <p class="text-muted small mb-2">{{ employee.position }}</p>
                                    <span class="badge bg-info mb-2">{{ employee.department }}</span>
                                    <div class="contact-actions">
                                        <a href="tel:{{ employee.phone }}" class="btn btn-sm btn-outline-success me-1" title="اتصال">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                        <a href="mailto:{{ employee.email }}" class="btn btn-sm btn-outline-info me-1" title="بريد إلكتروني">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Quick Access -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        الوصول السريع للأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dept in departments %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title text-primary">{{ dept.name }}</h6>
                                        <span class="badge bg-primary">{{ dept.employee_count }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>المدير:</strong> {{ dept.manager }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>الهاتف:</strong> 
                                        <a href="tel:{{ dept.phone }}" class="text-decoration-none">{{ dept.phone }}</a>
                                    </div>
                                    <div class="mb-3">
                                        <strong>الموقع:</strong> {{ dept.location }}
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary w-100" onclick="showDepartmentEmployees('{{ dept.id }}')">
                                        عرض الموظفين
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Contacts -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        أرقام الطوارئ والاتصالات المهمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-shield-alt fa-2x text-danger mb-2"></i>
                                <h6>الأمن</h6>
                                <a href="tel:123" class="btn btn-danger btn-sm">123</a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-fire-extinguisher fa-2x text-warning mb-2"></i>
                                <h6>الإطفاء</h6>
                                <a href="tel:180" class="btn btn-warning btn-sm">180</a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-ambulance fa-2x text-success mb-2"></i>
                                <h6>الإسعاف</h6>
                                <a href="tel:123" class="btn btn-success btn-sm">123</a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-tools fa-2x text-info mb-2"></i>
                                <h6>الصيانة</h6>
                                <a href="tel:456" class="btn btn-info btn-sm">456</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function searchEmployees() {
    const searchTerm = event.target.value.toLowerCase();

    // البحث في العرض الحالي
    const currentView = document.getElementById('viewSelect').value;

    if (currentView === 'list') {
        const rows = document.querySelectorAll('#listView tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    } else if (currentView === 'cards') {
        const cards = document.querySelectorAll('#cardsView .employee-card');
        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.parentElement.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    } else if (currentView === 'grid') {
        const gridCards = document.querySelectorAll('#gridView .employee-grid-card');
        gridCards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.parentElement.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    if (searchTerm) {
        showToast(`البحث عن: ${searchTerm}`);
    }
}

function filterByDepartment() {
    const department = event.target.value;
    const currentView = document.getElementById('viewSelect').value;

    if (currentView === 'list') {
        const rows = document.querySelectorAll('#listView tbody tr');
        rows.forEach(row => {
            if (department === '' || row.textContent.includes(department)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    } else if (currentView === 'cards') {
        const cards = document.querySelectorAll('#cardsView .employee-card');
        cards.forEach(card => {
            if (department === '' || card.textContent.includes(department)) {
                card.parentElement.style.display = '';
            } else {
                card.parentElement.style.display = 'none';
            }
        });
    } else if (currentView === 'grid') {
        const gridCards = document.querySelectorAll('#gridView .employee-grid-card');
        gridCards.forEach(card => {
            if (department === '' || card.textContent.includes(department)) {
                card.parentElement.style.display = '';
            } else {
                card.parentElement.style.display = 'none';
            }
        });
    }

    showToast(`تم تطبيق فلتر القسم: ${department || 'جميع الأقسام'}`);
}

function changeView() {
    const viewType = event.target.value;

    // إخفاء جميع العروض
    document.getElementById('listView').style.display = 'none';
    document.getElementById('cardsView').style.display = 'none';
    document.getElementById('gridView').style.display = 'none';

    // إظهار العرض المحدد
    if (viewType === 'list') {
        document.getElementById('listView').style.display = 'block';
    } else if (viewType === 'cards') {
        document.getElementById('cardsView').style.display = 'block';
    } else if (viewType === 'grid') {
        document.getElementById('gridView').style.display = 'block';
    }

    const viewNames = {
        'list': 'قائمة',
        'cards': 'بطاقات',
        'grid': 'شبكة'
    };

    showToast(`تم تغيير العرض إلى: ${viewNames[viewType]}`);
}

function sortEmployees() {
    const sortBy = event.target.value;
    showToast(`تم تطبيق الترتيب حسب: ${sortBy}`);
}

function exportDirectory() {
    // محاكاة تصدير البيانات
    const data = [
        ['الاسم', 'المنصب', 'القسم', 'الهاتف', 'البريد الإلكتروني', 'المكتب'],
        ['أحمد محمد علي', 'مدير المشاريع', 'تقنية المعلومات', '01234567890', '<EMAIL>', 'الطابق الثالث - مكتب 301'],
        // يمكن إضافة المزيد من البيانات هنا
    ];

    console.log('بيانات التصدير:', data);
    showToast('تم تصدير دليل الموظفين بنجاح!', 'success');
}

function exportVCards() {
    // محاكاة تصدير بطاقات الاتصال
    showToast('تم تصدير بطاقات الاتصال بنجاح!', 'success');
}

function printDirectory() {
    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.btn, .form-control, .form-select');
    elementsToHide.forEach(el => el.style.display = 'none');

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(el => el.style.display = '');
    }, 1000);
}

function showDepartmentEmployees(deptId) {
    // تطبيق فلتر القسم
    const departmentSelect = document.querySelector('select[onchange="filterByDepartment()"]');
    const deptNames = {
        'it': 'تقنية المعلومات',
        'finance': 'المالية',
        'sales': 'المبيعات',
        'production': 'الإنتاج',
        'hr': 'الموارد البشرية'
    };

    departmentSelect.value = deptNames[deptId] || '';
    filterByDepartment.call(departmentSelect);

    showToast(`عرض موظفي قسم: ${deptNames[deptId] || deptId}`);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    toast.innerHTML = `
        <strong>${type === 'success' ? 'نجح!' : type === 'warning' ? 'تحذير!' : 'معلومة:'}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 4 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

// إضافة وظائف تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const employeeCards = document.querySelectorAll('.employee-card');
    employeeCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 16px rgba(0,0,0,0.1)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // إضافة تأثيرات للبطاقات الشبكية
    const gridCards = document.querySelectorAll('.employee-grid-card');
    gridCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.15)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '';
        });
    });

    // إضافة تأثيرات لبطاقات الأقسام
    const deptCards = document.querySelectorAll('.card.border-primary');
    deptCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.1)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // إضافة البحث السريع
    const searchInput = document.querySelector('input[onkeyup="searchEmployees()"]');
    if (searchInput) {
        searchInput.addEventListener('input', searchEmployees);
    }
});
</script>

<style>
.employee-card {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef;
}

.employee-grid-card {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef;
}

.avatar {
    transition: all 0.2s ease-in-out;
}

.avatar:hover {
    transform: scale(1.1);
}

.contact-info a {
    color: #6c757d;
    transition: color 0.2s ease-in-out;
}

.contact-info a:hover {
    color: #0d6efd;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

.card-header {
    font-weight: 600;
}

.contact-actions .btn {
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .employee-card {
        margin-bottom: 1rem;
    }

    .contact-info {
        font-size: 0.875rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.alert {
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-success {
    border-left-color: #198754;
}

.alert-warning {
    border-left-color: #ffc107;
}
</style>
{% endblock %}
