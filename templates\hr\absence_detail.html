{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-times text-secondary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تفاصيل سجل الغياب/التأخير</p>
                </div>
                <div>
                    <a href="{% url 'hr:absence_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                    <a href="{% url 'hr:absence_edit' absence_record.pk %}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    {% if absence_record.status == 'PENDING' %}
                    <a href="{% url 'hr:absence_approve' absence_record.pk %}" class="btn btn-success me-2" onclick="return confirm('هل تريد اعتماد هذا السجل؟')">
                        <i class="fas fa-check me-2"></i>
                        اعتماد
                    </a>
                    <a href="{% url 'hr:absence_reject' absence_record.pk %}" class="btn btn-danger me-2" onclick="return confirm('هل تريد رفض هذا السجل؟')">
                        <i class="fas fa-times me-2"></i>
                        رفض
                    </a>
                    {% endif %}
                    <form method="post" action="{% url 'hr:absence_delete' absence_record.pk %}" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Details -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل السجل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات الموظف -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>
                                معلومات الموظف
                            </h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">اسم الموظف:</td>
                                    <td>{{ absence_record.employee.full_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">رقم الموظف:</td>
                                    <td>{{ absence_record.employee.employee_number }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">القسم:</td>
                                    <td>{{ absence_record.employee.department.name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">المنصب:</td>
                                    <td>{{ absence_record.employee.position.name }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- معلومات المخالفة -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                معلومات المخالفة
                            </h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">نوع المخالفة:</td>
                                    <td>
                                        <span class="badge {{ absence_record.absence_type_badge_class }}">
                                            {{ absence_record.get_absence_type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">التاريخ:</td>
                                    <td>{{ absence_record.date|date:"Y-m-d" }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">المدة:</td>
                                    <td>{{ absence_record.duration_display }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الحالة:</td>
                                    <td>
                                        <span class="badge {{ absence_record.status_badge_class }}">
                                            {{ absence_record.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <!-- الأوقات -->
                        <div class="col-md-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-clock me-2"></i>
                                الأوقات
                            </h6>
                            <table class="table table-borderless">
                                {% if absence_record.time_from %}
                                <tr>
                                    <td class="fw-bold">من وقت:</td>
                                    <td>{{ absence_record.time_from|time:"H:i" }}</td>
                                </tr>
                                {% endif %}
                                {% if absence_record.time_to %}
                                <tr>
                                    <td class="fw-bold">إلى وقت:</td>
                                    <td>{{ absence_record.time_to|time:"H:i" }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold">المدة بالساعات:</td>
                                    <td>{{ absence_record.duration_hours }} ساعة</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">المدة بالدقائق:</td>
                                    <td>{{ absence_record.duration_minutes }} دقيقة</td>
                                </tr>
                            </table>
                        </div>

                        <!-- المالية -->
                        <div class="col-md-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المعلومات المالية
                            </h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">مبلغ الخصم:</td>
                                    <td class="text-danger fw-bold">
                                        {{ absence_record.deduction_amount }} {{ absence_record.currency.symbol }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">العملة:</td>
                                    <td>{{ absence_record.currency.name }}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- معلومات النظام -->
                        <div class="col-md-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cog me-2"></i>
                                معلومات النظام
                            </h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">تم الإنشاء بواسطة:</td>
                                    <td>{{ absence_record.created_by.get_full_name|default:absence_record.created_by.username }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الإنشاء:</td>
                                    <td>{{ absence_record.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% if absence_record.updated_by %}
                                <tr>
                                    <td class="fw-bold">تم التحديث بواسطة:</td>
                                    <td>{{ absence_record.updated_by.get_full_name|default:absence_record.updated_by.username }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ التحديث:</td>
                                    <td>{{ absence_record.updated_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% endif %}
                                {% if absence_record.approved_by %}
                                <tr>
                                    <td class="fw-bold">تمت الموافقة بواسطة:</td>
                                    <td>{{ absence_record.approved_by.get_full_name|default:absence_record.approved_by.username }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الموافقة:</td>
                                    <td>{{ absence_record.approved_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <hr>

                    <!-- السبب والتفاصيل -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-comment me-2"></i>
                                السبب
                            </h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ absence_record.reason|linebreaks }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                تفاصيل إضافية
                            </h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {% if absence_record.details %}
                                        {{ absence_record.details|linebreaks }}
                                    {% else %}
                                        <span class="text-muted">لا توجد تفاصيل إضافية</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    {% if absence_record.notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات
                            </h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ absence_record.notes|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- ملاحظات الموافقة -->
                    {% if absence_record.approval_notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                ملاحظات الموافقة
                            </h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ absence_record.approval_notes|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- المرفقات -->
                    {% if absence_record.attachment %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-paperclip me-2"></i>
                                المرفقات
                            </h6>
                            <div class="card">
                                <div class="card-body">
                                    <a href="{{ absence_record.attachment.url }}" target="_blank" class="btn btn-outline-primary">
                                        <i class="fas fa-download me-2"></i>
                                        تحميل المرفق
                                    </a>
                                    <small class="text-muted ms-2">
                                        {{ absence_record.attachment.name|default_if_none:"" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
});
</script>
{% endblock %} 