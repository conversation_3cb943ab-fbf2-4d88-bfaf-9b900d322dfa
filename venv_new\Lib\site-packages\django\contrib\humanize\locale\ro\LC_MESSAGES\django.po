# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2018
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <razvan.<PERSON><PERSON><PERSON>@gmail.com>, 2016
# <PERSON><PERSON>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2018-05-18 06:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Humanize"
msgstr "Umanizare"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "al {}-lea"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "al {}-lea"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f milion"
msgstr[1] "%(value).1f milioane"
msgstr[2] "%(value).1f de milioane"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milion"
msgstr[1] "%(value)s milioane"
msgstr[2] "%(value)s de milioane"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f miliard"
msgstr[1] "%(value).1f miliarde"
msgstr[2] "%(value).1f de miliarde"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliard"
msgstr[1] "%(value)s miliarde"
msgstr[2] "%(value)s de miliarde"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f trilion"
msgstr[1] "%(value).1f trilioane"
msgstr[2] "%(value).1f de trilioane"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s trilion"
msgstr[1] "%(value)s trilioane"
msgstr[2] "%(value)s de trilioane"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f cuadrilion"
msgstr[1] "%(value).1f cuadrilioane"
msgstr[2] "%(value).1f de cuadrilioane"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s cuadrilion"
msgstr[1] "%(value)s cuadrilioane"
msgstr[2] "%(value)s de cuadrilioane"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f cuntilion"
msgstr[1] "%(value).1f cuntilioane"
msgstr[2] "%(value).1f de cuntilioane"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s cuntilion"
msgstr[1] "%(value)s cuntilioane"
msgstr[2] "%(value)s de cuntilioane"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f sextilion"
msgstr[1] "%(value).1f sextilioane"
msgstr[2] "%(value).1f de sextilioane"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sextilion"
msgstr[1] "%(value)s sextilioane"
msgstr[2] "%(value)s de sextilioane"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f septilion"
msgstr[1] "%(value).1f septilioane"
msgstr[2] "%(value).1f de septilioane"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septilion"
msgstr[1] "%(value)s septilioane"
msgstr[2] "%(value)s de septilioane"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f octilion"
msgstr[1] "%(value).1f octilioane"
msgstr[2] "%(value).1f de octilioane"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octilion"
msgstr[1] "%(value)s octilioane"
msgstr[2] "%(value)s de octilioane"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f nonilion"
msgstr[1] "%(value).1f nonilioane"
msgstr[2] "%(value).1f de nonilioane"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonilion"
msgstr[1] "%(value)s nonilioane"
msgstr[2] "%(value)s de nonilioane"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f decilion"
msgstr[1] "%(value).1f decilioane"
msgstr[2] "%(value).1f de decilioane"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s decilion"
msgstr[1] "%(value)s decilioane"
msgstr[2] "%(value)s de decilioane"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f googol"
msgstr[1] "%(value).1f googol"
msgstr[2] "%(value).1f de googol"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s de googol"

msgid "one"
msgstr "unu"

msgid "two"
msgstr "doi"

msgid "three"
msgstr "trei"

msgid "four"
msgstr "patru"

msgid "five"
msgstr "cinci"

msgid "six"
msgstr "șase"

msgid "seven"
msgstr "șapte"

msgid "eight"
msgstr "opt"

msgid "nine"
msgstr "nouă"

msgid "today"
msgstr "astăzi"

msgid "tomorrow"
msgstr "mâine"

msgid "yesterday"
msgstr "ieri"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "Acum %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "Acum o oră"
msgstr[1] "Acum %(count)s ore"
msgstr[2] "Acum %(count)s de ore"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "Acum un minut"
msgstr[1] "Acum %(count)s minute"
msgstr[2] "Acum %(count)s de minute"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "Acum o secundă"
msgstr[1] "Acum %(count)s secunde"
msgstr[2] "Acum %(count)s de secunde"

msgid "now"
msgstr "acum"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "Într-o secundă"
msgstr[1] "În %(count)s secunde"
msgstr[2] "În %(count)s de secunde"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "Într-un minut"
msgstr[1] "În %(count)s minute"
msgstr[2] "În %(count)s de minute"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "Într-o oră"
msgstr[1] "În %(count)s ore"
msgstr[2] "În %(count)s de ore"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "În %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d an"
msgstr[1] "%d ani"
msgstr[2] "%d de ani"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d lună"
msgstr[1] "%d luni"
msgstr[2] "%d de luni"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d săptămână"
msgstr[1] "%d săptămâni"
msgstr[2] "%d de săptămâni"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d zi"
msgstr[1] "%d zile"
msgstr[2] "%d de zile"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d oră"
msgstr[1] "%d ore"
msgstr[2] "%d de ore"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minut"
msgstr[1] "%d minute"
msgstr[2] "%d de minute"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d an"
msgstr[1] "%d ani"
msgstr[2] "%d de ani"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d lună"
msgstr[1] "%d luni"
msgstr[2] "%d de luni"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d săptămână"
msgstr[1] "%d săptămâni"
msgstr[2] "%d de săptămâni"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d zi"
msgstr[1] "%d zile"
msgstr[2] "%d de zile"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d oră"
msgstr[1] "%d ore"
msgstr[2] "%d de ore"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minut"
msgstr[1] "%d minute"
msgstr[2] "%d de minute"
