# Generated by Django 5.2.2 on 2025-06-28 03:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EarnedDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('discount_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الخصم')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('discount_type', models.CharField(choices=[('VOLUME', 'خصم الكمية'), ('PAYMENT_TERMS', 'خصم شروط الدفع'), ('SEASONAL', 'خصم موسمي'), ('LOYALTY', 'خصم الولاء'), ('PROMOTIONAL', 'خصم ترويجي'), ('EARLY_PAYMENT', 'خصم الدفع المبكر'), ('BULK_ORDER', 'خصم الطلبات الكبيرة'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع الخصم')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('base_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الأساسي')),
                ('minimum_quantity', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='الحد الأدنى للكمية')),
                ('minimum_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأدنى للمبلغ')),
                ('payment_days', models.IntegerField(blank=True, null=True, verbose_name='أيام الدفع المبكر')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('APPROVED', 'معتمد'), ('APPLIED', 'مطبق'), ('REJECTED', 'مرفوض'), ('EXPIRED', 'منتهي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('valid_from', models.DateField(verbose_name='صالح من')),
                ('valid_until', models.DateField(blank=True, null=True, verbose_name='صالح حتى')),
                ('applied_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التطبيق')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(limit_choices_to={'is_active_supplier': True, 'person_type__in': ['SUPPLIER', 'BOTH']}, on_delete=django.db.models.deletion.PROTECT, to='definitions.person', verbose_name='المورد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'خصم مكتسب',
                'verbose_name_plural': 'الخصومات المكتسبة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='EarnedDiscountItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('discount', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.earneddiscount', verbose_name='الخصم')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف خصم مكتسب',
                'verbose_name_plural': 'أصناف الخصومات المكتسبة',
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('supplier_invoice_number', models.CharField(blank=True, max_length=50, verbose_name='رقم فاتورة المورد')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المجموع الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ المدفوع')),
                ('remaining_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ المتبقي')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('CONFIRMED', 'مؤكدة'), ('RECEIVED', 'مستلمة'), ('PAID', 'مدفوعة'), ('CANCELLED', 'ملغية')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('supplier', models.ForeignKey(limit_choices_to={'is_active_supplier': True, 'person_type__in': ['SUPPLIER', 'BOTH']}, on_delete=django.db.models.deletion.PROTECT, to='definitions.person', verbose_name='المورد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'فاتورة مشتريات',
                'verbose_name_plural': 'فواتير المشتريات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.AddField(
            model_name='earneddiscount',
            name='reference_invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='purchases.purchaseinvoice', verbose_name='الفاتورة المرجعية'),
        ),
        migrations.CreateModel(
            name='PurchaseInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الضريبة')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseinvoice', verbose_name='الفاتورة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف فاتورة مشتريات',
                'verbose_name_plural': 'أصناف فواتير المشتريات',
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الأمر')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('SENT', 'مرسل'), ('CONFIRMED', 'مؤكد'), ('PARTIALLY_RECEIVED', 'مستلم جزئياً'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(limit_choices_to={'is_active_supplier': True, 'person_type__in': ['SUPPLIER', 'BOTH']}, on_delete=django.db.models.deletion.PROTECT, to='definitions.person', verbose_name='المورد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'أمر شراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.AddField(
            model_name='earneddiscount',
            name='reference_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='purchases.purchaseorder', verbose_name='أمر الشراء المرجعي'),
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('received_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستلمة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseorder', verbose_name='الأمر')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف أمر شراء',
                'verbose_name_plural': 'أصناف أوامر الشراء',
            },
        ),
        migrations.CreateModel(
            name='PurchaseReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('return_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المرتجع')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب الإرجاع')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('REJECTED', 'مرفوض')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('original_invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='purchases.purchaseinvoice', verbose_name='الفاتورة الأصلية')),
                ('supplier', models.ForeignKey(limit_choices_to={'is_active_supplier': True, 'person_type__in': ['SUPPLIER', 'BOTH']}, on_delete=django.db.models.deletion.PROTECT, to='definitions.person', verbose_name='المورد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'مرتجع مشتريات',
                'verbose_name_plural': 'مرتجعات المشتريات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('return_doc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchasereturn', verbose_name='المرتجع')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'صنف مرتجع مشتريات',
                'verbose_name_plural': 'أصناف مرتجعات المشتريات',
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المورد')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان')),
                ('payment_terms', models.IntegerField(default=0, verbose_name='شروط الدفع (أيام)')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
    ]
