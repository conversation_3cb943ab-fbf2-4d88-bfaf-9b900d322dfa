# Generated by Django 5.2.2 on 2025-07-04 15:33

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0002_allowancetype_deductiontype'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='allowancededuction',
            options={'ordering': ['-date'], 'verbose_name': 'إضافة/خصم', 'verbose_name_plural': 'الإضافات والخصومات'},
        ),
        migrations.RemoveField(
            model_name='allowancededuction',
            name='name',
        ),
        migrations.AddField(
            model_name='allowancededuction',
            name='allowance_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.allowancetype', verbose_name='نوع الإضافة'),
        ),
        migrations.AddField(
            model_name='allowancededuction',
            name='deduction_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.deductiontype', verbose_name='نوع الخصم'),
        ),
    ]
