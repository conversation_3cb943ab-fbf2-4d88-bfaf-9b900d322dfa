{% extends 'base/base.html' %}
{% load static %}

{% block title %}المؤهلات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <h2 class="mb-0">
                        <i class="fas fa-graduation-cap text-warning me-2"></i>
                        المؤهلات
                    </h2>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'hr:qualification_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة مؤهل جديد
                    </a>
                    <button class="btn btn-outline-success" onclick="exportTableToCSV('qualifications.csv')" title="تصدير إلى CSV">
                        <i class="fas fa-file-csv"></i>
                    </button>
                    <button class="btn btn-outline-dark" onclick="window.print()" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- كروت إحصائية -->
    <div class="row mb-4 g-3">
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ total_qualifications|default:0 }}</div>
                        <div>إجمالي المؤهلات</div>
                    </div>
                    <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ active_qualifications|default:0 }}</div>
                        <div>المؤهلات النشطة</div>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ inactive_qualifications|default:0 }}</div>
                        <div>غير النشطة</div>
                    </div>
                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ levels_count|default:0 }}</div>
                        <div>المستويات المختلفة</div>
                    </div>
                    <i class="fas fa-layer-group fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- بحث وتصفية -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" class="row g-2 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">بحث بالاسم أو الكود</label>
                    <input type="text" name="q" class="form-control" placeholder="بحث..." value="{{ search_query|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">المستوى</label>
                    <select name="level" class="form-select">
                        <option value="">كل المستويات</option>
                        <option value="PRIMARY" {% if level_filter == 'PRIMARY' %}selected{% endif %}>ابتدائي</option>
                        <option value="INTERMEDIATE" {% if level_filter == 'INTERMEDIATE' %}selected{% endif %}>متوسط</option>
                        <option value="SECONDARY" {% if level_filter == 'SECONDARY' %}selected{% endif %}>ثانوي</option>
                        <option value="DIPLOMA" {% if level_filter == 'DIPLOMA' %}selected{% endif %}>دبلوم</option>
                        <option value="BACHELOR" {% if level_filter == 'BACHELOR' %}selected{% endif %}>بكالوريوس</option>
                        <option value="MASTER" {% if level_filter == 'MASTER' %}selected{% endif %}>ماجستير</option>
                        <option value="PHD" {% if level_filter == 'PHD' %}selected{% endif %}>دكتوراه</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex gap-2">
                    <button class="btn btn-outline-primary w-100" type="submit"><i class="fas fa-search"></i> بحث</button>
                    <a href="?" class="btn btn-outline-secondary"><i class="fas fa-times"></i></a>
                </div>
            </form>
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    يمكنك استخدام أزرار التصدير والطباعة في أعلى الصفحة
                </small>
                <small class="text-muted">
                    <i class="fas fa-filter me-1"></i>
                    النتائج: {{ items|length }} مؤهل
                </small>
            </div>
        </div>
    </div>

    <!-- جدول المؤهلات -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="qualificationsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>المؤهل</th>
                            <th>الكود</th>
                            <th>المستوى</th>
                            <th>المدة</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-graduation-cap text-info me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ item.name }}</div>
                                        {% if item.description %}
                                            <small class="text-muted">{{ item.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ item.code }}</span>
                            </td>
                            <td>
                                {% if item.level %}
                                    <span class="badge bg-secondary">{{ item.get_level_display }}</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.duration %}
                                    <span class="badge bg-primary">{{ item.duration }}</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ item.created_at|date:"Y/m/d"|default:"غير محدد" }}
                                </small>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'hr:qualification_detail' item.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:qualification_edit' item.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="حذف" 
                                            onclick="confirmDelete('{{ item.name }}', '{% url 'hr:qualification_delete' item.pk %}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center text-muted py-5">
                                <i class="fas fa-graduation-cap fa-3x mb-3 text-muted"></i>
                                <h5 class="text-muted">لا توجد مؤهلات للعرض</h5>
                                <p class="text-muted mb-3">ابدأ بإضافة أول مؤهل في النظام</p>
                                <a href="{% url 'hr:qualification_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة مؤهل جديد
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="صفحات المؤهلات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&q={{ search_query|default:'' }}&level={{ level_filter|default:'' }}&status={{ status_filter|default:'' }}">
                            <i class="fas fa-chevron-right"></i> السابق
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-right"></i> السابق</span>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}&q={{ search_query|default:'' }}&level={{ level_filter|default:'' }}&status={{ status_filter|default:'' }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&q={{ search_query|default:'' }}&level={{ level_filter|default:'' }}&status={{ status_filter|default:'' }}">
                            التالي <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">التالي <i class="fas fa-chevron-left"></i></span>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<script>
function confirmDelete(qualificationName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف المؤهل "${qualificationName}"؟`)) {
        // إنشاء نموذج حذف مخفي
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        
        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function exportTableToCSV(filename) {
    var csv = [];
    var rows = document.querySelectorAll("#qualificationsTable tr");
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll("td, th");
        
        for (var j = 0; j < cols.length; j++) {
            // إزالة HTML tags والحصول على النص فقط
            var text = cols[j].innerText.replace(/\s+/g, ' ').trim();
            row.push('"' + text + '"');
        }
        
        csv.push(row.join(","));
    }

    // تحميل الملف
    var csvFile = new Blob([csv.join("\n")], {type: "text/csv"});
    var downloadLink = document.createElement("a");
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
}

// تفعيل البحث المباشر
document.addEventListener('DOMContentLoaded', function() {
    var searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    }
});
</script>
{% endblock %}
