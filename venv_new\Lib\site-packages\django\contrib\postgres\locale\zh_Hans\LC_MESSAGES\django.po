# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015,2017
# Li<PERSON> <<EMAIL>>, 2016
# Li<PERSON> <<EMAIL>>, 2016
# wang <PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: 高乐喆 <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL 扩展。"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "数组中的%(nth)s项目没有验证："

msgid "Nested arrays must have the same length."
msgstr "嵌套数组必须是相同长度。"

msgid "Map of strings to strings/nulls"
msgstr "字符串到字符串/空的映射"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "“%(key)s”的值不是一个字符串或null"

msgid "Could not load JSON data."
msgstr "不能加载JSON数据。"

msgid "Input must be a JSON dictionary."
msgstr "输入必须是JSON字典。"

msgid "Enter two valid values."
msgstr "输入两个有效的值。"

msgid "The start of the range must not exceed the end of the range."
msgstr "区间开头不能超过区间结尾。"

msgid "Enter two whole numbers."
msgstr "输入两个整数。"

msgid "Enter two numbers."
msgstr "输入两个数字。"

msgid "Enter two valid date/times."
msgstr "输入两个有效的日期／时间。"

msgid "Enter two valid dates."
msgstr "输入两个有效日期。"

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] "列表已包含 %(show_value)d 项，不应该超过 %(limit_value)d 项。"

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] "列表已包含 %(show_value)d 项，不应该少于 %(limit_value)d 项。"

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "某些键缺失：%(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "包含未知的键：%(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr "确保范围的上限不大于%(limit_value)s。"

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr "确保范围的下限不小于%(limit_value)s。"
