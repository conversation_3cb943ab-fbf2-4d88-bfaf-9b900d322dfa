{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-times text-danger me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل عن إجازات الموظفين والأرصدة المتاحة</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-danger" onclick="generateVacationReport()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vacation Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white vacation-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                    <h3>{{ total_vacation_days }}</h3>
                    <p class="mb-0">إجمالي أيام الإجازات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white vacation-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3>{{ approved_requests }}</h3>
                    <p class="mb-0">طلبات موافق عليها</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white vacation-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3>{{ pending_requests }}</h3>
                    <p class="mb-0">طلبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white vacation-card">
                <div class="card-body text-center">
                    <i class="fas fa-hourglass-half fa-2x mb-2"></i>
                    <h3>{{ remaining_balance }}</h3>
                    <p class="mb-0">الرصيد المتبقي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Vacation Types Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع أنواع الإجازات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-primary text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>45</strong>
                                </div>
                                <h6 class="text-primary">إجازة سنوية</h6>
                                <small class="text-muted">60%</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-success text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>12</strong>
                                </div>
                                <h6 class="text-success">إجازة مرضية</h6>
                                <small class="text-muted">16%</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-warning text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>8</strong>
                                </div>
                                <h6 class="text-warning">إجازة طارئة</h6>
                                <small class="text-muted">11%</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-info text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>5</strong>
                                </div>
                                <h6 class="text-info">إجازة أمومة</h6>
                                <small class="text-muted">7%</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-secondary text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>3</strong>
                                </div>
                                <h6 class="text-secondary">إجازة دراسية</h6>
                                <small class="text-muted">4%</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="vacation-circle bg-dark text-white mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>2</strong>
                                </div>
                                <h6 class="text-dark">أخرى</h6>
                                <small class="text-muted">2%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">نوع الإجازة:</label>
                            <select class="form-select" onchange="filterByVacationType()">
                                <option value="">جميع الأنواع</option>
                                <option value="إجازة سنوية">إجازة سنوية</option>
                                <option value="إجازة مرضية">إجازة مرضية</option>
                                <option value="إجازة طارئة">إجازة طارئة</option>
                                <option value="إجازة أمومة">إجازة أمومة</option>
                                <option value="إجازة دراسية">إجازة دراسية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="تقنية المعلومات">تقنية المعلومات</option>
                                <option value="المالية">المالية</option>
                                <option value="المبيعات">المبيعات</option>
                                <option value="الإنتاج">الإنتاج</option>
                                <option value="الموارد البشرية">الموارد البشرية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">حالة الرصيد:</label>
                            <select class="form-select" onchange="filterByBalance()">
                                <option value="">جميع الحالات</option>
                                <option value="جيد">جيد (أكثر من 15 يوم)</option>
                                <option value="متوسط">متوسط (5-15 يوم)</option>
                                <option value="منخفض">منخفض (أقل من 5 أيام)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportVacations()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Excel
                                </button>
                                <button class="btn btn-info" onclick="printVacations()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vacation Balance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        أرصدة الإجازات للموظفين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الرصيد السنوي</th>
                                    <th>المستخدم</th>
                                    <th>المتبقي</th>
                                    <th>آخر إجازة</th>
                                    <th>نوع آخر إجازة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for vacation in vacation_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                {{ vacation.employee_name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ vacation.employee_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ vacation.employee_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ vacation.department }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ vacation.annual_balance }} يوم</strong>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ vacation.used_days }} يوم</span>
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ vacation.remaining_days }} يوم</strong>
                                    </td>
                                    <td>{{ vacation.last_vacation_date }}</td>
                                    <td>
                                        {% if vacation.last_vacation_type == 'إجازة سنوية' %}
                                            <span class="badge bg-primary">{{ vacation.last_vacation_type }}</span>
                                        {% elif vacation.last_vacation_type == 'إجازة مرضية' %}
                                            <span class="badge bg-success">{{ vacation.last_vacation_type }}</span>
                                        {% elif vacation.last_vacation_type == 'إجازة طارئة' %}
                                            <span class="badge bg-warning">{{ vacation.last_vacation_type }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ vacation.last_vacation_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if vacation.remaining_days > 15 %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-thumbs-up me-1"></i>جيد
                                            </span>
                                        {% elif vacation.remaining_days > 5 %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>متوسط
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-exclamation-circle me-1"></i>منخفض
                                            </span>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">
                                            {% if vacation.remaining_days > 15 %}
                                                رصيد كافي
                                            {% elif vacation.remaining_days > 5 %}
                                                يحتاج متابعة
                                            {% else %}
                                                يحتاج تجديد
                                            {% endif %}
                                        </small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3 d-block"></i>
                                        لا توجد بيانات إجازات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Vacation Requests -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        طلبات الإجازات الأخيرة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>أحمد محمد</td>
                                    <td><span class="badge bg-primary">إجازة سنوية</span></td>
                                    <td>2024-02-15</td>
                                    <td>2024-02-20</td>
                                    <td>5 أيام</td>
                                    <td><span class="badge bg-warning">معلق</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-success me-1">موافقة</button>
                                        <button class="btn btn-sm btn-danger">رفض</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>فاطمة علي</td>
                                    <td><span class="badge bg-success">إجازة مرضية</span></td>
                                    <td>2024-02-10</td>
                                    <td>2024-02-12</td>
                                    <td>2 أيام</td>
                                    <td><span class="badge bg-success">موافق عليها</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info">عرض</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>محمد أحمد</td>
                                    <td><span class="badge bg-warning">إجازة طارئة</span></td>
                                    <td>2024-02-08</td>
                                    <td>2024-02-08</td>
                                    <td>1 يوم</td>
                                    <td><span class="badge bg-success">موافق عليها</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info">عرض</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Vacation Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        تحليل الإجازات حسب الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dept in department_vacation_analysis %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary vacation-dept-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-building fa-2x text-primary me-3"></i>
                                        <h6 class="card-title text-primary mb-0">{{ dept.name }}</h6>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي الإجازات:</span>
                                        <strong class="text-primary">{{ dept.total_vacations }} يوم</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>متوسط/موظف:</span>
                                        <strong class="text-info">{{ dept.avg_per_employee }} يوم</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الرصيد المتبقي:</span>
                                        <strong class="text-success">{{ dept.remaining_balance }} يوم</strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>معدل الاستخدام:</span>
                                        <strong class="text-warning">{{ dept.usage_rate }}%</strong>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar
                                            {% if dept.usage_rate <= 50 %}bg-success
                                            {% elif dept.usage_rate <= 75 %}bg-warning
                                            {% else %}bg-danger{% endif %}"
                                            style="width: {{ dept.usage_rate }}%">
                                        </div>
                                    </div>
                                    <small class="text-muted mt-1">
                                        {% if dept.usage_rate <= 50 %}
                                            <i class="fas fa-thumbs-up text-success me-1"></i>استخدام مثالي
                                        {% elif dept.usage_rate <= 75 %}
                                            <i class="fas fa-exclamation-triangle text-warning me-1"></i>استخدام متوسط
                                        {% else %}
                                            <i class="fas fa-exclamation-circle text-danger me-1"></i>استخدام عالي
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Vacation Trend -->
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه الإجازات الشهرية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder" style="height: 250px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">رسم بياني للإجازات الشهرية</h5>
                            <p class="text-muted">يظهر توزيع الإجازات عبر الأشهر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات الإجازات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <strong>تحذير!</strong>
                        <br>
                        5 موظفين لديهم رصيد إجازات منخفض
                    </div>
                    <div class="alert alert-info" role="alert">
                        <strong>ملاحظة:</strong>
                        <br>
                        12 طلب إجازة معلق يحتاج موافقة
                    </div>
                    <div class="alert alert-success" role="alert">
                        <strong>جيد:</strong>
                        <br>
                        انخفاض طلبات الإجازات الطارئة بنسبة 20%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterByVacationType() {
    const vacationType = event.target.value;
    console.log('تم تطبيق فلتر نوع الإجازة:', vacationType);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (vacationType === '' || row.textContent.includes(vacationType)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر نوع الإجازة: ${vacationType || 'جميع الأنواع'}`);
}

function filterByDepartment() {
    const department = event.target.value;
    console.log('تم تطبيق فلتر القسم:', department);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (department === '' || row.textContent.includes(department)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر القسم: ${department || 'جميع الأقسام'}`);
}

function filterByBalance() {
    const balance = event.target.value;
    console.log('تم تطبيق فلتر حالة الرصيد:', balance);

    // تطبيق الفلتر على الجدول
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const balanceCell = row.cells[8]; // خانة الحالة
        if (balance === '' || balanceCell.textContent.includes(balance)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر حالة الرصيد: ${balance || 'جميع الحالات'}`);
}

function exportVacations() {
    // محاكاة تصدير البيانات
    const data = [
        ['الموظف', 'القسم', 'الرصيد السنوي', 'المستخدم', 'المتبقي', 'آخر إجازة'],
        ['أحمد محمد', 'تقنية المعلومات', '30', '12', '18', '2024-01-15'],
        ['فاطمة علي', 'المالية', '30', '8', '22', '2024-01-20'],
        // يمكن إضافة المزيد من البيانات هنا
    ];

    console.log('بيانات التصدير:', data);
    showToast('تم تصدير تقرير الإجازات بنجاح!', 'success');
}

function printVacations() {
    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.btn, .alert, .card-header .btn-group');
    elementsToHide.forEach(el => el.style.display = 'none');

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(el => el.style.display = '');
    }, 1000);
}

function generateVacationReport() {
    // محاكاة إنشاء تقرير مخصص
    showToast('جاري إنشاء تقرير الإجازات المخصص...', 'info');

    setTimeout(() => {
        showToast('تم إنشاء تقرير الإجازات المخصص بنجاح!', 'success');
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    toast.innerHTML = `
        <strong>${type === 'success' ? 'نجح!' : type === 'warning' ? 'تحذير!' : 'معلومة:'}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 4 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

// إضافة وظائف تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للجدول
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // إضافة إمكانية البحث السريع
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'البحث السريع في الجدول...';
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // إدراج حقل البحث قبل الجدول
    const tableCard = document.querySelector('.table-responsive').parentNode;
    tableCard.insertBefore(searchInput, tableCard.querySelector('.table-responsive'));

    // إضافة تأثيرات للبطاقات
    const vacationCards = document.querySelectorAll('.vacation-card');
    vacationCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.15)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // إضافة تأثيرات لبطاقات الأقسام
    const deptCards = document.querySelectorAll('.vacation-dept-card');
    deptCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>

<style>
.vacation-card {
    transition: all 0.3s ease-in-out;
}

.vacation-dept-card {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

.progress-bar {
    transition: width 0.3s ease-in-out;
}

.alert {
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-success {
    border-left-color: #198754;
}

.card-header {
    font-weight: 600;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}
