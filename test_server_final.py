#!/usr/bin/env python
"""
اختبار نهائي للخادم
"""

import urllib.request
import urllib.error
import time

def test_server():
    """اختبار الخادم"""
    
    print("🔍 اختبار الخادم الجديد...")
    
    # انتظار قليل للخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(3)
    
    try:
        # اختبار الصفحة الرئيسية
        print("📄 اختبار الصفحة الرئيسية...")
        req = urllib.request.Request('http://127.0.0.1:8000/')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            status_code = response.getcode()
            print(f'Status Code: {status_code}')
            
            if status_code in [200, 302]:
                print('✅ الصفحة الرئيسية تعمل!')
            else:
                print(f'⚠️ Status غير متوقع: {status_code}')
        
        # اختبار صفحة dashboard
        print("\n📊 اختبار صفحة dashboard...")
        req = urllib.request.Request('http://127.0.0.1:8000/dashboard/')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            status_code = response.getcode()
            print(f'Status Code: {status_code}')
            
            if status_code == 200:
                print('✅ صفحة dashboard تعمل بنجاح!')
                
                # قراءة المحتوى
                content = response.read().decode('utf-8', errors='ignore')
                
                # التحقق من المحتوى
                if 'نظام إدارة المخزون' in content:
                    print('✅ اسم النظام صحيح: "نظام إدارة المخزون"')
                else:
                    print('⚠️ اسم النظام غير موجود')
                
                if 'sidebar' in content:
                    print('✅ القائمة الجانبية موجودة')
                else:
                    print('⚠️ القائمة الجانبية غير موجودة')
                
                print('🎉 الموقع يعمل بشكل مثالي!')
                return True
            else:
                print(f'❌ خطأ في dashboard - Status: {status_code}')
                return False
                
    except urllib.error.HTTPError as e:
        print(f'❌ خطأ HTTP: {e.code}')
        if e.code == 500:
            print('💡 خطأ 500 - مشكلة في الخادم')
        return False
    except urllib.error.URLError as e:
        print('❌ لا يمكن الاتصال بالخادم!')
        print('💡 تأكد من أن الخادم يعمل على http://127.0.0.1:8000/')
        return False
    except Exception as e:
        print(f'❌ خطأ غير متوقع: {e}')
        return False

if __name__ == "__main__":
    success = test_server()
    if success:
        print("\n🎯 النتيجة النهائية:")
        print("✅ الخادم يعمل بشكل مثالي!")
        print("✅ النظام عاد إلى حالته الأصلية!")
        print("✅ اسم النظام: 'نظام إدارة المخزون'")
        print("\n🌐 الروابط:")
        print("   الرئيسية: http://127.0.0.1:8000/")
        print("   Dashboard: http://127.0.0.1:8000/dashboard/")
    else:
        print("\n❌ هناك مشاكل في الخادم")
        print("💡 الحلول المقترحة:")
        print("1. تأكد من تشغيل الخادم:")
        print("   python manage.py runserver 127.0.0.1:8000 --noreload")
        print("2. تحقق من سجل الخادم للأخطاء")
        print("3. أعد تشغيل الخادم")
