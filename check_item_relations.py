#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item
from inventory.models import (
    StockIncreaseItem, StockDecreaseItem, 
    GoodsReceivedOnLoanItem, GoodsIssuedOnLoanItem,
    WarehouseTransferItem, ItemTransformationInput, ItemTransformationOutput,
    ManufacturingOrderMaterial, PhysicalInventoryItem
)

def check_item_relations():
    print("=== Checking Item Relations ===")
    
    inactive_items = Item.objects.filter(is_active=False)
    items_with_relations = []
    items_safe_to_delete = []
    
    for item in inactive_items:
        has_relations = False
        relations = []
        
        # Check all possible relations
        if StockIncreaseItem.objects.filter(item=item).exists():
            relations.append("StockIncreaseItem")
            has_relations = True
            
        if StockDecreaseItem.objects.filter(item=item).exists():
            relations.append("StockDecreaseItem")
            has_relations = True
            
        if GoodsReceivedOnLoanItem.objects.filter(item=item).exists():
            relations.append("GoodsReceivedOnLoanItem")
            has_relations = True
            
        if GoodsIssuedOnLoanItem.objects.filter(item=item).exists():
            relations.append("GoodsIssuedOnLoanItem")
            has_relations = True
            
        if WarehouseTransferItem.objects.filter(item=item).exists():
            relations.append("WarehouseTransferItem")
            has_relations = True
            
        if ItemTransformationInput.objects.filter(item=item).exists():
            relations.append("ItemTransformationInput")
            has_relations = True
            
        if ItemTransformationOutput.objects.filter(item=item).exists():
            relations.append("ItemTransformationOutput")
            has_relations = True
            
        if ManufacturingOrderMaterial.objects.filter(material=item).exists():
            relations.append("ManufacturingOrderMaterial")
            has_relations = True
            
        if PhysicalInventoryItem.objects.filter(item=item).exists():
            relations.append("PhysicalInventoryItem")
            has_relations = True
            
        # OpeningInventoryItem check removed as it doesn't exist
        
        if has_relations:
            items_with_relations.append((item, relations))
        else:
            items_safe_to_delete.append(item)
    
    print(f"Items with relations (cannot delete): {len(items_with_relations)}")
    print(f"Items safe to delete: {len(items_safe_to_delete)}")
    
    if items_with_relations:
        print("\nItems with relations:")
        for item, relations in items_with_relations:
            print(f"- {item.code}: {item.name} -> {', '.join(relations)}")
    
    if items_safe_to_delete:
        print("\nItems safe to delete:")
        for item in items_safe_to_delete:
            print(f"- {item.code}: {item.name}")
    
    return items_safe_to_delete, items_with_relations

if __name__ == "__main__":
    safe_items, related_items = check_item_relations()
    print(f"\nSummary:")
    print(f"- {len(safe_items)} items can be safely deleted")
    print(f"- {len(related_items)} items have relations and should be kept")