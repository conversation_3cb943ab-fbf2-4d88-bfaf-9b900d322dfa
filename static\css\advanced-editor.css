/**
 * تصميمات المحرر المتقدم للفاتورة
 * Advanced Invoice Editor Styles
 */

/* تحسين أقسام الأدوات */
.toolbar-section {
    position: relative;
    transition: all 0.3s ease;
}

.toolbar-section h6 {
    display: flex;
    align-items: center;
    font-size: 1rem;
    margin-bottom: 20px;
    color: #2c3e50;
    font-weight: 700;
}

.toolbar-section h6 i {
    color: #667eea;
    margin-left: 8px;
}

/* تحسين معاينة الشعار */
#logoPreview {
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

#logoPreview:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

#logoPreview img {
    transition: transform 0.3s ease;
}

#logoPreview:hover img {
    transform: scale(1.05);
}

/* تحسين عناصر النموذج */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control[type="color"] {
    height: 45px;
    padding: 5px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control[type="color"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسين checkbox */
.form-check {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.form-check:hover {
    background: rgba(102, 126, 234, 0.1);
}

.form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.2em;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
    transform: scale(1.1);
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    margin-right: 10px;
    transition: color 0.3s ease;
}

.form-check:hover .form-check-label {
    color: #667eea;
}

/* تحسين أزرار الأدوات */
.toolbar-actions {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    margin-top: 30px;
    position: sticky;
    bottom: 20px;
    z-index: 100;
}

.toolbar-actions .btn {
    margin: 8px;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    position: relative;
    overflow: hidden;
}

.toolbar-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.toolbar-actions .btn:hover::before {
    left: 100%;
}

.toolbar-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.toolbar-actions .btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
}

.toolbar-actions .btn-success {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    color: white;
}

.toolbar-actions .btn-info {
    background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%);
    border-color: #6f42c1;
    color: white;
}

.toolbar-actions .btn-warning {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border-color: #fd7e14;
    color: white;
}

.toolbar-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border-color: #6c757d;
    color: white;
}

.toolbar-actions .btn-dark {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    border-color: #343a40;
    color: white;
}

/* تحسين المعاينة */
.preview-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 25px;
    min-height: 600px;
    position: relative;
}

#designPreview {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

#designPreview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تحسين عناصر الفاتورة في المعاينة */
.invoice-preview .invoice-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.invoice-preview .company-info h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    margin-bottom: 15px;
}

.invoice-preview .invoice-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    border-radius: 12px;
    padding: 25px;
    margin: 30px 0;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.invoice-preview .invoice-title::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.invoice-preview .sample-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.invoice-preview table {
    border-radius: 12px;
    overflow: hidden;
    border-collapse: separate;
    border-spacing: 0;
}

.invoice-preview table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 15px 10px;
    border: none;
    position: relative;
}

.invoice-preview table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255,255,255,0.3);
}

.invoice-preview table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease;
}

.invoice-preview table tbody tr:hover td {
    background-color: rgba(102, 126, 234, 0.05);
}

.invoice-preview .invoice-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    margin-top: 30px;
    border: 1px solid #dee2e6;
}

/* تأثيرات التحميل */
.loading-effect {
    position: relative;
    overflow: hidden;
}

.loading-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسين الرسائل */
.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* تحسين الاستجابة */
@media (max-width: 1200px) {
    .toolbar-actions .btn {
        margin: 5px;
        padding: 10px 20px;
        font-size: 0.8rem;
    }
    
    .toolbar-section {
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .toolbar-actions {
        padding: 20px 15px;
    }
    
    .toolbar-actions .btn {
        display: block;
        width: 100%;
        margin: 8px 0;
        padding: 12px;
    }
    
    .form-check {
        margin-bottom: 10px;
        padding: 6px 10px;
    }
    
    #designPreview {
        padding: 15px;
    }
}
