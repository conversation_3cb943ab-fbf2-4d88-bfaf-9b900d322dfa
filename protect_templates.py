#!/usr/bin/env python3
"""
حماية ملفات القوالب من التغيير التلقائي
Template Protection Script
"""

import os
import stat
import hashlib
from pathlib import Path

def protect_file(file_path):
    """حماية ملف من التغيير"""
    try:
        # تغيير صلاحيات الملف لقراءة فقط
        os.chmod(file_path, stat.S_IREAD)
        print(f"✅ تم حماية الملف: {file_path}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حماية {file_path}: {e}")
        return False

def unprotect_file(file_path):
    """إلغاء حماية ملف للتعديل"""
    try:
        # إعادة صلاحيات الكتابة
        os.chmod(file_path, stat.S_IREAD | stat.S_IWRITE)
        print(f"✅ تم إلغاء حماية الملف: {file_path}")
        return True
    except Exception as e:
        print(f"❌ خطأ في إلغاء حماية {file_path}: {e}")
        return False

def get_file_hash(file_path):
    """حساب hash للملف"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def main():
    """الدالة الرئيسية"""
    dashboard_file = "templates/dashboard/home.html"
    
    if os.path.exists(dashboard_file):
        print("🛡️ حماية ملف لوحة التحكم...")
        
        # حساب hash الحالي
        current_hash = get_file_hash(dashboard_file)
        print(f"📊 Hash الحالي: {current_hash}")
        
        # حفظ hash في ملف
        with open("dashboard_hash.txt", "w") as f:
            f.write(current_hash)
        
        # حماية الملف
        protect_file(dashboard_file)
        
        print("✅ تم حماية ملف لوحة التحكم بنجاح!")
        print("💡 لتعديل الملف، استخدم: python protect_templates.py unprotect")
    else:
        print("❌ ملف لوحة التحكم غير موجود!")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "unprotect":
        dashboard_file = "templates/dashboard/home.html"
        if os.path.exists(dashboard_file):
            unprotect_file(dashboard_file)
        else:
            print("❌ ملف لوحة التحكم غير موجود!")
    else:
        main()
