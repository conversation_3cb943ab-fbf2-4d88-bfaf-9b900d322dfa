{% extends 'base/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title|default:'إدخال رصيد إجازة' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus-circle text-success me-2"></i>
                        {{ title|default:'إدخال رصيد إجازة' }}
                    </h2>
                    <p class="text-muted mb-0">يرجى تعبئة جميع الحقول المطلوبة</p>
                </div>
                <div>
                    <a href="{% url 'hr:leave_balance_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-md-8 col-lg-6 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        بيانات رصيد الإجازة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label class="form-label">الموظف <span class="text-danger">*</span></label>
                            <select name="employee" class="form-select" required>
                                <option value="">اختر الموظف</option>
                                {% for emp in employees %}
                                    <option value="{{ emp.id }}" {% if leave_balance and leave_balance.employee.id == emp.id %}selected{% endif %}>{{ emp }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الإجازة <span class="text-danger">*</span></label>
                            <select name="leave_type" class="form-select" required>
                                <option value="">اختر نوع الإجازة</option>
                                {% for lt in leave_types %}
                                    <option value="{{ lt.id }}" {% if leave_balance and leave_balance.leave_type.id == lt.id %}selected{% endif %}>{{ lt }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السنة <span class="text-danger">*</span></label>
                            <input type="number" name="year" class="form-control" value="{{ leave_balance.year|default:current_year }}" required min="2000" max="2100">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">إجمالي الأيام المخصصة</label>
                            <input type="number" name="total_days" class="form-control" value="{{ leave_balance.total_days|default:0 }}" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الأيام المستخدمة</label>
                            <input type="number" name="used_days" class="form-control" value="{{ leave_balance.used_days|default:0 }}" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الأيام المحولة من السنة السابقة</label>
                            <input type="number" name="carried_forward_days" class="form-control" value="{{ leave_balance.carried_forward_days|default:0 }}" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="2">{{ leave_balance.notes }}</textarea>
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <a href="{% url 'hr:leave_balance_list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 