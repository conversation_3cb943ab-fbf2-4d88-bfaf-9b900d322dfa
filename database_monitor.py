#!/usr/bin/env python3
"""
مراقب قاعدة البيانات
Database Monitor

يراقب صحة قاعدة البيانات ويقوم بالنسخ الاحتياطي التلقائي
Monitors database health and performs automatic backups
"""

import os
import sys
import time
import shutil
import sqlite3
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path

# إعداد اللوجز
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_monitor.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DatabaseMonitor:
    """مراقب قاعدة البيانات"""
    
    def __init__(self, db_path='db.sqlite3'):
        self.db_path = Path(db_path)
        self.backup_dir = Path('backup/auto')
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.monitoring = False
        self.check_interval = 300  # فحص كل 5 دقائق
        self.backup_interval = 3600  # نسخ احتياطي كل ساعة
        self.last_backup = None
        
        # إحصائيات
        self.stats = {
            'total_checks': 0,
            'successful_backups': 0,
            'failed_backups': 0,
            'database_errors': 0,
            'last_check': None,
            'database_size': 0
        }
    
    def check_database_health(self):
        """فحص صحة قاعدة البيانات"""
        try:
            if not self.db_path.exists():
                logger.error(f"❌ قاعدة البيانات غير موجودة: {self.db_path}")
                return False
            
            # فحص حجم قاعدة البيانات
            db_size = self.db_path.stat().st_size
            self.stats['database_size'] = db_size
            
            # فحص الاتصال بقاعدة البيانات
            conn = sqlite3.connect(str(self.db_path), timeout=10)
            cursor = conn.cursor()
            
            # فحص سلامة قاعدة البيانات
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()
            
            if integrity_result[0] != 'ok':
                logger.error(f"❌ مشكلة في سلامة قاعدة البيانات: {integrity_result[0]}")
                conn.close()
                return False
            
            # فحص الجداول الأساسية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            if len(tables) == 0:
                logger.warning("⚠️ لا توجد جداول في قاعدة البيانات")
            
            # فحص حجم الجداول
            total_rows = 0
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    total_rows += row_count
                except sqlite3.Error as e:
                    logger.warning(f"⚠️ خطأ في فحص الجدول {table_name}: {e}")
            
            conn.close()
            
            logger.info(f"✅ قاعدة البيانات سليمة - الحجم: {db_size/1024/1024:.2f} MB، الجداول: {len(tables)}, الصفوف: {total_rows}")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"❌ خطأ في قاعدة البيانات: {e}")
            self.stats['database_errors'] += 1
            return False
        except Exception as e:
            logger.error(f"❌ خطأ عام في فحص قاعدة البيانات: {e}")
            return False
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if not self.db_path.exists():
                logger.error("❌ لا يمكن إنشاء نسخة احتياطية - قاعدة البيانات غير موجودة")
                return False
            
            # اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"db_backup_{timestamp}.sqlite3"
            backup_path = self.backup_dir / backup_name
            
            # إنشاء النسخة الاحتياطية
            logger.info(f"💾 إنشاء نسخة احتياطية: {backup_name}")
            
            # استخدام SQLite backup API للنسخ الآمن
            source_conn = sqlite3.connect(str(self.db_path))
            backup_conn = sqlite3.connect(str(backup_path))
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            # التحقق من النسخة الاحتياطية
            if backup_path.exists() and backup_path.stat().st_size > 0:
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}")
                self.stats['successful_backups'] += 1
                self.last_backup = datetime.now()
                
                # تنظيف النسخ القديمة
                self.cleanup_old_backups()
                
                return True
            else:
                logger.error("❌ فشل في إنشاء النسخة الاحتياطية")
                self.stats['failed_backups'] += 1
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            self.stats['failed_backups'] += 1
            return False
    
    def cleanup_old_backups(self, keep_days=7):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            for backup_file in self.backup_dir.glob("db_backup_*.sqlite3"):
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                
                if file_time < cutoff_date:
                    backup_file.unlink()
                    logger.info(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup_file.name}")
                    
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            logger.info("🔧 تحسين قاعدة البيانات...")
            
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # تحسين قاعدة البيانات
            cursor.execute("VACUUM")
            cursor.execute("ANALYZE")
            
            conn.close()
            
            logger.info("✅ تم تحسين قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
            return False
    
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not self.db_path.exists():
                return None
            
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # معلومات عامة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            # حجم قاعدة البيانات
            db_size = self.db_path.stat().st_size
            
            # عدد الصفحات
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            # معلومات الجداول
            table_info = []
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    table_info.append({
                        'name': table_name,
                        'rows': row_count
                    })
                except:
                    pass
            
            conn.close()
            
            return {
                'size_bytes': db_size,
                'size_mb': db_size / 1024 / 1024,
                'tables_count': len(tables),
                'page_count': page_count,
                'page_size': page_size,
                'tables': table_info
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
            return None
    
    def monitor_loop(self):
        """حلقة المراقبة الرئيسية"""
        logger.info("🔍 بدء مراقبة قاعدة البيانات...")
        
        while self.monitoring:
            try:
                self.stats['total_checks'] += 1
                self.stats['last_check'] = datetime.now()
                
                # فحص صحة قاعدة البيانات
                is_healthy = self.check_database_health()
                
                # إنشاء نسخة احتياطية إذا حان الوقت
                if (self.last_backup is None or 
                    datetime.now() - self.last_backup > timedelta(seconds=self.backup_interval)):
                    
                    if is_healthy:
                        self.create_backup()
                    else:
                        logger.warning("⚠️ تم تأجيل النسخ الاحتياطي بسبب مشاكل في قاعدة البيانات")
                
                # تحسين قاعدة البيانات كل 24 ساعة
                if self.stats['total_checks'] % 288 == 0:  # 24 ساعة / 5 دقائق
                    self.optimize_database()
                
                # طباعة إحصائيات كل 12 فحص (ساعة واحدة)
                if self.stats['total_checks'] % 12 == 0:
                    db_stats = self.get_database_stats()
                    if db_stats:
                        logger.info(
                            f"📊 إحصائيات قاعدة البيانات: "
                            f"الحجم: {db_stats['size_mb']:.2f} MB، "
                            f"الجداول: {db_stats['tables_count']}"
                        )
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("⌨️ تم إيقاف مراقبة قاعدة البيانات")
                break
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة مراقبة قاعدة البيانات: {e}")
                time.sleep(self.check_interval)
    
    def start(self):
        """بدء المراقبة"""
        self.monitoring = True
        self.monitor_loop()
    
    def stop(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        logger.info("🛑 تم إيقاف مراقبة قاعدة البيانات")
    
    def generate_report(self):
        """إنشاء تقرير مفصل"""
        db_stats = self.get_database_stats()
        
        report = f"""
📊 تقرير مراقبة قاعدة البيانات
{'=' * 40}

📈 الإحصائيات العامة:
• إجمالي الفحوصات: {self.stats['total_checks']}
• النسخ الاحتياطية الناجحة: {self.stats['successful_backups']}
• النسخ الاحتياطية الفاشلة: {self.stats['failed_backups']}
• أخطاء قاعدة البيانات: {self.stats['database_errors']}
• آخر فحص: {self.stats['last_check']}
• آخر نسخة احتياطية: {self.last_backup}

💾 معلومات قاعدة البيانات:
"""
        
        if db_stats:
            report += f"""• الحجم: {db_stats['size_mb']:.2f} MB
• عدد الجداول: {db_stats['tables_count']}
• عدد الصفحات: {db_stats['page_count']}
• حجم الصفحة: {db_stats['page_size']} bytes

📋 الجداول:
"""
            for table in db_stats['tables']:
                report += f"• {table['name']}: {table['rows']} صف\n"
        else:
            report += "• غير متاح\n"
        
        return report

def main():
    """الدالة الرئيسية"""
    print("📊 مراقب قاعدة البيانات")
    print("=" * 25)
    
    monitor = DatabaseMonitor()
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n⌨️ تم الضغط على Ctrl+C")
    finally:
        monitor.stop()
        print("👋 تم إنهاء مراقب قاعدة البيانات")

if __name__ == "__main__":
    main()
