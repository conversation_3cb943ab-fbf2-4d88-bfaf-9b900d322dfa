<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>المواد الخام</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body class="container-fluid mt-4">
    
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary">
                <i class="bi bi-box-seam"></i>
                المواد الخام
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_dashboard' %}">التصنيع</a></li>
                    <li class="breadcrumb-item active">المواد الخام</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'inventory:raw_material_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                إضافة مادة خام
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-funnel"></i>
                البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في اسم المادة..." 
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-4">
                    <select name="warehouse" class="form-select">
                        <option value="">جميع المخازن</option>
                        {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" 
                                    {% if request.GET.warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                {{ warehouse.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المواد الخام -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-table"></i>
                قائمة المواد الخام ({{ materials.count }} مادة)
            </h5>
        </div>
        <div class="card-body">
            {% if materials %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم المادة</th>
                                <th>الكمية المتاحة</th>
                                <th>الوحدة</th>
                                <th>تكلفة الوحدة</th>
                                <th>الحد الأدنى</th>
                                <th>المخزن</th>
                                <th>المورد</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materials %}
                            <tr class="{% if material.is_low_stock %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ material.name }}</strong>
                                    {% if material.is_low_stock %}
                                        <i class="bi bi-exclamation-triangle text-warning" title="مخزون منخفض"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="{% if material.is_low_stock %}text-warning fw-bold{% endif %}">
                                        {{ material.quantity_available }}
                                    </span>
                                </td>
                                <td>{{ material.unit }}</td>
                                <td>{{ material.unit_cost }} ج.م</td>
                                <td>{{ material.minimum_stock }}</td>
                                <td>{{ material.warehouse.name }}</td>
                                <td>{{ material.supplier|default:"-" }}</td>
                                <td>
                                    {% if material.is_low_stock %}
                                        <span class="badge bg-warning">مخزون منخفض</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'inventory:raw_material_edit' material.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#materialModal{{ material.pk }}"
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal للتفاصيل -->
                            <div class="modal fade" id="materialModal{{ material.pk }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تفاصيل {{ material.name }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <th class="w-50">اسم المادة:</th>
                                                    <td>{{ material.name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>الكمية المتاحة:</th>
                                                    <td>{{ material.quantity_available }} {{ material.unit }}</td>
                                                </tr>
                                                <tr>
                                                    <th>تكلفة الوحدة:</th>
                                                    <td>{{ material.unit_cost }} ج.م</td>
                                                </tr>
                                                <tr>
                                                    <th>الحد الأدنى:</th>
                                                    <td>{{ material.minimum_stock }} {{ material.unit }}</td>
                                                </tr>
                                                <tr>
                                                    <th>المخزن:</th>
                                                    <td>{{ material.warehouse.name }}</td>
                                                </tr>
                                                <tr>
                                                    <th>المورد:</th>
                                                    <td>{{ material.supplier|default:"-" }}</td>
                                                </tr>
                                                {% if material.notes %}
                                                <tr>
                                                    <th>ملاحظات:</th>
                                                    <td>{{ material.notes }}</td>
                                                </tr>
                                                {% endif %}
                                                <tr>
                                                    <th>تاريخ الإنشاء:</th>
                                                    <td>{{ material.created_at }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            <a href="{% url 'inventory:raw_material_edit' material.pk %}" class="btn btn-primary">تعديل</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box display-1 text-muted"></i>
                    <h4 class="text-muted">لا توجد مواد خام</h4>
                    <p class="text-muted">لم يتم إضافة أي مواد خام حتى الآن</p>
                    <a href="{% url 'inventory:raw_material_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        إضافة أول مادة خام
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ materials.count }}</h3>
                    <p class="text-muted mb-0">إجمالي المواد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">
                        {% with low_stock=materials|length %}
                            {% for material in materials %}
                                {% if material.is_low_stock %}{{ forloop.counter }}{% endif %}
                            {% endfor %}
                        {% endwith %}
                    </h3>
                    <p class="text-muted mb-0">مخزون منخفض</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ warehouses.count }}</h3>
                    <p class="text-muted mb-0">عدد المخازن</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">
                        {% widthratio materials|length 1 100 %}%
                    </h3>
                    <p class="text-muted mb-0">معدل التوفر</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
