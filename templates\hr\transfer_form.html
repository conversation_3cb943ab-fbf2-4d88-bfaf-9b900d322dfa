{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .form-section h5 {
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-label {
        font-weight: 600;
        color: #34495e;
        margin-bottom: 5px;
    }
    
    .form-control {
        border: 1px solid #bdc3c7;
        border-radius: 4px;
        padding: 8px 12px;
        width: 100%;
    }
    
    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }
    
    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
        padding: 10px 20px;
        font-weight: 600;
    }
    
    .btn-secondary {
        background-color: #95a5a6;
        border-color: #95a5a6;
        padding: 10px 20px;
        font-weight: 600;
    }
    
    .alert {
        border-radius: 4px;
        padding: 12px 15px;
        margin-bottom: 20px;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    
    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">{{ title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:transfer_list' %}">النقل</a></li>
                        <li class="breadcrumb-item active">{{ title }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="form-section">
                            <h5>معلومات الموظف</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">الموظف *</label>
                                        {{ form.employee }}
                                        {% if form.employee.errors %}
                                            <div class="text-danger">{{ form.employee.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ النقل *</label>
                                        {{ form.transfer_date }}
                                        {% if form.transfer_date.errors %}
                                            <div class="text-danger">{{ form.transfer_date.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h5>معلومات النقل</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">القسم الحالي *</label>
                                        {{ form.from_department }}
                                        {% if form.from_department.errors %}
                                            <div class="text-danger">{{ form.from_department.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">القسم الجديد *</label>
                                        {{ form.to_department }}
                                        {% if form.to_department.errors %}
                                            <div class="text-danger">{{ form.to_department.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">المنصب الحالي *</label>
                                        {{ form.from_position }}
                                        {% if form.from_position.errors %}
                                            <div class="text-danger">{{ form.from_position.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">المنصب الجديد *</label>
                                        {{ form.to_position }}
                                        {% if form.to_position.errors %}
                                            <div class="text-danger">{{ form.to_position.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">نوع النقل *</label>
                                        {{ form.transfer_type }}
                                        {% if form.transfer_type.errors %}
                                            <div class="text-danger">{{ form.transfer_type.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ التنفيذ *</label>
                                        {{ form.effective_date }}
                                        {% if form.effective_date.errors %}
                                            <div class="text-danger">{{ form.effective_date.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h5>تفاصيل إضافية</h5>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">سبب النقل *</label>
                                        {{ form.reason }}
                                        {% if form.reason.errors %}
                                            <div class="text-danger">{{ form.reason.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">ملاحظات</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h5>الموظف البديل (اختياري)</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">الموظف البديل</label>
                                        {{ form.replacement_employee }}
                                        {% if form.replacement_employee.errors %}
                                            <div class="text-danger">{{ form.replacement_employee.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-save me-1"></i>
                                {% if transfer %}تحديث النقل{% else %}إنشاء النقل{% endif %}
                            </button>
                            <a href="{% url 'hr:transfer_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحسين مظهر النماذج
    $('select, input, textarea').addClass('form-control');
    
    // إضافة placeholder للنماذج
    $('input[type="text"], input[type="date"], textarea').each(function() {
        if (!$(this).val()) {
            $(this).attr('placeholder', $(this).prev('label').text());
        }
    });
    
    // تحسين عرض الأخطاء
    $('.text-danger').each(function() {
        $(this).addClass('small');
    });
});
</script>
{% endblock %} 