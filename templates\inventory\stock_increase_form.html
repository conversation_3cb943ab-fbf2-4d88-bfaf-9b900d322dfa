{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-plus-circle text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:stock_increase_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الإذن الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الإذن الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.increase_number.id_for_label }}" class="form-label">رقم الإذن</label>
                                {{ form.increase_number }}
                                {% if form.increase_number.errors %}
                                    <div class="text-danger small">{{ form.increase_number.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }}</label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                    <div class="text-danger small">{{ form.reason.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أصناف الإذن -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes me-2"></i>
                            أصناف الإذن
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="addItem()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة صنف
                        </button>
                    </div>
                    <div class="card-body">
                        {{ formset.management_form }}
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="items-table">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 20%">الصنف</th>
                                        <th style="width: 12%">الكمية</th>
                                        <th style="width: 12%">تكلفة الوحدة</th>
                                        <th style="width: 12%">الإجمالي</th>
                                        <th style="width: 12%">تاريخ الانتهاء</th>
                                        <th style="width: 10%">رقم الدفعة</th>
                                        <th style="width: 15%">ملاحظات</th>
                                        <th style="width: 7%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.item }}
                                                {% if form.item.errors %}
                                                    <div class="text-danger small">{{ form.item.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.quantity }}
                                                {% if form.quantity.errors %}
                                                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.unit_cost }}
                                                {% if form.unit_cost.errors %}
                                                    <div class="text-danger small">{{ form.unit_cost.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <input type="text" class="form-control total-cost" readonly>
                                            </td>
                                            <td>
                                                {{ form.expiry_date }}
                                                {% if form.expiry_date.errors %}
                                                    <div class="text-danger small">{{ form.expiry_date.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.batch_number }}
                                                {% if form.batch_number.errors %}
                                                    <div class="text-danger small">{{ form.batch_number.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.notes }}
                                                {% if form.notes.errors %}
                                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if form.DELETE %}
                                                    {{ form.DELETE }}
                                                {% endif %}
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                            {% for hidden in form.hidden_fields %}
                                                {{ hidden }}
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <td colspan="3"><strong>الإجمالي العام:</strong></td>
                                        <td><strong id="grand-total">0.00 ج.م</strong></td>
                                        <td colspan="3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'inventory:stock_increase_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let itemIndex = {{ formset.total_form_count }};

// وظيفة تحميل الأصناف عبر AJAX
async function loadItems(selectElement) {
    try {
        console.log('Loading items for select element:', selectElement);
        selectElement.innerHTML = '<option value="">جاري التحميل...</option>';
        
        const response = await fetch('/accounting/ajax/items/search/?q=');
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Loaded items:', data);
        
        // إضافة خيار افتراضي
        selectElement.innerHTML = '<option value="">اختر الصنف</option>';
        
        // إضافة الأصناف - معالجة البيانات الجديدة
        if (data && data.items && Array.isArray(data.items)) {
            data.items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                
                // إنشاء نص العرض للصنف
                let displayText = '';
                if (item.code) {
                    displayText += `[${item.code}] `;
                }
                displayText += item.name || 'صنف بدون اسم';
                if (item.unit) {
                    displayText += ` (${item.unit})`;
                }
                
                option.textContent = displayText;
                // إضافة تكلفة الوحدة كـ data attribute
                option.setAttribute('data-cost', item.cost_price || 0);
                selectElement.appendChild(option);
            });
        } else {
            console.error('Unexpected data format:', data);
            selectElement.innerHTML = '<option value="">خطأ في تنسيق البيانات</option>';
        }
        
        console.log('Items loaded successfully');
    } catch (error) {
        console.error('Error loading items:', error);
        selectElement.innerHTML = '<option value="">خطأ في تحميل الأصناف</option>';
    }
}

// وظيفة جلب تكلفة الصنف من API
async function getItemCost(itemId) {
    try {
        const response = await fetch(`/inventory/api/item-cost/?item_id=${itemId}`);
        if (response.ok) {
            const data = await response.json();
            return data.cost_price || 0;
        }
    } catch (error) {
        console.error('Error fetching item cost:', error);
    }
    return 0;
}

// وظيفة تحديث تكلفة الوحدة عند اختيار الصنف
async function updateUnitCost(selectElement) {
    const row = selectElement.closest('tr');
    const unitCostInput = row.querySelector('.unit-cost');
    const itemId = selectElement.value;
    
    if (itemId) {
        // محاولة الحصول على التكلفة من data attribute أولاً
        const selectedOption = selectElement.querySelector(`option[value="${itemId}"]`);
        let costPrice = 0;
        
        if (selectedOption && selectedOption.getAttribute('data-cost')) {
            costPrice = parseFloat(selectedOption.getAttribute('data-cost')) || 0;
        } else {
            // إذا لم تكن التكلفة متوفرة في data attribute، جلبها من API
            costPrice = await getItemCost(itemId);
        }
        
        if (unitCostInput) {
            unitCostInput.value = costPrice.toFixed(2);
            // تشغيل حساب الإجمالي
            calculateRowTotal(row);
        }
    } else {
        // إذا لم يتم اختيار صنف، مسح حقل التكلفة
        if (unitCostInput) {
            unitCostInput.value = '';
            calculateRowTotal(row);
        }
    }
}

function addItem() {
    const tbody = document.querySelector('#items-table tbody');
    const newRow = document.createElement('tr');
    newRow.className = 'item-row';
    newRow.innerHTML = `
        <td>
            <select name="items-${itemIndex}-item" class="form-select" required>
                <option value="">جاري التحميل...</option>
            </select>
        </td>
        <td>
            <input type="number" name="items-${itemIndex}-quantity" class="form-control quantity" step="0.001" min="0.001" required>
        </td>
        <td>
            <input type="number" name="items-${itemIndex}-unit_cost" class="form-control unit-cost" step="0.01" min="0" required>
        </td>
        <td>
            <input type="text" class="form-control total-cost" readonly>
        </td>
        <td>
            <input type="date" name="items-${itemIndex}-expiry_date" class="form-control">
        </td>
        <td>
            <input type="text" name="items-${itemIndex}-batch_number" class="form-control">
        </td>
        <td>
            <textarea name="items-${itemIndex}-notes" class="form-control" rows="2"></textarea>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(newRow);
    
    // تحميل الأصناف في الحقل الجديد
    const itemSelect = newRow.querySelector('select[name$="-item"]');
    loadItems(itemSelect);
    
    // تحديث عدد النماذج
    itemIndex++;
    document.querySelector('#id_items-TOTAL_FORMS').value = itemIndex;
    
    // إضافة event listeners للحقول الجديدة
    addEventListeners(newRow);
}

function removeItem(button) {
    const row = button.closest('tr');
    const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
    
    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        row.style.display = 'none';
    } else {
        row.remove();
        itemIndex--;
        document.querySelector('#id_items-TOTAL_FORMS').value = itemIndex;
    }
    
    calculateGrandTotal();
}

function calculateRowTotal(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitCostInput = row.querySelector('.unit-cost');
    const totalInput = row.querySelector('.total-cost');
    
    if (!quantityInput || !unitCostInput || !totalInput) {
        console.log('Missing required inputs in row:', row);
        return;
    }
    
    const quantity = parseFloat(quantityInput.value) || 0;
    const unitCost = parseFloat(unitCostInput.value) || 0;
    const total = quantity * unitCost;
    
    totalInput.value = total.toFixed(2);
    calculateGrandTotal();
}

function calculateGrandTotal() {
    let grandTotal = 0;
    const visibleRows = document.querySelectorAll('.item-row:not([style*="display: none"])');
    
    visibleRows.forEach(row => {
        const totalInput = row.querySelector('.total-cost');
        if (totalInput) {
            grandTotal += parseFloat(totalInput.value) || 0;
        }
    });
    
    const grandTotalElement = document.getElementById('grand-total');
    if (grandTotalElement) {
        grandTotalElement.textContent = grandTotal.toFixed(2) + ' ج.م';
    }
}

function addEventListeners(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitCostInput = row.querySelector('.unit-cost');
    const itemSelect = row.querySelector('select[name$="-item"]');
    
    if (quantityInput) {
        quantityInput.addEventListener('input', function() { 
            calculateRowTotal(row); 
        });
        quantityInput.addEventListener('change', function() { 
            calculateRowTotal(row); 
        });
    }
    
    if (unitCostInput) {
        unitCostInput.addEventListener('input', function() { 
            calculateRowTotal(row); 
        });
        unitCostInput.addEventListener('change', function() { 
            calculateRowTotal(row); 
        });
    }
    
    // إضافة event listener لاختيار الصنف
    if (itemSelect) {
        itemSelect.addEventListener('change', function() {
            updateUnitCost(this);
        });
    }
    
    // حساب الإجمالي مباشرة عند تحميل الصفحة أو إضافة صف جديد
    calculateRowTotal(row);
}

// إضافة event listeners للصفوف الموجودة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up event listeners...');
    
    const rows = document.querySelectorAll('.item-row');
    console.log('Found', rows.length, 'item rows');
    
    rows.forEach((row, index) => {
        console.log('Setting up row', index);
        addEventListeners(row);
    });
    
    calculateGrandTotal();
    
    // تحديد تاريخ اليوم افتراضياً
    const dateField = document.querySelector('input[type="date"][name="date"]');
    if (dateField && !dateField.value) {
        dateField.value = new Date().toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
