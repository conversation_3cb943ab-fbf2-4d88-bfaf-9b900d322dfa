/**
 * تصميمات صفحة تصميم نماذج الطباعة
 * Print Design Page Styles
 */

/* تحسين مظهر البطاقات */
.print-template-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.print-template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.print-template-card .card-header {
    font-weight: 600;
    border-bottom: none;
    padding: 15px 20px;
}

.print-template-card .card-body {
    padding: 25px 20px;
}

.print-template-card .fa-3x {
    margin-bottom: 20px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.print-template-card:hover .fa-3x {
    opacity: 1;
}

/* تحسين الأزرار */
.btn-group-vertical .btn {
    margin-bottom: 8px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-group-vertical .btn:hover {
    transform: translateX(-3px);
}

.btn-group-vertical .btn i {
    margin-left: 8px;
}

/* تحسين Modal التصميم */
.modal-xl {
    max-width: 95%;
}

.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 20px 25px;
}

.modal-header .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* تحسين نموذج التصميم */
.design-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.design-form .form-control,
.design-form .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: border-color 0.3s ease;
}

.design-form .form-control:focus,
.design-form .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.design-form .form-check-input {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.1em;
}

.design-form .form-check-label {
    font-weight: 500;
    margin-right: 10px;
}

/* تحسين معاينة مباشرة */
#livePreview {
    border-radius: 8px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6 !important;
    transition: border-color 0.3s ease;
    min-height: 400px;
    overflow-y: auto;
    position: relative;
}

#livePreview:hover {
    border-color: #667eea !important;
}

#livePreview:empty::before {
    content: "المعاينة ستظهر هنا...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #6c757d;
    font-style: italic;
    font-size: 1.1rem;
}

/* تحسين معاينة النموذج */
#previewContent {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-height: 70vh;
    overflow-y: auto;
}

/* تحسين الجداول في المعاينة */
.items-table,
.inventory-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.items-table th,
.inventory-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 12px 8px;
    border: none;
}

.items-table td,
.inventory-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    text-align: center;
}

.items-table tbody tr:hover,
.inventory-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* تحسين رأس الفاتورة */
.invoice-title,
.report-title {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin: 20px 0;
}

.invoice-title h2,
.report-title h2 {
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* تحسين معلومات الشركة */
.company-info,
.invoice-info,
.customer-info,
.supplier-info {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-section h4,
.customer-info h4,
.supplier-info h4 {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* تحسين قسم الإجماليات */
.totals-section {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background: #f8f9fa;
}

.total-row {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.final-total {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 15px;
    border-radius: 6px;
    margin-top: 10px;
    border: none !important;
}

/* تحسين ملخص التقرير */
.summary-card {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.summary-stats {
    gap: 15px;
}

.stat-item {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-value {
    color: #667eea;
    font-size: 1.2rem;
}

/* تحسين التذييل */
.invoice-footer,
.report-footer {
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    text-align: center;
}

/* تحسين الرسائل */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-left: 8px;
}

/* تحسين التخطيط العام */
.container-fluid {
    padding: 20px;
}

.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.card-header {
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .modal-xl {
        max-width: 98%;
        margin: 10px;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .print-template-card .card-body {
        padding: 20px 15px;
    }
    
    .btn-group-vertical .btn {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
    
    #livePreview {
        height: 300px;
    }
}

/* تحسين الطباعة */
@media print {
    .modal,
    .btn,
    .form-control,
    .form-select,
    .alert {
        display: none !important;
    }
    
    #previewContent {
        box-shadow: none;
        padding: 0;
        max-height: none;
        overflow: visible;
    }
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسين الألوان */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    --info-gradient: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}
