{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-arrow-up text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدخال بيانات الترقية الجديدة</p>
                </div>
                <div>
                    <a href="{% url 'hr:promotion_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة الترقيات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات الترقية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                    <select name="employee" class="form-select" required {% if promotion %}disabled{% endif %}>
                                        <option value="">اختر الموظف</option>
                                        {% for employee in employees %}
                                            <option value="{{ employee.id }}" 
                                                    {% if promotion and promotion.employee.id == employee.id %}selected{% endif %}
                                                    data-position="{{ employee.position.name|default:'' }}"
                                                    data-salary="{{ employee.salary|default:0 }}">
                                                {{ employee.full_name }} - {{ employee.employee_number }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الترقية <span class="text-danger">*</span></label>
                                    <input type="date" name="promotion_date" class="form-control" 
                                           value="{{ promotion.promotion_date|date:'Y-m-d'|default:'' }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنصب السابق</label>
                                    <select name="from_position" class="form-select">
                                        <option value="">اختر المنصب السابق</option>
                                        {% for position in positions %}
                                            <option value="{{ position.id }}" 
                                                    {% if promotion and promotion.from_position.id == position.id %}selected{% endif %}>
                                                {{ position.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنصب الجديد <span class="text-danger">*</span></label>
                                    <select name="to_position" class="form-select" required>
                                        <option value="">اختر المنصب الجديد</option>
                                        {% for position in positions %}
                                            <option value="{{ position.id }}" 
                                                    {% if promotion and promotion.to_position.id == position.id %}selected{% endif %}>
                                                {{ position.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الراتب السابق</label>
                                    <input type="number" name="from_salary" class="form-control" 
                                           value="{{ promotion.from_salary|default:'' }}" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الراتب الجديد <span class="text-danger">*</span></label>
                                    <input type="number" name="to_salary" class="form-control" 
                                           value="{{ promotion.to_salary|default:'' }}" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التنفيذ</label>
                                    <input type="date" name="effective_date" class="form-control" 
                                           value="{{ promotion.effective_date|date:'Y-m-d'|default:'' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة الترقية</label>
                                    <select name="status" class="form-select">
                                        <option value="PENDING" {% if promotion.status == 'PENDING' %}selected{% endif %}>قيد المراجعة</option>
                                        <option value="APPROVED" {% if promotion.status == 'APPROVED' %}selected{% endif %}>موافق عليها</option>
                                        <option value="REJECTED" {% if promotion.status == 'REJECTED' %}selected{% endif %}>مرفوضة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">سبب الترقية</label>
                            <textarea name="reason" class="form-control" rows="3" 
                                      placeholder="اذكر أسباب استحقاق الموظف للترقية">{{ promotion.reason|default:'' }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea name="notes" class="form-control" rows="2" 
                                      placeholder="ملاحظات إضافية">{{ promotion.notes|default:'' }}</textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'hr:promotion_list' %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الترقية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const employeeSelect = document.querySelector('select[name="employee"]');
    const fromPositionSelect = document.querySelector('select[name="from_position"]');
    const fromSalaryInput = document.querySelector('input[name="from_salary"]');
    
    if (employeeSelect) {
        employeeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const position = selectedOption.getAttribute('data-position');
                const salary = selectedOption.getAttribute('data-salary');
                
                // تحديث المنصب السابق
                if (fromPositionSelect) {
                    for (let option of fromPositionSelect.options) {
                        if (option.text === position) {
                            option.selected = true;
                            break;
                        }
                    }
                }
                
                // تحديث الراتب السابق
                if (fromSalaryInput) {
                    fromSalaryInput.value = salary;
                }
            }
        });
    }
});
</script>
{% endblock %} 