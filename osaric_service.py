"""
خدمة Windows لتشغيل خادم Django بشكل دائم
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# إعداد المسارات
PROJECT_DIR = Path(__file__).parent
PYTHON_EXE = sys.executable
MANAGE_PY = PROJECT_DIR / "manage.py"
LOG_FILE = PROJECT_DIR / "service.log"

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class OsaricService:
    """خدمة تشغيل خادم Django"""
    
    def __init__(self):
        self.process = None
        self.running = False
        
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            logger.info("بدء تشغيل خادم Django...")
            
            # تغيير المجلد الحالي
            os.chdir(PROJECT_DIR)
            
            # تشغيل الخادم
            cmd = [PYTHON_EXE, str(MANAGE_PY), "runserver", "0.0.0.0:8000", "--noreload"]
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            logger.info(f"تم بدء الخادم بـ PID: {self.process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء الخادم: {str(e)}")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.process:
            try:
                logger.info("إيقاف الخادم...")
                self.process.terminate()
                self.process.wait(timeout=10)
                logger.info("تم إيقاف الخادم بنجاح")
            except subprocess.TimeoutExpired:
                logger.warning("فرض إيقاف الخادم...")
                self.process.kill()
            except Exception as e:
                logger.error(f"خطأ في إيقاف الخادم: {str(e)}")
            finally:
                self.process = None
    
    def is_server_running(self):
        """التحقق من حالة الخادم"""
        if not self.process:
            return False
        
        # التحقق من حالة العملية
        poll = self.process.poll()
        return poll is None
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        logger.info("إعادة تشغيل الخادم...")
        self.stop_server()
        time.sleep(2)
        return self.start_server()
    
    def run(self):
        """تشغيل الخدمة الرئيسية"""
        logger.info("بدء خدمة أوساريك...")
        self.running = True
        
        # بدء الخادم
        if not self.start_server():
            logger.error("فشل في بدء الخادم")
            return
        
        # حلقة المراقبة
        restart_count = 0
        max_restarts = 5
        
        while self.running:
            try:
                # التحقق من حالة الخادم كل 30 ثانية
                time.sleep(30)
                
                if not self.is_server_running():
                    logger.warning("الخادم متوقف! محاولة إعادة التشغيل...")
                    
                    if restart_count < max_restarts:
                        if self.restart_server():
                            restart_count = 0  # إعادة تعيين العداد عند النجاح
                            logger.info("تم إعادة تشغيل الخادم بنجاح")
                        else:
                            restart_count += 1
                            logger.error(f"فشل في إعادة التشغيل. المحاولة {restart_count}/{max_restarts}")
                    else:
                        logger.critical("تم الوصول للحد الأقصى من محاولات إعادة التشغيل")
                        break
                
                # قراءة مخرجات الخادم
                if self.process and self.process.stdout:
                    try:
                        output = self.process.stdout.readline()
                        if output:
                            logger.info(f"Server: {output.strip()}")
                    except:
                        pass
                        
            except KeyboardInterrupt:
                logger.info("تم طلب إيقاف الخدمة...")
                break
            except Exception as e:
                logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                time.sleep(10)
        
        # إيقاف الخادم عند الخروج
        self.stop_server()
        logger.info("تم إيقاف خدمة أوساريك")


def main():
    """الدالة الرئيسية"""
    service = OsaricService()
    
    try:
        service.run()
    except Exception as e:
        logger.error(f"خطأ في الخدمة: {str(e)}")
    finally:
        service.stop_server()


if __name__ == "__main__":
    main()
