#!/usr/bin/env python
"""
Clean up deleted items from database
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.db import transaction

def clean_inactive_items():
    """Delete inactive items permanently"""
    
    print("Searching for inactive items...")
    
    # Find inactive items
    inactive_items = Item.objects.filter(is_active=False)
    count = inactive_items.count()
    
    if count == 0:
        print("No inactive items found.")
        return
    
    print(f"Found {count} inactive items:")
    print("-" * 50)
    
    for item in inactive_items:
        print(f"- {item.name} ({item.code})")
    
    print("-" * 50)
    
    # Confirm deletion
    confirm = input(f"\nDelete these {count} items permanently? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Delete items permanently
    try:
        with transaction.atomic():
            deleted_count = 0
            for item in inactive_items:
                item_name = item.name
                item_code = item.code
                item.delete()  # Permanent deletion
                deleted_count += 1
                print(f"Deleted: {item_name} ({item_code})")
            
            print(f"\nSuccessfully deleted {deleted_count} items from database.")
            
    except Exception as e:
        print(f"Error during deletion: {str(e)}")
        return

def clean_inactive_categories():
    """Delete inactive categories permanently"""
    
    print("\nSearching for inactive categories...")
    
    inactive_categories = ItemCategory.objects.filter(is_active=False)
    count = inactive_categories.count()
    
    if count == 0:
        print("No inactive categories found.")
        return
    
    print(f"Found {count} inactive categories:")
    print("-" * 50)
    
    for category in inactive_categories:
        print(f"- {category.name}")
    
    print("-" * 50)
    
    # Confirm deletion
    confirm = input(f"\nDelete these {count} categories permanently? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Delete categories permanently
    try:
        with transaction.atomic():
            deleted_count = 0
            for category in inactive_categories:
                category_name = category.name
                category.delete()  # Permanent deletion
                deleted_count += 1
                print(f"Deleted category: {category_name}")
            
            print(f"\nSuccessfully deleted {deleted_count} categories from database.")
            
    except Exception as e:
        print(f"Error during deletion: {str(e)}")
        return

def clean_inactive_units():
    """Delete inactive units permanently"""
    
    print("\nSearching for inactive units...")
    
    inactive_units = Unit.objects.filter(is_active=False)
    count = inactive_units.count()
    
    if count == 0:
        print("No inactive units found.")
        return
    
    print(f"Found {count} inactive units:")
    print("-" * 50)
    
    for unit in inactive_units:
        print(f"- {unit.name}")
    
    print("-" * 50)
    
    # Confirm deletion
    confirm = input(f"\nDelete these {count} units permanently? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Delete units permanently
    try:
        with transaction.atomic():
            deleted_count = 0
            for unit in inactive_units:
                unit_name = unit.name
                unit.delete()  # Permanent deletion
                deleted_count += 1
                print(f"Deleted unit: {unit_name}")
            
            print(f"\nSuccessfully deleted {deleted_count} units from database.")
            
    except Exception as e:
        print(f"Error during deletion: {str(e)}")
        return

def main():
    """Main function"""
    print("=" * 60)
    print("Database Cleanup Tool - Delete Inactive Data")
    print("=" * 60)
    
    print("\nOptions:")
    print("1. Delete inactive items")
    print("2. Delete inactive categories")
    print("3. Delete inactive units")
    print("4. Delete all inactive data")
    print("0. Exit")
    
    while True:
        choice = input("\nSelect option: ").strip()
        
        if choice == '1':
            clean_inactive_items()
        elif choice == '2':
            clean_inactive_categories()
        elif choice == '3':
            clean_inactive_units()
        elif choice == '4':
            clean_inactive_items()
            clean_inactive_categories()
            clean_inactive_units()
        elif choice == '0':
            print("Thank you!")
            break
        else:
            print("Invalid option, please try again")
            continue
        
        # Ask to continue
        continue_choice = input("\nPerform another operation? (y/N): ").strip().lower()
        if continue_choice not in ['y', 'yes']:
            print("Thank you!")
            break

if __name__ == '__main__':
    main()