{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-clock text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة ساعات العمل الإضافي للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printDataList('سجل العمل الإضافي')">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="{% url 'hr:overtime_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عمل إضافي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ total_hours|floatformat:1 }}</h3>
                    <p class="mb-0">إجمالي الساعات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ total_amount|floatformat:0 }} ج.م</h3>
                    <p class="mb-0">إجمالي المبلغ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ employee_count }}</h3>
                    <p class="mb-0">عدد الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ avg_rate|floatformat:0 }} ج.م</h3>
                    <p class="mb-0">متوسط السعر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Overtime Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        سجل العمل الإضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>عدد الساعات</th>
                                    <th>سعر الساعة</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for overtime in overtime_records %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ overtime.employee.full_name }}
                                    </td>
                                    <td>{{ overtime.date }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ overtime.hours|floatformat:1 }} ساعة</span>
                                    </td>
                                    <td>{{ overtime.hourly_rate|floatformat:0 }} ج.م</td>
                                    <td>
                                        <strong>{{ overtime.total_amount|floatformat:0 }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="badge {% if overtime.status == 'APPROVED' %}bg-success{% elif overtime.status == 'REJECTED' %}bg-danger{% else %}bg-warning{% endif %}">
                                            {{ overtime.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:overtime_edit' overtime.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" title="حذف" onclick="confirmDelete('{{ overtime.pk }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-clock fa-3x mb-3 d-block"></i>
                                        لا توجد سجلات عمل إضافي
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Delete Form -->
<form id="deleteForm" method="post" style="display: none;">
    {% csrf_token %}
</form>

<script>
function confirmDelete(overtimeId) {
    if (confirm('هل أنت متأكد من حذف هذا العمل الإضافي؟')) {
        const form = document.getElementById('deleteForm');
        form.action = `/hr/overtime/${overtimeId}/delete/`;
        form.submit();
    }
}
</script>

{% endblock %}
