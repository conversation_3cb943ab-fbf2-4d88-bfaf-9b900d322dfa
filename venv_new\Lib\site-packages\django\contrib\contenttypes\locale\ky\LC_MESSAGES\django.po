# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-23 06:00+0000\n"
"Last-Translator: Soyuzbek Orozbek uulu <<EMAIL>>\n"
"Language-Team: Kyrgyz (http://www.transifex.com/django/django/language/ky/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ky\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "Мазмун түрү"

msgid "python model class name"
msgstr "питон модел класс ысымы"

msgid "content type"
msgstr "мазмун түрү"

msgid "content types"
msgstr "мазмун түрлөрү"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "%(ct_id)s мазмун түрүнүн өзүнө байланышкан модели жок."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "%(ct_id)sмазмун түрүнүн %(obj_id)sобектисинде жашабайт."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)sобекттеринде get_absolute_url() ыкмасы жок"
