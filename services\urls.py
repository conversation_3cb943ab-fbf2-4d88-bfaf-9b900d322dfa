from django.urls import path
from . import views

app_name = 'services'

urlpatterns = [
    # لوحة تحكم الخدمات
    path('', views.services_dashboard, name='services_dashboard'),

    # إدارة البيانات
    path('delete-data/', views.delete_data, name='delete_data'),
    path('recycle-bin/', views.recycle_bin, name='recycle_bin'),
    path('edit-history/', views.edit_history, name='edit_history'),
    
    # النسخ الاحتياطي والترخيص
    path('backup/', views.backup, name='backup'),
    path('backup/download/<int:backup_id>/', views.download_backup, name='download_backup'),
    path('backup/delete/<int:backup_id>/', views.delete_backup, name='delete_backup'),
    path('backup/restore/<int:backup_id>/', views.restore_backup, name='restore_backup'),
    path('backup/cleanup/', views.cleanup_old_backups, name='cleanup_old_backups'),
    path('license/', views.license_info, name='license'),
    
    # أدوات النظام
    path('recalculate-costs/', views.recalculate_costs, name='recalculate_costs'),
    path('taskbar-settings/', views.taskbar_settings, name='taskbar_settings'),
    
    # الإعدادات والأمان
    path('system-settings/', views.system_settings, name='system_settings'),
    path('financial-settings/', views.financial_settings, name='financial_settings'),
    path('print-design/', views.print_design, name='print_design'),
    path('barcode-design/', views.barcode_design, name='barcode_design'),
    path('barcode-manager/', views.barcode_manager, name='barcode_manager'),
    path('relogin/', views.relogin, name='relogin'),

    # تقرير حالة النظام
    path('system-report/', views.system_report, name='system_report'),

    # اختبار الإعدادات
    path('test-settings/', views.test_settings, name='test_settings'),

    # AJAX endpoints
    path('ajax/delete-records/', views.ajax_delete_records, name='ajax_delete_records'),
    path('ajax/restore-records/', views.ajax_restore_records, name='ajax_restore_records'),
    path('ajax/backup-create/', views.ajax_create_backup, name='ajax_create_backup'),
    path('ajax/validate-path/', views.ajax_validate_path, name='ajax_validate_path'),
    path('ajax/auto-save-setting/', views.ajax_auto_save_setting, name='ajax_auto_save_setting'),
    path('ajax/bulk-save-settings/', views.ajax_bulk_save_settings, name='ajax_bulk_save_settings'),
    path('ajax/recalculate/', views.ajax_recalculate_costs, name='ajax_recalculate'),
    path('ajax/update-barcode-count/', views.ajax_update_barcode_count, name='ajax_update_barcode_count'),
    path('backup/upload-restore/', views.upload_restore_backup, name='upload_restore_backup'),
]
