#!/usr/bin/env python3
"""
إزالة تحذيرات SSL وتحسين التطبيق
Remove SSL Warnings and Optimize Application

إزالة جميع تحذيرات SSL وجعل التطبيق يعمل بشكل مثالي
Remove all SSL warnings and make the application work perfectly
"""

import os
import sys
import subprocess
import winreg
import ctypes
from pathlib import Path
from datetime import datetime

class SSLWarningRemover:
    """مزيل تحذيرات SSL"""
    
    def __init__(self):
        self.ssl_dir = Path('ssl')
        self.cert_file = self.ssl_dir / 'server.crt'
        self.key_file = self.ssl_dir / 'server.key'
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def is_admin(self):
        """فحص إذا كان يعمل بصلاحيات Administrator"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def run_as_admin(self):
        """تشغيل كـ Administrator"""
        if self.is_admin():
            return True
        else:
            self.log_info("إعادة تشغيل بصلاحيات Administrator...")
            try:
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
                return False
            except:
                self.log_error("فشل في الحصول على صلاحيات Administrator")
                return False
    
    def install_certificate_system(self):
        """تثبيت الشهادة في النظام"""
        try:
            self.log_info("تثبيت الشهادة في النظام...")
            
            # تثبيت في Trusted Root Certification Authorities
            result = subprocess.run([
                'certutil', '-addstore', '-f', 'Root', str(self.cert_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_success("تم تثبيت الشهادة في النظام!")
                return True
            else:
                self.log_error(f"فشل في تثبيت الشهادة: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في تثبيت الشهادة: {e}")
            return False
    
    def install_certificate_user(self):
        """تثبيت الشهادة للمستخدم الحالي"""
        try:
            self.log_info("تثبيت الشهادة للمستخدم الحالي...")
            
            # تثبيت في Current User
            result = subprocess.run([
                'certutil', '-user', '-addstore', '-f', 'Root', str(self.cert_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_success("تم تثبيت الشهادة للمستخدم!")
                return True
            else:
                self.log_error(f"فشل في تثبيت الشهادة للمستخدم: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في تثبيت الشهادة للمستخدم: {e}")
            return False
    
    def add_chrome_flags(self):
        """إضافة flags لـ Chrome لتجاهل تحذيرات SSL"""
        try:
            self.log_info("إضافة إعدادات Chrome...")
            
            # إنشاء سكريپت Chrome بدون تحذيرات
            chrome_script = '''@echo off
REM تشغيل Chrome بدون تحذيرات SSL
REM Run Chrome without SSL warnings

echo بدء Chrome بدون تحذيرات SSL...
echo Starting Chrome without SSL warnings...

REM البحث عن Chrome
set CHROME_PATH=""
if exist "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" (
    set CHROME_PATH="C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
) else if exist "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe" (
    set CHROME_PATH="C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
) else (
    echo Chrome غير موجود
    pause
    exit
)

REM تشغيل Chrome مع إعدادات خاصة
%CHROME_PATH% --ignore-certificate-errors --ignore-ssl-errors --ignore-certificate-errors-spki-list --ignore-certificate-errors-skip-list --disable-ssl-warnings --allow-running-insecure-content --disable-web-security --user-data-dir="%TEMP%\\chrome_dev" https://***************:8443/

pause
'''
            
            with open('chrome_no_warnings.bat', 'w', encoding='utf-8') as f:
                f.write(chrome_script)
            
            self.log_success("تم إنشاء سكريپت Chrome بدون تحذيرات")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إنشاء سكريپت Chrome: {e}")
            return False
    
    def add_edge_flags(self):
        """إضافة flags لـ Edge لتجاهل تحذيرات SSL"""
        try:
            self.log_info("إضافة إعدادات Edge...")
            
            # إنشاء سكريپت Edge بدون تحذيرات
            edge_script = '''@echo off
REM تشغيل Edge بدون تحذيرات SSL
REM Run Edge without SSL warnings

echo بدء Edge بدون تحذيرات SSL...
echo Starting Edge without SSL warnings...

REM البحث عن Edge
set EDGE_PATH=""
if exist "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe" (
    set EDGE_PATH="C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe"
) else if exist "C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe" (
    set EDGE_PATH="C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe"
) else (
    echo Edge غير موجود
    pause
    exit
)

REM تشغيل Edge مع إعدادات خاصة
%EDGE_PATH% --ignore-certificate-errors --ignore-ssl-errors --ignore-certificate-errors-spki-list --ignore-certificate-errors-skip-list --disable-ssl-warnings --allow-running-insecure-content --disable-web-security --user-data-dir="%TEMP%\\edge_dev" https://***************:8443/

pause
'''
            
            with open('edge_no_warnings.bat', 'w', encoding='utf-8') as f:
                f.write(edge_script)
            
            self.log_success("تم إنشاء سكريپت Edge بدون تحذيرات")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إنشاء سكريپت Edge: {e}")
            return False
    
    def create_hosts_entry(self):
        """إضافة إدخال في ملف hosts"""
        try:
            self.log_info("إضافة إدخال في ملف hosts...")
            
            hosts_file = Path("C:/Windows/System32/drivers/etc/hosts")
            
            # قراءة ملف hosts الحالي
            try:
                with open(hosts_file, 'r') as f:
                    content = f.read()
            except:
                content = ""
            
            # إضافة إدخالات جديدة
            new_entries = """
# إدخالات نظام المحاسبة الآمن
# Secure Accounting System Entries
*************** osaric.local
*************** accounting.local
127.0.0.1 osaric.local
127.0.0.1 accounting.local
"""
            
            if "osaric.local" not in content:
                with open(hosts_file, 'a') as f:
                    f.write(new_entries)
                self.log_success("تم إضافة إدخالات hosts")
            else:
                self.log_info("إدخالات hosts موجودة بالفعل")
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في تعديل ملف hosts: {e}")
            return False
    
    def optimize_django_settings(self):
        """تحسين إعدادات Django للـ HTTPS"""
        try:
            self.log_info("تحسين إعدادات Django...")
            
            settings_file = Path('osaric_accounts/settings.py')
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إضافة إعدادات HTTPS محسنة
                https_settings = '''

# إعدادات HTTPS محسنة - Enhanced HTTPS Settings
SECURE_SSL_REDIRECT = False  # للتطوير المحلي
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 0  # للتطوير المحلي
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'SAMEORIGIN'
CSRF_COOKIE_SECURE = False  # للتطوير المحلي
SESSION_COOKIE_SECURE = False  # للتطوير المحلي
CSRF_TRUSTED_ORIGINS = [
    'https://***************:8443',
    'https://localhost:8443',
    'https://osaric.local:8443',
    'https://accounting.local:8443',
]
ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    '***************',
    'osaric.local',
    'accounting.local',
    'DESKTOP-H8H1ID4',
]

# إعدادات الأمان المحسنة - Enhanced Security Settings
SECURE_REFERRER_POLICY = 'same-origin'
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin-allow-popups'
'''
                
                if 'CSRF_TRUSTED_ORIGINS' not in content:
                    content += https_settings
                    
                    with open(settings_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.log_success("تم تحسين إعدادات Django")
                else:
                    self.log_info("إعدادات Django محسنة بالفعل")
                
                return True
            else:
                self.log_error("ملف settings.py غير موجود")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في تحسين إعدادات Django: {e}")
            return False
    
    def create_secure_launcher(self):
        """إنشاء مشغل آمن للتطبيق"""
        try:
            self.log_info("إنشاء مشغل آمن...")
            
            launcher_script = '''@echo off
title التطبيق الآمن - Secure Application
color 0A

echo ============================================================
echo 🔒 نظام المحاسبة الآمن - بدون تحذيرات
echo Secure Accounting System - No Warnings
echo ============================================================
echo.

echo 🔍 فحص النظام...
echo Checking system...
echo.

REM فحص الخادم الأبدي
netstat -an | findstr ":8000" >nul
if %errorLevel% == 0 (
    echo ✅ الخادم الأبدي يعمل
) else (
    echo 🚀 بدء الخادم الأبدي...
    start "الخادم الأبدي" python eternal_server.py
    timeout /t 5 /nobreak >nul
)

REM فحص بروكسي HTTPS
netstat -an | findstr ":8443" >nul
if %errorLevel% == 0 (
    echo ✅ بروكسي HTTPS يعمل
) else (
    echo 🔒 بدء بروكسي HTTPS...
    start "بروكسي HTTPS" python https_proxy.py
    timeout /t 3 /nobreak >nul
)

echo.
echo ============================================================
echo 🌐 خيارات الوصول الآمن
echo Secure Access Options
echo ============================================================
echo.

echo 📋 اختر طريقة الوصول:
echo 1. Chrome بدون تحذيرات (موصى به)
echo 2. Edge بدون تحذيرات
echo 3. فتح الرابط في المتصفح الافتراضي
echo 4. عرض معلومات الوصول فقط
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" (
    echo 🚀 فتح Chrome بدون تحذيرات...
    call chrome_no_warnings.bat
) else if "%choice%"=="2" (
    echo 🚀 فتح Edge بدون تحذيرات...
    call edge_no_warnings.bat
) else if "%choice%"=="3" (
    echo 🌐 فتح الرابط في المتصفح الافتراضي...
    start https://***************:8443/dashboard/
) else if "%choice%"=="4" (
    goto show_info
) else if "%choice%"=="0" (
    goto exit
) else (
    echo ❌ خيار غير صحيح
    pause
    goto menu
)

goto end

:show_info
echo.
echo ============================================================
echo 🌐 معلومات الوصول الآمن
echo Secure Access Information
echo ============================================================
echo.
echo 🔒 الروابط الآمنة:
echo    https://***************:8443/dashboard/
echo    https://localhost:8443/dashboard/
echo    https://osaric.local:8443/dashboard/
echo    https://accounting.local:8443/dashboard/
echo.
echo 💡 نصائح:
echo • استخدم Chrome أو Edge بدون تحذيرات (الخيار 1 أو 2)
echo • أو اضغط Advanced ثم Proceed في المتصفح العادي
echo • الاتصال آمن ومشفر بالكامل
echo.
pause

:end
echo.
echo 🎉 شكراً لاستخدام النظام الآمن!
echo Thank you for using the Secure System!
echo.
pause

:exit
exit /b 0
'''
            
            with open('secure_launcher.bat', 'w', encoding='utf-8') as f:
                f.write(launcher_script)
            
            self.log_success("تم إنشاء المشغل الآمن: secure_launcher.bat")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إنشاء المشغل الآمن: {e}")
            return False
    
    def run(self):
        """تشغيل مزيل التحذيرات"""
        print("=" * 60)
        print("🔒 إزالة تحذيرات SSL وتحسين التطبيق")
        print("Remove SSL Warnings and Optimize Application")
        print("=" * 60)
        
        # فحص صلاحيات Administrator
        if not self.is_admin():
            self.log_info("يحتاج صلاحيات Administrator لبعض العمليات")
            choice = input("هل تريد المتابعة بدون صلاحيات Administrator؟ (y/n): ")
            if choice.lower() != 'y':
                self.log_info("إعادة تشغيل بصلاحيات Administrator...")
                self.run_as_admin()
                return
        
        success_count = 0
        total_operations = 6
        
        # تثبيت الشهادة في النظام
        if self.is_admin() and self.install_certificate_system():
            success_count += 1
        
        # تثبيت الشهادة للمستخدم
        if self.install_certificate_user():
            success_count += 1
        
        # إضافة سكريپتات المتصفحات
        if self.add_chrome_flags():
            success_count += 1
        
        if self.add_edge_flags():
            success_count += 1
        
        # تحسين إعدادات Django
        if self.optimize_django_settings():
            success_count += 1
        
        # إنشاء المشغل الآمن
        if self.create_secure_launcher():
            success_count += 1
        
        # إضافة إدخالات hosts (يحتاج صلاحيات)
        if self.is_admin():
            self.create_hosts_entry()
        
        print("\n" + "=" * 60)
        print("🎉 تم الانتهاء من تحسين النظام!")
        print("=" * 60)
        
        print(f"\n📊 النتائج:")
        print(f"   العمليات المكتملة: {success_count}/{total_operations}")
        print(f"   معدل النجاح: {(success_count/total_operations)*100:.1f}%")
        
        print(f"\n🚀 للوصول الآمن:")
        print(f"   شغل: secure_launcher.bat")
        print(f"   أو: chrome_no_warnings.bat")
        print(f"   أو: edge_no_warnings.bat")
        
        print(f"\n🔒 الروابط الآمنة:")
        print(f"   https://***************:8443/dashboard/")
        print(f"   https://localhost:8443/dashboard/")
        
        if success_count >= total_operations * 0.8:
            print(f"\n✅ النظام محسن بنجاح! لا تحذيرات أمان")
        else:
            print(f"\n⚠️ بعض التحسينات تحتاج صلاحيات Administrator")
        
        return success_count >= total_operations * 0.5

def main():
    """الدالة الرئيسية"""
    remover = SSLWarningRemover()
    success = remover.run()
    
    if success:
        print("\n🎊 تم تحسين النظام بنجاح!")
        print("استخدم secure_launcher.bat للوصول الآمن")
    else:
        print("\n❌ فشل في تحسين النظام")

if __name__ == "__main__":
    main()
