# Generated by Django 5.2.2 on 2025-06-28 03:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomPermissionGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permissions', models.ManyToManyField(blank=True, to='auth.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'مجموعة صلاحيات',
                'verbose_name_plural': 'مجموعات الصلاحيات',
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('view', 'عرض'), ('export', 'تصدير'), ('import', 'استيراد')], max_length=20, verbose_name='النشاط')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('object_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف الكائن')),
                ('object_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم الكائن')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='متصفح المستخدم')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('details', models.JSONField(blank=True, null=True, verbose_name='تفاصيل إضافية')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'نشاط مستخدم',
                'verbose_name_plural': 'نشاطات المستخدمين',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='القسم')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='المنصب')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الراتب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'ملف تعريف المستخدم',
                'verbose_name_plural': 'ملفات تعريف المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='UserPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(choices=[('dashboard', 'لوحة التحكم'), ('sales', 'المبيعات'), ('purchases', 'المشتريات'), ('inventory', 'المخزون'), ('treasury', 'الخزينة'), ('banking', 'البنوك'), ('accounts', 'الحسابات'), ('reports', 'التقارير'), ('definitions', 'التعريفات'), ('hr', 'الموارد البشرية'), ('assets', 'الأصول الثابتة'), ('branches', 'الفروع'), ('services', 'الخدمات'), ('windows', 'النوافذ')], max_length=50, verbose_name='الوحدة')),
                ('permission_type', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('change', 'تعديل'), ('delete', 'حذف'), ('export', 'تصدير'), ('import', 'استيراد'), ('approve', 'موافقة'), ('reject', 'رفض')], max_length=20, verbose_name='نوع الصلاحية')),
                ('is_granted', models.BooleanField(default=True, verbose_name='ممنوحة')),
                ('granted_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المنح')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to=settings.AUTH_USER_MODEL, verbose_name='منحت بواسطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_permissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'صلاحية مستخدم',
                'verbose_name_plural': 'صلاحيات المستخدمين',
                'unique_together': {('user', 'module', 'permission_type')},
            },
        ),
    ]
