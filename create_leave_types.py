#!/usr/bin/env python
"""
سكريبت لإنشاء أنواع الإجازات الأساسية
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from hr.models import LeaveType

def create_leave_types():
    """إنشاء أنواع الإجازات الأساسية"""
    
    # قائمة أنواع الإجازات
    leave_types_data = [
        {
            'code': 'ANNUAL',
            'name': 'إجازة سنوية',
            'name_english': 'Annual Leave',
            'description': 'الإجازة السنوية المقررة للموظفين',
            'category': 'ANNUAL',
            'allowed_days': 21,
            'is_paid': True,
            'requires_approval': True,
            'can_carry_forward': True,
            'max_carry_forward_days': 7,
            'color': '#28a745'
        },
        {
            'code': 'SICK',
            'name': 'إجازة مرضية',
            'name_english': 'Sick Leave',
            'description': 'الإجازة المرضية للموظفين',
            'category': 'SICK',
            'allowed_days': 30,
            'is_paid': True,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#dc3545'
        },
        {
            'code': 'EMERGENCY',
            'name': 'إجازة طارئة',
            'name_english': 'Emergency Leave',
            'description': 'الإجازة الطارئة للموظفين',
            'category': 'EMERGENCY',
            'allowed_days': 3,
            'is_paid': False,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#ffc107'
        },
        {
            'code': 'MATERNITY',
            'name': 'إجازة أمومة',
            'name_english': 'Maternity Leave',
            'description': 'إجازة الأمومة للموظفات',
            'category': 'MATERNITY',
            'allowed_days': 90,
            'is_paid': True,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#e83e8c'
        },
        {
            'code': 'PATERNITY',
            'name': 'إجازة أبوة',
            'name_english': 'Paternity Leave',
            'description': 'إجازة الأبوة للموظفين',
            'category': 'PATERNITY',
            'allowed_days': 7,
            'is_paid': True,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#17a2b8'
        },
        {
            'code': 'STUDY',
            'name': 'إجازة دراسية',
            'name_english': 'Study Leave',
            'description': 'الإجازة الدراسية للموظفين',
            'category': 'STUDY',
            'allowed_days': 15,
            'is_paid': False,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#6f42c1'
        },
        {
            'code': 'PILGRIMAGE',
            'name': 'إجازة حج',
            'name_english': 'Pilgrimage Leave',
            'description': 'إجازة الحج للموظفين',
            'category': 'PILGRIMAGE',
            'allowed_days': 30,
            'is_paid': True,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#fd7e14'
        },
        {
            'code': 'OTHER',
            'name': 'إجازة أخرى',
            'name_english': 'Other Leave',
            'description': 'إجازات أخرى متنوعة',
            'category': 'OTHER',
            'allowed_days': 5,
            'is_paid': False,
            'requires_approval': True,
            'can_carry_forward': False,
            'max_carry_forward_days': 0,
            'color': '#6c757d'
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for data in leave_types_data:
        leave_type, created = LeaveType.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        
        if created:
            print(f"✓ تم إنشاء نوع الإجازة: {data['name']}")
            created_count += 1
        else:
            # تحديث البيانات الموجودة
            for key, value in data.items():
                setattr(leave_type, key, value)
            leave_type.save()
            print(f"✓ تم تحديث نوع الإجازة: {data['name']}")
            updated_count += 1
    
    print(f"\n📊 ملخص العملية:")
    print(f"   - تم إنشاء {created_count} نوع إجازة جديد")
    print(f"   - تم تحديث {updated_count} نوع إجازة موجود")
    print(f"   - إجمالي أنواع الإجازات: {LeaveType.objects.count()}")

if __name__ == '__main__':
    print("🚀 بدء إنشاء أنواع الإجازات الأساسية...")
    create_leave_types()
    print("✅ تم الانتهاء من إنشاء أنواع الإجازات!") 