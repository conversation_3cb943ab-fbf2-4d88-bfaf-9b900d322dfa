#!/usr/bin/env python
"""
إنشاء إعدادات الشركة الافتراضية
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import CompanySettings, Currency

def create_company_settings():
    """إنشاء إعدادات الشركة الافتراضية"""
    
    print("🔧 إنشاء إعدادات الشركة الافتراضية...")
    
    try:
        # التحقق من وجود إعدادات الشركة
        company = CompanySettings.objects.first()
        
        if company:
            print(f"✅ إعدادات الشركة موجودة: {company.company_name}")
            return
        
        # إنشاء إعدادات الشركة الجديدة
        company = CompanySettings.objects.create(
            company_name="حسابات أوساريك",
            company_name_english="Osaric Accounts",
            app_name="حسابات أوساريك",
            phone="",
            mobile="",
            email="",
            website="",
            address="",
            city="",
            state="",
            country="مصر",
            postal_code="",
            tax_number="",
            commercial_register="",
            app_version="1.0.0",
            items_per_page=25,
            date_format='%d/%m/%Y',
            session_timeout_minutes=60,
            password_min_length=8,
            auto_backup_enabled=False,
            backup_frequency_days=7,
            email_notifications_enabled=True,
            sms_notifications_enabled=False,
            default_report_format='PDF',
            print_logo_on_reports=True,
            print_company_info=True,
            notes=""
        )
        
        print(f"✅ تم إنشاء إعدادات الشركة بنجاح: {company.company_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء إعدادات الشركة: {e}")

if __name__ == "__main__":
    create_company_settings()
