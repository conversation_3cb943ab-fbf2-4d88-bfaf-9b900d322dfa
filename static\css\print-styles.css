/**
 * تصميمات الطباعة المحسنة
 * Enhanced Print Styles for HR System
 */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* تصميمات الطباعة العامة */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-size: 11pt;
        line-height: 1.5;
        color: #2c3e50;
        margin: 0;
        padding: 15px;
        direction: rtl;
        text-align: right;
        background: white;
    }
    
    /* إخفاء العناصر غير المرغوب فيها */
    .no-print,
    .btn,
    .dropdown,
    .modal,
    .navbar-nav,
    .sidebar,
    .breadcrumb,
    .pagination,
    .form-control,
    .form-select,
    .alert.alert-dismissible {
        display: none !important;
    }
    
    /* تصميم الحاويات */
    .container,
    .container-fluid {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .row {
        margin: 0 !important;
    }
    
    .col,
    .col-md-6,
    .col-lg-3,
    .col-lg-4,
    .col-lg-8,
    .col-12 {
        padding: 0 5px !important;
    }
    
    /* تصميم البطاقات */
    .card {
        border: 2px solid #34495e !important;
        margin-bottom: 25px !important;
        page-break-inside: avoid;
        border-radius: 8px !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        background: white !important;
    }
    
    .card-header {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
        color: white !important;
        border-bottom: 2px solid #2980b9 !important;
        padding: 12px 20px !important;
        font-weight: 600 !important;
        font-size: 13pt !important;
        border-radius: 6px 6px 0 0 !important;
    }
    
    .card-body {
        padding: 20px !important;
        background: white !important;
    }
    
    /* تصميم الجداول */
    .table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 25px !important;
        font-size: 10pt !important;
        background: white !important;
    }
    
    .table th {
        background: linear-gradient(135deg, #34495e, #2c3e50) !important;
        color: white !important;
        border: 1px solid #2c3e50 !important;
        padding: 12px 8px !important;
        text-align: center !important;
        font-weight: 600 !important;
        font-size: 11pt !important;
    }
    
    .table td {
        border: 1px solid #bdc3c7 !important;
        padding: 10px 8px !important;
        text-align: center !important;
        background: white !important;
    }
    
    .table tbody tr:nth-child(even) {
        background-color: #f8f9fa !important;
    }
    
    .table tbody tr:hover {
        background-color: #e3f2fd !important;
    }
    
    /* تصميم الشارات */
    .badge {
        background-color: #6c757d !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 9pt !important;
        font-weight: 500 !important;
        display: inline-block !important;
    }
    
    .badge.bg-primary { background-color: #3498db !important; }
    .badge.bg-success { background-color: #27ae60 !important; }
    .badge.bg-danger { background-color: #e74c3c !important; }
    .badge.bg-warning { background-color: #f39c12 !important; color: #2c3e50 !important; }
    .badge.bg-info { background-color: #17a2b8 !important; }
    .badge.bg-secondary { background-color: #95a5a6 !important; }
    
    /* ألوان النصوص */
    .text-primary { color: #3498db !important; }
    .text-success { color: #27ae60 !important; }
    .text-danger { color: #e74c3c !important; }
    .text-warning { color: #f39c12 !important; }
    .text-info { color: #17a2b8 !important; }
    .text-muted { color: #7f8c8d !important; }
    
    /* خلفيات ملونة */
    .bg-primary { background-color: #3498db !important; color: white !important; }
    .bg-success { background-color: #27ae60 !important; color: white !important; }
    .bg-danger { background-color: #e74c3c !important; color: white !important; }
    .bg-warning { background-color: #f39c12 !important; color: #2c3e50 !important; }
    .bg-info { background-color: #17a2b8 !important; color: white !important; }
    
    /* رأس الطباعة */
    .print-header {
        text-align: center;
        margin-bottom: 40px;
        border: 3px solid #2c3e50;
        border-radius: 10px;
        padding: 25px;
        background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
        page-break-inside: avoid;
    }
    
    .print-title {
        font-size: 20pt;
        font-weight: 700;
        margin-bottom: 15px;
        color: #2c3e50;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }
    
    .print-subtitle {
        font-size: 14pt;
        color: #34495e;
        margin-bottom: 10px;
        font-weight: 500;
    }
    
    .print-date {
        font-size: 12pt;
        color: #7f8c8d;
        font-weight: 400;
    }
    
    /* ذيل الطباعة */
    .print-footer {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10pt;
        color: #7f8c8d;
        border-top: 1px solid #bdc3c7;
        padding-top: 10px;
    }
    
    /* بطاقات الإحصائيات */
    .stats-card {
        border: 2px solid #3498db !important;
        border-radius: 8px !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        padding: 15px !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    }
    
    .stats-number {
        font-size: 18pt !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 5px !important;
    }
    
    /* العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #2c3e50 !important;
        font-weight: 600 !important;
        margin-bottom: 15px !important;
    }
    
    .fw-bold, .font-weight-bold {
        font-weight: 600 !important;
    }
    
    .form-label {
        font-weight: 500 !important;
        color: #34495e !important;
        margin-bottom: 5px !important;
    }
    
    /* إعدادات الصفحة */
    @page {
        margin: 1.5cm;
        size: A4;
        @bottom-center {
            content: "صفحة " counter(page) " من " counter(pages);
            font-size: 10pt;
            color: #7f8c8d;
        }
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .no-break {
        page-break-inside: avoid;
    }
    
    /* شعار الشركة */
    .print-logo {
        max-height: 60px;
        margin-bottom: 15px;
    }
    
    /* معلومات الشركة */
    .company-info {
        background: linear-gradient(135deg, #2c3e50, #34495e);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
}

/* تصميمات خاصة للتقارير */
@media print {
    .report-header {
        background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
        color: white !important;
        text-align: center !important;
        padding: 20px !important;
        border-radius: 10px !important;
        margin-bottom: 30px !important;
    }
    
    .invoice-header {
        background: linear-gradient(135deg, #27ae60, #229954) !important;
        color: white !important;
        text-align: center !important;
        padding: 20px !important;
        border-radius: 10px !important;
        margin-bottom: 30px !important;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
        color: white !important;
        text-align: center !important;
        padding: 20px !important;
        border-radius: 10px !important;
        margin-bottom: 30px !important;
    }
}
