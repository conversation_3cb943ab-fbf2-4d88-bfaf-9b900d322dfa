# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <o.chern<PERSON><PERSON>@gmail.com>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> Volochii <<EMAIL>>, 2021
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-20 00:21+0000\n"
"Last-Translator: Illia Volochii <<EMAIL>>\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Administrative Documentation"
msgstr "Адміністративна документація"

msgid "Home"
msgstr "Домівка"

msgid "Documentation"
msgstr "Документація"

msgid "Bookmarklets"
msgstr "Інтерактивні закладки"

msgid "Documentation bookmarklets"
msgstr "Інтерактивні закладки документації"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Щоб встановити bookmarklets, перетягніть посилання на вашу bookmarklets або "
"клацніть правою кнопкою миші по посиланню і додайте його в bookmarklets. "
"Тепер ви можете вибрати bookmarklets з будь-якої сторінки сайту."

msgid "Documentation for this page"
msgstr "Документація для цієї сторінки"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Перекидає вас з будь-якої сторінки в документацію для відображення, що "
"сгенерувало цю сторінку."

msgid "Tags"
msgstr "Теги"

msgid "List of all the template tags and their functions."
msgstr "Список усіх тегів шаблонів та їх функцій."

msgid "Filters"
msgstr "Фільтри"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Фільтри - це дії, які можуть бути виконані над змінними в шаблоні, щоб "
"змінити їх відображення."

msgid "Models"
msgstr "Моделі"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Моделі - це описання всіх об'єктів системи та пов'язаних з ними полів.\n"
"Кожна модель має список полів що можуть бути доступні у вигляді змінних в "
"шаблонах."

msgid "Views"
msgstr "Відображення"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Кожна сторінка для відкритого сайту створена за допомогою відображення."
"Відображення визначає, який шаблон використовується для генерації сторінки "
"та об'єкти, які доступні для цього шаблону."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Інструменти для вашого браузеру для швидкого доступу до адміністративних "
"функцій."

msgid "Please install docutils"
msgstr "Будь ласка, встановіть docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Система адміністративної документації вимагає Python-бібліотеку <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Будь ласка, попросить вашого адміністратора встановити <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Модель: %(name)s"

msgid "Fields"
msgstr "Поля"

msgid "Field"
msgstr "Поле"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Опис"

msgid "Methods with arguments"
msgstr "Методи з аргументами"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументи"

msgid "Back to Model documentation"
msgstr "Назад до Документація моделі"

msgid "Model documentation"
msgstr "Документація моделі"

msgid "Model groups"
msgstr "Групи моделей"

msgid "Templates"
msgstr "Шаблони"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Шаблон: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Шлях для пошуку шаблону <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(не існує)"

msgid "Back to Documentation"
msgstr "Назад до Документації"

msgid "Template filters"
msgstr "Фільтри у шаблонах"

msgid "Template filter documentation"
msgstr "Документація фільтрів у шаблонах"

msgid "Built-in filters"
msgstr "Вбудовані фільтри"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Щоб використовувати ці фільтри, помістіть <code>%(code)s</code> в ваш шаблон "
"перед використанням фільтру."

msgid "Template tags"
msgstr "Теги шаблонів"

msgid "Template tag documentation"
msgstr "Документація по тегам шаблонів"

msgid "Built-in tags"
msgstr "Вбудовані теги"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Щоб використовувати ці теги, помістіть <code>%(code)s</code> в ваш шаблон "
"перед використанням тегу."

#, python-format
msgid "View: %(name)s"
msgstr "Відображення: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблони:"

msgid "Back to View documentation"
msgstr "Назад до Переглянути документацію"

msgid "View documentation"
msgstr "Переглянути документацію"

msgid "Jump to namespace"
msgstr "Перейти до іменованої зони"

msgid "Empty namespace"
msgstr "Порожня іменована зона"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Відображення у просторі імен %(name)s"

msgid "Views by empty namespace"
msgstr "Відображення у глобальному просторі імен "

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Функція відображення: <code>%(full_name)s</code>. Им'я: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "тег:"

msgid "filter:"
msgstr "відфільтрувати:"

msgid "view:"
msgstr "переглянути:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Застосунок %(app_label)r не знайдено"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модель %(model_name)r не знайдено в прикладній системі %(app_label)r"

msgid "model:"
msgstr "модель:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "пов'язаний `%(app_label)s.%(data_type)s` об'єкт"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "пов'язані з `%(app_label)s.%(object_name)s` об'єкти"

#, python-format
msgid "all %s"
msgstr "всі %s"

#, python-format
msgid "number of %s"
msgstr "кількість з %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не є об'єктом urlpattern"
