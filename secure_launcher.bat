@echo off
title التطبيق الآمن - Secure Application
color 0A

echo ============================================================
echo 🔒 نظام المحاسبة الآمن - بدون تحذيرات
echo Secure Accounting System - No Warnings
echo ============================================================
echo.

echo 🔍 فحص النظام...
echo Checking system...
echo.

REM فحص الخادم الأبدي
netstat -an | findstr ":8000" >nul
if %errorLevel% == 0 (
    echo ✅ الخادم الأبدي يعمل
) else (
    echo 🚀 بدء الخادم الأبدي...
    start "الخادم الأبدي" python eternal_server.py
    timeout /t 5 /nobreak >nul
)

REM فحص بروكسي HTTPS
netstat -an | findstr ":8443" >nul
if %errorLevel% == 0 (
    echo ✅ بروكسي HTTPS يعمل
) else (
    echo 🔒 بدء بروكسي HTTPS...
    start "بروكسي HTTPS" python https_proxy.py
    timeout /t 3 /nobreak >nul
)

echo.
echo ============================================================
echo 🌐 خيارات الوصول الآمن
echo Secure Access Options
echo ============================================================
echo.

echo 📋 اختر طريقة الوصول:
echo 1. Chrome بدون تحذيرات (موصى به)
echo 2. Edge بدون تحذيرات
echo 3. فتح الرابط في المتصفح الافتراضي
echo 4. عرض معلومات الوصول فقط
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" (
    echo 🚀 فتح Chrome بدون تحذيرات...
    call chrome_no_warnings.bat
) else if "%choice%"=="2" (
    echo 🚀 فتح Edge بدون تحذيرات...
    call edge_no_warnings.bat
) else if "%choice%"=="3" (
    echo 🌐 فتح الرابط في المتصفح الافتراضي...
    start https://***************:8443/dashboard/
) else if "%choice%"=="4" (
    goto show_info
) else if "%choice%"=="0" (
    goto exit
) else (
    echo ❌ خيار غير صحيح
    pause
    goto menu
)

goto end

:show_info
echo.
echo ============================================================
echo 🌐 معلومات الوصول الآمن
echo Secure Access Information
echo ============================================================
echo.
echo 🔒 الروابط الآمنة:
echo    https://***************:8443/dashboard/
echo    https://localhost:8443/dashboard/
echo    https://osaric.local:8443/dashboard/
echo    https://accounting.local:8443/dashboard/
echo.
echo 💡 نصائح:
echo • استخدم Chrome أو Edge بدون تحذيرات (الخيار 1 أو 2)
echo • أو اضغط Advanced ثم Proceed في المتصفح العادي
echo • الاتصال آمن ومشفر بالكامل
echo.
pause

:end
echo.
echo 🎉 شكراً لاستخدام النظام الآمن!
echo Thank you for using the Secure System!
echo.
pause

:exit
exit /b 0
