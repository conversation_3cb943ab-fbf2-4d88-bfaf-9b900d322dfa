/**
 * محرر التصميم المتقدم
 * Advanced Design Editor
 */

class DesignEditor {
    constructor() {
        this.currentElement = null;
        this.designSettings = {
            // معلومات الشركة
            companyLogo: null,
            companyName: 'شركة [اسم الشركة]',
            companyAddress: '[عنوان الشركة]',
            companyPhone: '[رقم الهاتف]',
            companyEmail: '[البريد الإلكتروني]',
            companyWebsite: '[الموقع الإلكتروني]',
            companyTaxNumber: '[الرقم الضريبي]',
            companyCommercialRecord: '[السجل التجاري]',

            // إعدادات الرأس
            headerColor: '#007bff',
            headerTextColor: '#ffffff',
            headerHeight: 'auto',
            headerBorderRadius: '8px',
            showCompanyInfo: true,
            companyInfoPosition: 'right',

            // إعدادات الخطوط والنصوص
            fontSize: '12pt',
            fontFamily: 'Arial',
            titleFontSize: '18pt',
            headerFontSize: '14pt',
            textAlign: 'right',
            lineHeight: '1.5',
            letterSpacing: 'normal',

            // إعدادات الشعار
            showLogo: true,
            logoPosition: 'right',
            logoSize: 'medium',
            logoShape: 'rectangle',
            logoBorder: false,
            logoShadow: false,

            // إعدادات الحدود والخلفيات
            borderStyle: 'solid',
            borderColor: '#333333',
            borderWidth: '1px',
            backgroundColor: '#ffffff',
            textColor: '#000000',
            pageMargin: '20px',
            contentPadding: '20px',

            // إعدادات الجداول
            tableHeaderColor: '#f8f9fa',
            tableHeaderTextColor: '#000000',
            tableRowColor: '#ffffff',
            tableAlternateColor: '#f8f9fa',
            tableBorderColor: '#dee2e6',
            tableBorderWidth: '1px',
            tableHeaderFontWeight: 'bold',
            tableCellPadding: '8px',

            // إعدادات الفاتورة
            invoiceTitle: 'فاتورة',
            showInvoiceNumber: true,
            showInvoiceDate: true,
            showDueDate: false,
            showPaymentTerms: false,
            showNotes: false,
            showSignature: false,

            // إعدادات العميل/المورد
            showCustomerInfo: true,
            customerInfoTitle: 'بيانات العميل',
            showCustomerAddress: true,
            showCustomerPhone: true,
            showCustomerEmail: false,
            showCustomerTaxNumber: true,

            // إعدادات الأصناف
            showItemCode: false,
            showItemDescription: true,
            showItemUnit: false,
            showItemDiscount: false,
            showItemTax: false,
            itemsTableTitle: 'تفاصيل الأصناف',

            // إعدادات الإجماليات
            showSubtotal: true,
            showTax: true,
            showDiscount: false,
            showShipping: false,
            showTotal: true,
            currencySymbol: 'ج.م',
            currencyPosition: 'after',

            // إعدادات التذييل
            showFooter: true,
            footerText: 'شكراً لتعاملكم معنا',
            footerSubText: 'هذه الفاتورة مُنشأة إلكترونياً',
            showPrintDate: true,
            showPageNumbers: false,

            // إعدادات متقدمة
            watermark: '',
            showWatermark: false,
            watermarkOpacity: '0.1',
            qrCode: false,
            qrCodeData: '',
            barcode: false,
            barcodeData: '',
            barcodeType: 'CODE128',
            barcodeWidth: 200,
            barcodeHeight: 60,
            barcodePosition: 'bottom-right',
            barcodeShowText: true
        };
        this.init();
    }

    init() {
        this.createEditorInterface();
        this.bindEvents();
    }

    createEditorInterface() {
        const editorHTML = `
            <div class="design-editor-container">
                <!-- شريط الأدوات -->
                <div class="editor-toolbar">
                    <!-- معلومات الشركة المحسنة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="companyName" value="${this.designSettings.companyName}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">عنوان الشركة</label>
                                <input type="text" class="form-control" id="companyAddress" value="${this.designSettings.companyAddress}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="companyPhone" value="${this.designSettings.companyPhone}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="companyEmail" value="${this.designSettings.companyEmail}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="companyWebsite" value="${this.designSettings.companyWebsite}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="companyTaxNumber" value="${this.designSettings.companyTaxNumber}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">السجل التجاري</label>
                                <input type="text" class="form-control" id="companyCommercialRecord" value="${this.designSettings.companyCommercialRecord}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showCompanyInfo" ${this.designSettings.showCompanyInfo ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCompanyInfo">إظهار معلومات الشركة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">موضع معلومات الشركة</label>
                                <select class="form-select" id="companyInfoPosition">
                                    <option value="right" ${this.designSettings.companyInfoPosition === 'right' ? 'selected' : ''}>يمين</option>
                                    <option value="left" ${this.designSettings.companyInfoPosition === 'left' ? 'selected' : ''}>يسار</option>
                                    <option value="center" ${this.designSettings.companyInfoPosition === 'center' ? 'selected' : ''}>وسط</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الشعار المحسنة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-image me-2"></i>إعدادات الشعار</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">رفع الشعار</label>
                                <input type="file" class="form-control" id="logoUpload" accept="image/*">
                                <small class="text-muted">يُفضل PNG أو JPG بحجم أقصى 2MB</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">معاينة الشعار</label>
                                <div id="logoPreview" style="border: 2px dashed #dee2e6; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 8px; background: #f8f9fa;">
                                    <span class="text-muted">لا يوجد شعار</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label class="form-label">موضع الشعار</label>
                                <select class="form-select" id="logoPosition">
                                    <option value="right" ${this.designSettings.logoPosition === 'right' ? 'selected' : ''}>يمين</option>
                                    <option value="left" ${this.designSettings.logoPosition === 'left' ? 'selected' : ''}>يسار</option>
                                    <option value="center" ${this.designSettings.logoPosition === 'center' ? 'selected' : ''}>وسط</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">حجم الشعار</label>
                                <select class="form-select" id="logoSize">
                                    <option value="small" ${this.designSettings.logoSize === 'small' ? 'selected' : ''}>صغير (60px)</option>
                                    <option value="medium" ${this.designSettings.logoSize === 'medium' ? 'selected' : ''}>متوسط (80px)</option>
                                    <option value="large" ${this.designSettings.logoSize === 'large' ? 'selected' : ''}>كبير (100px)</option>
                                    <option value="xlarge">كبير جداً (120px)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">شكل الشعار</label>
                                <select class="form-select" id="logoShape">
                                    <option value="rectangle" ${this.designSettings.logoShape === 'rectangle' ? 'selected' : ''}>مستطيل</option>
                                    <option value="circle" ${this.designSettings.logoShape === 'circle' ? 'selected' : ''}>دائري</option>
                                    <option value="rounded" ${this.designSettings.logoShape === 'rounded' ? 'selected' : ''}>زوايا مدورة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="mt-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="showLogo" ${this.designSettings.showLogo ? 'checked' : ''}>
                                        <label class="form-check-label" for="showLogo">إظهار الشعار</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="logoBorder" ${this.designSettings.logoBorder ? 'checked' : ''}>
                                    <label class="form-check-label" for="logoBorder">إضافة حدود للشعار</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="logoShadow" ${this.designSettings.logoShadow ? 'checked' : ''}>
                                    <label class="form-check-label" for="logoShadow">إضافة ظل للشعار</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الألوان المتقدمة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-palette me-2"></i>الألوان والتصميم</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">لون الرأس</label>
                                <input type="color" class="form-control" id="headerColor" value="${this.designSettings.headerColor}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">لون نص الرأس</label>
                                <input type="color" class="form-control" id="headerTextColor" value="${this.designSettings.headerTextColor}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">لون الخلفية</label>
                                <input type="color" class="form-control" id="backgroundColor" value="${this.designSettings.backgroundColor}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">لون النص</label>
                                <input type="color" class="form-control" id="textColor" value="${this.designSettings.textColor}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <label class="form-label">لون الحدود</label>
                                <input type="color" class="form-control" id="borderColor" value="${this.designSettings.borderColor}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سُمك الحدود</label>
                                <select class="form-select" id="borderWidth">
                                    <option value="1px" ${this.designSettings.borderWidth === '1px' ? 'selected' : ''}>رفيع (1px)</option>
                                    <option value="2px" ${this.designSettings.borderWidth === '2px' ? 'selected' : ''}>متوسط (2px)</option>
                                    <option value="3px" ${this.designSettings.borderWidth === '3px' ? 'selected' : ''}>سميك (3px)</option>
                                    <option value="0px">بدون حدود</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">نمط الحدود</label>
                                <select class="form-select" id="borderStyle">
                                    <option value="solid" ${this.designSettings.borderStyle === 'solid' ? 'selected' : ''}>خط مستمر</option>
                                    <option value="dashed" ${this.designSettings.borderStyle === 'dashed' ? 'selected' : ''}>خط متقطع</option>
                                    <option value="dotted" ${this.designSettings.borderStyle === 'dotted' ? 'selected' : ''}>نقاط</option>
                                    <option value="double" ${this.designSettings.borderStyle === 'double' ? 'selected' : ''}>خط مزدوج</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">هامش الصفحة</label>
                                <select class="form-select" id="pageMargin">
                                    <option value="10px">ضيق (10px)</option>
                                    <option value="15px">صغير (15px)</option>
                                    <option value="20px" ${this.designSettings.pageMargin === '20px' ? 'selected' : ''}>متوسط (20px)</option>
                                    <option value="25px">كبير (25px)</option>
                                    <option value="30px">كبير جداً (30px)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">حشو المحتوى</label>
                                <select class="form-select" id="contentPadding">
                                    <option value="10px">ضيق (10px)</option>
                                    <option value="15px">صغير (15px)</option>
                                    <option value="20px" ${this.designSettings.contentPadding === '20px' ? 'selected' : ''}>متوسط (20px)</option>
                                    <option value="25px">كبير (25px)</option>
                                    <option value="30px">كبير جداً (30px)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الخطوط والنصوص المتقدمة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-font me-2"></i>الخطوط والنصوص</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">نوع الخط</label>
                                <select class="form-select" id="fontFamily">
                                    <option value="Arial" ${this.designSettings.fontFamily === 'Arial' ? 'selected' : ''}>Arial</option>
                                    <option value="Cairo" ${this.designSettings.fontFamily === 'Cairo' ? 'selected' : ''}>Cairo</option>
                                    <option value="Tahoma" ${this.designSettings.fontFamily === 'Tahoma' ? 'selected' : ''}>Tahoma</option>
                                    <option value="Times New Roman" ${this.designSettings.fontFamily === 'Times New Roman' ? 'selected' : ''}>Times New Roman</option>
                                    <option value="Helvetica">Helvetica</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Verdana">Verdana</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">حجم الخط العام</label>
                                <select class="form-select" id="fontSize">
                                    <option value="9pt">9pt - صغير جداً</option>
                                    <option value="10pt" ${this.designSettings.fontSize === '10pt' ? 'selected' : ''}>10pt - صغير</option>
                                    <option value="11pt" ${this.designSettings.fontSize === '11pt' ? 'selected' : ''}>11pt - صغير متوسط</option>
                                    <option value="12pt" ${this.designSettings.fontSize === '12pt' ? 'selected' : ''}>12pt - متوسط</option>
                                    <option value="14pt" ${this.designSettings.fontSize === '14pt' ? 'selected' : ''}>14pt - كبير</option>
                                    <option value="16pt" ${this.designSettings.fontSize === '16pt' ? 'selected' : ''}>16pt - كبير جداً</option>
                                    <option value="18pt">18pt - ضخم</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">حجم خط العنوان</label>
                                <select class="form-select" id="titleFontSize">
                                    <option value="16pt">16pt</option>
                                    <option value="18pt" ${this.designSettings.titleFontSize === '18pt' ? 'selected' : ''}>18pt</option>
                                    <option value="20pt">20pt</option>
                                    <option value="22pt">22pt</option>
                                    <option value="24pt">24pt</option>
                                    <option value="26pt">26pt</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <label class="form-label">حجم خط الرأس</label>
                                <select class="form-select" id="headerFontSize">
                                    <option value="12pt">12pt</option>
                                    <option value="14pt" ${this.designSettings.headerFontSize === '14pt' ? 'selected' : ''}>14pt</option>
                                    <option value="16pt">16pt</option>
                                    <option value="18pt">18pt</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">ارتفاع السطر</label>
                                <select class="form-select" id="lineHeight">
                                    <option value="1.2">ضيق (1.2)</option>
                                    <option value="1.4">متوسط (1.4)</option>
                                    <option value="1.5" ${this.designSettings.lineHeight === '1.5' ? 'selected' : ''}>عادي (1.5)</option>
                                    <option value="1.6">واسع (1.6)</option>
                                    <option value="1.8">واسع جداً (1.8)</option>
                                    <option value="2.0">مضاعف (2.0)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">تباعد الأحرف</label>
                                <select class="form-select" id="letterSpacing">
                                    <option value="normal" ${this.designSettings.letterSpacing === 'normal' ? 'selected' : ''}>عادي</option>
                                    <option value="0.5px">ضيق (0.5px)</option>
                                    <option value="1px">متوسط (1px)</option>
                                    <option value="1.5px">واسع (1.5px)</option>
                                    <option value="2px">واسع جداً (2px)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <label class="form-label">محاذاة النص</label>
                                <select class="form-select" id="textAlign">
                                    <option value="right" ${this.designSettings.textAlign === 'right' ? 'selected' : ''}>يمين</option>
                                    <option value="left" ${this.designSettings.textAlign === 'left' ? 'selected' : ''}>يسار</option>
                                    <option value="center" ${this.designSettings.textAlign === 'center' ? 'selected' : ''}>وسط</option>
                                    <option value="justify" ${this.designSettings.textAlign === 'justify' ? 'selected' : ''}>ضبط</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الجداول المتقدمة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-table me-2"></i>إعدادات الجداول</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">لون رأس الجدول</label>
                                <input type="color" class="form-control" id="tableHeaderColor" value="${this.designSettings.tableHeaderColor}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">لون نص الرأس</label>
                                <input type="color" class="form-control" id="tableHeaderTextColor" value="${this.designSettings.tableHeaderTextColor}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">لون حدود الجدول</label>
                                <input type="color" class="form-control" id="tableBorderColor" value="${this.designSettings.tableBorderColor}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <label class="form-label">لون الصفوف</label>
                                <input type="color" class="form-control" id="tableRowColor" value="${this.designSettings.tableRowColor}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">لون الصفوف المتناوبة</label>
                                <input type="color" class="form-control" id="tableAlternateColor" value="${this.designSettings.tableAlternateColor}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">سُمك حدود الجدول</label>
                                <select class="form-select" id="tableBorderWidth">
                                    <option value="1px" ${this.designSettings.tableBorderWidth === '1px' ? 'selected' : ''}>رفيع (1px)</option>
                                    <option value="2px" ${this.designSettings.tableBorderWidth === '2px' ? 'selected' : ''}>متوسط (2px)</option>
                                    <option value="3px" ${this.designSettings.tableBorderWidth === '3px' ? 'selected' : ''}>سميك (3px)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <label class="form-label">وزن خط الرأس</label>
                                <select class="form-select" id="tableHeaderFontWeight">
                                    <option value="normal">عادي</option>
                                    <option value="bold" ${this.designSettings.tableHeaderFontWeight === 'bold' ? 'selected' : ''}>عريض</option>
                                    <option value="bolder">عريض جداً</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">حشو الخلايا</label>
                                <select class="form-select" id="tableCellPadding">
                                    <option value="4px">ضيق (4px)</option>
                                    <option value="6px">صغير (6px)</option>
                                    <option value="8px" ${this.designSettings.tableCellPadding === '8px' ? 'selected' : ''}>متوسط (8px)</option>
                                    <option value="10px">كبير (10px)</option>
                                    <option value="12px">كبير جداً (12px)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">عنوان جدول الأصناف</label>
                                <input type="text" class="form-control" id="itemsTableTitle" value="${this.designSettings.itemsTableTitle}">
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الفاتورة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-file-invoice me-2"></i>إعدادات الفاتورة</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">عنوان الفاتورة</label>
                                <input type="text" class="form-control" id="invoiceTitle" value="${this.designSettings.invoiceTitle}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">رمز العملة</label>
                                <input type="text" class="form-control" id="currencySymbol" value="${this.designSettings.currencySymbol}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showInvoiceNumber" ${this.designSettings.showInvoiceNumber ? 'checked' : ''}>
                                    <label class="form-check-label" for="showInvoiceNumber">إظهار رقم الفاتورة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showInvoiceDate" ${this.designSettings.showInvoiceDate ? 'checked' : ''}>
                                    <label class="form-check-label" for="showInvoiceDate">إظهار تاريخ الفاتورة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showDueDate" ${this.designSettings.showDueDate ? 'checked' : ''}>
                                    <label class="form-check-label" for="showDueDate">إظهار تاريخ الاستحقاق</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPaymentTerms" ${this.designSettings.showPaymentTerms ? 'checked' : ''}>
                                    <label class="form-check-label" for="showPaymentTerms">إظهار شروط الدفع</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showNotes" ${this.designSettings.showNotes ? 'checked' : ''}>
                                    <label class="form-check-label" for="showNotes">إظهار الملاحظات</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showSignature" ${this.designSettings.showSignature ? 'checked' : ''}>
                                    <label class="form-check-label" for="showSignature">إظهار التوقيع</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <label class="form-label">موضع رمز العملة</label>
                                <select class="form-select" id="currencyPosition">
                                    <option value="before" ${this.designSettings.currencyPosition === 'before' ? 'selected' : ''}>قبل الرقم</option>
                                    <option value="after" ${this.designSettings.currencyPosition === 'after' ? 'selected' : ''}>بعد الرقم</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات العميل/المورد -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-user-tie me-2"></i>إعدادات العميل/المورد</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">عنوان قسم العميل</label>
                                <input type="text" class="form-control" id="customerInfoTitle" value="${this.designSettings.customerInfoTitle}">
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="showCustomerInfo" ${this.designSettings.showCustomerInfo ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCustomerInfo">إظهار معلومات العميل</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showCustomerAddress" ${this.designSettings.showCustomerAddress ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCustomerAddress">إظهار العنوان</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showCustomerPhone" ${this.designSettings.showCustomerPhone ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCustomerPhone">إظهار الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showCustomerEmail" ${this.designSettings.showCustomerEmail ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCustomerEmail">إظهار البريد</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showCustomerTaxNumber" ${this.designSettings.showCustomerTaxNumber ? 'checked' : ''}>
                                    <label class="form-check-label" for="showCustomerTaxNumber">إظهار الرقم الضريبي</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الأصناف -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-boxes me-2"></i>إعدادات الأصناف</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showItemCode" ${this.designSettings.showItemCode ? 'checked' : ''}>
                                    <label class="form-check-label" for="showItemCode">إظهار كود الصنف</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showItemDescription" ${this.designSettings.showItemDescription ? 'checked' : ''}>
                                    <label class="form-check-label" for="showItemDescription">إظهار الوصف</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showItemUnit" ${this.designSettings.showItemUnit ? 'checked' : ''}>
                                    <label class="form-check-label" for="showItemUnit">إظهار الوحدة</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showItemDiscount" ${this.designSettings.showItemDiscount ? 'checked' : ''}>
                                    <label class="form-check-label" for="showItemDiscount">إظهار الخصم</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showItemTax" ${this.designSettings.showItemTax ? 'checked' : ''}>
                                    <label class="form-check-label" for="showItemTax">إظهار الضريبة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <!-- فارغ للتوازن -->
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الإجماليات -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-calculator me-2"></i>إعدادات الإجماليات</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showSubtotal" ${this.designSettings.showSubtotal ? 'checked' : ''}>
                                    <label class="form-check-label" for="showSubtotal">إظهار المجموع الفرعي</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showTax" ${this.designSettings.showTax ? 'checked' : ''}>
                                    <label class="form-check-label" for="showTax">إظهار الضريبة</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showDiscount" ${this.designSettings.showDiscount ? 'checked' : ''}>
                                    <label class="form-check-label" for="showDiscount">إظهار الخصم</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showShipping" ${this.designSettings.showShipping ? 'checked' : ''}>
                                    <label class="form-check-label" for="showShipping">إظهار الشحن</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showTotal" ${this.designSettings.showTotal ? 'checked' : ''}>
                                    <label class="form-check-label" for="showTotal">إظهار الإجمالي النهائي</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات التذييل -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-align-center me-2"></i>إعدادات التذييل</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">نص التذييل الرئيسي</label>
                                <input type="text" class="form-control" id="footerText" value="${this.designSettings.footerText}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نص التذييل الفرعي</label>
                                <input type="text" class="form-control" id="footerSubText" value="${this.designSettings.footerSubText}">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showFooter" ${this.designSettings.showFooter ? 'checked' : ''}>
                                    <label class="form-check-label" for="showFooter">إظهار التذييل</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPrintDate" ${this.designSettings.showPrintDate ? 'checked' : ''}>
                                    <label class="form-check-label" for="showPrintDate">إظهار تاريخ الطباعة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPageNumbers" ${this.designSettings.showPageNumbers ? 'checked' : ''}>
                                    <label class="form-check-label" for="showPageNumbers">إظهار أرقام الصفحات</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الميزات المتقدمة -->
                    <div class="toolbar-section">
                        <h6><i class="fas fa-magic me-2"></i>الميزات المتقدمة</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">نص العلامة المائية</label>
                                <input type="text" class="form-control" id="watermark" value="${this.designSettings.watermark}" placeholder="مثال: مسودة، سري، إلخ">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">شفافية العلامة المائية</label>
                                <select class="form-select" id="watermarkOpacity">
                                    <option value="0.05">شفاف جداً (5%)</option>
                                    <option value="0.1" ${this.designSettings.watermarkOpacity === '0.1' ? 'selected' : ''}>شفاف (10%)</option>
                                    <option value="0.15">متوسط الشفافية (15%)</option>
                                    <option value="0.2">واضح قليلاً (20%)</option>
                                    <option value="0.3">واضح (30%)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showWatermark" ${this.designSettings.showWatermark ? 'checked' : ''}>
                                    <label class="form-check-label" for="showWatermark">إظهار العلامة المائية</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qrCode" ${this.designSettings.qrCode ? 'checked' : ''}>
                                    <label class="form-check-label" for="qrCode">إضافة رمز QR</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="barcode" ${this.designSettings.barcode ? 'checked' : ''}>
                                    <label class="form-check-label" for="barcode">إضافة باركود</label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">بيانات رمز QR</label>
                                <input type="text" class="form-control" id="qrCodeData" value="${this.designSettings.qrCodeData}" placeholder="رابط أو نص للرمز">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">بيانات الباركود</label>
                                <input type="text" class="form-control" id="barcodeData" value="${this.designSettings.barcodeData}" placeholder="رقم الفاتورة أو كود">
                            </div>
                        </div>

                        <!-- إعدادات الباركود المتقدمة -->
                        <div class="row mt-3" id="barcodeAdvancedSettings" style="display: ${this.designSettings.barcode ? 'block' : 'none'};">
                            <div class="col-12">
                                <h6 class="text-primary">
                                    <i class="fas fa-barcode me-2"></i>
                                    إعدادات الباركود المتقدمة
                                </h6>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">نوع الباركود</label>
                                <select class="form-select" id="barcodeType">
                                    <option value="CODE128" ${this.designSettings.barcodeType === 'CODE128' ? 'selected' : ''}>Code 128</option>
                                    <option value="EAN13" ${this.designSettings.barcodeType === 'EAN13' ? 'selected' : ''}>EAN-13</option>
                                    <option value="QR" ${this.designSettings.barcodeType === 'QR' ? 'selected' : ''}>QR Code</option>
                                    <option value="CODE39" ${this.designSettings.barcodeType === 'CODE39' ? 'selected' : ''}>Code 39</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">عرض الباركود</label>
                                <input type="number" class="form-control" id="barcodeWidth" value="${this.designSettings.barcodeWidth}" min="100" max="400">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">ارتفاع الباركود</label>
                                <input type="number" class="form-control" id="barcodeHeight" value="${this.designSettings.barcodeHeight}" min="30" max="150">
                            </div>
                        </div>

                        <div class="row mt-2" id="barcodePositionSettings" style="display: ${this.designSettings.barcode ? 'block' : 'none'};">
                            <div class="col-md-6">
                                <label class="form-label">موضع الباركود</label>
                                <select class="form-select" id="barcodePosition">
                                    <option value="top-left" ${this.designSettings.barcodePosition === 'top-left' ? 'selected' : ''}>أعلى يسار</option>
                                    <option value="top-right" ${this.designSettings.barcodePosition === 'top-right' ? 'selected' : ''}>أعلى يمين</option>
                                    <option value="bottom-left" ${this.designSettings.barcodePosition === 'bottom-left' ? 'selected' : ''}>أسفل يسار</option>
                                    <option value="bottom-right" ${this.designSettings.barcodePosition === 'bottom-right' ? 'selected' : ''}>أسفل يمين</option>
                                    <option value="center" ${this.designSettings.barcodePosition === 'center' ? 'selected' : ''}>وسط</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="barcodeShowText" ${this.designSettings.barcodeShowText ? 'checked' : ''}>
                                    <label class="form-check-label" for="barcodeShowText">إظهار النص أسفل الباركود</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="designEditor.openBarcodeManager()">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    فتح مدير الباركود المتقدم
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm ms-2" onclick="designEditor.loadSavedBarcode()">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل باركود محفوظ
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="toolbar-actions">
                        <button class="btn btn-primary" onclick="designEditor.applyChanges()">
                            <i class="fas fa-check me-2"></i>تطبيق التغييرات
                        </button>
                        <button class="btn btn-success" onclick="designEditor.saveTemplate()">
                            <i class="fas fa-save me-2"></i>حفظ القالب
                        </button>
                        <button class="btn btn-info" onclick="designEditor.loadTemplate()">
                            <i class="fas fa-upload me-2"></i>تحميل قالب
                        </button>
                        <button class="btn btn-warning" onclick="designEditor.resetToDefault()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <button class="btn btn-secondary" onclick="designEditor.previewFullscreen()">
                            <i class="fas fa-expand me-2"></i>معاينة كاملة
                        </button>
                        <button class="btn btn-dark" onclick="designEditor.exportPDF()">
                            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                        </button>
                    </div>
                </div>

                <!-- منطقة المعاينة -->
                <div class="editor-preview">
                    <div class="preview-header">
                        <h6>معاينة مباشرة</h6>
                        <div class="preview-controls">
                            <button class="btn btn-sm btn-outline-primary" onclick="designEditor.zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="designEditor.zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="designEditor.resetZoom()">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="preview-content" id="designPreview">
                        <!-- سيتم عرض المعاينة هنا -->
                    </div>
                </div>
            </div>
        `;

        return editorHTML;
    }

    bindEvents() {
        // ربط أحداث التغيير للتحديث المباشر
        const inputs = [
            // معلومات الشركة
            'companyName', 'companyAddress', 'companyPhone', 'companyEmail', 'companyWebsite',
            'companyTaxNumber', 'companyCommercialRecord', 'companyInfoPosition',

            // إعدادات الألوان والتصميم
            'headerColor', 'headerTextColor', 'backgroundColor', 'textColor', 'borderColor',
            'borderWidth', 'borderStyle', 'pageMargin', 'contentPadding',

            // إعدادات الخطوط
            'fontFamily', 'fontSize', 'titleFontSize', 'headerFontSize', 'lineHeight',
            'letterSpacing', 'textAlign',

            // إعدادات الشعار
            'logoPosition', 'logoSize', 'logoShape',

            // إعدادات الجداول
            'tableHeaderColor', 'tableHeaderTextColor', 'tableBorderColor', 'tableRowColor',
            'tableAlternateColor', 'tableBorderWidth', 'tableHeaderFontWeight', 'tableCellPadding',
            'itemsTableTitle',

            // إعدادات الفاتورة
            'invoiceTitle', 'currencySymbol', 'currencyPosition', 'customerInfoTitle',

            // إعدادات التذييل
            'footerText', 'footerSubText',

            // الميزات المتقدمة
            'watermark', 'watermarkOpacity', 'qrCodeData', 'barcodeData'
        ];

        inputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('input', () => this.updatePreview());
                element.addEventListener('change', () => this.updatePreview());
            }
        });

        // ربط أحداث checkbox
        const checkboxes = [
            // إعدادات عامة
            'showLogo', 'showFooter', 'showCompanyInfo', 'logoBorder', 'logoShadow',

            // إعدادات الفاتورة
            'showInvoiceNumber', 'showInvoiceDate', 'showDueDate', 'showPaymentTerms',
            'showNotes', 'showSignature',

            // إعدادات العميل
            'showCustomerInfo', 'showCustomerAddress', 'showCustomerPhone', 'showCustomerEmail',
            'showCustomerTaxNumber',

            // إعدادات الأصناف
            'showItemCode', 'showItemDescription', 'showItemUnit', 'showItemDiscount', 'showItemTax',

            // إعدادات الإجماليات
            'showSubtotal', 'showTax', 'showDiscount', 'showShipping', 'showTotal',

            // إعدادات التذييل
            'showPrintDate', 'showPageNumbers',

            // الميزات المتقدمة
            'showWatermark', 'qrCode', 'barcode'
        ];

        checkboxes.forEach(checkboxId => {
            const element = document.getElementById(checkboxId);
            if (element) {
                element.addEventListener('change', () => this.updatePreview());
            }
        });

        // ربط حدث رفع الشعار
        const logoUpload = document.getElementById('logoUpload');
        if (logoUpload) {
            logoUpload.addEventListener('change', (e) => this.handleLogoUpload(e));
        }
    }

    handleLogoUpload(event) {
        const file = event.target.files[0];
        if (file) {
            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                this.showMessage('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            // التحقق من حجم الملف (2MB)
            if (file.size > 2 * 1024 * 1024) {
                this.showMessage('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 2MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.designSettings.companyLogo = e.target.result;

                // تحديث معاينة الشعار
                const logoPreview = document.getElementById('logoPreview');
                if (logoPreview) {
                    logoPreview.innerHTML = `
                        <img src="${e.target.result}" alt="معاينة الشعار" style="
                            max-width: 100%;
                            max-height: 100%;
                            object-fit: contain;
                            border-radius: 4px;
                        ">
                    `;
                }

                this.updatePreview();
                this.showMessage('تم رفع الشعار بنجاح', 'success');
            };
            reader.readAsDataURL(file);
        }
    }

    updatePreview() {
        this.collectSettings();
        const previewContent = this.generatePreviewHTML();
        document.getElementById('designPreview').innerHTML = previewContent;
    }

    collectSettings() {
        const getValue = (id) => document.getElementById(id)?.value || '';
        const getChecked = (id) => document.getElementById(id)?.checked || false;

        this.designSettings = {
            ...this.designSettings,

            // معلومات الشركة
            companyName: getValue('companyName'),
            companyAddress: getValue('companyAddress'),
            companyPhone: getValue('companyPhone'),
            companyEmail: getValue('companyEmail'),
            companyWebsite: getValue('companyWebsite'),
            companyTaxNumber: getValue('companyTaxNumber'),
            companyCommercialRecord: getValue('companyCommercialRecord'),
            showCompanyInfo: getChecked('showCompanyInfo'),
            companyInfoPosition: getValue('companyInfoPosition'),

            // إعدادات الألوان والتصميم
            headerColor: getValue('headerColor'),
            headerTextColor: getValue('headerTextColor'),
            backgroundColor: getValue('backgroundColor'),
            textColor: getValue('textColor'),
            borderColor: getValue('borderColor'),
            borderWidth: getValue('borderWidth'),
            borderStyle: getValue('borderStyle'),
            pageMargin: getValue('pageMargin'),
            contentPadding: getValue('contentPadding'),

            // إعدادات الخطوط
            fontFamily: getValue('fontFamily'),
            fontSize: getValue('fontSize'),
            titleFontSize: getValue('titleFontSize'),
            headerFontSize: getValue('headerFontSize'),
            lineHeight: getValue('lineHeight'),
            letterSpacing: getValue('letterSpacing'),
            textAlign: getValue('textAlign'),

            // إعدادات الشعار
            showLogo: getChecked('showLogo'),
            logoPosition: getValue('logoPosition'),
            logoSize: getValue('logoSize'),
            logoShape: getValue('logoShape'),
            logoBorder: getChecked('logoBorder'),
            logoShadow: getChecked('logoShadow'),

            // إعدادات الجداول
            tableHeaderColor: getValue('tableHeaderColor'),
            tableHeaderTextColor: getValue('tableHeaderTextColor'),
            tableBorderColor: getValue('tableBorderColor'),
            tableRowColor: getValue('tableRowColor'),
            tableAlternateColor: getValue('tableAlternateColor'),
            tableBorderWidth: getValue('tableBorderWidth'),
            tableHeaderFontWeight: getValue('tableHeaderFontWeight'),
            tableCellPadding: getValue('tableCellPadding'),
            itemsTableTitle: getValue('itemsTableTitle'),

            // إعدادات الفاتورة
            invoiceTitle: getValue('invoiceTitle'),
            currencySymbol: getValue('currencySymbol'),
            currencyPosition: getValue('currencyPosition'),
            showInvoiceNumber: getChecked('showInvoiceNumber'),
            showInvoiceDate: getChecked('showInvoiceDate'),
            showDueDate: getChecked('showDueDate'),
            showPaymentTerms: getChecked('showPaymentTerms'),
            showNotes: getChecked('showNotes'),
            showSignature: getChecked('showSignature'),

            // إعدادات العميل
            showCustomerInfo: getChecked('showCustomerInfo'),
            customerInfoTitle: getValue('customerInfoTitle'),
            showCustomerAddress: getChecked('showCustomerAddress'),
            showCustomerPhone: getChecked('showCustomerPhone'),
            showCustomerEmail: getChecked('showCustomerEmail'),
            showCustomerTaxNumber: getChecked('showCustomerTaxNumber'),

            // إعدادات الأصناف
            showItemCode: getChecked('showItemCode'),
            showItemDescription: getChecked('showItemDescription'),
            showItemUnit: getChecked('showItemUnit'),
            showItemDiscount: getChecked('showItemDiscount'),
            showItemTax: getChecked('showItemTax'),

            // إعدادات الإجماليات
            showSubtotal: getChecked('showSubtotal'),
            showTax: getChecked('showTax'),
            showDiscount: getChecked('showDiscount'),
            showShipping: getChecked('showShipping'),
            showTotal: getChecked('showTotal'),

            // إعدادات التذييل
            showFooter: getChecked('showFooter'),
            footerText: getValue('footerText'),
            footerSubText: getValue('footerSubText'),
            showPrintDate: getChecked('showPrintDate'),
            showPageNumbers: getChecked('showPageNumbers'),

            // الميزات المتقدمة
            watermark: getValue('watermark'),
            showWatermark: getChecked('showWatermark'),
            watermarkOpacity: getValue('watermarkOpacity'),
            qrCode: getChecked('qrCode'),
            qrCodeData: getValue('qrCodeData'),
            barcode: getChecked('barcode'),
            barcodeData: getValue('barcodeData')
        };
    }

    generatePreviewHTML() {
        const settings = this.designSettings;
        const logoSizeMap = {
            small: '60px',
            medium: '80px',
            large: '100px'
        };

        return `
            <div class="invoice-preview" style="
                font-family: ${settings.fontFamily}, Arial, sans-serif;
                font-size: ${settings.fontSize};
                color: ${settings.textColor};
                background-color: ${settings.backgroundColor};
                border: 2px ${settings.borderStyle} ${settings.borderColor};
                border-radius: 8px;
                padding: 20px;
                direction: rtl;
            ">
                ${settings.showLogo ? `
                    <div class="invoice-header" style="
                        display: flex;
                        justify-content: ${settings.logoPosition === 'center' ? 'center' : 'space-between'};
                        align-items: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px ${settings.borderStyle} ${settings.borderColor};
                    ">
                        ${settings.companyLogo ? `
                            <div class="company-logo" style="order: ${settings.logoPosition === 'left' ? '2' : '1'};">
                                <img src="${settings.companyLogo}" alt="شعار الشركة" style="
                                    max-height: ${logoSizeMap[settings.logoSize]};
                                    border-radius: 8px;
                                    border: 1px solid ${settings.borderColor};
                                ">
                            </div>
                        ` : `
                            <div class="company-logo" style="
                                width: ${logoSizeMap[settings.logoSize]};
                                height: ${logoSizeMap[settings.logoSize]};
                                border: 2px dashed ${settings.borderColor};
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                order: ${settings.logoPosition === 'left' ? '2' : '1'};
                            ">
                                <span style="color: ${settings.borderColor};">الشعار</span>
                            </div>
                        `}
                        <div class="company-info" style="
                            text-align: ${settings.logoPosition === 'left' ? 'left' : 'right'};
                            order: ${settings.logoPosition === 'left' ? '1' : '2'};
                        ">
                            <h1 style="margin: 0 0 10px 0; font-size: 18pt; color: ${settings.textColor};">${settings.companyName}</h1>
                            <p style="margin: 5px 0; color: ${settings.textColor};">${settings.companyAddress}</p>
                            <p style="margin: 5px 0; color: ${settings.textColor};">الهاتف: ${settings.companyPhone}</p>
                            <p style="margin: 5px 0; color: ${settings.textColor};">البريد: ${settings.companyEmail}</p>
                        </div>
                    </div>
                ` : ''}
                
                <div class="invoice-title" style="
                    text-align: center;
                    padding: 15px;
                    margin-bottom: 30px;
                    border-radius: 8px;
                    background-color: ${settings.headerColor};
                    color: ${settings.headerTextColor};
                    border: 1px ${settings.borderStyle} ${settings.borderColor};
                ">
                    <h2 style="margin: 0; font-size: 16pt;">عنوان النموذج</h2>
                </div>
                
                <div class="sample-table" style="margin-bottom: 30px;">
                    <table style="
                        width: 100%;
                        border-collapse: collapse;
                        border: 1px ${settings.borderStyle} ${settings.borderColor};
                    ">
                        <thead>
                            <tr style="background-color: ${settings.tableHeaderColor};">
                                <th style="
                                    border: 1px ${settings.borderStyle} ${settings.borderColor};
                                    padding: 10px;
                                    text-align: center;
                                    color: ${settings.textColor};
                                ">العمود الأول</th>
                                <th style="
                                    border: 1px ${settings.borderStyle} ${settings.borderColor};
                                    padding: 10px;
                                    text-align: center;
                                    color: ${settings.textColor};
                                ">العمود الثاني</th>
                                <th style="
                                    border: 1px ${settings.borderStyle} ${settings.borderColor};
                                    padding: 10px;
                                    text-align: center;
                                    color: ${settings.textColor};
                                ">العمود الثالث</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: ${settings.tableRowColor};">
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 1</td>
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 2</td>
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 3</td>
                            </tr>
                            <tr style="background-color: ${settings.tableAlternateColor};">
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 4</td>
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 5</td>
                                <td style="border: 1px ${settings.borderStyle} ${settings.borderColor}; padding: 8px; text-align: center;">بيانات 6</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                ${settings.showFooter ? `
                    <div class="invoice-footer" style="
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px ${settings.borderStyle} ${settings.borderColor};
                        color: ${settings.textColor};
                        font-size: 10pt;
                    ">
                        <p>شكراً لتعاملكم معنا</p>
                        <p>هذا النموذج مُنشأ إلكترونياً</p>
                    </div>
                ` : ''}

                <!-- الباركود -->
                ${this.generateBarcodeHTML()}
            </div>
        `;
    }

    applyChanges() {
        this.collectSettings();
        this.updatePreview();
        
        // تحديث المعاينة في النافذة الرئيسية إذا كانت مفتوحة
        if (typeof updateLivePreview === 'function') {
            updateLivePreview();
        }
        
        this.showMessage('تم تطبيق التغييرات بنجاح', 'success');
    }

    saveTemplate() {
        this.collectSettings();
        const templateName = prompt('اسم القالب:', `قالب_${currentTemplate}_${new Date().getTime()}`);

        if (templateName) {
            // إضافة تاريخ الحفظ
            const templateData = {
                ...this.designSettings,
                savedDate: new Date().toISOString(),
                templateType: currentTemplate || 'general'
            };

            localStorage.setItem(`custom_template_${templateName}`, JSON.stringify(templateData));
            this.showMessage(`تم حفظ القالب "${templateName}" بنجاح`, 'success');
        }
    }

    loadTemplate() {
        // إنشاء قائمة بالقوالب المحفوظة
        const savedTemplates = this.getSavedTemplates();

        if (savedTemplates.length === 0) {
            this.showMessage('لا توجد قوالب محفوظة', 'info');
            return;
        }

        // إنشاء نافذة اختيار القالب
        const templateSelector = this.createTemplateSelector(savedTemplates);
        document.body.appendChild(templateSelector);

        // إظهار النافذة
        const modal = new bootstrap.Modal(templateSelector);
        modal.show();

        // إزالة النافذة عند الإغلاق
        templateSelector.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(templateSelector);
        });
    }

    getSavedTemplates() {
        const templates = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('custom_template_')) {
                const templateName = key.replace('custom_template_', '');
                const templateData = JSON.parse(localStorage.getItem(key));
                templates.push({
                    name: templateName,
                    data: templateData,
                    date: templateData.savedDate || 'غير محدد'
                });
            }
        }
        return templates.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    createTemplateSelector(templates) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-folder-open me-2"></i>
                            اختيار القالب المحفوظ
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            ${templates.map(template => `
                                <div class="col-md-6 mb-3">
                                    <div class="card template-card" style="cursor: pointer;" onclick="designEditor.loadSpecificTemplate('${template.name}')">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-file-alt text-primary me-2"></i>
                                                ${template.name}
                                            </h6>
                                            <p class="card-text text-muted small">
                                                تاريخ الحفظ: ${template.date}
                                            </p>
                                            <div class="template-preview" style="font-size: 0.8rem; color: #666;">
                                                <div>الخط: ${template.data.fontFamily}</div>
                                                <div>الحجم: ${template.data.fontSize}</div>
                                                <div style="background: ${template.data.headerColor}; width: 20px; height: 20px; display: inline-block; border-radius: 3px; margin-right: 5px;"></div>
                                                لون الرأس
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-primary btn-sm" onclick="event.stopPropagation(); designEditor.loadSpecificTemplate('${template.name}')">
                                                <i class="fas fa-download me-1"></i>تحميل
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="event.stopPropagation(); designEditor.deleteTemplate('${template.name}')">
                                                <i class="fas fa-trash me-1"></i>حذف
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-info" onclick="designEditor.exportTemplates()">
                            <i class="fas fa-file-export me-2"></i>تصدير جميع القوالب
                        </button>
                        <button type="button" class="btn btn-success" onclick="document.getElementById('importTemplates').click()">
                            <i class="fas fa-file-import me-2"></i>استيراد قوالب
                        </button>
                        <input type="file" id="importTemplates" accept=".json" style="display: none;" onchange="designEditor.importTemplates(event)">
                    </div>
                </div>
            </div>
        `;
        return modal;
    }

    loadSpecificTemplate(templateName) {
        const savedTemplate = localStorage.getItem(`custom_template_${templateName}`);

        if (savedTemplate) {
            this.designSettings = JSON.parse(savedTemplate);
            this.populateForm();
            this.updatePreview();
            this.showMessage(`تم تحميل القالب "${templateName}" بنجاح`, 'success');

            // إغلاق نافذة الاختيار
            const modal = document.querySelector('.modal.show');
            if (modal) {
                bootstrap.Modal.getInstance(modal).hide();
            }
        } else {
            this.showMessage(`لم يتم العثور على القالب "${templateName}"`, 'error');
        }
    }

    deleteTemplate(templateName) {
        if (confirm(`هل أنت متأكد من حذف القالب "${templateName}"؟`)) {
            localStorage.removeItem(`custom_template_${templateName}`);
            this.showMessage(`تم حذف القالب "${templateName}" بنجاح`, 'success');

            // إعادة تحميل قائمة القوالب
            setTimeout(() => {
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                    setTimeout(() => this.loadTemplate(), 300);
                }
            }, 1000);
        }
    }

    exportTemplates() {
        const templates = this.getSavedTemplates();
        const exportData = {
            exportDate: new Date().toISOString(),
            templates: templates
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `print_templates_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('تم تصدير القوالب بنجاح', 'success');
    }

    importTemplates(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importData = JSON.parse(e.target.result);

                if (importData.templates && Array.isArray(importData.templates)) {
                    let importedCount = 0;

                    importData.templates.forEach(template => {
                        const templateKey = `custom_template_${template.name}`;
                        localStorage.setItem(templateKey, JSON.stringify(template.data));
                        importedCount++;
                    });

                    this.showMessage(`تم استيراد ${importedCount} قالب بنجاح`, 'success');

                    // إعادة تحميل قائمة القوالب
                    setTimeout(() => {
                        const modal = document.querySelector('.modal.show');
                        if (modal) {
                            bootstrap.Modal.getInstance(modal).hide();
                            setTimeout(() => this.loadTemplate(), 300);
                        }
                    }, 1000);
                } else {
                    this.showMessage('ملف القوالب غير صحيح', 'error');
                }
            } catch (error) {
                this.showMessage('خطأ في قراءة ملف القوالب', 'error');
            }
        };
        reader.readAsText(file);

        // إعادة تعيين قيمة input
        event.target.value = '';
    }

    populateForm() {
        const settings = this.designSettings;
        
        Object.keys(settings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = settings[key];
                } else {
                    element.value = settings[key];
                }
            }
        });
    }

    resetToDefault() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
            this.designSettings = {
                companyLogo: null,
                companyName: 'شركة [اسم الشركة]',
                companyAddress: '[عنوان الشركة]',
                companyPhone: '[رقم الهاتف]',
                companyEmail: '[البريد الإلكتروني]',
                headerColor: '#007bff',
                headerTextColor: '#ffffff',
                fontSize: '12pt',
                fontFamily: 'Arial',
                showLogo: true,
                showFooter: true,
                logoPosition: 'right',
                logoSize: 'medium',
                borderStyle: 'solid',
                borderColor: '#333333',
                backgroundColor: '#ffffff',
                textColor: '#000000',
                tableHeaderColor: '#f8f9fa',
                tableRowColor: '#ffffff',
                tableAlternateColor: '#f8f9fa'
            };
            
            this.populateForm();
            this.updatePreview();
            this.showMessage('تم إعادة تعيين الإعدادات بنجاح', 'info');
        }
    }

    zoomIn() {
        const preview = document.getElementById('designPreview');
        const currentScale = parseFloat(preview.style.transform.replace('scale(', '').replace(')', '')) || 1;
        const newScale = Math.min(currentScale + 0.1, 2);
        preview.style.transform = `scale(${newScale})`;
        preview.style.transformOrigin = 'top right';
    }

    zoomOut() {
        const preview = document.getElementById('designPreview');
        const currentScale = parseFloat(preview.style.transform.replace('scale(', '').replace(')', '')) || 1;
        const newScale = Math.max(currentScale - 0.1, 0.5);
        preview.style.transform = `scale(${newScale})`;
        preview.style.transformOrigin = 'top right';
    }

    resetZoom() {
        const preview = document.getElementById('designPreview');
        preview.style.transform = 'scale(1)';
    }

    previewFullscreen() {
        const content = this.generatePreviewHTML();
        const fullscreenWindow = window.open('', '_blank', 'width=1200,height=800');

        fullscreenWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>معاينة كاملة - ${this.designSettings.invoiceTitle}</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: ${this.designSettings.fontFamily}, Arial, sans-serif;
                        background: #f5f5f5;
                    }
                    .preview-container {
                        max-width: 210mm;
                        margin: 0 auto;
                        background: white;
                        box-shadow: 0 0 20px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .toolbar {
                        background: #333;
                        color: white;
                        padding: 10px 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .toolbar button {
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-left: 10px;
                    }
                    .toolbar button:hover {
                        background: #0056b3;
                    }
                </style>
            </head>
            <body>
                <div class="preview-container">
                    <div class="toolbar">
                        <h3 style="margin: 0;">معاينة كاملة</h3>
                        <div>
                            <button onclick="window.print()">طباعة</button>
                            <button onclick="window.close()">إغلاق</button>
                        </div>
                    </div>
                    <div style="padding: 20px;">
                        ${content}
                    </div>
                </div>
            </body>
            </html>
        `);

        fullscreenWindow.document.close();
    }

    exportPDF() {
        // هذه الدالة تحتاج مكتبة PDF مثل jsPDF
        this.showMessage('ميزة تصدير PDF قيد التطوير', 'info');

        // يمكن إضافة مكتبة jsPDF لاحقاً
        /*
        const content = this.generatePreviewHTML();
        const pdf = new jsPDF('p', 'mm', 'a4');
        pdf.html(content, {
            callback: function (pdf) {
                pdf.save(`${this.designSettings.invoiceTitle}.pdf`);
            }
        });
        */
    }

    // دوال الباركود
    openBarcodeManager() {
        window.open('/services/barcode-manager/', '_blank');
    }

    loadSavedBarcode() {
        const saved = localStorage.getItem('invoice_barcode');
        if (saved) {
            try {
                const barcodeData = JSON.parse(saved);

                // تحديث إعدادات الباركود
                this.designSettings.barcode = true;
                this.designSettings.barcodeData = barcodeData.data;
                this.designSettings.barcodeType = barcodeData.type;
                this.designSettings.barcodeWidth = barcodeData.options.width || 200;
                this.designSettings.barcodeHeight = barcodeData.options.height || 60;
                this.designSettings.barcodeShowText = barcodeData.options.showText !== false;

                // تحديث النموذج
                this.populateForm();
                this.updatePreview();

                // إظهار إعدادات الباركود
                this.toggleBarcodeSettings(true);

                this.showMessage('تم تحميل الباركود المحفوظ بنجاح', 'success');
            } catch (error) {
                this.showMessage('خطأ في تحميل الباركود المحفوظ', 'error');
            }
        } else {
            this.showMessage('لا يوجد باركود محفوظ. يرجى إنشاء باركود من مدير الباركود أولاً.', 'info');
        }
    }

    toggleBarcodeSettings(show) {
        const advancedSettings = document.getElementById('barcodeAdvancedSettings');
        const positionSettings = document.getElementById('barcodePositionSettings');

        if (advancedSettings) {
            advancedSettings.style.display = show ? 'block' : 'none';
        }
        if (positionSettings) {
            positionSettings.style.display = show ? 'block' : 'none';
        }
    }

    generateBarcodeHTML() {
        if (!this.designSettings.barcode || !this.designSettings.barcodeData) {
            return '';
        }

        const position = this.designSettings.barcodePosition;
        const positionStyles = {
            'top-left': 'position: absolute; top: 10px; left: 10px;',
            'top-right': 'position: absolute; top: 10px; right: 10px;',
            'bottom-left': 'position: absolute; bottom: 10px; left: 10px;',
            'bottom-right': 'position: absolute; bottom: 10px; right: 10px;',
            'center': 'text-align: center; margin: 20px 0;'
        };

        // محاولة الحصول على الباركود المحفوظ
        const saved = localStorage.getItem('invoice_barcode');
        let barcodeImage = '';

        if (saved) {
            try {
                const barcodeData = JSON.parse(saved);
                barcodeImage = barcodeData.image;
            } catch (error) {
                console.error('خطأ في تحميل صورة الباركود:', error);
            }
        }

        if (!barcodeImage) {
            // إنشاء باركود بديل إذا لم يكن متوفراً
            return `
                <div style="${positionStyles[position]}">
                    <div style="
                        border: 2px dashed #666;
                        padding: 10px;
                        background: #f8f9fa;
                        border-radius: 5px;
                        width: ${this.designSettings.barcodeWidth}px;
                        height: ${this.designSettings.barcodeHeight}px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        color: #666;
                    ">
                        باركود: ${this.designSettings.barcodeData}
                        <br>
                        <small>استخدم مدير الباركود لإنشاء الصورة</small>
                    </div>
                </div>
            `;
        }

        return `
            <div style="${positionStyles[position]}">
                <img src="${barcodeImage}" alt="باركود" style="
                    width: ${this.designSettings.barcodeWidth}px;
                    height: ${this.designSettings.barcodeHeight}px;
                    border-radius: 3px;
                ">
                ${this.designSettings.barcodeShowText ? `
                    <div style="text-align: center; font-size: 10px; margin-top: 5px;">
                        ${this.designSettings.barcodeData}
                    </div>
                ` : ''}
            </div>
        `;
    }

    showMessage(message, type = 'info') {
        const alertClass = {
            success: 'alert-success',
            error: 'alert-danger',
            info: 'alert-info',
            warning: 'alert-warning'
        }[type] || 'alert-info';

        const toast = document.createElement('div');
        toast.className = `alert ${alertClass} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
            ${message}
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 4000);
    }

    getDesignCSS() {
        const settings = this.designSettings;
        
        return `
            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                body {
                    font-family: ${settings.fontFamily}, Arial, sans-serif !important;
                    font-size: ${settings.fontSize} !important;
                    color: ${settings.textColor} !important;
                    background-color: ${settings.backgroundColor} !important;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                }
                
                .invoice-container {
                    border: 2px ${settings.borderStyle} ${settings.borderColor} !important;
                    border-radius: 8px !important;
                    padding: 20px !important;
                    background-color: ${settings.backgroundColor} !important;
                }
                
                .invoice-header {
                    display: flex !important;
                    justify-content: ${settings.logoPosition === 'center' ? 'center' : 'space-between'} !important;
                    align-items: center !important;
                    margin-bottom: 30px !important;
                    padding-bottom: 20px !important;
                    border-bottom: 2px ${settings.borderStyle} ${settings.borderColor} !important;
                }
                
                .company-logo img {
                    max-height: ${settings.logoSize === 'small' ? '60px' : settings.logoSize === 'large' ? '100px' : '80px'} !important;
                    border-radius: 8px !important;
                    border: 1px solid ${settings.borderColor} !important;
                }
                
                .invoice-title {
                    background-color: ${settings.headerColor} !important;
                    color: ${settings.headerTextColor} !important;
                    border: 1px ${settings.borderStyle} ${settings.borderColor} !important;
                }
                
                .table th {
                    background-color: ${settings.tableHeaderColor} !important;
                    border: 1px ${settings.borderStyle} ${settings.borderColor} !important;
                    color: ${settings.textColor} !important;
                }
                
                .table td {
                    border: 1px ${settings.borderStyle} ${settings.borderColor} !important;
                    color: ${settings.textColor} !important;
                }
                
                .table tbody tr:nth-child(even) {
                    background-color: ${settings.tableAlternateColor} !important;
                }
                
                .table tbody tr:nth-child(odd) {
                    background-color: ${settings.tableRowColor} !important;
                }
            }
        `;
    }
}

// إنشاء مثيل من محرر التصميم
let designEditor;

// تهيئة المحرر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    designEditor = new DesignEditor();
});
