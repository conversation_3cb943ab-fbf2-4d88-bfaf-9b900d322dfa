{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-exchange-alt text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة نقل وتحويل الموظفين بين الأقسام</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransferModal">
                        <i class="fas fa-plus me-2"></i>
                        نقل جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ completed_transfers }}</h3>
                    <p class="mb-0">نقل مكتمل</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ pending_reviews }}</h3>
                    <p class="mb-0">قيد المعالجة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ internal_transfers }}</h3>
                    <p class="mb-0">نقل داخلي</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ external_transfers }}</h3>
                    <p class="mb-0">نقل خارجي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfers Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        سجل النقل والتحويل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>من قسم</th>
                                    <th>إلى قسم</th>
                                    <th>تاريخ النقل</th>
                                    <th>نوع النقل</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ transfer.employee_name }}
                                        <br><small class="text-muted">{{ transfer.employee_number }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ transfer.from_dept }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ transfer.to_dept }}</span>
                                    </td>
                                    <td>{{ transfer.transfer_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ transfer.transfer_type }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ transfer.reason }}</small>
                                    </td>
                                    <td>
                                        <span class="badge {{ transfer.status_badge }}">{{ transfer.status }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:transfer_detail' transfer.transfer.id %}" class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:transfer_edit' transfer.transfer.id %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="post" action="{% url 'hr:transfer_delete' transfer.transfer.id %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف النقل؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-exchange-alt fa-3x mb-3 d-block"></i>
                                        لا توجد عمليات نقل مسجلة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Transfer Modal -->
<div class="modal fade" id="addTransferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">نقل موظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'hr:transfer_create' %}">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select name="employee" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    {% for employee in eligible_employees %}
                                    <option value="{{ employee.id }}">
                                        {{ employee.person.name }} - {{ employee.employee_number }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ النقل</label>
                                <input type="date" name="transfer_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من قسم</label>
                                <select name="from_department" class="form-select" required>
                                    <option value="">اختر القسم السابق</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى قسم</label>
                                <select name="to_department" class="form-select" required>
                                    <option value="">اختر القسم الجديد</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}">{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من منصب</label>
                                <select name="from_position" class="form-select" required>
                                    <option value="">اختر المنصب السابق</option>
                                    {% for pos in positions %}
                                    <option value="{{ pos.id }}">{{ pos.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى منصب</label>
                                <select name="to_position" class="form-select" required>
                                    <option value="">اختر المنصب الجديد</option>
                                    {% for pos in positions %}
                                    <option value="{{ pos.id }}">{{ pos.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع النقل</label>
                                <select name="transfer_type" class="form-select" required>
                                    <option value="">اختر نوع النقل</option>
                                    <option value="INTERNAL">داخلي</option>
                                    <option value="EXTERNAL">خارجي</option>
                                    <option value="PROMOTION">ترقية</option>
                                    <option value="TEMPORARY">مؤقت</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ التنفيذ</label>
                                <input type="date" name="effective_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب النقل</label>
                        <textarea name="reason" class="form-control" rows="3" required placeholder="اذكر أسباب النقل"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-primary">حفظ النقل</button>
            </div>
                </form>
        </div>
    </div>
</div>
{% endblock %}
