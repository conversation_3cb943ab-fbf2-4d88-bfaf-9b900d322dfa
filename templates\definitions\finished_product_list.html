{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-box text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'definitions:finished_product_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    إضافة منتج جديد
                </a>
            </div>

            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في الكود أو الاسم أو الباركود">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                            <a href="{% url 'definitions:finished_product_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                مسح
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المنتجات التامة
                        <span class="badge bg-success ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الكود</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in page_obj %}
                                        <tr>
                                            <td>
                                                {% if product.image %}
                                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" 
                                                         class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                                {% else %}
                                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px; border-radius: 4px;">
                                                        <i class="fas fa-box text-muted"></i>
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>{{ product.code }}</strong>
                                                {% if product.barcode %}
                                                    <br><small class="text-muted">{{ product.barcode }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'definitions:finished_product_detail' product.pk %}" 
                                                   class="text-decoration-none">
                                                    {{ product.name }}
                                                </a>
                                                {% if product.name_english %}
                                                    <br><small class="text-muted">{{ product.name_english }}</small>
                                                {% endif %}
                                                {% if product.brand %}
                                                    <br><span class="badge bg-secondary">{{ product.brand }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if product.category %}
                                                    <span class="badge bg-info">{{ product.category.name }}</span>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ product.unit.name }}</td>
                                            <td>
                                                {% if product.standard_cost %}
                                                    {{ product.standard_cost|floatformat:2 }} ج.م
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if product.selling_price %}
                                                    <strong class="text-success">{{ product.selling_price|floatformat:2 }} ج.م</strong>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    {% if product.is_manufactured %}
                                                        <span class="badge bg-primary">مصنع</span>
                                                    {% endif %}
                                                    {% if product.is_sellable %}
                                                        <span class="badge bg-success">قابل للبيع</span>
                                                    {% endif %}
                                                    {% if product.is_purchasable %}
                                                        <span class="badge bg-warning">قابل للشراء</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'definitions:finished_product_detail' product.pk %}" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'definitions:finished_product_edit' product.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteProduct({{ product.pk }}, '{{ product.name }}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات تامة</h5>
                            <p class="text-muted">ابدأ بإضافة منتج تام جديد</p>
                            <a href="{% url 'definitions:finished_product_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteProduct(productId, productName) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
        fetch(`/definitions/finished-products/${productId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
