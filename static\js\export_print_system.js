/**
 * Export and Print System
 * نظام التصدير والطباعة المتقدم
 */

class ExportPrintSystem {
    constructor() {
        this.init();
    }

    init() {
        this.addExportButtons();
        this.setupPrintStyles();
    }

    addExportButtons() {
        // إضافة أزرار التصدير إلى النموذج
        const exportButtons = `
            <div class="export-buttons mt-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="exportSystem.printPreview()">
                        <i class="fas fa-print me-2"></i>معاينة الطباعة
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportSystem.exportToPDF()">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="exportSystem.exportToExcel()">
                        <i class="fas fa-file-excel me-2"></i>تصدير Excel
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="exportSystem.exportToWord()">
                        <i class="fas fa-file-word me-2"></i>تصدير Word
                    </button>
                </div>
            </div>
        `;
        
        $('.form-section').last().append(exportButtons);
    }

    setupPrintStyles() {
        const printStyles = `
            <style media="print">
                @page {
                    margin: 1cm;
                    size: A4;
                }

                body {
                    font-family: 'Arial', sans-serif;
                    font-size: 12pt;
                    line-height: 1.4;
                    color: #000;
                    background: white !important;
                }

                .manufacturing-container {
                    max-width: none;
                    padding: 0;
                    margin: 0;
                }

                .digital-card {
                    box-shadow: none;
                    border: 1px solid #000;
                    border-radius: 0;
                    background: white !important;
                }

                .digital-header {
                    background: #f8f9fa !important;
                    color: #000 !important;
                    border-bottom: 2px solid #000;
                    text-align: center;
                    padding: 1cm;
                }

                .digital-title {
                    font-size: 18pt;
                    font-weight: bold;
                    color: #000 !important;
                    text-shadow: none;
                }

                .digital-subtitle {
                    font-size: 12pt;
                    color: #000 !important;
                }

                .form-section {
                    padding: 0.5cm;
                    border-bottom: 1px solid #ccc;
                    page-break-inside: avoid;
                }

                .section-title {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #000;
                    margin-bottom: 0.5cm;
                    border-bottom: 1px solid #000;
                    padding-bottom: 0.2cm;
                }

                .cost-calculator {
                    background: #f8f9fa !important;
                    color: #000 !important;
                    border: 2px solid #000;
                    border-radius: 0;
                    padding: 0.5cm;
                    margin: 0.5cm 0;
                }

                .calculator-display {
                    background: white !important;
                    border: 1px solid #000;
                    border-radius: 0;
                    padding: 0.3cm;
                }

                .display-row {
                    border-bottom: 1px solid #ccc;
                    padding: 0.2cm 0;
                    display: flex;
                    justify-content: space-between;
                }

                .display-value {
                    font-weight: bold;
                    color: #000 !important;
                    text-shadow: none;
                }

                .materials-table {
                    border: 2px solid #000;
                    border-radius: 0;
                    margin: 0.5cm 0;
                    background: white !important;
                }

                .table-header {
                    background: #f8f9fa !important;
                    color: #000 !important;
                    border-bottom: 2px solid #000;
                    padding: 0.3cm;
                    font-weight: bold;
                }

                .table-row {
                    border-bottom: 1px solid #ccc;
                    padding: 0.3cm;
                    background: white !important;
                }

                .btn, .export-buttons, .progress-indicator,
                .form-text, .invalid-feedback, .tooltip {
                    display: none !important;
                }

                .form-control, .form-select {
                    border: 1px solid #000 !important;
                    background: white !important;
                    color: #000 !important;
                    box-shadow: none !important;
                    font-size: 10pt;
                }

                .badge {
                    border: 1px solid #000;
                    background: white !important;
                    color: #000 !important;
                }

                .stock-indicator {
                    border: 1px solid #000;
                    background: white !important;
                    color: #000 !important;
                    box-shadow: none;
                }

                .alert {
                    border: 1px solid #000 !important;
                    background: white !important;
                    color: #000 !important;
                    box-shadow: none;
                }

                .print-info {
                    margin-top: 1cm;
                    border-top: 1px solid #000;
                    padding-top: 0.5cm;
                    font-size: 10pt;
                }

                .print-signature {
                    margin-top: 2cm;
                    display: flex;
                    justify-content: space-between;
                }

                .signature-box {
                    width: 4cm;
                    height: 2cm;
                    border: 1px solid #000;
                    text-align: center;
                    padding-top: 1.5cm;
                    font-size: 10pt;
                }
            </style>
        `;
        
        $('head').append(printStyles);
    }

    printPreview() {
        // إضافة معلومات الطباعة
        this.addPrintInfo();
        
        // فتح معاينة الطباعة
        window.print();
        
        // إزالة معلومات الطباعة بعد الطباعة
        setTimeout(() => {
            this.removePrintInfo();
        }, 1000);
    }

    addPrintInfo() {
        const currentDate = new Date().toLocaleDateString('ar-EG');
        const currentTime = new Date().toLocaleTimeString('ar-EG');
        const userName = $('#currentUser').text() || 'غير محدد';
        
        const printInfo = `
            <div class="print-info d-none d-print-block">
                <div class="row">
                    <div class="col-6">
                        <strong>تاريخ الطباعة:</strong> ${currentDate}<br>
                        <strong>وقت الطباعة:</strong> ${currentTime}
                    </div>
                    <div class="col-6 text-end">
                        <strong>طُبع بواسطة:</strong> ${userName}<br>
                        <strong>النظام:</strong> نظام إدارة الإنتاج
                    </div>
                </div>
                
                <div class="print-signature">
                    <div class="signature-box">
                        توقيع المسؤول
                    </div>
                    <div class="signature-box">
                        توقيع المدير
                    </div>
                    <div class="signature-box">
                        ختم الشركة
                    </div>
                </div>
            </div>
        `;
        
        $('.digital-card').append(printInfo);
    }

    removePrintInfo() {
        $('.print-info').remove();
    }

    exportToPDF() {
        notificationSystem.info('جاري تحضير ملف PDF...', 'تصدير PDF');
        
        // محاكاة تصدير PDF
        setTimeout(() => {
            const formData = this.collectFormData();
            
            // هنا يمكن إضافة كود تصدير PDF الفعلي
            // مثل استخدام jsPDF أو إرسال البيانات للخادم
            
            notificationSystem.success('تم تصدير ملف PDF بنجاح', 'تم التصدير');
            
            // محاكاة تحميل الملف
            this.simulateDownload('manufacturing_order.pdf', 'application/pdf');
        }, 2000);
    }

    exportToExcel() {
        notificationSystem.info('جاري تحضير ملف Excel...', 'تصدير Excel');
        
        setTimeout(() => {
            const formData = this.collectFormData();
            
            // تحضير بيانات Excel
            const excelData = this.prepareExcelData(formData);
            
            notificationSystem.success('تم تصدير ملف Excel بنجاح', 'تم التصدير');
            this.simulateDownload('manufacturing_order.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        }, 1500);
    }

    exportToWord() {
        notificationSystem.info('جاري تحضير ملف Word...', 'تصدير Word');
        
        setTimeout(() => {
            const formData = this.collectFormData();
            
            notificationSystem.success('تم تصدير ملف Word بنجاح', 'تم التصدير');
            this.simulateDownload('manufacturing_order.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        }, 1800);
    }

    collectFormData() {
        const formData = {
            orderNumber: $('#orderNumber').text(),
            orderDate: $('#orderDate').text(),
            finishedProduct: $('#id_finished_product option:selected').text(),
            quantity: $('#id_quantity_to_produce').val(),
            unit: $('#productUnit').val(),
            rawMaterialsWarehouse: $('#id_raw_materials_warehouse option:selected').text(),
            finishedGoodsWarehouse: $('#id_finished_goods_warehouse option:selected').text(),
            expectedStartDate: $('#id_expected_start_date').val(),
            expectedCompletionDate: $('#id_expected_completion_date').val(),
            priority: $('#id_priority option:selected').text(),
            notes: $('#id_notes').val(),
            materials: [],
            costs: {
                totalMaterialCost: $('#totalMaterialCost').text(),
                operatingCost: $('#operatingCost').text(),
                additionalCosts: $('#additionalCosts').text(),
                unitCost: $('#unitCost').text(),
                totalProductionCost: $('#totalProductionCost').text()
            }
        };

        // جمع بيانات المواد الخام
        $('.material-row').each(function() {
            const material = {
                name: $(this).find('.material-select option:selected').text(),
                code: $(this).find('.material-code').text(),
                unit: $(this).find('.material-unit').text(),
                quantity: $(this).find('.quantity-input').val(),
                unitCost: $(this).find('.unit-cost-display').val(),
                totalCost: $(this).find('.partial-cost-display').val(),
                stockStatus: $(this).find('.stock-status').text()
            };
            
            if (material.name && material.name !== 'اختر المادة الخام...') {
                formData.materials.push(material);
            }
        });

        return formData;
    }

    prepareExcelData(formData) {
        // تحضير البيانات لتصدير Excel
        const data = [
            ['أمر الإنتاج رقم', formData.orderNumber],
            ['التاريخ', formData.orderDate],
            ['المنتج النهائي', formData.finishedProduct],
            ['الكمية', formData.quantity + ' ' + formData.unit],
            ['مخزن المواد الخام', formData.rawMaterialsWarehouse],
            ['مخزن المنتج النهائي', formData.finishedGoodsWarehouse],
            [],
            ['المواد الخام المطلوبة'],
            ['المادة', 'الكود', 'الوحدة', 'الكمية', 'سعر الوحدة', 'التكلفة الإجمالية']
        ];

        formData.materials.forEach(material => {
            data.push([
                material.name,
                material.code,
                material.unit,
                material.quantity,
                material.unitCost,
                material.totalCost
            ]);
        });

        data.push([]);
        data.push(['ملخص التكاليف']);
        data.push(['إجمالي تكلفة المواد', formData.costs.totalMaterialCost]);
        data.push(['تكلفة التشغيل', formData.costs.operatingCost]);
        data.push(['التكاليف الإضافية', formData.costs.additionalCosts]);
        data.push(['تكلفة الوحدة', formData.costs.unitCost]);
        data.push(['إجمالي تكلفة الإنتاج', formData.costs.totalProductionCost]);

        return data;
    }

    simulateDownload(filename, mimeType) {
        // محاكاة تحميل الملف
        const link = document.createElement('a');
        link.href = '#';
        link.download = filename;
        link.click();
    }
}

// إنشاء نسخة عامة من النظام
let exportSystem;

$(document).ready(function() {
    exportSystem = new ExportPrintSystem();
});
