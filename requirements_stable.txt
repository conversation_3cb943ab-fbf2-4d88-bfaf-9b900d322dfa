# متطلبات النظام المستقر
# Stable System Requirements

# Django Framework - الإطار الأساسي
Django>=4.2,<5.0

# Database - قاعدة البيانات
# SQLite مدمج مع Python

# System Monitoring - مراقبة النظام
psutil>=5.9.0

# HTTP Requests - طلبات HTTP
requests>=2.28.0
urllib3>=1.26.0

# Security - الأمان
cryptography>=3.4.8

# Performance - الأداء
django-cache-machine>=1.2.0

# Logging - التسجيل
django-extensions>=3.2.0

# Development Tools - أدوات التطوير (اختيارية)
# django-debug-toolbar>=4.0.0  # للتطوير فقط

# Production Server - خادم الإنتاج (اختياري)
# gunicorn>=20.1.0  # لخادم الإنتاج
# whitenoise>=6.2.0  # لتقديم الملفات الثابتة

# Backup Tools - أدوات النسخ الاحتياطي
# django-dbbackup>=4.0.0  # للنسخ الاحتياطي المتقدم

# Monitoring - مراقبة متقدمة (اختيارية)
# sentry-sdk>=1.9.0  # لمراقبة الأخطاء
# django-health-check>=3.17.0  # لفحص صحة النظام
