#!/usr/bin/env python3
"""
بروكسي HTTPS للخادم الأبدي
HTTPS Proxy for Eternal Server

بروكسي بسيط يحول HTTP إلى HTTPS للخادم الأبدي
Simple proxy that converts HTTP to HTTPS for the eternal server
"""

import os
import sys
import time
import socket
import ssl
import threading
import http.server
import socketserver
import urllib.request
import urllib.parse
from pathlib import Path
from datetime import datetime

class HTTPSProxy:
    """بروكسي HTTPS"""
    
    def __init__(self):
        self.django_port = 8000  # منفذ الخادم الأبدي
        self.https_port = 8443   # منفذ HTTPS
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.ssl_cert = None
        self.ssl_key = None
        self.proxy_server = None
        self.is_running = False
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ERROR: {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_info("إنشاء شهادة SSL...")
            if self.create_ssl_certificate():
                self.ssl_cert = ssl_dir / 'server.crt'
                self.ssl_key = ssl_dir / 'server.key'
                self.log_success("تم إنشاء شهادة SSL")
            else:
                self.log_error("فشل في إنشاء شهادة SSL")
    
    def create_ssl_certificate(self):
        """إنشاء شهادة SSL"""
        try:
            from simple_ssl import create_ssl_certificate
            cert_file, key_file, pem_file = create_ssl_certificate()
            return cert_file and key_file
        except Exception as e:
            self.log_error(f"خطأ في إنشاء شهادة SSL: {e}")
            return False
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def check_django_server(self):
        """فحص إذا كان الخادم الأبدي يعمل"""
        try:
            response = urllib.request.urlopen(f"http://127.0.0.1:{self.django_port}/", timeout=5)
            return response.getcode() == 200
        except Exception:
            return False
    
    def create_proxy_handler(self):
        """إنشاء معالج البروكسي"""
        
        class ProxyHandler(http.server.BaseHTTPRequestHandler):
            def __init__(self, django_port, *args, **kwargs):
                self.django_port = django_port
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                self.proxy_request()
            
            def do_POST(self):
                self.proxy_request()
            
            def do_PUT(self):
                self.proxy_request()
            
            def do_DELETE(self):
                self.proxy_request()
            
            def do_PATCH(self):
                self.proxy_request()
            
            def proxy_request(self):
                try:
                    # إنشاء URL للخادم Django
                    django_url = f"http://127.0.0.1:{self.django_port}{self.path}"
                    
                    # إنشاء الطلب
                    req = urllib.request.Request(django_url, method=self.command)
                    
                    # نسخ الهيدرز المهمة
                    for header, value in self.headers.items():
                        if header.lower() not in ['host', 'connection', 'upgrade-insecure-requests']:
                            req.add_header(header, value)
                    
                    # إضافة هيدرز إضافية
                    req.add_header('Host', f'127.0.0.1:{self.django_port}')
                    req.add_header('X-Forwarded-Proto', 'https')
                    req.add_header('X-Forwarded-For', self.client_address[0])
                    
                    # إضافة البيانات للطلبات POST/PUT/PATCH
                    if self.command in ['POST', 'PUT', 'PATCH']:
                        content_length = int(self.headers.get('Content-Length', 0))
                        if content_length > 0:
                            req.data = self.rfile.read(content_length)
                    
                    # إرسال الطلب مع timeout محسن
                    response = urllib.request.urlopen(req, timeout=10)
                    
                    # إرسال الاستجابة
                    self.send_response(response.getcode())
                    
                    # نسخ هيدرز الاستجابة
                    for header, value in response.headers.items():
                        if header.lower() not in ['connection', 'transfer-encoding']:
                            # تحديث URLs في الهيدرز
                            if 'location' in header.lower() and value.startswith('http://'):
                                value = value.replace('http://', 'https://')
                                value = value.replace(f':{self.django_port}', f':{8443}')
                            self.send_header(header, value)
                    
                    # إضافة هيدرز أمان
                    self.send_header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
                    self.send_header('X-Content-Type-Options', 'nosniff')
                    self.send_header('X-Frame-Options', 'DENY')
                    self.send_header('X-XSS-Protection', '1; mode=block')
                    
                    self.end_headers()
                    
                    # نسخ محتوى الاستجابة مع تحديث URLs
                    content = response.read()
                    if content:
                        # تحديث URLs في المحتوى
                        if b'text/html' in response.headers.get('Content-Type', '').encode():
                            content = content.replace(
                                f'http://127.0.0.1:{self.django_port}'.encode(),
                                f'https://127.0.0.1:8443'.encode()
                            )
                            content = content.replace(
                                f'http://{self.server.local_ip}:{self.django_port}'.encode(),
                                f'https://{self.server.local_ip}:8443'.encode()
                            )
                        
                        self.wfile.write(content)
                    
                except Exception as e:
                    self.send_error(500, f"Proxy Error: {e}")
            
            def log_message(self, format, *args):
                # تجاهل رسائل اللوج العادية لتقليل الضوضاء
                pass
        
        # إنشاء معالج مع django_port
        def handler_factory(*args, **kwargs):
            return ProxyHandler(self.django_port, *args, **kwargs)
        
        return handler_factory
    
    def start_proxy(self):
        """بدء تشغيل البروكسي"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # فحص إذا كان الخادم الأبدي يعمل
            if not self.check_django_server():
                self.log_error(f"الخادم الأبدي لا يعمل على المنفذ {self.django_port}")
                self.log_info("تأكد من تشغيل الخادم الأبدي أولاً")
                return False
            
            # البحث عن منفذ متاح لـ HTTPS
            https_port = self.find_available_port(8443)
            if not https_port:
                self.log_error("لا يمكن العثور على منفذ متاح لـ HTTPS")
                return False
            
            self.https_port = https_port
            
            # إنشاء معالج البروكسي
            handler = self.create_proxy_handler()
            
            # إنشاء الخادم
            self.proxy_server = socketserver.TCPServer(('0.0.0.0', https_port), handler)
            self.proxy_server.local_ip = self.local_ip  # إضافة IP للخادم
            
            # إعداد SSL
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain(str(self.ssl_cert), str(self.ssl_key))
            
            self.proxy_server.socket = context.wrap_socket(
                self.proxy_server.socket,
                server_side=True
            )
            
            self.is_running = True
            self.log_success(f"تم بدء بروكسي HTTPS على المنفذ {https_port}")
            self.display_access_info()
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في بدء البروكسي: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🔒 بروكسي HTTPS للخادم الأبدي")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 منفذ Django (HTTP): {self.django_port}")
        print(f"🔒 منفذ HTTPS: {self.https_port}")
        print("\n🔒 للوصول الآمن (HTTPS):")
        print(f"   https://{self.local_ip}:{self.https_port}/")
        print(f"   https://localhost:{self.https_port}/")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   https://{self.local_ip}:{self.https_port}/")
        print("\n⚠️ ملاحظات مهمة:")
        print("   • قد تظهر تحذيرات الأمان في المتصفح")
        print("   • اضغط 'Advanced' ثم 'Proceed to localhost'")
        print("   • الاتصال مشفر ومحمي بشهادة SSL")
        print("   • البروكسي يحول HTTP إلى HTTPS تلقائياً")
        print("=" * 60)
    
    def run(self):
        """تشغيل البروكسي"""
        print("=" * 60)
        print("🔒 بروكسي HTTPS للخادم الأبدي")
        print("=" * 60)
        
        self.log_info("بدء تشغيل بروكسي HTTPS...")
        
        if not self.start_proxy():
            self.log_error("فشل في بدء بروكسي HTTPS!")
            return False
        
        self.log_success("بروكسي HTTPS يعمل بنجاح!")
        self.log_info("اضغط Ctrl+C للإيقاف")
        
        try:
            self.proxy_server.serve_forever()
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في البروكسي: {e}")
        finally:
            if self.proxy_server:
                self.proxy_server.shutdown()
                self.proxy_server.server_close()
            self.log_info("تم إنهاء بروكسي HTTPS")
        
        return True

def main():
    """الدالة الرئيسية"""
    proxy = HTTPSProxy()
    success = proxy.run()
    
    if success:
        print("✅ تم تشغيل بروكسي HTTPS بنجاح!")
    else:
        print("❌ فشل في تشغيل بروكسي HTTPS!")
        sys.exit(1)

if __name__ == "__main__":
    main()
