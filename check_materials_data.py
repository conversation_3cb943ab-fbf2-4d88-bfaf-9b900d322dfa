#!/usr/bin/env python
"""
سكريبت فحص بيانات المواد الخام
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.models import Item, Stock, Warehouse

def check_materials_data():
    """فحص بيانات المواد الخام"""
    print("=== فحص بيانات المواد الخام ===")
    
    # فحص العناصر
    total_items = Item.objects.count()
    active_items = Item.objects.filter(is_active=True).count()
    raw_materials = Item.objects.filter(item_type='RAW_MATERIAL').count()
    active_raw_materials = Item.objects.filter(item_type='RAW_MATERIAL', is_active=True).count()
    
    print(f"إجمالي العناصر: {total_items}")
    print(f"العناصر النشطة: {active_items}")
    print(f"المواد الخام: {raw_materials}")
    print(f"المواد الخام النشطة: {active_raw_materials}")
    
    # فحص المخازن
    total_warehouses = Warehouse.objects.count()
    active_warehouses = Warehouse.objects.filter(is_active=True).count()
    
    print(f"\nإجمالي المخازن: {total_warehouses}")
    print(f"المخازن النشطة: {active_warehouses}")
    
    # فحص المخزون
    total_stocks = Stock.objects.count()
    stocks_with_quantity = Stock.objects.filter(quantity__gt=0).count()
    raw_material_stocks = Stock.objects.filter(item__item_type='RAW_MATERIAL').count()
    active_raw_stocks = Stock.objects.filter(item__item_type='RAW_MATERIAL', item__is_active=True).count()
    raw_stocks_with_quantity = Stock.objects.filter(item__item_type='RAW_MATERIAL', quantity__gt=0).count()
    
    print(f"\nإجمالي المخزون: {total_stocks}")
    print(f"المخزون بكمية > 0: {stocks_with_quantity}")
    print(f"مخزون المواد الخام: {raw_material_stocks}")
    print(f"مخزون المواد الخام النشطة: {active_raw_stocks}")
    print(f"مخزون المواد الخام بكمية > 0: {raw_stocks_with_quantity}")
    
    # عرض تفاصيل المواد الخام
    print(f"\n=== تفاصيل المواد الخام ===")
    raw_materials_list = Item.objects.filter(item_type='RAW_MATERIAL', is_active=True)
    
    if raw_materials_list.exists():
        for item in raw_materials_list[:10]:  # عرض أول 10 مواد
            stocks = Stock.objects.filter(item=item, quantity__gt=0)
            total_quantity = sum(stock.quantity for stock in stocks)
            warehouses = [stock.warehouse.name for stock in stocks]
            
            print(f"المادة: {item.name} ({item.code})")
            print(f"  الكمية الإجمالية: {total_quantity}")
            print(f"  المخازن: {', '.join(warehouses) if warehouses else 'لا يوجد مخزون'}")
            print()
    else:
        print("لا توجد مواد خام نشطة!")
    
    # فحص API
    print(f"\n=== فحص API ===")
    try:
        from inventory.api_views import get_all_warehouse_materials
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        
        # إنشاء طلب وهمي
        factory = RequestFactory()
        request = factory.get('/inventory/api/all-warehouse-materials/')
        
        # إنشاء مستخدم وهمي
        user, created = User.objects.get_or_create(username='test_user')
        request.user = user
        
        # استدعاء API
        import json
        from django.http import JsonResponse
        
        response = get_all_warehouse_materials(request)
        
        if hasattr(response, 'content'):
            data = json.loads(response.content.decode('utf-8'))
            print(f"استجابة API:")
            print(f"  النجاح: {data.get('success', False)}")
            print(f"  عدد المواد: {len(data.get('materials', []))}")
            print(f"  الرسالة: {data.get('message', '')}")
            
            # عرض أول 5 مواد من API
            materials = data.get('materials', [])
            if materials:
                print(f"\nأول 5 مواد من API:")
                for i, material in enumerate(materials[:5]):
                    print(f"  {i+1}. {material.get('name')} ({material.get('code')}) - {material.get('warehouse_name')}")
        else:
            print("خطأ في استجابة API")
            
    except Exception as e:
        print(f"خطأ في فحص API: {e}")

def create_sample_raw_materials():
    """إنشاء مواد خام تجريبية"""
    print("\n=== إنشاء مواد خام تجريبية ===")
    
    # الحصول على أول مخزن نشط
    warehouse = Warehouse.objects.filter(is_active=True).first()
    if not warehouse:
        print("لا يوجد مخزن نشط!")
        return
    
    # إنشاء وحدة قياس
    from definitions.models import Unit
    unit, created = Unit.objects.get_or_create(
        name='قطعة',
        defaults={'code': 'PCS'}
    )
    
    # قائمة المواد الخام التجريبية
    raw_materials = [
        {
            'name': 'خشب أبيض',
            'code': 'WOOD001',
            'cost_price': 50.0,
            'unit': unit
        },
        {
            'name': 'حديد صلب',
            'code': 'STEEL001',
            'cost_price': 25.0,
            'unit': unit
        },
        {
            'name': 'بلاستيك',
            'code': 'PLASTIC001',
            'cost_price': 15.0,
            'unit': unit
        },
        {
            'name': 'زجاج',
            'code': 'GLASS001',
            'cost_price': 30.0,
            'unit': unit
        },
        {
            'name': 'قماش',
            'code': 'FABRIC001',
            'cost_price': 20.0,
            'unit': unit
        }
    ]
    
    created_count = 0
    for material_data in raw_materials:
        item, created = Item.objects.get_or_create(
            code=material_data['code'],
            defaults={
                'name': material_data['name'],
                'item_type': 'RAW_MATERIAL',
                'cost_price': material_data['cost_price'],
                'unit': material_data['unit'],
                'is_active': True
            }
        )
        
        if created:
            print(f"تم إنشاء: {item.name}")
            created_count += 1
            
            # إنشاء مخزون للمادة
            stock, stock_created = Stock.objects.get_or_create(
                item=item,
                warehouse=warehouse,
                defaults={
                    'quantity': 100.0,
                    'average_cost': material_data['cost_price']
                }
            )
            
            if stock_created:
                print(f"  تم إنشاء مخزون: {stock.quantity} في {warehouse.name}")
        else:
            print(f"موجود مسبقاً: {item.name}")
    
    print(f"\nتم إنشاء {created_count} مادة خام جديدة")

if __name__ == '__main__':
    check_materials_data()
    
    # سؤال المستخدم
    response = input("\nهل تريد إنشاء مواد خام تجريبية؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم']:
        create_sample_raw_materials()
        print("\n=== فحص البيانات بعد الإنشاء ===")
        check_materials_data() 