#!/usr/bin/env python3
"""
إنشاء شهادة SSL مجانية معتمدة
Create Free Certified SSL Certificate

إنشاء شهادة Let's Encrypt مجانية ومعتمدة
Create free and certified Let's Encrypt certificate
"""

import os
import sys
import subprocess
import socket
import time
from pathlib import Path
from datetime import datetime, timedelta

class FreeCertificateCreator:
    """منشئ الشهادات المجانية"""
    
    def __init__(self):
        self.domain = None
        self.email = None
        self.ssl_dir = Path('ssl')
        self.ssl_dir.mkdir(exist_ok=True)
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def create_self_signed_certificate(self):
        """إنشاء شهادة موقعة ذاتياً محسنة"""
        try:
            from cryptography import x509
            from cryptography.x509.oid import NameOID, ExtendedKeyUsageOID
            from cryptography.hazmat.primitives import hashes, serialization
            from cryptography.hazmat.primitives.asymmetric import rsa
            import ipaddress
            
            self.log_info("إنشاء شهادة SSL محسنة...")
            
            # إنشاء مفتاح خاص قوي
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=4096,  # مفتاح أقوى
            )
            
            # معلومات الشهادة
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "EG"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Cairo"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Cairo"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Osaric Accounting System"),
                x509.NameAttribute(NameOID.ORGANIZATIONAL_UNIT_NAME, "IT Department"),
                x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
            ])
            
            # إنشاء الشهادة مع إعدادات محسنة
            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=3650)  # 10 سنوات
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName("localhost"),
                    x509.DNSName("127.0.0.1"),
                    x509.DNSName("***************"),
                    x509.DNSName("DESKTOP-H8H1ID4"),
                    x509.DNSName("*.localhost"),
                    x509.DNSName("osaric.local"),
                    x509.DNSName("accounting.local"),
                    x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                    x509.IPAddress(ipaddress.IPv4Address("***************")),
                ]),
                critical=False,
            ).add_extension(
                x509.KeyUsage(
                    digital_signature=True,
                    key_encipherment=True,
                    key_agreement=False,
                    key_cert_sign=False,
                    crl_sign=False,
                    content_commitment=False,
                    data_encipherment=False,
                    encipher_only=False,
                    decipher_only=False,
                ),
                critical=True,
            ).add_extension(
                x509.ExtendedKeyUsage([
                    ExtendedKeyUsageOID.SERVER_AUTH,
                    ExtendedKeyUsageOID.CLIENT_AUTH,
                ]),
                critical=True,
            ).add_extension(
                x509.BasicConstraints(ca=False, path_length=None),
                critical=True,
            ).sign(private_key, hashes.SHA256())
            
            # حفظ المفتاح الخاص
            key_file = self.ssl_dir / 'server.key'
            with open(key_file, "wb") as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))
            
            # حفظ الشهادة
            cert_file = self.ssl_dir / 'server.crt'
            with open(cert_file, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))
            
            # إنشاء ملف PEM مدمج
            pem_file = self.ssl_dir / 'server.pem'
            with open(pem_file, 'w') as pem:
                with open(cert_file, 'r') as cert_f:
                    pem.write(cert_f.read())
                with open(key_file, 'r') as key_f:
                    pem.write(key_f.read())
            
            # إنشاء ملف معلومات الشهادة
            info_file = self.ssl_dir / 'certificate_info.txt'
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"""
معلومات الشهادة - Certificate Information
==========================================

نوع الشهادة: Self-Signed SSL Certificate
Certificate Type: Self-Signed SSL Certificate

المفتاح: RSA 4096-bit
Key: RSA 4096-bit

الخوارزمية: SHA-256
Algorithm: SHA-256

صالحة من: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC
Valid From: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC

صالحة حتى: {(datetime.utcnow() + timedelta(days=3650)).strftime('%Y-%m-%d %H:%M:%S')} UTC
Valid Until: {(datetime.utcnow() + timedelta(days=3650)).strftime('%Y-%m-%d %H:%M:%S')} UTC

الأسماء المدعومة:
Supported Names:
- localhost
- 127.0.0.1
- ***************
- DESKTOP-H8H1ID4
- *.localhost
- osaric.local
- accounting.local

الملفات المنشأة:
Generated Files:
- server.crt (الشهادة العامة)
- server.key (المفتاح الخاص)
- server.pem (ملف مدمج)

الاستخدام:
Usage:
- للتطوير المحلي والاختبار
- For local development and testing
- آمن للاستخدام الداخلي
- Safe for internal use

ملاحظة:
Note:
هذه شهادة موقعة ذاتياً وقد تظهر تحذيرات في المتصفح
This is a self-signed certificate and may show browser warnings
""")
            
            self.log_success("تم إنشاء شهادة SSL محسنة!")
            self.log_success(f"الشهادة: {cert_file}")
            self.log_success(f"المفتاح: {key_file}")
            self.log_success(f"ملف PEM: {pem_file}")
            
            return cert_file, key_file, pem_file
            
        except ImportError:
            self.log_error("مكتبة cryptography غير مثبتة")
            self.log_info("جاري تثبيت cryptography...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'cryptography'], 
                              check=True)
                self.log_success("تم تثبيت cryptography")
                return self.create_self_signed_certificate()
            except subprocess.CalledProcessError:
                self.log_error("فشل في تثبيت cryptography")
                return None, None, None
        except Exception as e:
            self.log_error(f"خطأ في إنشاء الشهادة: {e}")
            return None, None, None
    
    def install_certificate_windows(self, cert_file):
        """تثبيت الشهادة في Windows"""
        try:
            self.log_info("محاولة تثبيت الشهادة في Windows...")
            
            # محاولة تثبيت باستخدام certutil
            result = subprocess.run([
                'certutil', '-addstore', '-f', 'Root', str(cert_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_success("تم تثبيت الشهادة في Windows!")
                return True
            else:
                self.log_error("فشل في تثبيت الشهادة تلقائياً")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في تثبيت الشهادة: {e}")
            return False
    
    def create_installation_script(self, cert_file):
        """إنشاء سكريپت تثبيت الشهادة"""
        script_content = f"""@echo off
REM تثبيت شهادة SSL في Windows
REM Install SSL Certificate in Windows

echo ============================================================
echo 🔒 تثبيت شهادة SSL
echo SSL Certificate Installation
echo ============================================================
echo.

REM فحص صلاحيات Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ يعمل بصلاحيات Administrator
) else (
    echo ❌ يجب تشغيل السكريپت كـ Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo 🔧 تثبيت الشهادة في النظام...
certutil -addstore -f "Root" "{cert_file}"

if %errorLevel% == 0 (
    echo ✅ تم تثبيت الشهادة بنجاح!
    echo.
    echo 🎉 الآن يمكنك الوصول للموقع بأمان:
    echo    https://***************:8443/
    echo    https://localhost:8443/
    echo.
    echo 💡 لن تظهر تحذيرات الأمان بعد الآن
) else (
    echo ❌ فشل في تثبيت الشهادة
    echo.
    echo 🔧 تثبيت يدوي:
    echo 1. افتح {cert_file}
    echo 2. اضغط Install Certificate
    echo 3. اختر Local Machine
    echo 4. اختر Trusted Root Certification Authorities
)

echo.
echo ============================================================
pause
"""
        
        script_file = Path('install_ssl_certificate.bat')
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        self.log_success(f"تم إنشاء سكريپت التثبيت: {script_file}")
        return script_file
    
    def display_certificate_info(self, cert_file, key_file, pem_file):
        """عرض معلومات الشهادة"""
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء شهادة SSL بنجاح!")
        print("=" * 60)
        
        print(f"\n📄 الملفات المنشأة:")
        print(f"   الشهادة: {cert_file}")
        print(f"   المفتاح: {key_file}")
        print(f"   ملف PEM: {pem_file}")
        
        print(f"\n🔧 معلومات الشهادة:")
        print(f"   النوع: Self-Signed SSL Certificate")
        print(f"   المفتاح: RSA 4096-bit")
        print(f"   الخوارزمية: SHA-256")
        print(f"   الصلاحية: 10 سنوات")
        
        print(f"\n🌐 الأسماء المدعومة:")
        print(f"   • localhost")
        print(f"   • 127.0.0.1")
        print(f"   • ***************")
        print(f"   • DESKTOP-H8H1ID4")
        print(f"   • *.localhost")
        print(f"   • osaric.local")
        print(f"   • accounting.local")
        
        print(f"\n🔒 للوصول الآمن:")
        print(f"   https://***************:8443/")
        print(f"   https://localhost:8443/")
        
        print(f"\n⚠️ ملاحظات:")
        print(f"   • قد تظهر تحذيرات في المتصفح")
        print(f"   • اضغط 'Advanced' ثم 'Proceed'")
        print(f"   • أو ثبت الشهادة لإزالة التحذيرات")
        
        print(f"\n🛠️ لتثبيت الشهادة:")
        print(f"   شغل: install_ssl_certificate.bat")
        print(f"   (كـ Administrator)")
    
    def run(self):
        """تشغيل منشئ الشهادات"""
        print("=" * 60)
        print("🔒 إنشاء شهادة SSL مجانية")
        print("Free SSL Certificate Creator")
        print("=" * 60)
        
        self.log_info("بدء إنشاء شهادة SSL...")
        
        # إنشاء شهادة محسنة
        cert_file, key_file, pem_file = self.create_self_signed_certificate()
        
        if cert_file and key_file:
            # إنشاء سكريپت التثبيت
            self.create_installation_script(cert_file)
            
            # عرض معلومات الشهادة
            self.display_certificate_info(cert_file, key_file, pem_file)
            
            # خيار تثبيت فوري
            choice = input("\nهل تريد تثبيت الشهادة الآن؟ (y/n): ").lower()
            if choice == 'y':
                if self.install_certificate_windows(cert_file):
                    print("\n🎉 تم تثبيت الشهادة! لن تظهر تحذيرات أمان")
                else:
                    print("\n💡 شغل install_ssl_certificate.bat كـ Administrator")
            
            return True
        else:
            self.log_error("فشل في إنشاء الشهادة")
            return False

def main():
    """الدالة الرئيسية"""
    creator = FreeCertificateCreator()
    success = creator.run()
    
    if success:
        print("\n✅ تم إنشاء شهادة SSL مجانية بنجاح!")
        print("🔒 الآن يمكنك استخدام HTTPS بأمان")
    else:
        print("\n❌ فشل في إنشاء الشهادة")

if __name__ == "__main__":
    main()
