#!/usr/bin/env python
"""
سكريبت تشخيصي لمعرفة سبب عدم عمل زر الحفظ
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.models import ManufacturingOrder, ManufacturingMaterial, Warehouse, Item
from django.contrib.auth.models import User

def debug_materials_list():
    """تشخيص مشكلة قائمة المواد"""
    print("=== تشخيص مشكلة قائمة المواد ===")
    
    # التحقق من وجود مستخدم
    try:
        user = User.objects.first()
        print(f"✓ المستخدم: {user.username if user else 'لا يوجد مستخدم'}")
    except Exception as e:
        print(f"✗ خطأ في المستخدم: {e}")
    
    # التحقق من وجود مخازن
    try:
        warehouses = Warehouse.objects.all()
        print(f"✓ عدد المخازن: {warehouses.count()}")
        for warehouse in warehouses[:3]:
            print(f"  - {warehouse.name}")
    except Exception as e:
        print(f"✗ خطأ في المخازن: {e}")
    
    # التحقق من وجود مواد
    try:
        items = Item.objects.all()
        print(f"✓ عدد المواد: {items.count()}")
        for item in items[:5]:
            print(f"  - {item.name} (كود: {item.code})")
    except Exception as e:
        print(f"✗ خطأ في المواد: {e}")
    
    # التحقق من وجود أوامر تصنيع
    try:
        orders = ManufacturingOrder.objects.all()
        print(f"✓ عدد أوامر التصنيع: {orders.count()}")
    except Exception as e:
        print(f"✗ خطأ في أوامر التصنيع: {e}")
    
    # التحقق من وجود مواد التصنيع
    try:
        materials = ManufacturingMaterial.objects.all()
        print(f"✓ عدد مواد التصنيع: {materials.count()}")
    except Exception as e:
        print(f"✗ خطأ في مواد التصنيع: {e}")
    
    print("\n=== اقتراحات الحل ===")
    print("1. تأكد من أن JavaScript يعمل بشكل صحيح")
    print("2. تأكد من أن متغير materialsList معرف بشكل صحيح")
    print("3. تأكد من أن المواد تُضاف للقائمة عند الضغط على زر الإضافة")
    print("4. تأكد من أن النموذج يحتوي على action صحيح")
    print("5. تأكد من أن CSRF token موجود")

if __name__ == "__main__":
    debug_materials_list() 