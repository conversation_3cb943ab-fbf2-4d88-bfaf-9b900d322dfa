/**
 * CSS مخصص للوحة تحكم المخازن
 * Custom CSS for Warehouse Dashboard
 */

/* البطاقات الرئيسية */
.warehouse-dashboard .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    overflow: hidden;
}

.warehouse-dashboard .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.stats-card h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ألوان مختلفة للبطاقات */
.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #3494e6 0%, #ec6ead 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* بطاقات المخازن */
.warehouse-card {
    border-left: 5px solid #007bff;
    transition: all 0.3s ease;
}

.warehouse-card:hover {
    border-left-color: #28a745;
    transform: translateX(5px);
}

/* حدود ملونة للبطاقات */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-left-secondary {
    border-left: 4px solid #6c757d !important;
}

.warehouse-card .card-body {
    padding: 1.5rem;
}

.warehouse-card h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* شريط التقدم */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* الجداول */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 1rem;
    font-size: 0.875rem;
}

/* الأيقونات */
.fas, .far {
    margin-left: 0.5rem;
}

/* تخطيط متجاوب */
@media (max-width: 768px) {
    .warehouse-dashboard .card {
        margin-bottom: 1rem;
    }
    
    .stats-card {
        padding: 1.5rem;
    }
    
    .stats-card h4 {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تأثيرات خاصة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيق خاص للأرقام */
.number-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    direction: ltr;
    text-align: center;
}

/* تنسيق العملة */
.currency-display {
    color: #28a745;
    font-weight: 600;
}

/* حالات التنبيه */
.alert-low-stock {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-out-of-stock {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* تنسيق الوقت */
.time-display {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* تحسينات إضافية */
.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.card-header h6 {
    margin-bottom: 0;
    color: #5a5c69;
    font-weight: 600;
}

/* تنسيق خاص للمخازن */
.warehouse-utilization {
    display: flex;
    align-items: center;
    gap: 10px;
}

.utilization-bar {
    flex: 1;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.utilization-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #ffc107 70%, #dc3545 100%);
    border-radius: 10px;
    transition: width 0.8s ease;
}

/* تنسيق الحالات */
.status-active {
    color: #28a745;
}

.status-inactive {
    color: #6c757d;
}

.status-warning {
    color: #ffc107;
}

.status-danger {
    color: #dc3545;
}

/* تحسينات للطباعة */
@media print {
    .warehouse-dashboard .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
    
    .stats-card {
        background: #f8f9fa !important;
        color: #495057 !important;
    }
}
