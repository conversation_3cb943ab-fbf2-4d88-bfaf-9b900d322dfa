#!/usr/bin/env python3
"""
إنشاء كلمة مرور قوية للمستخدم admin
Create Strong Password for Admin User
"""

import os
import sys
import django
import secrets
import string
from datetime import datetime

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.contrib.auth.models import User

def generate_strong_password(length=12):
    """إنشاء كلمة مرور قوية"""
    # تعريف مجموعات الأحرف
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%&*"
    
    # التأكد من وجود حرف واحد على الأقل من كل نوع
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special_chars)
    ]
    
    # إكمال باقي الأحرف
    all_chars = lowercase + uppercase + digits + special_chars
    for _ in range(length - 4):
        password.append(secrets.choice(all_chars))
    
    # خلط الأحرف
    secrets.SystemRandom().shuffle(password)
    
    return ''.join(password)

def create_admin_password(password=None):
    """إنشاء كلمة مرور جديدة للمستخدم admin"""
    
    print("=" * 60)
    print("Setting Admin User Password")
    print("=" * 60)
    
    try:
        # البحث عن المستخدم admin
        try:
            admin_user = User.objects.get(username='admin')
            print("[SUCCESS] Found admin user")
        except User.DoesNotExist:
            print("[ERROR] Admin user not found - creating new user...")
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='temp123'
            )
            print("✅ تم إنشاء المستخدم admin")
        
        # استخدام كلمة المرور المقدمة أو إنشاء واحدة جديدة
        new_password = password if password else generate_strong_password(12)
        
        # تحديث كلمة المرور
        admin_user.set_password(new_password)
        admin_user.save()
        
        print("\n" + "=" * 60)
        print("Successfully created new password!")
        print("=" * 60)
        
        print(f"\nLogin credentials:")
        print(f"   Username: admin")
        print(f"   Password: {new_password}")
        
        print(f"\nAdditional info:")
        print(f"   Email: {admin_user.email}")
        print(f"   User type: Super Admin")
        print(f"   Status: {'Active' if admin_user.is_active else 'Inactive'}")
        print(f"   Created: {admin_user.date_joined.strftime('%Y-%m-%d %H:%M')}")
        print(f"   Last login: {admin_user.last_login.strftime('%Y-%m-%d %H:%M') if admin_user.last_login else 'Never logged in'}")
        
        print(f"\nAccess URLs:")
        print(f"   Main app: http://127.0.0.1:8000/")
        print(f"   Login page: http://127.0.0.1:8000/accounts/login/")
        print(f"   Admin panel: http://127.0.0.1:8000/admin/")
        
        print(f"\nSecurity tips:")
        print(f"   - Store password securely")
        print(f"   - Don't share password")
        print(f"   - Change password regularly")
        print(f"   - Use password manager")
        
        # حفظ البيانات في ملف
        with open('admin_credentials.txt', 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("🔐 بيانات تسجيل الدخول للمستخدم admin\n")
            f.write("=" * 60 + "\n")
            f.write(f"اسم المستخدم: admin\n")
            f.write(f"كلمة المرور: {new_password}\n")
            f.write(f"البريد الإلكتروني: {admin_user.email}\n")
            f.write(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("\n" + "=" * 60 + "\n")
            f.write("🌐 روابط الوصول:\n")
            f.write("التطبيق الرئيسي: http://127.0.0.1:8000/\n")
            f.write("صفحة تسجيل الدخول: http://127.0.0.1:8000/accounts/login/\n")
            f.write("لوحة الإدارة: http://127.0.0.1:8000/admin/\n")
            f.write("\n" + "=" * 60 + "\n")
            f.write("Note: Keep this data in a secure place\n")
        
        print(f"\n📄 تم حفظ البيانات في ملف: admin_credentials.txt")
        print("=" * 60)
        
        return new_password
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء كلمة المرور: {e}")
        return None

def test_new_password(password):
    """اختبار كلمة المرور الجديدة"""
    try:
        from django.contrib.auth import authenticate
        
        print(f"\n🧪 اختبار كلمة المرور الجديدة...")
        
        user = authenticate(username='admin', password=password)
        
        if user:
            print("✅ كلمة المرور تعمل بشكل صحيح!")
            return True
        else:
            print("❌ كلمة المرور لا تعمل!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار كلمة المرور: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    # استخدام كلمة المرور المحددة
    custom_password = "OsamaOsama010@"
    
    # إنشاء كلمة مرور جديدة
    new_password = create_admin_password(password=custom_password)
    
    if new_password:
        # اختبار كلمة المرور
        if test_new_password(new_password):
            print(f"\n🎊 تم تعيين كلمة المرور بنجاح!")
            print(f"🔑 كلمة المرور الجديدة: {new_password}")
        else:
            print(f"\n⚠️ تم تعيين كلمة المرور لكن الاختبار فشل")
    else:
        print(f"\nFailed to set password")

if __name__ == "__main__":
    main()
