# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# Emin Mast<PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2016
# <PERSON><PERSON><PERSON>, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-08-07 18:32+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>mmadov, 2024\n"
"Language-Team: Azerbaijani (http://app.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Yönləndirmələr"

msgid "site"
msgstr "sayt"

msgid "redirect from"
msgstr "burdan yönləndir"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "Bu, domen adı xaric, tam yol olmalıdır. Məsələn: “/events/search/”."

msgid "redirect to"
msgstr "bura yönləndir"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Bu ya tam yol (yuxarıdakı kimi), ya da “https://” ilə başyalan full URL "
"olmalıdır."

msgid "redirect"
msgstr "yönləndirmə"

msgid "redirects"
msgstr "yönləndirmələr"
