{% extends 'base/base.html' %}
{% load static %}
{% load currency_tags %}

{% block extra_css %}
<link href="{% static 'css/warehouse_dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid warehouse-dashboard">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-warehouse text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">نظرة شاملة على جميع المخازن والمخزون</p>
                </div>
                <div>
                    <a href="{% url 'inventory:warehouse_analytics' %}" class="btn btn-outline-info me-2">
                        <i class="fas fa-chart-line me-2"></i>
                        التحليلات
                    </a>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-plus me-2"></i>
                            إجراءات سريعة
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_increase_create' %}">
                                <i class="fas fa-plus-circle me-2 text-success"></i>إذن إضافة
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_decrease_create' %}">
                                <i class="fas fa-minus-circle me-2 text-danger"></i>إذن صرف
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:warehouse_transfer_create' %}">
                                <i class="fas fa-exchange-alt me-2 text-info"></i>تحويل بين المخازن
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card primary fade-in">
                <h4 class="mb-0">{{ total_warehouses }}</h4>
                <p class="mb-0">إجمالي المخازن</p>
                <i class="fas fa-warehouse fa-2x" style="position: absolute; top: 20px; left: 20px; opacity: 0.3;"></i>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success fade-in">
                <h4 class="mb-0">{{ total_items }}</h4>
                <p class="mb-0">إجمالي الأصناف</p>
                <i class="fas fa-boxes fa-2x" style="position: absolute; top: 20px; left: 20px; opacity: 0.3;"></i>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info fade-in">
                <h4 class="mb-0 currency-display">{{ total_stock_value }}</h4>
                <p class="mb-0">قيمة المخزون</p>
                <i class="fas fa-dollar-sign fa-2x" style="position: absolute; top: 20px; left: 20px; opacity: 0.3;"></i>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning fade-in">
                <h4 class="mb-0">{{ manufacturing_stats.total }}</h4>
                <p class="mb-0">أوامر الإنتاج</p>
                <i class="fas fa-cogs fa-2x" style="position: absolute; top: 20px; left: 20px; opacity: 0.3;"></i>
            </div>
        </div>
    </div>

    <!-- المخازن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        المخازن وإحصائياتها
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for warehouse_stat in warehouses_stats %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card warehouse-card h-100 slide-in-right">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">{{ warehouse_stat.warehouse.name }}</h6>
                                        <span class="badge bg-primary">{{ warehouse_stat.warehouse.code }}</span>
                                    </div>
                                    
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h5 class="text-primary mb-0">{{ warehouse_stat.stock_count }}</h5>
                                                <small class="text-muted">صنف</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-success mb-0">{{ warehouse_stat.stock_value|currency }}</h5>
                                            <small class="text-muted">القيمة</small>
                                        </div>
                                    </div>
                                    
                                    {% if warehouse_stat.warehouse.capacity %}
                                    <div class="mt-2">
                                        <div class="d-flex justify-content-between">
                                            <small>الاستخدام</small>
                                            <small>{{ warehouse_stat.utilization|floatformat:1 }}%</small>
                                        </div>
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" style="width: {{ warehouse_stat.utilization }}%"></div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="mt-2 d-flex gap-2">
                                        <a href="#" onclick="showWarehouseDetails({{ warehouse_stat.warehouse.id }})"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>
                                            التفاصيل
                                        </a>
                                        <a href="/inventory/warehouse-stock/{{ warehouse_stat.warehouse.id }}/" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-boxes me-1"></i>
                                            عرض الجرد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الصف الثاني -->
    <div class="row mb-4">
        <!-- حركات المخزون الأخيرة -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        آخر حركات المخزون
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>النوع</th>
                                    <th>المخزن</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in recent_movements %}
                                <tr>
                                    <td>{{ movement.reference_number }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ movement.get_movement_type_display }}</span>
                                    </td>
                                    <td>{{ movement.warehouse.name }}</td>
                                    <td>{{ movement.date|date:"d/m/Y" }}</td>
                                    <td>{{ movement.total_amount|currency }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">لا توجد حركات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأصناف منخفضة المخزون -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        أصناف منخفضة المخزون
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>المخزن</th>
                                    <th>الكمية</th>
                                    <th>الحد الأدنى</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in low_stock_items %}
                                <tr>
                                    <td>{{ stock.item.name }}</td>
                                    <td>{{ stock.warehouse.name }}</td>
                                    <td class="text-danger">{{ stock.quantity }}</td>
                                    <td>{{ stock.item.min_stock }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-success">جميع الأصناف في المستوى الطبيعي</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- عمليات المخازن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        عمليات المخازن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إدارة المخازن -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-warehouse fa-3x text-primary mb-3"></i>
                                    <h6>إدارة المخازن</h6>
                                    <p class="text-muted small">تعريف وإدارة المخازن</p>
                                    <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- إذن إضافة الزيادات -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                                    <h6>إذن إضافة الزيادات</h6>
                                    <p class="text-muted small">إضافة أصناف للمخزن</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:stock_increase_list' %}" class="btn btn-sm btn-outline-success mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:stock_increase_create' %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إذن صرف النواقص -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-minus-circle fa-3x text-danger mb-3"></i>
                                    <h6>إذن صرف النواقص</h6>
                                    <p class="text-muted small">صرف أصناف من المخزن</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-sm btn-outline-danger mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:stock_decrease_create' %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تحويل بين المخازن -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt fa-3x text-info mb-3"></i>
                                    <h6>تحويل بين المخازن</h6>
                                    <p class="text-muted small">نقل أصناف بين المخازن</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:warehouse_transfer_list' %}" class="btn btn-sm btn-outline-info mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:warehouse_transfer_create' %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- بضاعة مضافة سلفة من الغير -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-arrow-down fa-3x text-warning mb-3"></i>
                                    <h6>بضاعة مضافة سلفة</h6>
                                    <p class="text-muted small">بضاعة من الغير</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:goods_received_on_loan_list' %}" class="btn btn-sm btn-outline-warning mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:goods_received_on_loan_create' %}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بضاعة مصروفة سلفة للغير -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-secondary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-arrow-up fa-3x text-secondary mb-3"></i>
                                    <h6>بضاعة مصروفة سلفة</h6>
                                    <p class="text-muted small">بضاعة للغير</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:goods_issued_on_loan_list' %}" class="btn btn-sm btn-outline-secondary mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:goods_issued_on_loan_create' %}" class="btn btn-sm btn-secondary">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التحويل من صنف إلى صنف -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-recycle fa-3x text-success mb-3"></i>
                                    <h6>التحويل من صنف إلى صنف</h6>
                                    <p class="text-muted small">تحويل الأصناف</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:item_transformation_list' %}" class="btn btn-sm btn-outline-success mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:item_transformation_create' %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إنتاج تام تصنيع -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-industry fa-3x text-primary mb-3"></i>
                                    <h6>إنتاج تام تصنيع</h6>
                                    <p class="text-muted small">أوامر الإنتاج</p>
                                    <div class="btn-group-vertical w-100">
                                        <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-sm btn-outline-primary mb-1">
                                            <i class="fas fa-list me-1"></i>القائمة
                                        </a>
                                        <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus me-1"></i>جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- الجرد الفعلي -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-clipboard-check fa-3x text-info mb-3"></i>
                                    <h6>الجرد الفعلي وتسجيل الفرق</h6>
                                    <p class="text-muted small">جرد المخازن</p>
                                    <div class="alert alert-warning p-2">تم تعطيل أزرار الجرد الفعلي مؤقتاً لعدم وجود المسارات المطلوبة.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الصف الثالث -->
    <div class="row mb-4">
        <!-- إحصائيات الحركات -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات الحركات (آخر 30 يوم)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-success">{{ movements_stats.increases }}</h4>
                                <small class="text-muted">إضافات</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-danger">{{ movements_stats.decreases }}</h4>
                            <small class="text-muted">صرف</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-info">{{ movements_stats.transfers }}</h4>
                                <small class="text-muted">تحويلات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ movements_stats.inventories }}</h4>
                            <small class="text-muted">جرد</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأصناف الأكثر حركة -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-fire text-danger me-2"></i>
                        الأصناف الأكثر حركة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>الحركات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in top_moving_items %}
                                <tr>
                                    <td>{{ item.item__name }}</td>
                                    <td>{{ item.total_quantity }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ item.movement_count }}</span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">لا توجد حركات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات وتعريفات المخازن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات وتعريفات المخازن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- فئات الأصناف -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-tags fa-2x text-info mb-3"></i>
                                    <h6>فئات الأصناف</h6>
                                    <p class="text-muted small">تصنيف الأصناف</p>
                                    <a href="{% url 'definitions:category_list' %}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- وحدات القياس -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-ruler fa-2x text-success mb-3"></i>
                                    <h6>وحدات القياس</h6>
                                    <p class="text-muted small">وحدات الأصناف</p>
                                    <a href="{% url 'definitions:unit_list' %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- الأصناف -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-2x text-primary mb-3"></i>
                                    <h6>الأصناف</h6>
                                    <p class="text-muted small">إدارة الأصناف</p>
                                    <a href="{% url 'definitions:item_list' %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- مناطق المخازن -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-map-marked-alt fa-2x text-warning mb-3"></i>
                                    <h6>مناطق المخازن</h6>
                                    <p class="text-muted small">تقسيم المناطق</p>
                                    <a href="{% url 'definitions:zone_list' %}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- مواقع المخازن -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-map-pin fa-2x text-danger mb-3"></i>
                                    <h6>مواقع المخازن</h6>
                                    <p class="text-muted small">مواقع التخزين</p>
                                    <a href="{% url 'definitions:location_list' %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- مواقع الأصناف -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-secondary h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-sitemap fa-2x text-secondary mb-3"></i>
                                    <h6>مواقع الأصناف</h6>
                                    <p class="text-muted small">ربط الأصناف بالمواقع</p>
                                    <a href="{% url 'definitions:item_location_list' %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-eye me-1"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تقارير المخازن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقارير المخازن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- تقارير الزيادات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle fa-2x text-success mb-3"></i>
                                    <h6>تقارير الزيادات</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-success mb-1">
                                            <i class="fas fa-chart-line me-1"></i>تقرير تفصيلي
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-chart-bar me-1"></i>تقرير إجمالي
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير النواقص -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-minus-circle fa-2x text-danger mb-3"></i>
                                    <h6>تقارير النواقص</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-danger mb-1">
                                            <i class="fas fa-chart-line me-1"></i>تقرير تفصيلي
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-calendar me-1"></i>تقرير شهري
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير التحويلات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt fa-2x text-info mb-3"></i>
                                    <h6>تقارير التحويلات</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-info mb-1">
                                            <i class="fas fa-chart-line me-1"></i>تقرير تفصيلي
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-chart-bar me-1"></i>تقرير إجمالي
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير الإنتاج -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-industry fa-2x text-primary mb-3"></i>
                                    <h6>تقارير الإنتاج</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-primary mb-1">
                                            <i class="fas fa-cogs me-1"></i>إنتاج تام
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-clock me-1"></i>تحت التشغيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير الأصناف -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-2x text-warning mb-3"></i>
                                    <h6>تقارير الأصناف</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-warning mb-1">
                                            <i class="fas fa-tags me-1"></i>الأصناف وأسعارها
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-chart-line me-1"></i>حركة الأصناف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقارير الجرد -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card bg-light h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-clipboard-check fa-2x text-secondary mb-3"></i>
                                    <h6>تقارير الجرد</h6>
                                    <div class="btn-group-vertical w-100">
                                        <a href="#" class="btn btn-sm btn-outline-secondary mb-1">
                                            <i class="fas fa-list me-1"></i>تقرير الجرد
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-exclamation-triangle me-1"></i>الفروقات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.progress {
    background-color: #eaecf4;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}
</style>

<script>
function showWarehouseDetails(warehouseId) {
    // إعادة توجيه إلى صفحة تفاصيل المخزن بالمسار الصحيح
    window.location.href = `/inventory/warehouse/${warehouseId}/`;
}

// تحديث البيانات كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);

// إضافة تأثيرات تفاعلية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    // تأثير hover للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث الوقت
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG');
        const dateString = now.toLocaleDateString('ar-EG');

        // إضافة الوقت إلى العنوان إذا لم يكن موجوداً
        const header = document.querySelector('h2');
        if (header && !header.querySelector('.time-display')) {
            const timeDisplay = document.createElement('small');
            timeDisplay.className = 'time-display text-muted ms-3';
            timeDisplay.innerHTML = `<i class="fas fa-clock me-1"></i>${timeString}`;
            header.appendChild(timeDisplay);
        } else if (header) {
            const timeDisplay = header.querySelector('.time-display');
            if (timeDisplay) {
                timeDisplay.innerHTML = `<i class="fas fa-clock me-1"></i>${timeString}`;
            }
        }
    }

    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);

    // إضافة تأثيرات للبطاقات
    addCardEffects();

    // إضافة إحصائيات سريعة
    addQuickStats();
});

// تأثيرات البطاقات
function addCardEffects() {
    const operationCards = document.querySelectorAll('.card.border-left-primary, .card.border-left-success, .card.border-left-danger, .card.border-left-info, .card.border-left-warning, .card.border-left-secondary');

    operationCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            this.style.transition = 'all 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15)';
        });
    });
}

// إضافة إحصائيات سريعة
function addQuickStats() {
    // إضافة عدادات للبطاقات
    const statsCards = document.querySelectorAll('.stats-card h4');

    statsCards.forEach(card => {
        const finalValue = parseInt(card.textContent) || 0;
        let currentValue = 0;
        const increment = finalValue / 50;

        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }

            if (card.textContent.includes('ج.م')) {
                card.textContent = Math.floor(currentValue).toLocaleString() + ' ج.م';
            } else {
                card.textContent = Math.floor(currentValue).toLocaleString();
            }
        }, 20);
    });
}

// دالة لإظهار تفاصيل سريعة
function showQuickInfo(type, title) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>معلومات سريعة عن ${title}</p>
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // إزالة المودال بعد الإغلاق
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// دالة للبحث السريع
function quickSearch(query) {
    const cards = document.querySelectorAll('.card');

    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        const searchQuery = query.toLowerCase();

        if (text.includes(searchQuery) || searchQuery === '') {
            card.style.display = 'block';
            card.style.opacity = '1';
        } else {
            card.style.opacity = '0.3';
        }
    });
}

// إضافة شريط بحث سريع
function addQuickSearchBar() {
    const header = document.querySelector('.row.mb-4 .col-12 .d-flex');
    if (header) {
        const searchDiv = document.createElement('div');
        searchDiv.className = 'me-3';
        searchDiv.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control" placeholder="بحث سريع..." id="quickSearch">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
            </div>
        `;

        header.insertBefore(searchDiv, header.lastElementChild);

        // إضافة وظيفة البحث
        document.getElementById('quickSearch').addEventListener('input', function() {
            quickSearch(this.value);
        });
    }
}

// تشغيل البحث السريع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addQuickSearchBar, 1000);
});
</script>
{% endblock %}
