{% extends 'base/base.html' %}

{% block title %}تعديل الملف الشخصي - حسابات أوساريك{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الملف الشخصي
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                {{ form.first_name.label }}
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.first_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                {{ form.last_name.label }}
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.last_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            {{ form.email.label }}
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-user-tag me-1"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" class="form-control" value="{{ user.username }}" readonly>
                        <small class="text-muted">لا يمكن تغيير اسم المستخدم</small>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>نصائح:</strong>
                    <ul class="mb-0 mt-2">
                        <li>تأكد من صحة البريد الإلكتروني لاستقبال الإشعارات</li>
                        <li>استخدم اسمك الحقيقي لسهولة التعرف عليك</li>
                        <li>يمكنك تغيير كلمة المرور من صفحة منفصلة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
