{% extends 'base/base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-line text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تحليلات شاملة لأداء المخازن والمخزون</p>
                </div>
                <div>
                    <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل الحركات الشهرية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تحليل الحركات الشهرية (آخر 12 شهر)
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyMovementsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل الأصناف والمخازن -->
    <div class="row mb-4">
        <!-- تحليل الأصناف حسب الفئات -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-pie-chart me-2"></i>
                        تحليل الأصناف حسب الفئات
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="categoriesChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- تحليل المخازن -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        تحليل أداء المخازن
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المخزن</th>
                                    <th>الأصناف</th>
                                    <th>القيمة</th>
                                    <th>الاستخدام</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for warehouse in warehouses_analysis %}
                                <tr>
                                    <td>{{ warehouse.warehouse.name }}</td>
                                    <td>{{ warehouse.items_count }}</td>
                                    <td>{{ warehouse.total_value|currency }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                <div class="progress-bar" style="width: {{ warehouse.utilization }}%">
                                                    {{ warehouse.utilization|floatformat:1 }}%
                                                </div>
                                            </div>
                                            <a href="{% url 'inventory:warehouse_detail' warehouse.warehouse.id %}" class="btn btn-sm btn-info ms-2">تفاصيل</a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الفئات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل الأصناف حسب الفئات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>عدد الأصناف</th>
                                    <th>إجمالي الكمية</th>
                                    <th>إجمالي القيمة</th>
                                    <th>متوسط التكلفة</th>
                                    <th>النسبة من الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items_analysis %}
                                <tr>
                                    <td>{{ item.item__category__name|default:"غير محدد" }}</td>
                                    <td>{{ item.items_count }}</td>
                                    <td>{{ item.total_quantity|floatformat:2 }}</td>
                                    <td>{{ item.total_value|currency }}</td>
                                    <td>{{ item.avg_cost|currency }}</td>
                                    <td>
                                        {% widthratio item.total_value total_value 100 as percentage %}
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2" style="height: 15px;">
                                                <div class="progress-bar bg-success" style="width: {{ percentage }}%"></div>
                                            </div>
                                            <small>{{ percentage|floatformat:1 }}%</small>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للحركات الشهرية
const monthlyCtx = document.getElementById('monthlyMovementsChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month in months_data %}
            '{{ month.month_name }}',
            {% endfor %}
        ],
        datasets: [{
            label: 'إضافات',
            data: [
                {% for month in months_data %}
                {{ month.increases|default:0 }},
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'صرف',
            data: [
                {% for month in months_data %}
                {{ month.decreases|default:0 }},
                {% endfor %}
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }, {
            label: 'تحويلات',
            data: [
                {% for month in months_data %}
                {{ month.transfers|default:0 }},
                {% endfor %}
            ],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'حركات المخزون الشهرية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني دائري للفئات
const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
const categoriesChart = new Chart(categoriesCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for item in items_analysis %}
            '{{ item.item__category__name|default:"غير محدد" }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for item in items_analysis %}
                {{ item.total_value|default:0 }},
                {% endfor %}
            ],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40',
                '#FF6384',
                '#C9CBCF',
                '#4BC0C0',
                '#FF6384'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'توزيع القيمة حسب الفئات'
            },
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<style>
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border: 1px solid #e3e6f0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

.progress {
    background-color: #eaecf4;
}
</style>
{% endblock %}
