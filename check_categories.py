#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory

def check_categories():
    print("=== Checking Item Categories ===")
    
    # Get all categories
    categories = ItemCategory.objects.filter(is_active=True)
    print(f"Active categories: {categories.count()}")
    
    for category in categories:
        items_count = Item.objects.filter(category=category, is_active=True).count()
        print(f"- {category.code}: {category.name} ({items_count} items)")
    
    print("\n=== Items by Category ===")
    for category in categories:
        items = Item.objects.filter(category=category, is_active=True)
        if items.count() > 0:
            print(f"\n{category.name}:")
            for item in items:
                print(f"  - {item.code}: {item.name}")

if __name__ == "__main__":
    check_categories()