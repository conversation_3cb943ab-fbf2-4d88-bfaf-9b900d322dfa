# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-07-07 20:28+0000\n"
"Last-Translator: Resulkary <<EMAIL>>\n"
"Language-Team: Turkmen (http://www.transifex.com/django/django/language/"
"tk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tk\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Mazmunyň görnüşleri"

msgid "python model class name"
msgstr "python model synpynyň ady"

msgid "content type"
msgstr "mazmunyň görnüşi"

msgid "content types"
msgstr "mazmunyň görnüşleri"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Mazmun görnüşi %(ct_id)s obýekti bilen baglanyşykly model ýok"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Mazmun görnüşi %(ct_id)s %(obj_id)s ýok"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s obýektlerde get_absolute_url() metody ýok"
