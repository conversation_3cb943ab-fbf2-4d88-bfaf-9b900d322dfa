{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice text-success me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات فاتورة المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.invoice_number.id_for_label }}" class="form-label">{{ form.invoice_number.label }}</label>
                                {{ form.invoice_number }}
                                {% if form.invoice_number.errors %}
                                    <div class="text-danger small">{{ form.invoice_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التواريخ والمسؤول -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    التواريخ والمسؤول
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label">{{ form.due_date.label }}</label>
                                {{ form.due_date }}
                                {% if form.due_date.errors %}
                                    <div class="text-danger small">{{ form.due_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.salesperson.id_for_label }}" class="form-label">{{ form.salesperson.label }}</label>
                                {{ form.salesperson }}
                                {% if form.salesperson.errors %}
                                    <div class="text-danger small">{{ form.salesperson.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    الملاحظات
                                </h6>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- العملة والصرف -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    العملة والصرف
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">{{ form.currency.label }}</label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small">{{ form.currency.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exchange_rate.id_for_label }}" class="form-label">{{ form.exchange_rate.label }}</label>
                                {{ form.exchange_rate }}
                                {% if form.exchange_rate.errors %}
                                    <div class="text-danger small">{{ form.exchange_rate.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 