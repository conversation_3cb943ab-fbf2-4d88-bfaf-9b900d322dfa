"""
API Views for Inventory Management
واجهات برمجة التطبيقات لإدارة المخزون
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from definitions.models import Item, Warehouse
from .models import Stock
from .utils import get_stock_quantity
# تم حذف استيراد نماذج التصنيع
import json


@login_required
@require_http_methods(["GET"])
def get_material_details(request, item_id, warehouse_id):
    """
    جلب تفاصيل مادة خام من مخزن معين
    """
    try:
        # جلب بيانات المخزون
        try:
            stock = Stock.objects.get(
                warehouse_id=warehouse_id,
                item_id=item_id
            )
            available_quantity = float(stock.quantity)
        except Stock.DoesNotExist:
            available_quantity = 0
        
        # جلب سعر المادة من جدول الأصناف
        try:
            item = Item.objects.get(id=item_id)
            unit_price = float(item.cost_price) if item.cost_price else 0
        except Item.DoesNotExist:
            unit_price = 0
        
        data = {
            'available_quantity': available_quantity,
            'unit_price': unit_price
        }
        
        return JsonResponse(data)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_item_price(request, item_id):
    """
    جلب سعر صنف معين
    """
    try:
        item = Item.objects.get(id=item_id, is_active=True)
        cost_price = float(item.cost_price) if item.cost_price else 0
        
        data = {
            'cost_price': cost_price
        }
        
        return JsonResponse(data)
    
    except Item.DoesNotExist:
        return JsonResponse({'error': 'الصنف غير موجود'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_item_prices(request):
    """
    جلب أسعار جميع الأصناف
    """
    try:
        items = Item.objects.filter(is_active=True).values(
            'id', 'name', 'code', 'cost_price', 'selling_price'
        )
        
        # تحويل البيانات إلى قاموس مفهرس بـ ID
        item_prices = {}
        for item in items:
            item_prices[str(item['id'])] = {
                'name': item['name'],
                'code': item['code'],
                'cost_price': float(item['cost_price']) if item['cost_price'] else 0,
                'selling_price': float(item['selling_price']) if item['selling_price'] else 0,
            }
        
        return JsonResponse(item_prices)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_item_details(request, item_id):
    """
    جلب تفاصيل صنف معين
    """
    try:
        item = Item.objects.get(id=item_id, is_active=True)
        
        data = {
            'id': item.id,
            'name': item.name,
            'code': item.code,
            'cost_price': float(item.cost_price) if item.cost_price else 0,
            'selling_price': float(item.selling_price) if item.selling_price else 0,
            'unit': item.unit.name if item.unit else '',
            'category': item.category.name if item.category else '',
            'description': item.description,
        }
        
        return JsonResponse(data)
    
    except Item.DoesNotExist:
        return JsonResponse({'error': 'الصنف غير موجود'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def warehouse_items(request):
    """
    جلب الأصناف الموجودة في مخزن معين
    """
    try:
        warehouse_id = request.GET.get('warehouse_id')
        item_type = request.GET.get('item_type', 'RAW_MATERIAL')
        
        if not warehouse_id:
            return JsonResponse({'error': 'معرف المخزن مطلوب'}, status=400)
        
        # جلب الأصناف الموجودة في المخزن
        stock_items = Stock.objects.filter(
            warehouse_id=warehouse_id,
            item__item_type=item_type,
            item__is_active=True,
            quantity__gt=0
        ).select_related('item').values(
            'item__id',
            'item__name',
            'item__code',
            'quantity'
        )
        
        items = []
        for stock in stock_items:
            items.append({
                'id': stock['item__id'],
                'name': stock['item__name'],
                'code': stock['item__code'],
                'available_quantity': float(stock['quantity'])
            })
        
        return JsonResponse({'items': items})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def material_data(request):
    """
    جلب بيانات مادة خام من مخزن معين
    """
    try:
        warehouse_id = request.GET.get('warehouse_id')
        material_id = request.GET.get('material_id')
        
        if not warehouse_id or not material_id:
            return JsonResponse({'error': 'معرف المخزن والمادة مطلوبان'}, status=400)
        
        # جلب بيانات المخزون
        try:
            stock = Stock.objects.get(
                warehouse_id=warehouse_id,
                item_id=material_id
            )
            available_quantity = float(stock.quantity)
            unit_cost = float(stock.average_cost) if stock.average_cost else 0
        except Stock.DoesNotExist:
            available_quantity = 0
            unit_cost = 0
        
        # جلب سعر المادة من جدول الأصناف
        try:
            item = Item.objects.get(id=material_id)
            if not unit_cost:
                unit_cost = float(item.cost_price) if item.cost_price else 0
        except Item.DoesNotExist:
            unit_cost = 0
        
        data = {
            'available_quantity': available_quantity,
            'unit_cost': unit_cost
        }
        
        return JsonResponse(data)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def item_cost(request):
    """
    جلب سعر صنف معين
    """
    try:
        item_id = request.GET.get('item_id')
        
        if not item_id:
            return JsonResponse({'error': 'معرف الصنف مطلوب'}, status=400)
        
        try:
            item = Item.objects.get(id=item_id, is_active=True)
            cost_price = float(item.cost_price) if item.cost_price else 0
            
            data = {
                'cost_price': cost_price
            }
            
            return JsonResponse(data)
        
        except Item.DoesNotExist:
            return JsonResponse({'error': 'الصنف غير موجود'}, status=404)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# تم حذف دوال API الخاصة بالتصنيع


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def save_materials_data(request):
    """
    حفظ بيانات المواد الخام الاحترافية
    """
    try:
        data = json.loads(request.body)
        materials_data = data.get('materials', [])
        order_id = data.get('order_id')
        total_cost = data.get('total_cost', 0)

        # التحقق من صحة البيانات
        if not materials_data:
            return JsonResponse({
                'success': False,
                'message': 'لا توجد مواد خام للحفظ'
            }, status=400)

        # معالجة البيانات
        saved_materials = []
        for material in materials_data:
            try:
                item = Item.objects.get(id=material['material_id'])
                saved_materials.append({
                    'item_id': item.id,
                    'item_name': item.name,
                    'quantity': material['quantity'],
                    'unit_cost': material['unit_cost'],
                    'total_cost': material['total_cost']
                })
            except Item.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'المادة الخام غير موجودة: {material["material_id"]}'
                }, status=400)

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ بيانات المواد الخام بنجاح',
            'data': {
                'materials_count': len(saved_materials),
                'total_cost': total_cost,
                'materials': saved_materials
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'بيانات JSON غير صحيحة'
        }, status=400)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في الحفظ: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_warehouse_raw_materials(request, warehouse_id):
    """
    جلب جميع المواد الموجودة في مخزن معين مع الكمية المتاحة والتكلفة
    """
    try:
        # التحقق من وجود المخزن
        try:
            warehouse = Warehouse.objects.get(id=warehouse_id, is_active=True)
        except Warehouse.DoesNotExist:
            return JsonResponse({
                'error': 'المخزن غير موجود',
                'items': []
            }, status=404)
        
        # جلب جميع المواد الموجودة في المخزن (وليس فقط المواد الخام)
        stock_items = Stock.objects.filter(
            warehouse_id=warehouse_id,
            item__is_active=True,
            quantity__gt=0
        ).select_related('item', 'item__unit').values(
            'item__id',
            'item__name',
            'item__code',
            'item__item_type',
            'quantity',
            'item__cost_price',
            'item__unit__name',
            'average_cost'
        )
        
        items = []
        for stock in stock_items:
            # استخدام متوسط التكلفة من المخزون إذا كان متوفراً، وإلا استخدم سعر التكلفة من الصنف
            unit_price = float(stock['average_cost']) if stock['average_cost'] else float(stock['item__cost_price']) if stock['item__cost_price'] else 0
            available_quantity = float(stock['quantity'])
            total_cost = unit_price * available_quantity
            unit_name = stock['item__unit__name'] or ''
            item_type = stock['item__item_type']
            
            # تحديد نوع المادة باللغة العربية
            item_type_ar = {
                'RAW_MATERIAL': 'مادة خام',
                'FINISHED_PRODUCT': 'منتج نهائي',
                'SEMI_FINISHED': 'نصف مصنعة',
                'CONSUMABLE': 'مستهلكات',
                'SPARE_PARTS': 'قطع غيار',
                'PACKAGING': 'تغليف'
            }.get(item_type, item_type)
            
            items.append({
                'id': stock['item__id'],
                'name': stock['item__name'],
                'code': stock['item__code'],
                'item_type': item_type,
                'item_type_ar': item_type_ar,
                'available_quantity': available_quantity,
                'unit_price': unit_price,
                'total_cost': total_cost,
                'unit': unit_name,
                'stock_quantity': available_quantity  # إضافة كمية المخزون الفعلية
            })
        
        # ترتيب المواد حسب النوع ثم الاسم
        items.sort(key=lambda x: (x['item_type'], x['name']))
        
        # إضافة معلومات إضافية للتصحيح
        response_data = {
            'items': items,
            'warehouse_id': warehouse_id,
            'warehouse_name': warehouse.name,
            'warehouse_code': warehouse.code,
            'total_items': len(items),
            'success': True,
            'message': f'تم جلب {len(items)} مادة من مخزن {warehouse.name}'
        }
        
        return JsonResponse(response_data)
    
    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'items': [],
            'success': False
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_all_warehouse_materials(request):
    """
    جلب جميع المواد الموجودة في جميع المخازن مع تجميعها حسب المخزن
    يدعم البحث في اسم المادة ورمزها واسم المخزن
    """
    try:
        # جلب جميع المخازن النشطة
        warehouses = Warehouse.objects.filter(is_active=True).order_by('name')
        
        all_materials = []
        
        for warehouse in warehouses:
            # جلب جميع المواد الموجودة في هذا المخزن
            stock_items = Stock.objects.filter(
                warehouse_id=warehouse.id,
                item__is_active=True,
                quantity__gt=0
            ).select_related('item', 'item__unit').values(
                'item__id',
                'item__name',
                'item__code',
                'item__item_type',
                'quantity',
                'item__cost_price',
                'item__unit__name',
                'average_cost'
            )
            
            for stock in stock_items:
                # استخدام متوسط التكلفة من المخزون إذا كان متوفراً
                unit_price = float(stock['average_cost']) if stock['average_cost'] else float(stock['item__cost_price']) if stock['item__cost_price'] else 0
                available_quantity = float(stock['quantity'])
                total_cost = unit_price * available_quantity
                unit_name = stock['item__unit__name'] or ''
                item_type = stock['item__item_type']
                
                # تحديد نوع المادة باللغة العربية
                item_type_ar = {
                    'RAW_MATERIAL': 'مادة خام',
                    'FINISHED_PRODUCT': 'منتج نهائي',
                    'SEMI_FINISHED': 'نصف مصنعة',
                    'CONSUMABLE': 'مستهلكات',
                    'SPARE_PARTS': 'قطع غيار',
                    'PACKAGING': 'تغليف'
                }.get(item_type, item_type)
        
                all_materials.append({
                    'id': stock['item__id'],
                    'name': stock['item__name'],
                    'code': stock['item__code'],
                    'item_type': item_type,
                    'item_type_ar': item_type_ar,
                    'available_quantity': available_quantity,
                    'unit_price': unit_price,
                    'total_cost': total_cost,
                    'unit': unit_name,
                    'stock_quantity': available_quantity,
                    'warehouse_id': warehouse.id,
                    'warehouse_name': warehouse.name,
                    'warehouse_code': warehouse.code
                })
        
        # تطبيق البحث إذا كان موجوداً
        search_term = request.GET.get('search', '').strip()
        if search_term:
            search_term_lower = search_term.lower()
            all_materials = [
                material for material in all_materials
                if (material['name'] and search_term_lower in material['name'].lower()) or
                   (material['code'] and search_term_lower in material['code'].lower()) or
                   (material['warehouse_name'] and search_term_lower in material['warehouse_name'].lower())
            ]
        # إذا لم يكن هناك نص بحث، عرض جميع المواد (مع حد أقصى 100 مادة)
        elif len(all_materials) > 100:
            all_materials = all_materials[:100]
        
        # ترتيب المواد حسب نوع المخزن ثم نوع المادة ثم الاسم
        all_materials.sort(key=lambda x: (x['warehouse_name'], x['item_type'], x['name']))
        
        # تجميع المواد حسب المخزن
        materials_by_warehouse = {}
        for material in all_materials:
            warehouse_key = f"{material['warehouse_name']} ({material['warehouse_code']})"
            if warehouse_key not in materials_by_warehouse:
                materials_by_warehouse[warehouse_key] = []
            materials_by_warehouse[warehouse_key].append(material)
        
        return JsonResponse({
            'materials': all_materials,
            'materials_by_warehouse': materials_by_warehouse,
            'total_materials': len(all_materials),
            'total_warehouses': len(warehouses),
            'search_term': search_term,
            'success': True,
            'message': f'تم جلب {len(all_materials)} مادة من {len(warehouses)} مخزن'
        })
    
    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'materials': [],
            'materials_by_warehouse': {},
            'success': False
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_all_items_with_prices(request):
    """
    جلب جميع الأصناف مع أسعارها
    """
    try:
        items = Item.objects.filter(is_active=True).select_related('unit').values(
            'id', 'name', 'code', 'cost_price', 'unit__name'
        )
        
        items_data = []
        for item in items:
            items_data.append({
                'id': item['id'],
                'name': item['name'],
                'code': item['code'],
                'cost_price': float(item['cost_price']) if item['cost_price'] else 0,
                'unit_name': item['unit__name'] or ''
            })
        
        return JsonResponse({
            'success': True,
            'items': items_data,
            'total_items': len(items_data)
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'items': []
        }, status=500)


@login_required
@require_http_methods(["POST"])
def update_manufacturing_order_availability(request, order_id):
    """
    تحديث الكميات المتاحة لأمر التصنيع
    """
    try:
        from .models import ManufacturingOrder
        order = ManufacturingOrder.objects.get(id=order_id, is_active=True)
        
        # تحديث الكميات المتاحة
        order.update_materials_availability()
        
        # جلب البيانات المحدثة
        materials_data = []
        for material in order.materials.all():
            materials_data.append({
                'id': material.id,
                'material_name': material.material.name,
                'available_quantity': float(material.available_quantity),
                'quantity_required': float(material.quantity_required),
                'is_sufficient': material.is_sufficient,
                'shortage_quantity': float(material.shortage_quantity)
            })
        
        return JsonResponse({
            'status': 'success',
            'message': 'تم تحديث الكميات المتاحة بنجاح',
            'materials': materials_data,
            'all_sufficient': order.materials_sufficient
        })
        
    except ManufacturingOrder.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'أمر التصنيع غير موجود'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'حدث خطأ: {str(e)}'
        }, status=500)