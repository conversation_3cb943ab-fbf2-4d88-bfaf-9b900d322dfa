from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import logout, login
from django.contrib.auth.models import User
import json
from django.utils import timezone
from datetime import datetime, timedelta
import json
import os
import subprocess
import hashlib
import shutil

from .models import (
    DeletedRecord, EditHistory, SystemBackup, SystemSettings,
    LicenseInfo, TaskbarSettings
)


# لوحة تحكم الخدمات
@login_required
def services_dashboard(request):
    """لوحة تحكم الخدمات"""

    # إحصائيات سريعة
    total_deleted_records = DeletedRecord.objects.filter(is_restored=False).count()
    total_edit_history = EditHistory.objects.count()
    total_backups = SystemBackup.objects.filter(is_valid=True).count()
    total_settings = SystemSettings.objects.count()

    # آخر العمليات
    recent_deleted = DeletedRecord.objects.filter(is_restored=False).order_by('-deleted_at')[:5]
    recent_edits = EditHistory.objects.order_by('-edited_at')[:5]
    recent_backups = SystemBackup.objects.filter(is_valid=True).order_by('-created_at')[:3]

    # معلومات الترخيص
    try:
        license_info = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_info = None

    # إعدادات شريط المهام للمستخدم الحالي
    try:
        taskbar_settings = TaskbarSettings.objects.get(user=request.user)
    except TaskbarSettings.DoesNotExist:
        taskbar_settings = None

    # إحصائيات النظام
    total_users = User.objects.filter(is_active=True).count()
    total_staff = User.objects.filter(is_staff=True, is_active=True).count()

    # حجم النسخ الاحتياطية
    total_backup_size = SystemBackup.objects.filter(is_valid=True).aggregate(
        total_size=Sum('file_size'))['total_size'] or 0

    # إعدادات النظام حسب الفئة
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = 0
        settings_by_category[setting.category] += 1

    # إحصائيات الباركود
    total_barcodes_generated = 0
    try:
        # الحصول على عدد الباركود من قاعدة البيانات
        barcode_setting = SystemSettings.objects.filter(key='total_barcodes_generated').first()
        if barcode_setting:
            total_barcodes_generated = int(barcode_setting.value or 0)
    except:
        total_barcodes_generated = 0

    context = {
        'title': 'لوحة تحكم الخدمات',
        'total_deleted_records': total_deleted_records,
        'total_edit_history': total_edit_history,
        'total_backups': total_backups,
        'total_settings': total_settings,
        'total_barcodes_generated': total_barcodes_generated,
        'recent_deleted': recent_deleted,
        'recent_edits': recent_edits,
        'recent_backups': recent_backups,
        'license': license_info,  # تغيير الاسم ليتطابق مع القالب
        'taskbar_settings': taskbar_settings,
        'total_users': total_users,
        'total_staff': total_staff,
        'total_backup_size': total_backup_size,
        'settings_by_category': settings_by_category,
    }
    return render(request, 'services/dashboard.html', context)


# إدارة البيانات
@login_required
@staff_member_required
def delete_data(request):
    """حذف البيانات المسجلة"""

    # قائمة النماذج المتاحة للحذف
    available_models = [
        {'name': 'Person', 'app': 'definitions', 'verbose_name': 'الأشخاص'},
        {'name': 'Item', 'app': 'definitions', 'verbose_name': 'الأصناف'},
        {'name': 'SalesInvoice', 'app': 'sales', 'verbose_name': 'فواتير المبيعات'},
        {'name': 'PurchaseInvoice', 'app': 'purchases', 'verbose_name': 'فواتير المشتريات'},
        {'name': 'JournalEntry', 'app': 'accounting', 'verbose_name': 'القيود المحاسبية'},
    ]

    if request.method == 'POST':
        selected_models = request.POST.getlist('models')
        date_from = request.POST.get('date_from')
        date_to = request.POST.get('date_to')
        confirm_delete = request.POST.get('confirm_delete')

        if not confirm_delete:
            messages.error(request, 'يجب تأكيد عملية الحذف')
            return redirect('services:delete_data')

        # تنفيذ عملية الحذف
        deleted_count = 0
        for model_name in selected_models:
            try:
                # هنا يمكن إضافة منطق الحذف الفعلي
                deleted_count += 1
            except Exception as e:
                messages.error(request, f'خطأ في حذف {model_name}: {str(e)}')

        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} نوع من البيانات بنجاح')

        return redirect('services:delete_data')

    context = {
        'title': 'حذف البيانات المسجلة',
        'available_models': available_models,
    }
    return render(request, 'services/delete_data.html', context)


@login_required
def recycle_bin(request):
    """سلة المحذوفات"""
    context = {
        'title': 'سلة المحذوفات',
    }
    return render(request, 'services/recycle_bin.html', context)


@login_required
def edit_history(request):
    """سلة التعديلات"""
    context = {
        'title': 'سلة التعديلات',
    }
    return render(request, 'services/edit_history.html', context)


@login_required
@staff_member_required
def backup(request):
    """النسخ الاحتياطي"""
    if request.method == 'POST':
        return handle_backup_creation(request)

    # جلب النسخ الاحتياطية الموجودة
    backups = SystemBackup.objects.filter(is_valid=True).order_by('-created_at')

    # إحصائيات النسخ الاحتياطية
    total_backups = backups.count()
    total_size = backups.aggregate(total=Sum('file_size'))['total'] or 0
    latest_backup = backups.first()

    # إعدادات النسخ الاحتياطي
    backup_settings = get_backup_settings()

    # جلب الأقراص المتاحة والمسارات الشائعة
    available_drives = get_available_drives()
    common_paths = get_common_backup_paths()

    context = {
        'title': 'النسخ الاحتياطي',
        'backups': backups,
        'total_backups': total_backups,
        'total_size': total_size,
        'total_size_mb': round(total_size / (1024 * 1024), 2) if total_size else 0,
        'latest_backup': latest_backup,
        'backup_settings': backup_settings,
        'available_drives': available_drives,
        'common_paths': common_paths,
    }
    return render(request, 'services/backup.html', context)


@login_required
def license_info(request):
    """معلومات الترخيص"""
    try:
        license_obj = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_obj = None

    context = {
        'title': 'ترخيص النسخة',
        'license': license_obj,
    }
    return render(request, 'services/license.html', context)


@login_required
@staff_member_required
def recalculate_costs(request):
    """إعادة حساب أسعار تكلفة الأصناف"""
    context = {
        'title': 'إعادة حساب أسعار تكلفة الأصناف',
    }
    return render(request, 'services/recalculate_costs.html', context)


@login_required
def taskbar_settings(request):
    """إعدادات شريط المهام"""

    try:
        settings = TaskbarSettings.objects.get(user=request.user)
    except TaskbarSettings.DoesNotExist:
        settings = TaskbarSettings.objects.create(user=request.user)

    if request.method == 'POST':
        settings.position = request.POST.get('position', 'HORIZONTAL')
        settings.auto_hide = request.POST.get('auto_hide') == 'on'
        settings.show_icons = request.POST.get('show_icons') == 'on'
        settings.show_text = request.POST.get('show_text') == 'on'
        settings.theme = request.POST.get('theme', 'default')
        settings.size = request.POST.get('size', 'medium')

        # ترتيب القوائم
        menu_order = request.POST.get('menu_order', '[]')
        try:
            settings.menu_order = json.loads(menu_order)
        except:
            settings.menu_order = []

        # العناصر المثبتة
        pinned_items = request.POST.getlist('pinned_items')
        settings.pinned_items = pinned_items

        settings.save()
        messages.success(request, 'تم حفظ إعدادات شريط المهام بنجاح')
        return redirect('services:taskbar_settings')

    context = {
        'title': 'إعدادات شريط المهام',
        'settings': settings,
    }
    return render(request, 'services/taskbar_settings.html', context)


@login_required
@staff_member_required
def system_settings(request):
    """كلمات السر وخيارات البرنامج"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'change_password':
            # تغيير كلمة المرور
            old_password = request.POST.get('old_password')
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')

            if not request.user.check_password(old_password):
                messages.error(request, 'كلمة المرور الحالية غير صحيحة')
            elif new_password != confirm_password:
                messages.error(request, 'كلمة المرور الجديدة غير متطابقة')
            elif len(new_password) < 8:
                messages.error(request, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
            else:
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, 'تم تغيير كلمة المرور بنجاح')
                return redirect('services:system_settings')

        elif action == 'update_setting':
            # تحديث إعداد معين
            setting_key = request.POST.get('setting_key')
            setting_value = request.POST.get('setting_value')
            setting_file = request.FILES.get('setting_file')
            remove_file = request.POST.get('remove_file')

            try:
                setting = SystemSettings.objects.get(key=setting_key)
                if setting.is_editable:
                    # التحقق من صحة البيانات حسب النوع
                    if setting.value_type == 'BOOLEAN':
                        setting_value = 'true' if setting_value == 'true' else 'false'
                    elif setting.value_type == 'INTEGER':
                        try:
                            int(setting_value)
                        except ValueError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم صحيح'})
                            messages.error(request, 'يجب أن تكون القيمة رقم صحيح')
                            return redirect('services:system_settings')
                    elif setting.value_type == 'FLOAT':
                        try:
                            float(setting_value)
                        except ValueError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم عشري'})
                            messages.error(request, 'يجب أن تكون القيمة رقم عشري')
                            return redirect('services:system_settings')
                    elif setting.value_type == 'JSON':
                        try:
                            json.loads(setting_value)
                        except json.JSONDecodeError:
                            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                                return JsonResponse({'success': False, 'message': 'صيغة JSON غير صحيحة'})
                            messages.error(request, 'صيغة JSON غير صحيحة')
                            return redirect('services:system_settings')
                    elif setting.value_type == 'FILE':
                        # معالجة الملفات
                        if remove_file == 'true':
                            # حذف الملف
                            if setting.file_value:
                                setting.file_value.delete()
                            setting.value = ''
                        elif setting_file:
                            # رفع ملف جديد
                            if setting.file_value:
                                setting.file_value.delete()  # حذف الملف القديم
                            setting.file_value = setting_file
                            setting.value = setting_file.name

                    if setting.value_type != 'FILE':
                        setting.value = setting_value
                    setting.updated_by = request.user
                    setting.save()

                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'success': True, 'message': f'تم تحديث إعداد {setting.description} بنجاح'})
                    messages.success(request, f'تم تحديث إعداد {setting.description} بنجاح')
                else:
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'success': False, 'message': 'هذا الإعداد غير قابل للتعديل'})
                    messages.error(request, 'هذا الإعداد غير قابل للتعديل')
            except SystemSettings.DoesNotExist:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'success': False, 'message': 'الإعداد غير موجود'})
                messages.error(request, 'الإعداد غير موجود')

        elif action == 'create_user':
            # إنشاء مستخدم جديد
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            password = request.POST.get('password')
            is_staff = request.POST.get('is_staff') == 'on'

            if User.objects.filter(username=username).exists():
                messages.error(request, 'اسم المستخدم موجود بالفعل')
            elif User.objects.filter(email=email).exists():
                messages.error(request, 'البريد الإلكتروني موجود بالفعل')
            else:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    password=password,
                    is_staff=is_staff
                )
                messages.success(request, f'تم إنشاء المستخدم {username} بنجاح')

        elif action == 'toggle_user_status':
            # تفعيل/تعطيل مستخدم
            user_id = request.POST.get('user_id')
            try:
                user = User.objects.get(id=user_id)
                if user != request.user:  # لا يمكن تعطيل النفس
                    user.is_active = not user.is_active
                    user.save()
                    status = 'تم تفعيل' if user.is_active else 'تم تعطيل'
                    messages.success(request, f'{status} المستخدم {user.username} بنجاح')
                else:
                    messages.error(request, 'لا يمكنك تعطيل حسابك الخاص')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        elif action == 'reset_password':
            # إعادة تعيين كلمة مرور مستخدم
            user_id = request.POST.get('user_id')
            new_password = request.POST.get('new_password', '12345678')
            try:
                user = User.objects.get(id=user_id)
                user.set_password(new_password)
                user.save()
                messages.success(request, f'تم إعادة تعيين كلمة مرور المستخدم {user.username}')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        elif action == 'edit_user':
            # تعديل بيانات مستخدم
            user_id = request.POST.get('user_id')
            username = request.POST.get('username')
            email = request.POST.get('email')
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            is_staff = request.POST.get('is_staff') == 'on'

            try:
                user = User.objects.get(id=user_id)

                # التحقق من عدم تكرار اسم المستخدم
                if User.objects.filter(username=username).exclude(id=user_id).exists():
                    messages.error(request, 'اسم المستخدم موجود بالفعل')
                elif User.objects.filter(email=email).exclude(id=user_id).exists():
                    messages.error(request, 'البريد الإلكتروني موجود بالفعل')
                else:
                    user.username = username
                    user.email = email
                    user.first_name = first_name
                    user.last_name = last_name
                    user.is_staff = is_staff
                    user.save()
                    messages.success(request, f'تم تحديث بيانات المستخدم {username} بنجاح')
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        elif action == 'update_performance':
            # تحديث إعدادات الأداء
            cache_size = request.POST.get('cache_size', '128')
            query_timeout = request.POST.get('query_timeout', '30')
            enable_compression = request.POST.get('enable_compression') == 'on'
            enable_caching = request.POST.get('enable_caching') == 'on'

            # حفظ إعدادات الأداء
            performance_settings = [
                ('cache_size', cache_size, 'حجم ذاكرة التخزين المؤقت'),
                ('query_timeout', query_timeout, 'مهلة انتظار الاستعلام'),
                ('enable_compression', str(enable_compression).lower(), 'تفعيل ضغط البيانات'),
                ('enable_caching', str(enable_caching).lower(), 'تفعيل التخزين المؤقت'),
            ]

            for key, value, desc in performance_settings:
                setting, created = SystemSettings.objects.get_or_create(
                    key=key,
                    defaults={
                        'value': value,
                        'category': 'الأداء',
                        'description': desc,
                        'value_type': 'INTEGER' if key in ['cache_size', 'query_timeout'] else 'BOOLEAN',
                        'is_editable': True,
                        'updated_by': request.user
                    }
                )
                if not created:
                    setting.value = value
                    setting.updated_by = request.user
                    setting.save()

            messages.success(request, 'تم حفظ إعدادات الأداء بنجاح')

        elif action == 'update_security':
            # تحديث إعدادات الأمان المتقدمة
            enable_2fa = request.POST.get('enable_2fa') == 'on'
            enable_ip_whitelist = request.POST.get('enable_ip_whitelist') == 'on'
            enable_audit_log = request.POST.get('enable_audit_log') == 'on'
            encryption_level = request.POST.get('encryption_level', 'standard')

            security_settings = [
                ('enable_2fa', str(enable_2fa).lower(), 'تفعيل المصادقة الثنائية'),
                ('enable_ip_whitelist', str(enable_ip_whitelist).lower(), 'تفعيل قائمة IP المسموحة'),
                ('enable_audit_log', str(enable_audit_log).lower(), 'تفعيل سجل المراجعة'),
                ('encryption_level', encryption_level, 'مستوى التشفير'),
            ]

            for key, value, desc in security_settings:
                setting, created = SystemSettings.objects.get_or_create(
                    key=key,
                    defaults={
                        'value': value,
                        'category': 'الأمان المتقدم',
                        'description': desc,
                        'value_type': 'STRING' if key == 'encryption_level' else 'BOOLEAN',
                        'is_editable': True,
                        'updated_by': request.user
                    }
                )
                if not created:
                    setting.value = value
                    setting.updated_by = request.user
                    setting.save()

            messages.success(request, 'تم حفظ إعدادات الأمان بنجاح')

        elif action == 'update_integration':
            # تحديث إعدادات التكامل
            api_key = request.POST.get('api_key', '')
            enable_api = request.POST.get('enable_api') == 'on'
            enable_webhooks = request.POST.get('enable_webhooks') == 'on'
            rate_limit = request.POST.get('rate_limit', '60')

            integration_settings = [
                ('api_key', api_key, 'مفتاح API'),
                ('enable_api', str(enable_api).lower(), 'تفعيل واجهة برمجة التطبيقات'),
                ('enable_webhooks', str(enable_webhooks).lower(), 'تفعيل Webhooks'),
                ('api_rate_limit', rate_limit, 'معدل الطلبات'),
            ]

            for key, value, desc in integration_settings:
                setting, created = SystemSettings.objects.get_or_create(
                    key=key,
                    defaults={
                        'value': value,
                        'category': 'التكامل',
                        'description': desc,
                        'value_type': 'INTEGER' if key == 'api_rate_limit' else ('STRING' if key == 'api_key' else 'BOOLEAN'),
                        'is_editable': True,
                        'updated_by': request.user
                    }
                )
                if not created:
                    setting.value = value
                    setting.updated_by = request.user
                    setting.save()

            messages.success(request, 'تم حفظ إعدادات التكامل بنجاح')

    # جلب البيانات للعرض
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = []
        settings_by_category[setting.category].append(setting)

    users = User.objects.all().order_by('username')
    staff_users = User.objects.filter(is_staff=True).order_by('username')

    context = {
        'title': 'كلمات السر وخيارات البرنامج',
        'settings_by_category': settings_by_category,
        'users': users,
        'staff_users': staff_users,
        'total_users': users.count(),
        'total_staff': staff_users.count(),
    }
    return render(request, 'services/system_settings.html', context)


@login_required
def print_design(request):
    """تصميم نماذج الطباعة"""
    context = {
        'title': 'تصميم نماذج الطباعة',
    }
    return render(request, 'services/print_design.html', context)


@login_required
def barcode_design(request):
    """تصميم نماذج الباركود"""
    context = {
        'title': 'تصميم نماذج الباركود',
    }
    return render(request, 'services/barcode_design.html', context)


@login_required
def relogin(request):
    """إعادة الدخول كمستخدم آخر"""
    context = {
        'title': 'إعادة الدخول كمستخدم',
    }
    return render(request, 'services/relogin.html', context)


@login_required
@staff_member_required
def system_report(request):
    """تقرير حالة النظام"""
    import platform
    try:
        import psutil
        has_psutil = True
    except ImportError:
        has_psutil = False

    from django.db import connection

    # معلومات النظام الأساسية
    system_info = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'django_version': '4.2.0',
    }

    # معلومات الأداء إذا كان psutil متاح
    if has_psutil:
        system_info.update({
            'cpu_count': psutil.cpu_count(),
            'memory_total': round(psutil.virtual_memory().total / (1024**3), 2),  # GB
            'memory_available': round(psutil.virtual_memory().available / (1024**3), 2),  # GB
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent,
        })

    # إحصائيات قاعدة البيانات
    with connection.cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM auth_user")
        total_users = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM services_systemsettings")
        total_settings = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM services_deletedrecord WHERE is_restored = false")
        deleted_records = cursor.fetchone()[0]

    # إحصائيات الإعدادات حسب الفئة
    settings_by_category = {}
    for setting in SystemSettings.objects.all():
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = 0
        settings_by_category[setting.category] += 1

    # معلومات الترخيص
    try:
        license_info = LicenseInfo.objects.first()
    except LicenseInfo.DoesNotExist:
        license_info = None

    context = {
        'title': 'تقرير حالة النظام',
        'system_info': system_info,
        'has_psutil': has_psutil,
        'total_users': total_users,
        'total_settings': total_settings,
        'deleted_records': deleted_records,
        'settings_by_category': settings_by_category,
        'license_info': license_info,
    }
    return render(request, 'services/system_report.html', context)


@login_required
def financial_settings(request):
    """عرض الإعدادات المالية"""
    from definitions.models import Currency
    from core.currency_utils import get_decimal_places, get_currency_symbol, get_currency_name

    # جلب الإعدادات المالية
    financial_keys = [
        'default_currency', 'currency_symbol', 'currency_name', 'decimal_places',
        'tax_rate', 'currency_position', 'thousands_separator', 'decimal_separator',
        'show_currency_code', 'auto_update_rates'
    ]

    financial_settings = []
    for key in financial_keys:
        try:
            setting = SystemSettings.objects.get(key=key)
            financial_settings.append(setting)
        except SystemSettings.DoesNotExist:
            continue

    # جلب العملات
    currencies = Currency.objects.filter(is_active=True).order_by('name')

    # إحصائيات
    currencies_count = currencies.count()
    decimal_places = get_decimal_places()

    try:
        tax_rate_setting = SystemSettings.objects.get(key='tax_rate')
        tax_rate = float(tax_rate_setting.value)
    except (SystemSettings.DoesNotExist, ValueError):
        tax_rate = 14.0

    active_settings_count = SystemSettings.objects.filter(
        category='الإعدادات المالية'
    ).count()

    context = {
        'title': 'الإعدادات المالية',
        'financial_settings': financial_settings,
        'currencies': currencies,
        'currencies_count': currencies_count,
        'decimal_places': decimal_places,
        'tax_rate': tax_rate,
        'active_settings_count': active_settings_count,
    }

    return render(request, 'services/financial_settings.html', context)


@login_required
def test_settings(request):
    """اختبار الإعدادات"""
    from .templatetags.system_tags import get_setting, format_currency, company_info

    # اختبار الإعدادات المختلفة
    test_data = {
        'company_name': get_setting('company_name', 'غير محدد'),
        'currency_symbol': get_setting('currency_symbol', 'ر.س'),
        'decimal_places': get_setting('decimal_places', 2),
        'tax_rate': get_setting('tax_rate', 15.0),
        'ui_theme': get_setting('ui_theme', 'default'),
        'ui_primary_color': get_setting('ui_primary_color', '#0d6efd'),
        'enable_dark_mode': get_setting('ui_enable_dark_mode', False),
        'records_per_page': get_setting('ui_records_per_page', 25),
    }

    # اختبار تنسيق العملة
    test_amount = 1234.56
    formatted_amount = format_currency(test_amount)

    # اختبار معلومات الشركة
    company_data = {
        'name': company_info('name'),
        'address': company_info('address'),
        'phone': company_info('phone'),
        'email': company_info('email'),
    }

    context = {
        'title': 'اختبار الإعدادات',
        'test_data': test_data,
        'test_amount': test_amount,
        'formatted_amount': formatted_amount,
        'company_data': company_data,
    }
    return render(request, 'services/test_settings.html', context)


# AJAX endpoints
@require_POST
@csrf_exempt
def ajax_delete_records(request):
    """حذف السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الحذف بنجاح'})


@require_POST
@csrf_exempt
def ajax_restore_records(request):
    """استرداد السجلات عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم الاسترداد بنجاح'})


def handle_backup_creation(request):
    """معالجة إنشاء النسخة الاحتياطية"""
    try:
        backup_name = request.POST.get('backup_name', f'backup_{timezone.now().strftime("%Y%m%d_%H%M%S")}')
        backup_type = request.POST.get('backup_type', 'FULL')
        include_media = request.POST.get('include_media') == 'on'
        include_logs = request.POST.get('include_logs') == 'on'
        compress_backup = request.POST.get('compress_backup', 'on') == 'on'
        description = request.POST.get('description', '')
        custom_path = request.POST.get('custom_path', '').strip()
        save_external = request.POST.get('save_external') == 'on'

        # التحقق من صحة المسار المخصص
        if save_external and custom_path:
            if not os.path.exists(custom_path):
                messages.error(request, f'المسار المحدد غير موجود: {custom_path}')
                return redirect('services:backup')
            if not os.access(custom_path, os.W_OK):
                messages.error(request, f'لا يمكن الكتابة في المسار المحدد: {custom_path}')
                return redirect('services:backup')

        # إنشاء النسخة الاحتياطية
        backup = create_database_backup(
            backup_name=backup_name,
            backup_type=backup_type,
            include_media=include_media,
            include_logs=include_logs,
            compress_backup=compress_backup,
            description=description,
            created_by=request.user,
            custom_path=custom_path if save_external else None
        )

        if backup:
            messages.success(request, f'تم إنشاء النسخة الاحتياطية "{backup_name}" بنجاح!')
        else:
            messages.error(request, 'فشل في إنشاء النسخة الاحتياطية')

    except Exception as e:
        messages.error(request, f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}')

    return redirect('services:backup')


def get_backup_settings():
    """جلب إعدادات النسخ الاحتياطي"""
    settings = {}
    try:
        settings['auto_backup'] = SystemSettings.objects.get(key='backup_auto').value == 'true'
        settings['frequency'] = SystemSettings.objects.get(key='backup_frequency').value
        settings['time'] = SystemSettings.objects.get(key='backup_time').value
        settings['retention_days'] = int(SystemSettings.objects.get(key='backup_retention_days').value)
    except SystemSettings.DoesNotExist:
        settings = {
            'auto_backup': False,
            'frequency': 'daily',
            'time': '02:00',
            'retention_days': 30
        }
    return settings


def get_available_drives():
    """جلب قائمة الأقراص المتاحة في النظام"""
    import string
    import platform

    drives = []

    if platform.system() == 'Windows':
        # للنظام Windows
        for letter in string.ascii_uppercase:
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                try:
                    # محاولة الحصول على معلومات القرص
                    total, used, free = shutil.disk_usage(drive)
                    drives.append({
                        'path': drive,
                        'name': f"القرص {letter}:",
                        'free_space': free,
                        'free_space_gb': round(free / (1024**3), 2),
                        'total_space_gb': round(total / (1024**3), 2)
                    })
                except:
                    # إذا لم نتمكن من الوصول للقرص
                    drives.append({
                        'path': drive,
                        'name': f"القرص {letter}: (غير متاح)",
                        'free_space': 0,
                        'free_space_gb': 0,
                        'total_space_gb': 0
                    })
    else:
        # للأنظمة Unix/Linux/Mac
        drives.append({
            'path': '/home',
            'name': 'المجلد الرئيسي',
            'free_space': 0,
            'free_space_gb': 0,
            'total_space_gb': 0
        })
        drives.append({
            'path': '/tmp',
            'name': 'المجلد المؤقت',
            'free_space': 0,
            'free_space_gb': 0,
            'total_space_gb': 0
        })

    return drives


def get_common_backup_paths():
    """جلب المسارات الشائعة للنسخ الاحتياطية"""
    import platform

    paths = []

    if platform.system() == 'Windows':
        # مسارات Windows الشائعة
        common_paths = [
            os.path.expanduser('~/Desktop'),
            os.path.expanduser('~/Documents'),
            os.path.expanduser('~/Downloads'),
            'D:\\Backups',
            'E:\\Backups',
            'F:\\Backups'
        ]
    else:
        # مسارات Unix/Linux/Mac الشائعة
        common_paths = [
            os.path.expanduser('~/Desktop'),
            os.path.expanduser('~/Documents'),
            os.path.expanduser('~/Downloads'),
            '/tmp/backups',
            '/var/backups'
        ]

    for path in common_paths:
        if os.path.exists(path):
            try:
                if os.access(path, os.W_OK):
                    paths.append({
                        'path': path,
                        'name': os.path.basename(path) or path,
                        'accessible': True
                    })
                else:
                    paths.append({
                        'path': path,
                        'name': f"{os.path.basename(path) or path} (للقراءة فقط)",
                        'accessible': False
                    })
            except:
                pass

    return paths


def create_database_backup(backup_name, backup_type='FULL', include_media=True,
                          include_logs=False, compress_backup=True, description='', created_by=None, custom_path=None):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    import os
    import sqlite3
    import shutil
    import zipfile
    from django.conf import settings

    try:
        # تحديد مجلد النسخ الاحتياطية
        if custom_path and os.path.exists(custom_path):
            backup_dir = custom_path
        else:
            backup_dir = os.path.join(settings.BASE_DIR, 'backups')

        os.makedirs(backup_dir, exist_ok=True)

        # تحديد مسار الملف
        timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
        if compress_backup:
            backup_filename = f"{backup_name}_{timestamp}.zip"
        else:
            backup_filename = f"{backup_name}_{timestamp}.db"

        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        db_path = settings.DATABASES['default']['NAME']

        if compress_backup:
            # إنشاء ملف مضغوط
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                zipf.write(db_path, 'database.db')

                # إضافة ملفات الوسائط إذا طُلب ذلك
                if include_media and hasattr(settings, 'MEDIA_ROOT') and os.path.exists(settings.MEDIA_ROOT):
                    for root, dirs, files in os.walk(settings.MEDIA_ROOT):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, settings.BASE_DIR)
                            zipf.write(file_path, arcname)

                # إضافة ملفات السجلات إذا طُلب ذلك
                if include_logs:
                    logs_dir = os.path.join(settings.BASE_DIR, 'logs')
                    if os.path.exists(logs_dir):
                        for root, dirs, files in os.walk(logs_dir):
                            for file in files:
                                if file.endswith('.log'):
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path, settings.BASE_DIR)
                                    zipf.write(file_path, arcname)
        else:
            # نسخ مباشر لقاعدة البيانات
            shutil.copy2(db_path, backup_path)

        # حساب حجم الملف
        file_size = os.path.getsize(backup_path)

        # حساب المجموع التحققي
        checksum = calculate_file_checksum(backup_path)

        # حفظ معلومات النسخة في قاعدة البيانات
        backup = SystemBackup.objects.create(
            backup_name=backup_name,
            backup_type=backup_type,
            file_path=backup_path,
            file_size=file_size,
            created_by=created_by,
            description=description,
            include_media=include_media,
            include_logs=include_logs,
            compress_backup=compress_backup,
            checksum=checksum,
            is_valid=True
        )

        return backup

    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None


def calculate_file_checksum(file_path):
    """حساب المجموع التحققي للملف"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except:
        return ""


@require_POST
@csrf_exempt
def ajax_create_backup(request):
    """إنشاء نسخة احتياطية عبر AJAX"""
    try:
        backup_name = request.POST.get('backup_name', f'backup_{timezone.now().strftime("%Y%m%d_%H%M%S")}')
        backup_type = request.POST.get('backup_type', 'FULL')
        custom_path = request.POST.get('custom_path', '').strip()

        # التحقق من المسار المخصص إذا تم تحديده
        if custom_path:
            if not os.path.exists(custom_path):
                return JsonResponse({'success': False, 'message': f'المسار المحدد غير موجود: {custom_path}'})
            if not os.access(custom_path, os.W_OK):
                return JsonResponse({'success': False, 'message': f'لا يمكن الكتابة في المسار المحدد: {custom_path}'})

        backup = create_database_backup(
            backup_name=backup_name,
            backup_type=backup_type,
            created_by=request.user,
            custom_path=custom_path if custom_path else None
        )

        if backup:
            return JsonResponse({
                'success': True,
                'message': f'تم إنشاء النسخة الاحتياطية "{backup_name}" بنجاح!',
                'backup_id': backup.id,
                'file_size': backup.file_size_mb,
                'file_path': backup.file_path
            })
        else:
            return JsonResponse({'success': False, 'message': 'فشل في إنشاء النسخة الاحتياطية'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ: {str(e)}'})


@login_required
@staff_member_required
def download_backup(request, backup_id):
    """تحميل نسخة احتياطية"""
    try:
        backup = get_object_or_404(SystemBackup, id=backup_id, is_valid=True)

        if not os.path.exists(backup.file_path):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('services:backup')

        # التحقق من صحة الملف
        if backup.checksum:
            current_checksum = calculate_file_checksum(backup.file_path)
            if current_checksum != backup.checksum:
                messages.error(request, 'ملف النسخة الاحتياطية تالف')
                return redirect('services:backup')

        # إرسال الملف للتحميل
        response = HttpResponse(content_type='application/octet-stream')
        response['Content-Disposition'] = f'attachment; filename="{os.path.basename(backup.file_path)}"'

        with open(backup.file_path, 'rb') as f:
            response.write(f.read())

        return response

    except Exception as e:
        messages.error(request, f'خطأ في تحميل النسخة الاحتياطية: {str(e)}')
        return redirect('services:backup')


@login_required
@staff_member_required
def delete_backup(request, backup_id):
    """حذف نسخة احتياطية"""
    try:
        backup = get_object_or_404(SystemBackup, id=backup_id)

        # حذف الملف من النظام
        if os.path.exists(backup.file_path):
            os.remove(backup.file_path)

        # حذف السجل من قاعدة البيانات
        backup_name = backup.backup_name
        backup.delete()

        messages.success(request, f'تم حذف النسخة الاحتياطية "{backup_name}" بنجاح')

    except Exception as e:
        messages.error(request, f'خطأ في حذف النسخة الاحتياطية: {str(e)}')

    return redirect('services:backup')


@login_required
@staff_member_required
def restore_backup(request, backup_id):
    """استعادة نسخة احتياطية"""
    try:
        backup = get_object_or_404(SystemBackup, id=backup_id, is_valid=True)

        if not os.path.exists(backup.file_path):
            messages.error(request, 'ملف النسخة الاحتياطية غير موجود')
            return redirect('services:backup')

        # التحقق من صحة الملف
        if backup.checksum:
            current_checksum = calculate_file_checksum(backup.file_path)
            if current_checksum != backup.checksum:
                messages.error(request, 'ملف النسخة الاحتياطية تالف')
                return redirect('services:backup')

        if request.method == 'POST':
            # تنفيذ عملية الاستعادة
            success = perform_backup_restore(backup)

            if success:
                messages.success(request, f'تم استعادة النسخة الاحتياطية "{backup.backup_name}" بنجاح')
                # إعادة تسجيل الدخول مطلوبة بعد استعادة قاعدة البيانات
                logout(request)
                return redirect('accounts:login')
            else:
                messages.error(request, 'فشل في استعادة النسخة الاحتياطية')

        context = {
            'title': 'استعادة نسخة احتياطية',
            'backup': backup,
        }
        return render(request, 'services/restore_backup.html', context)

    except Exception as e:
        messages.error(request, f'خطأ في استعادة النسخة الاحتياطية: {str(e)}')
        return redirect('services:backup')


def perform_backup_restore(backup):
    """تنفيذ عملية استعادة النسخة الاحتياطية"""
    import zipfile
    import shutil
    from django.conf import settings

    try:
        # إنشاء نسخة احتياطية من الوضع الحالي قبل الاستعادة
        current_backup = create_database_backup(
            backup_name=f'before_restore_{timezone.now().strftime("%Y%m%d_%H%M%S")}',
            backup_type='FULL',
            description='نسخة احتياطية تلقائية قبل الاستعادة'
        )

        db_path = settings.DATABASES['default']['NAME']

        if backup.compress_backup and backup.file_path.endswith('.zip'):
            # استخراج من ملف مضغوط
            with zipfile.ZipFile(backup.file_path, 'r') as zipf:
                # استخراج قاعدة البيانات
                zipf.extract('database.db', settings.BASE_DIR)
                extracted_db = os.path.join(settings.BASE_DIR, 'database.db')

                # استبدال قاعدة البيانات الحالية
                shutil.move(extracted_db, db_path)

                # استخراج ملفات الوسائط إذا كانت موجودة
                if backup.include_media:
                    for file_info in zipf.filelist:
                        if file_info.filename.startswith('media/'):
                            zipf.extract(file_info, settings.BASE_DIR)
        else:
            # نسخ مباشر لقاعدة البيانات
            shutil.copy2(backup.file_path, db_path)

        return True

    except Exception as e:
        print(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
        return False


@login_required
@staff_member_required
def cleanup_old_backups(request):
    """تنظيف النسخ الاحتياطية القديمة"""
    try:
        retention_days = get_backup_settings().get('retention_days', 30)
        cutoff_date = timezone.now() - timedelta(days=retention_days)

        old_backups = SystemBackup.objects.filter(created_at__lt=cutoff_date)
        deleted_count = 0

        for backup in old_backups:
            try:
                # حذف الملف
                if os.path.exists(backup.file_path):
                    os.remove(backup.file_path)

                # حذف السجل
                backup.delete()
                deleted_count += 1

            except Exception as e:
                print(f"خطأ في حذف النسخة {backup.backup_name}: {str(e)}")

        if deleted_count > 0:
            messages.success(request, f'تم حذف {deleted_count} نسخة احتياطية قديمة')
        else:
            messages.info(request, 'لا توجد نسخ احتياطية قديمة للحذف')

    except Exception as e:
        messages.error(request, f'خطأ في تنظيف النسخ الاحتياطية: {str(e)}')

    return redirect('services:backup')


@require_POST
@csrf_exempt
def ajax_validate_path(request):
    """التحقق من صحة المسار عبر AJAX"""
    try:
        path = request.POST.get('path', '').strip()

        if not path:
            return JsonResponse({'success': False, 'message': 'يرجى تحديد مسار'})

        if not os.path.exists(path):
            return JsonResponse({'success': False, 'message': 'المسار غير موجود'})

        if not os.path.isdir(path):
            return JsonResponse({'success': False, 'message': 'المسار المحدد ليس مجلد'})

        if not os.access(path, os.W_OK):
            return JsonResponse({'success': False, 'message': 'لا يمكن الكتابة في هذا المسار'})

        # حساب المساحة المتاحة
        try:
            total, used, free = shutil.disk_usage(path)
            free_gb = round(free / (1024**3), 2)

            return JsonResponse({
                'success': True,
                'message': 'المسار صالح للاستخدام',
                'free_space_gb': free_gb
            })
        except:
            return JsonResponse({
                'success': True,
                'message': 'المسار صالح للاستخدام',
                'free_space_gb': 0
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ: {str(e)}'})


@require_POST
@csrf_exempt
def ajax_auto_save_setting(request):
    """حفظ إعداد تلقائياً عبر AJAX"""
    try:
        setting_key = request.POST.get('setting_key')
        setting_value = request.POST.get('setting_value')

        if not setting_key:
            return JsonResponse({'success': False, 'message': 'مفتاح الإعداد مطلوب'})

        try:
            setting = SystemSettings.objects.get(key=setting_key)

            if not setting.is_editable:
                return JsonResponse({'success': False, 'message': 'هذا الإعداد غير قابل للتعديل'})

            # التحقق من صحة البيانات حسب النوع
            if setting.value_type == 'BOOLEAN':
                setting_value = 'true' if setting_value in ['true', '1', 'on'] else 'false'
            elif setting.value_type == 'INTEGER':
                try:
                    int(setting_value)
                except ValueError:
                    return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم صحيح'})
            elif setting.value_type == 'FLOAT':
                try:
                    float(setting_value)
                except ValueError:
                    return JsonResponse({'success': False, 'message': 'يجب أن تكون القيمة رقم عشري'})
            elif setting.value_type == 'JSON':
                try:
                    json.loads(setting_value)
                except json.JSONDecodeError:
                    return JsonResponse({'success': False, 'message': 'صيغة JSON غير صحيحة'})

            # حفظ القيمة الجديدة
            old_value = setting.value
            setting.value = setting_value
            setting.updated_by = request.user
            setting.save()

            return JsonResponse({
                'success': True,
                'message': f'تم حفظ إعداد "{setting.description}" تلقائياً',
                'old_value': old_value,
                'new_value': setting_value,
                'setting_key': setting_key
            })

        except SystemSettings.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'الإعداد غير موجود'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ: {str(e)}'})


@require_POST
@csrf_exempt
def ajax_bulk_save_settings(request):
    """حفظ عدة إعدادات دفعة واحدة عبر AJAX"""
    try:
        settings_data = request.POST.get('settings_data')

        if not settings_data:
            return JsonResponse({'success': False, 'message': 'بيانات الإعدادات مطلوبة'})

        try:
            settings_dict = json.loads(settings_data)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'صيغة البيانات غير صحيحة'})

        saved_count = 0
        errors = []

        for setting_key, setting_value in settings_dict.items():
            try:
                setting = SystemSettings.objects.get(key=setting_key)

                if setting.is_editable:
                    # التحقق من صحة البيانات
                    if setting.value_type == 'BOOLEAN':
                        setting_value = 'true' if setting_value in ['true', '1', 'on'] else 'false'
                    elif setting.value_type == 'INTEGER':
                        try:
                            int(setting_value)
                        except ValueError:
                            errors.append(f'{setting.description}: يجب أن تكون القيمة رقم صحيح')
                            continue
                    elif setting.value_type == 'FLOAT':
                        try:
                            float(setting_value)
                        except ValueError:
                            errors.append(f'{setting.description}: يجب أن تكون القيمة رقم عشري')
                            continue

                    setting.value = setting_value
                    setting.updated_by = request.user
                    setting.save()
                    saved_count += 1
                else:
                    errors.append(f'{setting.description}: غير قابل للتعديل')

            except SystemSettings.DoesNotExist:
                errors.append(f'{setting_key}: الإعداد غير موجود')

        if saved_count > 0:
            message = f'تم حفظ {saved_count} إعداد بنجاح'
            if errors:
                message += f' مع {len(errors)} خطأ'

            return JsonResponse({
                'success': True,
                'message': message,
                'saved_count': saved_count,
                'errors': errors
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'لم يتم حفظ أي إعداد',
                'errors': errors
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'خطأ: {str(e)}'})


@require_POST
@csrf_exempt
def ajax_recalculate_costs(request):
    """إعادة حساب التكلفة عبر AJAX"""
    return JsonResponse({'success': True, 'message': 'تم إعادة حساب التكلفة'})


@require_POST
@csrf_exempt
def ajax_update_barcode_count(request):
    """تحديث عداد الباركود عبر AJAX"""
    try:
        import json
        data = json.loads(request.body)
        count = int(data.get('count', 0))

        # حفظ العدد في إعدادات النظام
        setting, created = SystemSettings.objects.get_or_create(
            key='total_barcodes_generated',
            defaults={
                'value': str(count),
                'category': 'الباركود',
                'description': 'إجمالي الباركود المُنشأ',
                'value_type': 'INTEGER',
                'is_editable': False,
                'updated_by': request.user if request.user.is_authenticated else None
            }
        )

        if not created:
            setting.value = str(count)
            if request.user.is_authenticated:
                setting.updated_by = request.user
            setting.save()

        return JsonResponse({
            'success': True,
            'message': f'تم تحديث عداد الباركود إلى {count}',
            'count': count
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في تحديث العداد: {str(e)}'
        })


@login_required
def barcode_manager(request):
    """إدارة الباركود والـ QR Code"""

    # إحصائيات الباركود
    barcode_stats = {
        'total_generated': 0,  # يمكن إضافة نموذج لتتبع الباركود المُنشأ
        'most_used_type': 'CODE128',
        'recent_barcodes': [],
    }

    # أنواع الباركود المدعومة
    supported_types = [
        {
            'code': 'CODE128',
            'name': 'Code 128',
            'description': 'الأكثر شيوعاً للفواتير والمستندات',
            'icon': 'fas fa-barcode',
            'color': 'primary',
            'max_length': 80,
            'charset': 'ASCII'
        },
        {
            'code': 'EAN13',
            'name': 'EAN-13',
            'description': 'للمنتجات التجارية (13 رقم)',
            'icon': 'fas fa-barcode',
            'color': 'success',
            'max_length': 13,
            'charset': 'أرقام فقط'
        },
        {
            'code': 'QR',
            'name': 'QR Code',
            'description': 'للبيانات المتقدمة والروابط',
            'icon': 'fas fa-qrcode',
            'color': 'warning',
            'max_length': 2953,
            'charset': 'جميع الأحرف'
        },
        {
            'code': 'CODE39',
            'name': 'Code 39',
            'description': 'للنصوص والأرقام البسيطة',
            'icon': 'fas fa-barcode',
            'color': 'danger',
            'max_length': 43,
            'charset': 'A-Z, 0-9, رموز خاصة'
        }
    ]

    # قوالب الفواتير
    invoice_templates = [
        {
            'type': 'sales',
            'name': 'فاتورة مبيعات مع باركود',
            'description': 'قالب فاتورة مبيعات يتضمن باركود تلقائي',
            'barcode_type': 'CODE128',
            'prefix': 'INV',
            'icon': 'fas fa-file-alt',
            'color': 'primary'
        },
        {
            'type': 'purchase',
            'name': 'فاتورة مشتريات مع QR',
            'description': 'قالب فاتورة مشتريات مع رمز QR متقدم',
            'barcode_type': 'QR',
            'prefix': 'PUR',
            'icon': 'fas fa-shopping-cart',
            'color': 'success'
        },
        {
            'type': 'receipt',
            'name': 'إيصال مع باركود مخصص',
            'description': 'قالب إيصال مع باركود قابل للتخصيص',
            'barcode_type': 'CODE128',
            'prefix': 'REC',
            'icon': 'fas fa-receipt',
            'color': 'warning'
        }
    ]

    # إعدادات افتراضية للباركود
    default_settings = {
        'width': 200,
        'height': 60,
        'font_size': 12,
        'show_text': True,
        'include_border': False,
        'include_date': True,
        'include_amount': False,
        'include_customer': False
    }

    context = {
        'title': 'إدارة الباركود والـ QR Code',
        'barcode_stats': barcode_stats,
        'supported_types': supported_types,
        'invoice_templates': invoice_templates,
        'default_settings': default_settings,
    }

    return render(request, 'services/barcode_manager.html', context)


@login_required
@staff_member_required
def upload_restore_backup(request):
    """رفع واسترجاع نسخة احتياطية محفوظة من الجهاز (SQLite أو ZIP)"""
    from django.conf import settings
    import zipfile
    if request.method == 'POST' and request.FILES.get('backup_file'):
        backup_file = request.FILES['backup_file']
        file_name = backup_file.name
        ext = os.path.splitext(file_name)[1].lower()
        temp_path = os.path.join(settings.BASE_DIR, f'temp_uploaded_backup{ext}')
        # حفظ الملف مؤقتاً
        with open(temp_path, 'wb+') as dest:
            for chunk in backup_file.chunks():
                dest.write(chunk)
        try:
            db_path = settings.DATABASES['default']['NAME']
            # إنشاء نسخة احتياطية قبل الاستبدال
            create_database_backup(
                backup_name=f'before_upload_restore_{timezone.now().strftime("%Y%m%d_%H%M%S")}',
                backup_type='FULL',
                description='نسخة تلقائية قبل رفع واسترجاع نسخة يدوية',
                include_media=False,
                include_logs=False,
                compress_backup=False,
                created_by=request.user
            )
            # استرجاع النسخة حسب النوع
            if ext in ['.db', '.sqlite3']:
                shutil.copy2(temp_path, db_path)
            elif ext == '.zip':
                with zipfile.ZipFile(temp_path, 'r') as zipf:
                    if 'database.db' in zipf.namelist():
                        zipf.extract('database.db', settings.BASE_DIR)
                        extracted_db = os.path.join(settings.BASE_DIR, 'database.db')
                        shutil.move(extracted_db, db_path)
                    else:
                        messages.error(request, 'ملف ZIP لا يحتوي على قاعدة بيانات صالحة (database.db)')
                        os.remove(temp_path)
                        return redirect('services:backup')
            else:
                messages.error(request, 'صيغة الملف غير مدعومة. الرجاء رفع ملف SQLite أو ZIP فقط.')
                os.remove(temp_path)
                return redirect('services:backup')
            os.remove(temp_path)
            messages.success(request, 'تم استرجاع النسخة الاحتياطية المرفوعة بنجاح! يجب تسجيل الدخول مجدداً.')
            logout(request)
            return redirect('accounts:login')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء الاسترجاع: {str(e)}')
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return redirect('services:backup')
    else:
        messages.error(request, 'يرجى اختيار ملف نسخة احتياطية صالح')
        return redirect('services:backup')
