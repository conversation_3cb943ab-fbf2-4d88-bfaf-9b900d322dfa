# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# Irriep <PERSON>la <PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-03-12 14:19+0000\n"
"Last-Translator: Irriep <PERSON>la <PERSON> <<EMAIL>>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

msgid "Advanced options"
msgstr "Dibarzhioù araokaet"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Da skouer: '/diwar-benn/darempred/'. Bezit sur da gaout beskellioù \"/\" e "
"penn-kentañ hag e fin ar chadenn."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"An dalvoudegezh-mañ a c'hall enderc'hel lizherennoù hepken, sifroù, pikoù, "
"barrennigoù islinennañ, beskellioù pe tildeoù c'hoazh."

msgid "Example: '/about/contact'. Make sure to have a leading slash."
msgstr ""

msgid "URL is missing a leading slash."
msgstr "An URL a vank enni ur veskell \"/\" en he fenn-kentañ."

msgid "URL is missing a trailing slash."
msgstr "An URL a vank enni ur veskell \"/\" en he dilost."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""
"Ar bajenn difiñv d'an URL %(url)s a zo anezhi e-barzh al lec'hienn %(site)s "
"endeo"

msgid "title"
msgstr "titl"

msgid "content"
msgstr "danvez"

msgid "enable comments"
msgstr "aotren an evezhiadennoù"

msgid "template name"
msgstr "anv patrom"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Da skouer : 'flatpages/contact_page.html'. Ma neket pourvezet, ar sistem a "
"raio gant 'flatpages/default.html'."

msgid "registration required"
msgstr "enskrivadur rekiset"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Ma vez kochet, ar bajenn a c'hallo bezañ gwelet gant an implijerien kevreet "
"hepken."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "pajenn difiñv"

msgid "flat pages"
msgstr "pajennoù difiñv"
