@echo off
title Django Server - Osaric Accounts
color 0A

echo ========================================
echo    Django Server - Osaric Accounts
echo ========================================
echo.

echo Activating virtual environment...
call "venv_new\Scripts\activate.bat"

echo.
echo Starting server on http://127.0.0.1:8000/
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver 127.0.0.1:8000

echo.
echo Server stopped. Press any key to exit...
pause > nul 