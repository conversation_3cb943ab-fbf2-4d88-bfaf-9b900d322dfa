{% extends "base/base.html" %}
{% load static %}
{% load system_tags %}

{% company_info "name" as company_name %}
{% company_info "logo" as company_logo %}

{% block title %}{{ company_name|default:"أوساريك للحسابات" }} - لوحة التحكم الجديدة المحدثة{% endblock %}

{% block extra_css %}
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

* {
    font-family: 'Cairo', sans-serif;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.modern-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.25rem 0;
    color: white;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.modern-header.scrolled {
    padding: 1rem 0;
    backdrop-filter: blur(10px);
    background: rgba(102, 126, 234, 0.95);
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.company-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.company-logo:hover {
    transform: scale(1.1) rotate(5deg);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

.time-widget {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
}

.time-display {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.date-display {
    font-size: 1rem;
    opacity: 0.8;
}

.stat-card {
    background: white;
    border-radius: 25px;
    padding: 2rem;
    transition: all 0.4s ease;
    border: none;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.stat-number {
    font-size: 2.8rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stat-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    display: inline-block;
}

.stat-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Quick Action Buttons */
.quick-action-btn {
    transition: all 0.3s ease;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    text-decoration: none;
}

/* Activity Timeline */
.activity-timeline {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h6 {
    font-size: 0.9rem;
    font-weight: 600;
}

.activity-content p {
    font-size: 0.8rem;
}

/* Performance Metrics */
.performance-metric {
    text-align: center;
    padding: 1rem;
    border-radius: 10px;
    background: rgba(0,0,0,0.02);
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

/* Progress Bars */
.progress-modern {
    height: 8px;
    border-radius: 10px;
    background: rgba(0,0,0,0.1);
    overflow: hidden;
}

.progress-modern .progress-bar {
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

/* System Status */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.system-status .status-item {
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(0,0,0,0.02);
    transition: all 0.3s ease;
}

.system-status .status-item:hover {
    background: rgba(0,0,0,0.05);
    transform: translateX(5px);
}

/* Chart Container */
.chart-container {
    position: relative;
    background: rgba(0,0,0,0.02);
    border-radius: 10px;
    padding: 1rem;
}

/* Scrollbar Styling */
.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animated Counter */
.stat-number {
    transition: all 0.3s ease;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

@media (max-width: 768px) {
    .header-title {
        font-size: 2rem;
    }
    
    .time-display {
        font-size: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .modern-header {
        padding: 1rem 0;
    }
    
    .company-logo {
        width: 60px;
        height: 60px;
    }
}

/* إصلاح مشكلة القوائم المنسدلة */
.modern-header {
    overflow: visible !important;
    position: relative !important;
    z-index: 1000 !important;
}

.dropdown-custom {
    position: static !important;
}

.dropdown-custom .dropdown-menu {
    position: fixed !important;
    z-index: 1070 !important;
    transform: none !important;
    will-change: auto !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
    border-radius: 12px !important;
    padding: 0.5rem 0 !important;
    margin-top: 0.5rem !important;
    background: white !important;
    min-width: 200px !important;
}

.dropdown-custom .notification-dropdown {
    min-width: 350px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.dropdown-custom .user-dropdown {
    min-width: 250px !important;
}

.dropdown-custom .dropdown-item {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
    transition: all 0.2s ease !important;
    color: #333 !important;
}

.dropdown-custom .dropdown-item:hover {
    background: #f8f9fa !important;
    color: #333 !important;
}

.dropdown-custom .dropdown-header {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 0.5rem 1rem !important;
    background: #f8f9fa !important;
}

.dropdown-custom .text-danger {
    color: #dc3545 !important;
}

.dropdown-custom .text-danger:hover {
    color: #dc3545 !important;
    background: #f8f9fa !important;
}

/* شريط البحث الجديد */
.search-widget-main {
    position: relative;
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
}

.search-group-main {
    border-radius: 30px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    background: white;
}

.search-input-main {
    background: white;
    border: none;
    padding: 1rem 1.5rem;
    color: #333;
    font-size: 1rem;
    border-radius: 30px 0 0 30px;
}

.search-input-main:focus {
    background: white;
    box-shadow: none;
    border: none;
    outline: none;
}

.search-input-main::placeholder {
    color: #999;
    font-style: italic;
}

.btn-search-main {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    transition: all 0.3s ease;
    font-weight: 600;
    border-radius: 0 30px 30px 0;
}

.btn-search-main:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
    transform: translateY(-1px);
}

/* تحسين التخطيط */
.modern-header .mb-3 {
    margin-bottom: 1rem !important;
}

/* تحسينات متجاوبة للتخطيط الجديد */
@media (max-width: 992px) {
    .search-widget-main {
        max-width: 100%;
        margin-top: 1rem;
    }

    .search-input-main {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }

    .btn-search-main {
        padding: 0.8rem 1.5rem;
    }
}

@media (max-width: 768px) {
    .modern-header .mb-3 {
        margin-bottom: 0.5rem !important;
    }

    .search-input-main::placeholder {
        font-size: 0.85rem;
    }
}
</style>
{% endblock %}

{% block content %}

<!-- Modern Professional Header -->
<div class="modern-header">
    <div class="container-fluid px-4">
        <div class="row align-items-center">
            <!-- معلومات الشركة -->
            <div class="col-lg-4 col-md-6">
                <div class="d-flex align-items-center header-content">
                    {% if company_logo %}
                    <img src="{{ company_logo.url }}" alt="شعار الشركة" class="company-logo me-3">
                    {% else %}
                    <div class="company-logo me-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-building fa-lg"></i>
                    </div>
                    {% endif %}
                    <div class="company-info">
                        <h1 class="header-title mb-0">{{ company_name|default:"أوساريك للحسابات" }}</h1>
                        <p class="header-subtitle mb-0">نظام إدارة الحسابات المتكامل</p>
                    </div>
                </div>
            </div>

            <!-- شريط البحث -->
            <div class="col-lg-4 col-md-6 d-none d-md-block">
                <div class="search-widget mx-auto">
                    <div class="input-group search-group">
                        <input type="text" class="form-control search-input" id="globalSearch" placeholder="البحث في النظام..." autocomplete="off">
                        <button class="btn btn-search" type="button" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="search-results" id="searchResults"></div>
                </div>
            </div>

            <!-- الأدوات والمستخدم -->
            <div class="col-lg-4 col-md-12">
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <!-- الوقت والتاريخ -->
                    <div class="time-widget d-none d-lg-block">
                        <div class="time-display" id="currentTime">00:00:00</div>
                        <div class="date-display" id="currentDate">اليوم</div>
                    </div>

                    <!-- الأزرار -->
                    <div class="header-actions d-flex gap-1">
                        <!-- الإشعارات -->
                        <div class="dropdown">
                            <button class="btn btn-header-action position-relative" data-bs-toggle="dropdown" aria-expanded="false" title="الإشعارات">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="notificationCount">3</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                                <li><h6 class="dropdown-header">الإشعارات الجديدة</h6></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle text-info me-2"></i>فاتورة جديدة تحتاج موافقة</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-exclamation-triangle text-warning me-2"></i>مخزون منخفض للصنف ABC123</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-check-circle text-success me-2"></i>تم تحديث أسعار العملات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#" onclick="markAllAsRead()">تحديد الكل كمقروء</a></li>
                            </ul>
                        </div>

                        <!-- الإعدادات -->
                        <div class="dropdown">
                            <button class="btn btn-header-action" data-bs-toggle="dropdown" aria-expanded="false" title="الإعدادات">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'definitions:company_settings' %}"><i class="fas fa-building me-2"></i>إعدادات الشركة</a></li>
                                <li><a class="dropdown-item" href="#" onclick="toggleTheme()"><i class="fas fa-palette me-2"></i>تغيير المظهر</a></li>
                                <li><a class="dropdown-item" href="#" onclick="showBackupDialog()"><i class="fas fa-download me-2"></i>نسخة احتياطية</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="showSystemInfo()"><i class="fas fa-info-circle me-2"></i>معلومات النظام</a></li>
                            </ul>
                        </div>

                        <!-- تحديث -->
                        <button class="btn btn-header-action" onclick="refreshDashboard()" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>

                    <!-- المستخدم -->
                    <div class="dropdown user-dropdown-container">
                        <button class="btn btn-user" data-bs-toggle="dropdown" aria-expanded="false" title="المستخدم">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-info d-none d-xl-block">
                                    <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                                    <div class="user-role">مدير النظام</div>
                                </div>
                                <i class="fas fa-chevron-down ms-2 d-none d-lg-block"></i>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li><h6 class="dropdown-header">{{ user.get_full_name|default:user.username }}</h6></li>
                            <li><small class="dropdown-text text-muted px-3">{{ user.email|default:"<EMAIL>" }}</small></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changePassword()"><i class="fas fa-key me-2"></i>تغيير كلمة المرور</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showUserSettings()"><i class="fas fa-cog me-2"></i>إعدادات المستخدم</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmLogout()"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="container-fluid mb-4">
    <div class="row g-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="stat-icon" style="background: var(--success-gradient);">
                    <i class="fas fa-chart-line text-white"></i>
                </div>
                <div class="stat-number" data-target="{{ sales_today|default:'125000' }}">0</div>
                <div class="stat-label">مبيعات اليوم</div>
                <span class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+12%
                </span>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="stat-icon" style="background: var(--warning-gradient);">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <div class="stat-number" data-target="{{ sales_this_month|default:'850000' }}">0</div>
                <div class="stat-label">مبيعات الشهر</div>
                <span class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+8%
                </span>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="stat-icon" style="background: var(--info-gradient);">
                    <i class="fas fa-cash-register text-white"></i>
                </div>
                <div class="stat-number" data-target="{{ treasury_balance|default:'2500000' }}">0</div>
                <div class="stat-label">رصيد الخزينة</div>
                <span class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+5%
                </span>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="stat-icon" style="background: var(--primary-gradient);">
                    <i class="fas fa-university text-white"></i>
                </div>
                <div class="stat-number" data-target="{{ bank_balance|default:'1250000' }}">0</div>
                <div class="stat-label">رصيد البنوك</div>
                <span class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+3%
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-bolt text-primary me-2"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('فاتورة مبيعات')" class="quick-action-btn btn btn-outline-primary w-100 p-3 text-center">
                                <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">فاتورة مبيعات</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('فاتورة مشتريات')" class="quick-action-btn btn btn-outline-success w-100 p-3 text-center">
                                <i class="fas fa-shopping-cart fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">فاتورة مشتريات</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('سند قبض')" class="quick-action-btn btn btn-outline-info w-100 p-3 text-center">
                                <i class="fas fa-receipt fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">سند قبض</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('سند صرف')" class="quick-action-btn btn btn-outline-warning w-100 p-3 text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">سند صرف</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('زيادة مخزون')" class="quick-action-btn btn btn-outline-secondary w-100 p-3 text-center">
                                <i class="fas fa-arrow-up fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">زيادة مخزون</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="#" onclick="showComingSoon('عميل جديد')" class="quick-action-btn btn btn-outline-dark w-100 p-3 text-center">
                                <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                                <span class="small fw-bold">عميل جديد</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Section -->
<div class="container-fluid mb-4">
    <div class="row g-4">
        <!-- Sales Chart -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-chart-area text-primary me-2"></i>
                            تحليل المبيعات
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary active" data-period="week">أسبوع</button>
                            <button class="btn btn-outline-primary" data-period="month">شهر</button>
                            <button class="btn btn-outline-primary" data-period="year">سنة</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-clock text-warning me-2"></i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-icon bg-success">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">فاتورة مبيعات جديدة</h6>
                                <p class="mb-1 text-muted small">فاتورة رقم #2024001</p>
                                <span class="badge bg-success">15,000 ج.م</span>
                                <small class="text-muted d-block">منذ 5 دقائق</small>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon bg-info">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">سند قبض</h6>
                                <p class="mb-1 text-muted small">من العميل: أحمد محمد</p>
                                <span class="badge bg-info">8,500 ج.م</span>
                                <small class="text-muted d-block">منذ 15 دقيقة</small>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon bg-warning">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">زيادة مخزون</h6>
                                <p class="mb-1 text-muted small">صنف: لابتوب ديل</p>
                                <span class="badge bg-warning">+50 قطعة</span>
                                <small class="text-muted d-block">منذ 30 دقيقة</small>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">عميل جديد</h6>
                                <p class="mb-1 text-muted small">شركة النور للتجارة</p>
                                <span class="badge bg-primary">عميل جديد</span>
                                <small class="text-muted d-block">منذ ساعة</small>
                            </div>
                        </div>
                    </div>
                    <div class="text-center p-3 border-top">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            عرض جميع الأنشطة
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="container-fluid mb-4">
    <div class="row g-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-tachometer-alt text-info me-2"></i>
                        مؤشرات الأداء
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="performance-metric">
                                <div class="metric-label">معدل النمو</div>
                                <div class="metric-value text-success">+15.2%</div>
                                <div class="progress progress-modern mt-2">
                                    <div class="progress-bar bg-success" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="performance-metric">
                                <div class="metric-label">رضا العملاء</div>
                                <div class="metric-value text-primary">92%</div>
                                <div class="progress progress-modern mt-2">
                                    <div class="progress-bar bg-primary" style="width: 92%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="performance-metric">
                                <div class="metric-label">كفاءة المخزون</div>
                                <div class="metric-value text-warning">88%</div>
                                <div class="progress progress-modern mt-2">
                                    <div class="progress-bar bg-warning" style="width: 88%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="performance-metric">
                                <div class="metric-label">سرعة التحصيل</div>
                                <div class="metric-value text-info">85%</div>
                                <div class="progress progress-modern mt-2">
                                    <div class="progress-bar bg-info" style="width: 85%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-cloud text-primary me-2"></i>
                        حالة النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="status-indicator bg-success me-3"></div>
                                <span>خادم قاعدة البيانات</span>
                            </div>
                            <span class="badge bg-success">متصل</span>
                        </div>

                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="status-indicator bg-success me-3"></div>
                                <span>النسخ الاحتياطي</span>
                            </div>
                            <span class="badge bg-success">محدث</span>
                        </div>

                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="status-indicator bg-warning me-3"></div>
                                <span>مساحة التخزين</span>
                            </div>
                            <span class="badge bg-warning">75%</span>
                        </div>

                        <div class="status-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="status-indicator bg-success me-3"></div>
                                <span>الأمان</span>
                            </div>
                            <span class="badge bg-success">آمن</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الوقت والتاريخ
function updateDateTime() {
    const now = new Date();
    const timeOptions = { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit',
        hour12: false 
    };
    const dateOptions = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-EG', timeOptions);
    document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-EG', dateOptions);
}

// تحديث كل ثانية
setInterval(updateDateTime, 1000);
updateDateTime();

// تحديث لوحة التحكم
function refreshDashboard() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="loading-spinner me-2"></span>جاري التحديث...';
    btn.disabled = true;
    
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// تفعيل التلميحات
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// تثبيت الشريط العلوي عند التمرير
window.addEventListener('scroll', function() {
    const header = document.querySelector('.modern-header');
    if (window.scrollY > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
});

// تحريك الأرقام (Counter Animation)
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number[data-target]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString('ar-EG');
        }, 20);
    });
}

// تحريك أشرطة التقدم
function animateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');

    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';

        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
}

// تبديل فترات الرسم البياني
document.querySelectorAll('[data-period]').forEach(btn => {
    btn.addEventListener('click', function() {
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));

        // إضافة الفئة النشطة للزر المحدد
        this.classList.add('active');

        // تحديث الرسم البياني (يمكن إضافة منطق هنا)
        const period = this.getAttribute('data-period');
        console.log('تم تغيير فترة الرسم البياني إلى:', period);
    });
});

// تحديث الوقت في الأنشطة
function updateActivityTimes() {
    const timeElements = document.querySelectorAll('.activity-item small');
    // يمكن إضافة منطق لتحديث الأوقات هنا
}

// تأثير الظهور التدريجي للعناصر
function observeElements() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate__fadeInUp');
            }
        });
    });

    document.querySelectorAll('.card, .stat-card').forEach(el => {
        observer.observe(el);
    });
}

// تشغيل الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحريك الأرقام
    setTimeout(animateCounters, 500);

    // تحريك أشرطة التقدم
    setTimeout(animateProgressBars, 1000);

    // مراقبة العناصر للتأثيرات
    observeElements();

    // تفعيل التلميحات
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// إضافة تأثير النقر للبطاقات
document.querySelectorAll('.stat-card').forEach(card => {
    card.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
    });
});

// دالة لعرض رسالة "قريباً"
function showComingSoon(feature) {
    alert(`🚀 ${feature} - هذه الميزة ستكون متاحة قريباً!\n\nنحن نعمل على تطوير هذه الميزة لتوفير أفضل تجربة لك.`);
}

// تحديث البيانات كل 30 ثانية
setInterval(() => {
    // يمكن إضافة منطق لتحديث البيانات هنا
    console.log('تحديث البيانات...');
}, 30000);

// رسالة تأكيد في console
console.log('✅ لوحة التحكم الاحترافية تم تحميلها بنجاح');
console.log('🎯 المميزات: شريط ثابت، تحريك الأرقام، تأثيرات بصرية');
console.log('📅 تاريخ التحديث:', new Date().toLocaleString('ar-EG'));
</script>
{% endblock %}
