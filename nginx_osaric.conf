# إعدادات Nginx لخادم أوساريك المحاسبي
# Nginx Configuration for Osaric Accounting Server

upstream osaric_backend {
    server 127.0.0.1:8000;
    # يمكن إضافة خوادم متعددة للتوازن
    # server 127.0.0.1:8001;
    # server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name localhost osaric-accounting.local accounting.local;
    
    # إعادة توجيه HTTP إلى HTTPS (اختياري)
    # return 301 https://$server_name$request_uri;
    
    # حد أقصى لحجم الملف المرفوع
    client_max_body_size 50M;
    
    # الملفات الثابتة
    location /static/ {
        alias /path/to/your/project/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        gzip_static on;
    }
    
    # ملفات الوسائط
    location /media/ {
        alias /path/to/your/project/media/;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # الطلبات الرئيسية
    location / {
        proxy_pass http://osaric_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # إعدادات المهلة الزمنية
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # إعدادات WebSocket (إذا كانت مطلوبة)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # تسجيل الأخطاء
    error_log /var/log/nginx/osaric_error.log;
    access_log /var/log/nginx/osaric_access.log;
}

# إعدادات HTTPS (اختياري)
# server {
#     listen 443 ssl http2;
#     server_name localhost osaric-accounting.local;
#     
#     ssl_certificate /path/to/ssl/certificate.crt;
#     ssl_certificate_key /path/to/ssl/private.key;
#     
#     # إعدادات SSL المحسنة
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # باقي الإعدادات مثل HTTP
# }
