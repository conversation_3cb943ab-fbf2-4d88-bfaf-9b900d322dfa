#!/usr/bin/env python3
"""
خادم بسيط يعمل بدون مشاكل
Simple Working Server

خادم Django بسيط مع HTTP عادي يعمل بدون أي مشاكل
Simple Django server with regular HTTP that works without any issues
"""

import os
import sys
import socket
import subprocess
import threading
import time
from pathlib import Path
from datetime import datetime

class SimpleWorkingServer:
    """خادم بسيط يعمل"""
    
    def __init__(self):
        self.port = 8000
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.server_process = None
        self.is_running = False
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def clean_django_cache(self):
        """تنظيف cache Django"""
        try:
            # حذف ملفات pyc
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.pyc'):
                        try:
                            os.remove(os.path.join(root, file))
                        except:
                            pass
                
                # حذف مجلدات __pycache__
                if '__pycache__' in dirs:
                    import shutil
                    try:
                        shutil.rmtree(os.path.join(root, '__pycache__'))
                    except:
                        pass
            
            self.log_success("تم تنظيف Django cache")
            return True
        except Exception as e:
            self.log_error(f"خطأ في تنظيف cache: {e}")
            return False
    
    def start_simple_server(self):
        """بدء خادم Django بسيط"""
        try:
            # تنظيف cache أولاً
            self.clean_django_cache()
            
            # البحث عن منفذ متاح
            port = self.find_available_port(8000)
            if not port:
                self.log_error("لا يمكن العثور على منفذ متاح")
                return False
            
            self.port = port
            self.log_info(f"بدء خادم Django البسيط على المنفذ {port}")
            
            # تشغيل Django بأبسط طريقة
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{port}',
                '--insecure'
            ]
            
            # إعداد متغيرات البيئة
            env = os.environ.copy()
            env['DJANGO_SETTINGS_MODULE'] = 'osaric_accounts.settings'
            env['PYTHONPATH'] = os.getcwd()
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                env=env
            )
            
            # انتظار بدء الخادم
            time.sleep(5)
            
            if self.server_process.poll() is None:
                self.log_success(f"خادم Django البسيط يعمل على المنفذ {port}")
                self.display_access_info()
                return True
            else:
                self.log_error("فشل في بدء خادم Django")
                # قراءة رسالة الخطأ
                if self.server_process.stderr:
                    error = self.server_process.stderr.read()
                    self.log_error(f"خطأ: {error[:300]}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في بدء الخادم: {e}")
            return False
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            import urllib.request
            response = urllib.request.urlopen(f"http://127.0.0.1:{self.port}/", timeout=3)
            return response.getcode() == 200
        except Exception:
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🌐 خادم Django البسيط")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 منفذ HTTP: {self.port}")
        print("\n🌐 للوصول من المتصفح:")
        print(f"   http://{self.local_ip}:{self.port}/dashboard/")
        print(f"   http://localhost:{self.port}/dashboard/")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   http://{self.local_ip}:{self.port}/dashboard/")
        print("\n✅ مميزات الخادم البسيط:")
        print("   • يعمل بدون مشاكل")
        print("   • لا أخطاء بروكسي")
        print("   • أداء سريع ومستقر")
        print("   • سهل الوصول")
        print("\n💡 ملاحظة:")
        print("   هذا خادم HTTP عادي (ليس HTTPS)")
        print("   لكنه يعمل بدون أي مشاكل أو أخطاء")
        print("=" * 60)
    
    def monitor_server(self):
        """مراقبة الخادم"""
        consecutive_failures = 0
        max_failures = 3
        
        while self.is_running:
            try:
                time.sleep(10)
                
                # فحص العملية
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("الخادم توقف - إعادة تشغيل...")
                    self.start_simple_server()
                    consecutive_failures = 0
                    continue
                
                # فحص الصحة
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_success("الخادم عاد للعمل بنجاح")
                    consecutive_failures = 0
                    self.log_info("الخادم يعمل بصحة جيدة")
                else:
                    consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.log_error("فشل متكرر - إعادة تشغيل الخادم")
                        if self.server_process:
                            self.server_process.terminate()
                            time.sleep(2)
                        self.start_simple_server()
                        consecutive_failures = 0
                
            except Exception as e:
                self.log_error(f"خطأ في المراقبة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل الخادم البسيط"""
        print("=" * 60)
        print("🌐 خادم Django البسيط - يعمل بدون مشاكل")
        print("Simple Django Server - Works Without Issues")
        print("=" * 60)
        
        if not self.start_simple_server():
            self.log_error("فشل في بدء الخادم!")
            return False
        
        self.is_running = True
        
        # بدء المراقبة
        monitor_thread = threading.Thread(target=self.monitor_server)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        self.log_success("الخادم البسيط يعمل بنجاح!")
        self.log_info("اضغط Ctrl+C للإيقاف")
        
        try:
            # انتظار
            while self.is_running:
                time.sleep(1)
                if self.server_process and self.server_process.poll() is not None:
                    break
                    
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        finally:
            self.is_running = False
            if self.server_process:
                self.server_process.terminate()
            self.log_info("تم إنهاء الخادم")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = SimpleWorkingServer()
    success = server.run()
    
    if success:
        print("✅ الخادم البسيط يعمل بنجاح!")
    else:
        print("❌ فشل في تشغيل الخادم!")

if __name__ == "__main__":
    main()
