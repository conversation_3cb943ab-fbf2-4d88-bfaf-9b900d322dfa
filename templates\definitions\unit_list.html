{% extends 'definitions/currency_list.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
<form method="post" style="display:none;">
    {% csrf_token %}
</form>
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">
            <i class="fas fa-ruler me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة وحدة جديدة
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="البحث في وحدات القياس (الكود، الاسم، الرمز)">
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{% url 'definitions:unit_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Units Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة وحدات القياس
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>كود الوحدة</th>
                            <th>اسم الوحدة</th>
                            <th>رمز الوحدة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for unit in page_obj %}
                        <tr>
                            <td><strong>{{ unit.code }}</strong></td>
                            <td>{{ unit.name }}</td>
                            <td><span class="badge bg-secondary">{{ unit.symbol }}</span></td>
                            <td>{{ unit.created_at|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'definitions:unit_edit' unit.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ unit.pk }}"
                                            data-name="{{ unit.name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وحدات قياس</h5>
                <p class="text-muted">لم يتم العثور على أي وحدات قياس مطابقة لمعايير البحث</p>
                <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة وحدة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف وحدة القياس: <strong id="delete-item-name"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Toast Success Message -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
  <div id="deleteSuccessToast" class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="d-flex">
      <div class="toast-body">
        تم حذف وحدة القياس بنجاح
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete');
    const deleteItemName = document.getElementById('delete-item-name');
    let deleteId = null;
    // Toast
    const deleteSuccessToast = new bootstrap.Toast(document.getElementById('deleteSuccessToast'));

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteId = this.dataset.id;
            deleteItemName.textContent = this.dataset.name;
            deleteModal.show();
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        if (deleteId) {
            fetch(`/definitions/units/${deleteId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    deleteSuccessToast.show();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    alert('حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
            });
        }
        deleteModal.hide();
    });
});
</script>
{% endblock %}
