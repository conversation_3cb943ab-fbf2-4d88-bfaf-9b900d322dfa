/**
 * Advanced Search and Filter System
 * نظام البحث والتصفية المتقدم
 */

class AdvancedSearchFilter {
    constructor() {
        this.searchData = [];
        this.filters = {};
        this.init();
    }

    init() {
        this.addSearchInterface();
        this.setupEventListeners();
        this.loadSearchData();
    }

    addSearchInterface() {
        // إضافة واجهة البحث المتقدم
        const searchInterface = `
            <div class="advanced-search-panel" id="advancedSearchPanel" style="display: none;">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            البحث المتقدم في المواد الخام
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">البحث بالاسم</label>
                                <input type="text" class="form-control" id="searchByName" 
                                       placeholder="ابحث عن المادة...">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">البحث بالكود</label>
                                <input type="text" class="form-control" id="searchByCode" 
                                       placeholder="ابحث بالكود...">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" id="filterByCategory">
                                    <option value="">جميع الفئات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">حالة المخزون</label>
                                <select class="form-select" id="filterByStock">
                                    <option value="">جميع الحالات</option>
                                    <option value="available">متوفر</option>
                                    <option value="low">نقص</option>
                                    <option value="out">غير متوفر</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">نطاق السعر</label>
                                <select class="form-select" id="filterByPrice">
                                    <option value="">جميع الأسعار</option>
                                    <option value="0-50">0 - 50 ج.م</option>
                                    <option value="50-100">50 - 100 ج.م</option>
                                    <option value="100-500">100 - 500 ج.م</option>
                                    <option value="500+">أكثر من 500 ج.م</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الوحدة</label>
                                <select class="form-select" id="filterByUnit">
                                    <option value="">جميع الوحدات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-secondary mt-4" 
                                            onclick="advancedSearch.clearFilters()">
                                        <i class="fas fa-times me-2"></i>مسح الفلاتر
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showFavorites">
                                <label class="form-check-label" for="showFavorites">
                                    إظهار المواد المفضلة فقط
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة زر البحث المتقدم
        const searchButton = `
            <button type="button" class="btn btn-outline-info btn-sm" 
                    onclick="advancedSearch.toggleSearchPanel()" 
                    title="البحث المتقدم">
                <i class="fas fa-search-plus me-1"></i>بحث متقدم
            </button>
        `;

        // إضافة الواجهة قبل جدول المواد
        $('.materials-table').before(searchInterface);
        $('.section-title').first().append(`<div class="ms-auto">${searchButton}</div>`);
    }

    setupEventListeners() {
        // ربط أحداث البحث والتصفية
        $('#searchByName, #searchByCode').on('input', () => {
            this.applyFilters();
        });

        $('#filterByCategory, #filterByStock, #filterByPrice, #filterByUnit, #showFavorites').on('change', () => {
            this.applyFilters();
        });

        // البحث السريع في قوائم المواد
        $(document).on('focus', '.material-select', function() {
            advancedSearch.enhanceSelect($(this));
        });
    }

    loadSearchData() {
        // تحميل بيانات البحث من المواد المتاحة
        $('.material-select option').each((index, option) => {
            if ($(option).val()) {
                const data = {
                    id: $(option).val(),
                    name: $(option).text(),
                    code: $(option).data('code') || '',
                    unit: $(option).data('unit') || '',
                    cost: parseFloat($(option).data('cost')) || 0,
                    stock: parseFloat($(option).data('stock')) || 0,
                    category: $(option).data('category') || 'عام'
                };
                
                this.searchData.push(data);
            }
        });

        // تحديث قوائم الفلاتر
        this.updateFilterOptions();
    }

    updateFilterOptions() {
        // تحديث قائمة الفئات
        const categories = [...new Set(this.searchData.map(item => item.category))];
        const categorySelect = $('#filterByCategory');
        categories.forEach(category => {
            categorySelect.append(`<option value="${category}">${category}</option>`);
        });

        // تحديث قائمة الوحدات
        const units = [...new Set(this.searchData.map(item => item.unit))];
        const unitSelect = $('#filterByUnit');
        units.forEach(unit => {
            if (unit) {
                unitSelect.append(`<option value="${unit}">${unit}</option>`);
            }
        });
    }

    applyFilters() {
        const filters = {
            name: $('#searchByName').val().toLowerCase(),
            code: $('#searchByCode').val().toLowerCase(),
            category: $('#filterByCategory').val(),
            stock: $('#filterByStock').val(),
            price: $('#filterByPrice').val(),
            unit: $('#filterByUnit').val(),
            favorites: $('#showFavorites').is(':checked')
        };

        // تطبيق الفلاتر على جميع قوائم المواد
        $('.material-select').each((index, select) => {
            this.filterSelectOptions($(select), filters);
        });

        // إظهار عدد النتائج
        this.showFilterResults(filters);
    }

    filterSelectOptions($select, filters) {
        const $options = $select.find('option');
        let visibleCount = 0;

        $options.each((index, option) => {
            const $option = $(option);
            
            if (!$option.val()) {
                $option.show();
                return;
            }

            const data = this.searchData.find(item => item.id == $option.val());
            if (!data) {
                $option.hide();
                return;
            }

            let show = true;

            // فلتر الاسم
            if (filters.name && !data.name.toLowerCase().includes(filters.name)) {
                show = false;
            }

            // فلتر الكود
            if (filters.code && !data.code.toLowerCase().includes(filters.code)) {
                show = false;
            }

            // فلتر الفئة
            if (filters.category && data.category !== filters.category) {
                show = false;
            }

            // فلتر حالة المخزون
            if (filters.stock) {
                switch (filters.stock) {
                    case 'available':
                        if (data.stock <= 0) show = false;
                        break;
                    case 'low':
                        if (data.stock > 10 || data.stock <= 0) show = false;
                        break;
                    case 'out':
                        if (data.stock > 0) show = false;
                        break;
                }
            }

            // فلتر السعر
            if (filters.price) {
                const [min, max] = filters.price.split('-').map(p => 
                    p === '+' ? Infinity : parseFloat(p) || 0
                );
                if (data.cost < min || (max !== Infinity && data.cost > max)) {
                    show = false;
                }
            }

            // فلتر الوحدة
            if (filters.unit && data.unit !== filters.unit) {
                show = false;
            }

            // فلتر المفضلة (يمكن تطويره لاحقاً)
            if (filters.favorites) {
                // منطق المفضلة
            }

            if (show) {
                $option.show();
                visibleCount++;
            } else {
                $option.hide();
            }
        });

        // إضافة مؤشر عدد النتائج
        this.updateSelectPlaceholder($select, visibleCount);
    }

    updateSelectPlaceholder($select, count) {
        const $firstOption = $select.find('option:first');
        if (count === 0) {
            $firstOption.text('لا توجد نتائج مطابقة...');
        } else {
            $firstOption.text(`اختر المادة... (${count} نتيجة)`);
        }
    }

    showFilterResults(filters) {
        const activeFilters = Object.values(filters).filter(f => f && f !== false).length;
        
        if (activeFilters > 0) {
            const resultCount = this.searchData.filter(item => {
                // تطبيق نفس منطق الفلترة
                return true; // مبسط للمثال
            }).length;

            notificationSystem.info(
                `تم العثور على ${resultCount} نتيجة مطابقة للفلاتر المحددة`,
                'نتائج البحث',
                3000
            );
        }
    }

    enhanceSelect($select) {
        // تحسين قائمة الاختيار بإضافة بحث سريع
        if ($select.hasClass('enhanced')) return;

        $select.addClass('enhanced');
        
        // إضافة حقل بحث سريع
        const searchInput = $(`
            <input type="text" class="form-control form-control-sm mb-2" 
                   placeholder="بحث سريع..." 
                   style="position: absolute; top: -35px; right: 0; width: 200px; z-index: 1000;">
        `);

        $select.parent().css('position', 'relative').prepend(searchInput);

        searchInput.on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            $select.find('option').each(function() {
                const $option = $(this);
                if (!$option.val()) return;
                
                const text = $option.text().toLowerCase();
                if (text.includes(searchTerm)) {
                    $option.show();
                } else {
                    $option.hide();
                }
            });
        });

        // إخفاء حقل البحث عند فقدان التركيز
        $select.on('blur', function() {
            setTimeout(() => {
                searchInput.remove();
                $select.removeClass('enhanced');
            }, 200);
        });
    }

    toggleSearchPanel() {
        const $panel = $('#advancedSearchPanel');
        if ($panel.is(':visible')) {
            $panel.slideUp(300);
        } else {
            $panel.slideDown(300);
        }
    }

    clearFilters() {
        $('#searchByName, #searchByCode').val('');
        $('#filterByCategory, #filterByStock, #filterByPrice, #filterByUnit').val('');
        $('#showFavorites').prop('checked', false);
        
        // إظهار جميع الخيارات
        $('.material-select option').show();
        $('.material-select option:first').text('اختر المادة...');
        
        notificationSystem.success('تم مسح جميع الفلاتر', 'تم المسح', 2000);
    }

    // إضافة مادة للمفضلة
    addToFavorites(materialId) {
        let favorites = JSON.parse(localStorage.getItem('favoriteMaterials') || '[]');
        if (!favorites.includes(materialId)) {
            favorites.push(materialId);
            localStorage.setItem('favoriteMaterials', JSON.stringify(favorites));
            notificationSystem.success('تم إضافة المادة للمفضلة', 'مفضلة');
        }
    }

    // إزالة مادة من المفضلة
    removeFromFavorites(materialId) {
        let favorites = JSON.parse(localStorage.getItem('favoriteMaterials') || '[]');
        favorites = favorites.filter(id => id !== materialId);
        localStorage.setItem('favoriteMaterials', JSON.stringify(favorites));
        notificationSystem.info('تم إزالة المادة من المفضلة', 'مفضلة');
    }

    // فحص إذا كانت المادة مفضلة
    isFavorite(materialId) {
        const favorites = JSON.parse(localStorage.getItem('favoriteMaterials') || '[]');
        return favorites.includes(materialId);
    }
}

// إنشاء نسخة عامة من النظام
let advancedSearch;

$(document).ready(function() {
    advancedSearch = new AdvancedSearchFilter();
});
