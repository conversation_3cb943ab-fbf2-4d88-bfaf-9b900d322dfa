{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            {% if error %}
                <div class="alert alert-danger">
                    <h4>خطأ في تحميل أسعار العملات</h4>
                    <p>{{ error }}</p>
                </div>
            {% endif %}

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-exchange-alt text-success me-2"></i>
                    {{ title }}
                </h2>
                <div class="d-flex gap-2">
                    <form method="post" action="{% url 'definitions:update_currency_rates' %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث الأسعار
                        </button>
                    </form>
                    <a href="{% url 'definitions:currency_list' %}" class="btn btn-secondary">
                        <i class="fas fa-cog me-1"></i>
                        إدارة العملات
                    </a>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                    <i class="fas fa-coins fa-2x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_currencies }}</h4>
                            <p class="card-text text-muted">العملات المتاحة</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                    <i class="fas fa-university fa-2x text-success"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_banks }}</h4>
                            <p class="card-text text-muted">البنوك المصرية</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                    <i class="fas fa-clock fa-2x text-info"></i>
                                </div>
                            </div>
                            <h6 class="card-title">آخر تحديث</h6>
                            <p class="card-text text-muted">
                                {% if last_update %}
                                    {{ last_update.last_updated|date:"d/m/Y H:i" }}
                                {% else %}
                                    لم يتم التحديث بعد
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                                    <i class="fas fa-chart-line fa-2x text-warning"></i>
                                </div>
                            </div>
                            <h6 class="card-title">العملة الأساسية</h6>
                            <p class="card-text text-muted">الجنيه المصري (EGP)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أسعار العملات -->
            {% for currency_code, currency_data in rates_by_currency.items %}
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                {{ currency_data.currency.name }} ({{ currency_data.currency.code }})
                                <span class="badge bg-primary ms-2">{{ currency_data.currency.symbol }}</span>
                            </h5>
                            {% if currency_code in best_rates and best_rates.currency_code %}
                                <div class="text-end">
                                    <small class="text-muted d-block">أفضل سعر شراء</small>
                                    <strong class="text-success">{{ best_rates.currency_code.best_buy.rate }} ج.م</strong>
                                    <small class="text-muted d-block">{{ best_rates.currency_code.best_buy.bank }}</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>البنك</th>
                                        <th class="text-center">سعر الشراء</th>
                                        <th class="text-center">سعر البيع</th>
                                        <th class="text-center">المتوسط</th>
                                        <th class="text-center">آخر تحديث</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rate in currency_data.banks %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-university text-primary me-2"></i>
                                                {{ rate.bank_name }}
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success">{{ rate.buy_rate }} ج.م</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-danger">{{ rate.sell_rate }} ج.م</span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">{{ rate.average_rate }} ج.م</span>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">{{ rate.last_updated|date:"d/m/Y H:i" }}</small>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- أفضل الأسعار -->
                        {% if currency_code in best_rates and best_rates.currency_code %}
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            أفضل سعر شراء
                                        </h6>
                                        <p class="mb-0">
                                            <strong>{{ best_rates.currency_code.best_buy.rate }} ج.م</strong>
                                            من {{ best_rates.currency_code.best_buy.bank }}
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-arrow-down me-1"></i>
                                            أفضل سعر بيع
                                        </h6>
                                        <p class="mb-0">
                                            <strong>{{ best_rates.currency_code.best_sell.rate }} ج.م</strong>
                                            من {{ best_rates.currency_code.best_sell.bank }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% empty %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>لا توجد أسعار عملات متاحة</h5>
                        <p class="text-muted">يرجى تحديث أسعار العملات أولاً</p>
                        <form method="post" action="{% url 'definitions:update_currency_rates' %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث الأسعار الآن
                            </button>
                        </form>
                    </div>
                </div>
            {% endfor %}

            <!-- معلومات إضافية -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات مهمة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-clock me-1"></i>
                                        التحديث التلقائي
                                    </h6>
                                    <p class="text-muted">يتم تحديث أسعار العملات تلقائياً كل 15 دقيقة من البنوك المصرية الرئيسية.</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-university me-1"></i>
                                        البنوك المدعومة
                                    </h6>
                                    <p class="text-muted">البنك المركزي المصري، البنك الأهلي، البنك التجاري الدولي، بنك قطر الوطني، بنك الإسكندرية، بنك مصر.</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-coins me-1"></i>
                                        العملات المدعومة
                                    </h6>
                                    <p class="text-muted">الدولار الأمريكي، اليورو، الجنيه الإسترليني، الجنيه السعودي، الدرهم الإماراتي، والمزيد.</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">
                                        <i class="fas fa-chart-line me-1"></i>
                                        دقة الأسعار
                                    </h6>
                                    <p class="text-muted">الأسعار محدثة ومطابقة لأسعار البنوك المصرية الرسمية مع تقلبات السوق.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث تلقائي كل 5 دقائق
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
