@echo off
REM ===================================================
REM سكريبت تشغيل خادم الشبكة للوصول من أي جهاز
REM Network Server for Access from Any Device
REM ===================================================

title خادم الشبكة - نظام المحاسبة - Network Server

echo.
echo ========================================
echo    🌐 خادم الشبكة - نظام المحاسبة
echo    Network Server - Accounting System
echo ========================================
echo.

REM تحديد مجلد المشروع
cd /d "%~dp0"

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "static" mkdir static
if not exist "media" mkdir media
if not exist "backup" mkdir backup

echo ✅ تم إنشاء المجلدات المطلوبة

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المتطلبات للشبكة...
pip install django psutil >nul 2>&1

REM فحص Django
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django غير مثبت بشكل صحيح
    echo جاري تثبيت Django...
    pip install django
)

echo ✅ Django جاهز

REM فحص psutil
python -c "import psutil" 2>nul
if errorlevel 1 (
    echo جاري تثبيت psutil...
    pip install psutil
)

echo ✅ psutil جاهز

REM تحديث إعدادات Django للشبكة
echo.
echo 🔧 تحديث إعدادات Django للوصول من الشبكة...
python -c "
import re
import os

settings_file = 'osaric_accounts/settings.py'
if os.path.exists(settings_file):
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # تحديث ALLOWED_HOSTS
    if 'ALLOWED_HOSTS = []' in content:
        content = content.replace('ALLOWED_HOSTS = []', 'ALLOWED_HOSTS = [\"*\"]')
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print('✅ تم تحديث ALLOWED_HOSTS للوصول من الشبكة')
    else:
        print('✅ ALLOWED_HOSTS محدث بالفعل')
else:
    print('❌ ملف الإعدادات غير موجود')
"

REM إضافة قاعدة الجدار الناري
echo.
echo 🔥 إعداد الجدار الناري...
netsh advfirewall firewall add rule name="Django-Network-8000" dir=in action=allow protocol=TCP localport=8000 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: لم يتم إضافة قاعدة الجدار الناري تلقائياً
    echo يرجى إضافتها يدوياً أو تشغيل السكريبت كمدير
) else (
    echo ✅ تم إعداد قاعدة الجدار الناري للمنفذ 8000
)

REM الحصول على عنوان IP المحلي
echo.
echo 🌐 الحصول على معلومات الشبكة...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set "ip=%%a"
    goto :found_ip
)
:found_ip
set ip=%ip: =%

echo.
echo 🎯 خيارات خادم الشبكة:
echo ========================
echo 1. تشغيل خادم الشبكة (للوصول من أي جهاز)
echo 2. تشغيل الخادم المحلي فقط
echo 3. عرض معلومات الشبكة
echo 4. اختبار الاتصال
echo 5. إعداد الجدار الناري يدوياً
echo 6. عرض الأجهزة المتصلة
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-6): "

if "%choice%"=="1" goto network_server
if "%choice%"=="2" goto local_server
if "%choice%"=="3" goto network_info
if "%choice%"=="4" goto test_connection
if "%choice%"=="5" goto firewall_setup
if "%choice%"=="6" goto show_devices
if "%choice%"=="0" goto exit
goto invalid_choice

:network_server
echo.
echo 🚀 تشغيل خادم الشبكة...
echo ========================
echo.
echo 🌐 معلومات الوصول:
echo • عنوان IP المحلي: %ip%
echo • المنفذ: 8000
echo.
echo 📱 للوصول من أجهزة أخرى:
echo   http://%ip%:8000/
echo.
echo 💻 للوصول من نفس الجهاز:
echo   http://localhost:8000/
echo   http://127.0.0.1:8000/
echo.
echo 📋 تعليمات:
echo • تأكد من أن الأجهزة على نفس الشبكة
echo • تأكد من إعدادات الجدار الناري
echo • استخدم عنوان IP المحلي للوصول
echo.
echo ⌨️ اضغط Ctrl+C لإيقاف الخادم
echo.
pause
python network_server.py
goto end

:local_server
echo.
echo 🚀 تشغيل الخادم المحلي فقط...
python manage.py runserver 127.0.0.1:8000
goto end

:network_info
echo.
echo 🌐 معلومات الشبكة:
echo ==================
echo عنوان IP المحلي: %ip%
echo اسم الجهاز: %COMPUTERNAME%
echo المنفذ المستخدم: 8000
echo.
echo 📡 فحص الشبكة:
ipconfig | findstr /c:"IPv4" /c:"Subnet" /c:"Default Gateway"
echo.
echo 🔌 فحص المنفذ 8000:
netstat -an | findstr :8000
if errorlevel 1 (
    echo ✅ المنفذ 8000 متاح
) else (
    echo ⚠️ المنفذ 8000 مستخدم
)
echo.
pause
goto start

:test_connection
echo.
echo 🧪 اختبار الاتصال:
echo ==================
echo فحص الاتصال بالإنترنت...
ping -n 1 ******* >nul 2>&1
if errorlevel 1 (
    echo ❌ لا يوجد اتصال بالإنترنت
) else (
    echo ✅ الاتصال بالإنترنت يعمل
)

echo.
echo فحص الشبكة المحلية...
ping -n 1 %ip% >nul 2>&1
if errorlevel 1 (
    echo ❌ مشكلة في الشبكة المحلية
) else (
    echo ✅ الشبكة المحلية تعمل
)

echo.
echo فحص DNS...
nslookup google.com >nul 2>&1
if errorlevel 1 (
    echo ❌ مشكلة في DNS
) else (
    echo ✅ DNS يعمل بشكل طبيعي
)

echo.
pause
goto start

:firewall_setup
echo.
echo 🔥 إعداد الجدار الناري يدوياً:
echo ==============================
echo.
echo لإضافة قاعدة الجدار الناري يدوياً:
echo.
echo 1. افتح "Windows Defender Firewall"
echo 2. اختر "Advanced settings"
echo 3. اختر "Inbound Rules" من الجانب الأيسر
echo 4. اضغط "New Rule" من الجانب الأيمن
echo 5. اختر "Port" ثم "Next"
echo 6. اختر "TCP" واكتب "8000" في "Specific local ports"
echo 7. اختر "Allow the connection"
echo 8. اختر جميع الشبكات (Domain, Private, Public)
echo 9. اكتب اسم القاعدة: "Django Network Server"
echo 10. اضغط "Finish"
echo.
echo أو استخدم الأمر التالي في Command Prompt كمدير:
echo netsh advfirewall firewall add rule name="Django-Network" dir=in action=allow protocol=TCP localport=8000
echo.
pause
goto start

:show_devices
echo.
echo 📱 الأجهزة المتصلة بالشبكة:
echo ============================
echo جاري البحث عن الأجهزة...
echo.
arp -a | findstr /v "Interface" | findstr /v "Internet"
echo.
echo 📋 ملاحظة: هذه قائمة بالأجهزة التي تم الاتصال بها مؤخراً
echo للحصول على قائمة كاملة، استخدم أدوات مسح الشبكة المتخصصة
echo.
pause
goto start

:invalid_choice
echo ❌ خيار غير صحيح
pause
goto start

:exit
echo 👋 شكراً لاستخدام خادم الشبكة!
exit /b 0

:end
echo.
echo 🎉 تم إنهاء العملية
pause

:start
cls
goto start
