"""
Views للوحة تحكم المخازن
Warehouse Dashboard Views
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Q, F, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from definitions.models import Warehouse, Item
from .models import (
    Stock, StockMovement, StockMovementItem, StockIncrease, StockDecrease,
    WarehouseTransfer
)


@login_required
def warehouse_dashboard(request):
    """لوحة تحكم المخازن الرئيسية"""
    
    # إحصائيات عامة
    total_warehouses = Warehouse.objects.filter(is_active=True).count()
    total_items = Item.objects.filter(is_active=True).count()
    total_stock_value = Stock.objects.aggregate(
        total=Sum(F('quantity') * F('average_cost'))
    )['total'] or Decimal('0')
    
    # المخازن مع إحصائياتها
    warehouses_stats = []
    for warehouse in Warehouse.objects.filter(is_active=True):
        stock_count = Stock.objects.filter(warehouse=warehouse, quantity__gt=0).count()
        stock_value = Stock.objects.filter(warehouse=warehouse).aggregate(
            total=Sum(F('quantity') * F('average_cost'))
        )['total'] or Decimal('0')
        
        # آخر حركة
        last_movement = StockMovement.objects.filter(warehouse=warehouse).first()
        
        warehouses_stats.append({
            'warehouse': warehouse,
            'stock_count': stock_count,
            'stock_value': stock_value,
            'last_movement': last_movement,
            'utilization': (stock_value / warehouse.capacity * 100) if warehouse.capacity else 0
        })
    
    # حركات المخزون الأخيرة
    recent_movements = StockMovement.objects.select_related(
        'warehouse'
    ).order_by('-date', '-id')[:10]
    
    # الأصناف منخفضة المخزون
    low_stock_items = Stock.objects.filter(
        quantity__lte=F('item__min_stock'),
        quantity__gt=0
    ).select_related('item', 'warehouse')[:10]
    
    # الأصناف منتهية الصلاحية قريباً
    expiry_date = timezone.now().date() + timedelta(days=30)
    expiring_items = StockMovementItem.objects.filter(
        expiry_date__lte=expiry_date,
        expiry_date__gte=timezone.now().date()
    ).select_related('item', 'movement__warehouse').order_by('expiry_date')[:10]
    
    # إحصائيات الحركات (آخر 30 يوم)
    thirty_days_ago = timezone.now().date() - timedelta(days=30)
    movements_stats = {
        'increases': StockIncrease.objects.filter(date__gte=thirty_days_ago).count(),
        'decreases': StockDecrease.objects.filter(date__gte=thirty_days_ago).count(),
        'transfers': WarehouseTransfer.objects.filter(date__gte=thirty_days_ago).count(),
    }
    
    # تم حذف إحصائيات التصنيع
    
    # الأصناف الأكثر حركة
    top_moving_items = StockMovementItem.objects.filter(
        movement__date__gte=thirty_days_ago
    ).values('item__name', 'item__code').annotate(
        total_quantity=Sum('quantity'),
        movement_count=Count('id')
    ).order_by('-total_quantity')[:10]
    
    context = {
        'title': 'لوحة تحكم المخازن',
        'total_warehouses': total_warehouses,
        'total_items': total_items,
        'total_stock_value': total_stock_value,
        'warehouses_stats': warehouses_stats,
        'recent_movements': recent_movements,
        'low_stock_items': low_stock_items,
        'expiring_items': expiring_items,
        'movements_stats': movements_stats,
        # تم حذف manufacturing_stats
        'top_moving_items': top_moving_items,
    }
    
    return render(request, 'inventory/warehouse_dashboard.html', context)


@login_required
def warehouse_detail_dashboard(request, warehouse_id):
    """لوحة تحكم مخزن محدد"""
    
    warehouse = get_object_or_404(Warehouse, pk=warehouse_id)
    
    # إحصائيات المخزن
    stock_items = Stock.objects.filter(warehouse=warehouse, quantity__gt=0)
    total_items = stock_items.count()
    total_value = stock_items.aggregate(
        total=Sum(F('quantity') * F('average_cost'))
    )['total'] or Decimal('0')
    
    # الأصناف حسب الفئات
    categories_stats = stock_items.values(
        'item__category__name'
    ).annotate(
        items_count=Count('id'),
        total_quantity=Sum('quantity'),
        total_value=Sum(F('quantity') * F('average_cost'))
    ).order_by('-total_value')
    
    # حركات المخزن (آخر 30 يوم)
    thirty_days_ago = timezone.now().date() - timedelta(days=30)
    recent_movements = StockMovement.objects.filter(
        warehouse=warehouse,
        date__gte=thirty_days_ago
    ).order_by('-date', '-id')[:20]
    
    # الأصناف منخفضة المخزون في هذا المخزن
    low_stock = stock_items.filter(
        quantity__lte=F('item__min_stock')
    ).select_related('item')[:10]
    
    # الأصناف عالية القيمة
    high_value_items = stock_items.annotate(
        total_value=F('quantity') * F('average_cost')
    ).order_by('-total_value')[:10]
    
    # إحصائيات الحركات
    movements_summary = {
        'total_in': StockMovementItem.objects.filter(
            movement__warehouse=warehouse,
            movement__movement_type__in=['INCREASE', 'TRANSFER_IN', 'RETURN'],
            movement__date__gte=thirty_days_ago
        ).aggregate(total=Sum('quantity'))['total'] or 0,
        
        'total_out': StockMovementItem.objects.filter(
            movement__warehouse=warehouse,
            movement__movement_type__in=['DECREASE', 'TRANSFER_OUT', 'SALE'],
            movement__date__gte=thirty_days_ago
        ).aggregate(total=Sum('quantity'))['total'] or 0,
    }
    
    context = {
        'title': f'لوحة تحكم مخزن {warehouse.name}',
        'warehouse': warehouse,
        'total_items': total_items,
        'total_value': total_value,
        'categories_stats': categories_stats,
        'recent_movements': recent_movements,
        'low_stock': low_stock,
        'high_value_items': high_value_items,
        'movements_summary': movements_summary,
    }
    
    return render(request, 'inventory/warehouse_detail_dashboard.html', context)


@login_required
def warehouse_analytics(request):
    """تحليلات المخازن"""
    
    # تحليل الحركات الشهرية
    current_date = timezone.now().date()
    months_data = []
    
    for i in range(12):
        month_start = current_date.replace(day=1) - timedelta(days=i*30)
        month_end = month_start + timedelta(days=30)
        
        month_movements = StockMovement.objects.filter(
            date__gte=month_start,
            date__lt=month_end
        ).aggregate(
            increases=Count('id', filter=Q(movement_type='INCREASE')),
            decreases=Count('id', filter=Q(movement_type='DECREASE')),
            transfers=Count('id', filter=Q(movement_type__in=['TRANSFER_IN', 'TRANSFER_OUT'])),
            total_value=Sum('total_amount')
        )
        
        months_data.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            **month_movements
        })
    
    months_data.reverse()
    
    # تحليل الأصناف
    items_analysis = Stock.objects.filter(
        quantity__gt=0
    ).values(
        'item__category__name'
    ).annotate(
        items_count=Count('id'),
        total_quantity=Sum('quantity'),
        total_value=Sum(F('quantity') * F('average_cost')),
        avg_cost=Avg('average_cost')
    ).order_by('-total_value')
    
    # تحليل المخازن
    warehouses_analysis = []
    for warehouse in Warehouse.objects.filter(is_active=True):
        warehouse_data = Stock.objects.filter(warehouse=warehouse).aggregate(
            items_count=Count('id', filter=Q(quantity__gt=0)),
            total_value=Sum(F('quantity') * F('average_cost')),
            avg_cost=Avg('average_cost')
        )
        
        warehouse_data['warehouse'] = warehouse
        warehouse_data['utilization'] = (
            warehouse_data['total_value'] / warehouse.capacity * 100
        ) if warehouse.capacity and warehouse_data['total_value'] else 0
        
        warehouses_analysis.append(warehouse_data)
    
    context = {
        'title': 'تحليلات المخازن',
        'months_data': months_data,
        'items_analysis': items_analysis,
        'warehouses_analysis': warehouses_analysis,
    }
    
    return render(request, 'inventory/warehouse_analytics.html', context)
