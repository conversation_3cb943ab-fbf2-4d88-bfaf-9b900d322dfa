{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل أمر التصنيع - {{ order.finished_product.name|default:"غير محدد" }}{% endblock %}

{% block extra_css %}
<style>
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .status-badge {
        font-size: 1.1rem;
        padding: 0.6rem 1.2rem;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .priority-badge {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 500;
    }
    
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 25px;
    }
    
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .material-card {
        border-left: 5px solid #28a745;
    }
    
    .step-card {
        border-left: 5px solid #ffc107;
    }
    
    .quality-card {
        border-left: 5px solid #17a2b8;
    }
    
    .cost-highlight {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .progress-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
    }
    
    .progress-bar {
        height: 12px;
        border-radius: 6px;
        background: rgba(255,255,255,0.3);
    }
    
    .progress-fill {
        height: 100%;
        border-radius: 6px;
        background: linear-gradient(90deg, #28a745, #20c997);
        transition: width 0.5s ease;
    }
    
    .action-btn {
        border-radius: 25px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    }
    
    .table-hover tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(180deg, #667eea, #28a745);
        border-radius: 2px;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 25px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -25px;
        top: 8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #667eea;
        border: 3px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .validation-error {
        border-left: 5px solid #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-radius: 10px;
    }
    
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 25px;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    
    .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="order-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-industry me-2"></i>
                    تفاصيل أمر التصنيع #{{ order.id }}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}" class="text-white-50">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_order_list' %}" class="text-white-50">أوامر التصنيع</a></li>
                        <li class="breadcrumb-item active text-white">التفاصيل</li>
                    </ol>
                </nav>
            </div>
            <div class="text-end">
                <div class="mb-3">
                    <span class="badge status-badge 
                        {% if order.status == 'DRAFT' %}bg-secondary
                        {% elif order.status == 'PLANNED' %}bg-info
                        {% elif order.status == 'IN_PROGRESS' %}bg-warning
                        {% elif order.status == 'COMPLETED' %}bg-success
                        {% elif order.status == 'CANCELLED' %}bg-danger
                        {% else %}bg-secondary{% endif %}">
                        {% if order.status == 'DRAFT' %}
                            <i class="fas fa-edit me-1"></i>مسودة
                        {% elif order.status == 'PLANNED' %}
                            <i class="fas fa-calendar-check me-1"></i>مخطط
                        {% elif order.status == 'IN_PROGRESS' %}
                            <i class="fas fa-cogs me-1"></i>قيد التنفيذ
                        {% elif order.status == 'COMPLETED' %}
                            <i class="fas fa-check me-1"></i>مكتمل
                        {% elif order.status == 'CANCELLED' %}
                            <i class="fas fa-times me-1"></i>ملغي
                        {% else %}
                            {{ order.get_status_display }}
                        {% endif %}
                    </span>
                </div>
                
                <!-- أزرار تغيير الحالة -->
                <div class="btn-group-vertical btn-group-sm" role="group">
                    {% if order.status == 'DRAFT' %}
                        <button type="button" class="btn btn-outline-info action-btn" onclick="changeStatus('PLANNED')">
                            <i class="fas fa-calendar-check me-1"></i>تخطيط الأمر
                        </button>
                    {% elif order.status == 'PLANNED' %}
                        <button type="button" class="btn btn-outline-warning action-btn" onclick="changeStatus('IN_PROGRESS')">
                            <i class="fas fa-play me-1"></i>بدء الإنتاج
                        </button>
                    {% elif order.status == 'IN_PROGRESS' %}
                        <button type="button" class="btn btn-outline-success action-btn" onclick="changeStatus('COMPLETED')">
                            <i class="fas fa-check me-1"></i>إكمال الإنتاج
                        </button>
                    {% endif %}
                    
                    {% if order.status in 'DRAFT,PLANNED,IN_PROGRESS' %}
                        <button type="button" class="btn btn-outline-danger action-btn" onclick="changeStatus('CANCELLED')">
                            <i class="fas fa-times me-1"></i>إلغاء الأمر
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- أخطاء التحقق -->
    {% if validation_errors %}
        <div class="alert alert-danger validation-error">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء في التحقق:</h6>
            <ul class="mb-0">
                {% for error in validation_errors %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    <!-- الإحصائيات السريعة -->
    <div class="quick-stats">
        <div class="stat-card">
            <div class="stat-icon text-primary">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-value text-primary">{{ order.quantity_to_produce|default:0 }}</div>
            <div class="stat-label">الكمية المطلوبة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-success">
                <i class="fas fa-cubes"></i>
            </div>
            <div class="stat-value text-success">{{ materials.count }}</div>
            <div class="stat-label">المواد الخام</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-warning">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-value text-warning">{{ steps.count }}</div>
            <div class="stat-label">خطوات التصنيع</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon text-info">
                <i class="fas fa-clipboard-check"></i>
            </div>
            <div class="stat-value text-info">{{ quality_checks.count }}</div>
            <div class="stat-label">فحوصات الجودة</div>
        </div>
    </div>

    <!-- تقدم الإنتاج -->
    {% if order.status == 'IN_PROGRESS' %}
    <div class="progress-section">
        <h6 class="mb-3"><i class="fas fa-chart-line me-2"></i>تقدم الإنتاج</h6>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {% if steps.count > 0 %}{{ steps|length|add:"-1"|multiply:100|divide:steps.count }}{% else %}0{% endif %}%"></div>
        </div>
        <div class="d-flex justify-content-between mt-2">
            <small class="text-muted">الخطوات المكتملة: {{ steps|length|add:"-1" }} من {{ steps.count }}</small>
            <small class="text-muted">{% if steps.count > 0 %}{{ steps|length|add:"-1"|multiply:100|divide:steps.count }}{% else %}0{% endif %}%</small>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- المعلومات الأساسية -->
        <div class="col-lg-8">
            <!-- بيانات الأمر الأساسية -->
            <div class="card info-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات أمر التصنيع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="w-50">المنتج النهائي:</th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-box text-primary me-2"></i>
                                            <div>
                                                <strong>{{ order.finished_product.name|default:"غير محدد" }}</strong>
                                                {% if order.finished_product.code %}
                                                    <br><small class="text-muted">{{ order.finished_product.code }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>الكمية المطلوبة:</th>
                                    <td><span class="badge bg-info fs-6">{{ order.quantity_to_produce|default:0 }}</span></td>
                                </tr>
                                <tr>
                                    <th>مخزن المواد الخام:</th>
                                    <td>
                                        <i class="fas fa-warehouse text-secondary me-1"></i>
                                        {{ order.raw_materials_warehouse.name|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>مخزن المنتجات النهائية:</th>
                                    <td>
                                        <i class="fas fa-warehouse text-secondary me-1"></i>
                                        {{ order.finished_goods_warehouse.name|default:"غير محدد" }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="w-50">تاريخ الأمر:</th>
                                    <td>
                                        <i class="fas fa-calendar text-secondary me-1"></i>
                                        {{ order.order_date|date:"Y/m/d" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنتهاء المتوقع:</th>
                                    <td>
                                        <i class="fas fa-calendar-check text-secondary me-1"></i>
                                        {{ order.expected_completion_date|date:"Y/m/d"|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنتهاء الفعلي:</th>
                                    <td>
                                        <i class="fas fa-calendar-day text-secondary me-1"></i>
                                        {{ order.actual_completion_date|date:"Y/m/d"|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>المشرف:</th>
                                    <td>
                                        <i class="fas fa-user text-secondary me-1"></i>
                                        {{ order.supervisor.get_full_name|default:order.supervisor.username|default:"غير محدد" }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    {% if order.notes %}
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <h6><i class="fas fa-sticky-note me-2"></i>الملاحظات:</h6>
                                <p class="text-muted">{{ order.notes }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- المواد الخام المطلوبة -->
            <div class="card material-card info-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-box-seam me-2"></i>
                        المواد الخام المطلوبة
                    </h6>
                </div>
                <div class="card-body">
                    {% if materials %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المادة الخام</th>
                                        <th>الكمية المطلوبة</th>
                                        <th>الكمية المستهلكة</th>
                                        <th>الوحدة</th>
                                        <th>التكلفة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for material in materials %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-cube text-success me-2"></i>
                                                <div>
                                                    <strong>{{ material.material.name }}</strong>
                                                    {% if material.material.code %}
                                                        <br><small class="text-muted">{{ material.material.code }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-info">{{ material.quantity_required|default:0 }}</span></td>
                                        <td><span class="badge bg-secondary">{{ material.quantity_consumed|default:0 }}</span></td>
                                        <td>{{ material.material.unit|default:"-" }}</td>
                                        <td><strong>{{ material.total_cost|default:0|floatformat:2 }} ج.م</strong></td>
                                        <td>
                                            {% if material.quantity_consumed >= material.quantity_required %}
                                                <span class="badge bg-success"><i class="fas fa-check me-1"></i>مستهلكة</span>
                                            {% else %}
                                                <span class="badge bg-warning"><i class="fas fa-clock me-1"></i>في الانتظار</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-info">
                                    <tr>
                                        <td colspan="4"><strong>إجمالي تكلفة المواد:</strong></td>
                                        <td><strong>{{ order.total_material_cost|default:0|floatformat:2 }} ج.م</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي مواد خام لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- خطوات التصنيع -->
            <div class="card step-card info-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-tasks me-2"></i>
                        خطوات التصنيع
                    </h6>
                </div>
                <div class="card-body">
                    {% if steps %}
                        <div class="timeline">
                            {% for step in steps %}
                            <div class="timeline-item">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ step.name }}</h6>
                                                <p class="text-muted mb-2">{{ step.description|default:"لا توجد وصف" }}</p>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user me-1"></i>
                                                            {{ step.assigned_to.get_full_name|default:step.assigned_to.username|default:"غير محدد" }}
                                                        </small>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {{ step.estimated_duration|default:0 }} ساعة
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge 
                                                    {% if step.is_completed %}bg-success
                                                    {% elif step.is_in_progress %}bg-info
                                                    {% else %}bg-secondary{% endif %}">
                                                    {% if step.is_completed %}
                                                        <i class="fas fa-check me-1"></i>مكتمل
                                                    {% elif step.is_in_progress %}
                                                        <i class="fas fa-cogs me-1"></i>قيد التنفيذ
                                                    {% else %}
                                                        <i class="fas fa-clock me-1"></i>في الانتظار
                                                    {% endif %}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-tasks fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي خطوات تصنيع لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- فحوصات الجودة -->
            <div class="card quality-card info-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-clipboard-check me-2"></i>
                        فحوصات الجودة
                    </h6>
                </div>
                <div class="card-body">
                    {% if quality_checks %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>نوع الفحص</th>
                                        <th>المواصفات</th>
                                        <th>النتيجة</th>
                                        <th>المفتش</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for check in quality_checks %}
                                    <tr>
                                        <td><strong>{{ check.check_type }}</strong></td>
                                        <td>{{ check.specifications|default:"-" }}</td>
                                        <td>{{ check.result|default:"-" }}</td>
                                        <td>
                                            <i class="fas fa-user text-secondary me-1"></i>
                                            {{ check.checked_by.get_full_name|default:check.checked_by.username|default:"غير محدد" }}
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar text-secondary me-1"></i>
                                            {{ check.check_date|date:"Y/m/d"|default:"-" }}
                                        </td>
                                        <td>
                                            <span class="badge 
                                                {% if check.is_passed %}bg-success
                                                {% elif check.is_failed %}bg-danger
                                                {% else %}bg-warning{% endif %}">
                                                {% if check.is_passed %}
                                                    <i class="fas fa-check me-1"></i>مقبول
                                                {% elif check.is_failed %}
                                                    <i class="fas fa-times me-1"></i>مرفوض
                                                {% else %}
                                                    <i class="fas fa-clock me-1"></i>في الانتظار
                                                {% endif %}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-check fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي فحوصات جودة لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- التكاليف -->
            <div class="card cost-highlight mb-4">
                <div class="card-body">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-calculator me-2"></i>
                        تفاصيل التكاليف
                    </h6>
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="text-white-50">تكلفة المواد</div>
                            <div class="h5 text-white">{{ order.total_material_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-white-50">تكلفة التشغيل</div>
                            <div class="h5 text-white">{{ order.total_operating_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="col-12">
                            <hr class="border-white">
                            <div class="text-white-50">التكلفة الإجمالية</div>
                            <div class="h3 text-white">{{ order.total_production_cost|default:0|floatformat:2 }} ج.م</div>
                            <div class="text-white-50">تكلفة الوحدة: {{ order.unit_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإجراءات -->
            <div class="card info-card mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات المتاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary action-btn">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                        
                        {% if order.status in 'DRAFT,PLANNED' %}
                            <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" class="btn btn-warning action-btn">
                                <i class="fas fa-edit me-2"></i>
                                تعديل الأمر
                            </a>
                        {% endif %}
                        
                        {% if order.status == 'COMPLETED' %}
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h6>تم إكمال التصنيع بنجاح</h6>
                                <small>تاريخ الإكمال: {{ order.actual_completion_date|date:"Y/m/d" }}</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card info-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <div>{{ order.created_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">آخر تحديث:</small>
                        <div>{{ order.updated_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">أنشئ بواسطة:</small>
                        <div>{{ order.created_by.get_full_name|default:order.created_by.username|default:"غير محدد" }}</div>
                    </div>
                    {% if order.is_active %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i>نشط</span>
                    {% else %}
                        <span class="badge bg-danger"><i class="fas fa-times me-1"></i>غير نشط</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تعريف معرف الأمر
const orderId = {{ order.id }};

// دالة تغيير حالة الأمر
function changeStatus(newStatus) {
    const statusNames = {
        'DRAFT': 'مسودة',
        'PLANNED': 'مخطط',
        'IN_PROGRESS': 'قيد التنفيذ',
        'COMPLETED': 'مكتمل',
        'CANCELLED': 'ملغي'
    };
    
    const statusName = statusNames[newStatus] || newStatus;
    
    if (confirm(`هل أنت متأكد من تغيير حالة الأمر إلى "${statusName}"؟`)) {
        // إظهار مؤشر التحميل
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
        button.disabled = true;
        
        // إرسال طلب AJAX لتغيير الحالة
        fetch(`/inventory/manufacturing/orders/${orderId}/change-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح
                showAlert(`تم تغيير حالة الأمر إلى "${statusName}" بنجاح`, 'success');
                
                // إعادة تحميل الصفحة بعد ثانيتين
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                // إظهار رسالة خطأ
                showAlert(data.message || 'حدث خطأ أثناء تغيير الحالة', 'error');
                
                // إعادة تفعيل الزر
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'error');
            
            // إعادة تفعيل الزر
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// دالة الحصول على CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // تحديث تلقائي كل دقيقة إذا كان الأمر قيد التنفيذ
    {% if order.status == 'IN_PROGRESS' %}
    setInterval(function() {
        location.reload();
    }, 60000);
    {% endif %}
});
</script>
{% endblock %} 