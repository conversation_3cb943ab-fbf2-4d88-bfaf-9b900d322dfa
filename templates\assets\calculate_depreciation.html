{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-calculator text-danger me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'assets:depreciation_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة لحساب الإهلاك
                    </a>
                    <a href="{% url 'assets:depreciation_entry_list' %}" class="btn btn-outline-danger">
                        <i class="fas fa-list me-1"></i>
                        قيود الإهلاك
                    </a>
                </div>
            </div>

            <!-- نموذج اختيار الفترة -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        اختيار الفترة المحاسبية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="depreciation-form">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الشهر</label>
                                <select class="form-select" name="month" required>
                                    <option value="">اختر الشهر</option>
                                    <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                    <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                    <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                    <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                    <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                    <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                    <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                    <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                    <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                    <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                    <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                    <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">السنة</label>
                                <select class="form-select" name="year" required>
                                    <option value="">اختر السنة</option>
                                    <option value="2020" {% if current_year == 2020 %}selected{% endif %}>2020</option>
                                    <option value="2021" {% if current_year == 2021 %}selected{% endif %}>2021</option>
                                    <option value="2022" {% if current_year == 2022 %}selected{% endif %}>2022</option>
                                    <option value="2023" {% if current_year == 2023 %}selected{% endif %}>2023</option>
                                    <option value="2024" {% if current_year == 2024 %}selected{% endif %}>2024</option>
                                    <option value="2025" {% if current_year == 2025 %}selected{% endif %}>2025</option>
                                    <option value="2026" {% if current_year == 2026 %}selected{% endif %}>2026</option>
                                    <option value="2027" {% if current_year == 2027 %}selected{% endif %}>2027</option>
                                    <option value="2028" {% if current_year == 2028 %}selected{% endif %}>2028</option>
                                    <option value="2029" {% if current_year == 2029 %}selected{% endif %}>2029</option>
                                    <option value="2030" {% if current_year == 2030 %}selected{% endif %}>2030</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-calculator me-2"></i>
                                    حساب الإهلاك الشهري
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نتائج الحساب -->
            {% if calculation_results %}
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            نتائج حساب الإهلاك لشهر {{ selected_month_name }} {{ selected_year }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- إحصائيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h6>عدد الأصول المحسوبة</h6>
                                        <h4>{{ calculation_results.assets_count }}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h6>إجمالي الإهلاك</h6>
                                        <h4>{{ calculation_results.total_depreciation|floatformat:2 }} ج.م</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h6>قيود جديدة</h6>
                                        <h4>{{ calculation_results.new_entries }}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h6>قيود موجودة</h6>
                                        <h4>{{ calculation_results.existing_entries }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الحساب -->
                        {% if calculation_results.details %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>كود الأصل</th>
                                            <th>اسم الأصل</th>
                                            <th>طريقة الإهلاك</th>
                                            <th>الإهلاك الشهري</th>
                                            <th>مجمع الإهلاك قبل</th>
                                            <th>مجمع الإهلاك بعد</th>
                                            <th>القيمة الدفترية بعد</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for detail in calculation_results.details %}
                                            <tr>
                                                <td><strong>{{ detail.asset.asset_code }}</strong></td>
                                                <td>{{ detail.asset.name }}</td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        {{ detail.asset.get_depreciation_method_display }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-danger fw-bold">
                                                        {{ detail.monthly_depreciation|floatformat:2 }} ج.م
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-secondary">
                                                        {{ detail.accumulated_before|floatformat:2 }} ج.م
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-warning fw-bold">
                                                        {{ detail.accumulated_after|floatformat:2 }} ج.م
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-success fw-bold">
                                                        {{ detail.book_value_after|floatformat:2 }} ج.م
                                                    </span>
                                                </td>
                                                <td>
                                                    {% if detail.is_new %}
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-plus me-1"></i>
                                                            قيد جديد
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-check me-1"></i>
                                                            موجود مسبقاً
                                                        </span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endif %}

                        <!-- رسائل التنبيه -->
                        {% if calculation_results.warnings %}
                            <div class="alert alert-warning mt-4">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    تنبيهات
                                </h6>
                                <ul class="mb-0">
                                    {% for warning in calculation_results.warnings %}
                                        <li>{{ warning }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        <!-- رسائل النجاح -->
                        {% if calculation_results.success_messages %}
                            <div class="alert alert-success mt-4">
                                <h6 class="alert-heading">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم بنجاح
                                </h6>
                                <ul class="mb-0">
                                    {% for message in calculation_results.success_messages %}
                                        <li>{{ message }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- الأصول النشطة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        الأصول النشطة المؤهلة للإهلاك
                        <span class="badge bg-success ms-2">{{ active_assets.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_assets %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>كود الأصل</th>
                                        <th>اسم الأصل</th>
                                        <th>تاريخ الشراء</th>
                                        <th>تكلفة الشراء</th>
                                        <th>طريقة الإهلاك</th>
                                        <th>العمر الإنتاجي</th>
                                        <th>الإهلاك الشهري</th>
                                        <th>مجمع الإهلاك</th>
                                        <th>القيمة الدفترية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for asset in active_assets %}
                                        <tr>
                                            <td><strong>{{ asset.asset_code }}</strong></td>
                                            <td>{{ asset.name }}</td>
                                            <td>{{ asset.purchase_date|date:"d/m/Y" }}</td>
                                            <td>
                                                <span class="text-info fw-bold">
                                                    {{ asset.purchase_cost|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    {{ asset.get_depreciation_method_display }}
                                                </span>
                                            </td>
                                            <td>{{ asset.useful_life_years }} سنوات</td>
                                            <td>
                                                <span class="text-danger fw-bold">
                                                    {{ asset.monthly_depreciation|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-warning">
                                                    {{ asset.accumulated_depreciation|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">
                                                    {{ asset.book_value|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أصول نشطة</h5>
                            <p class="text-muted">لا توجد أصول نشطة مؤهلة لحساب الإهلاك</p>
                            <a href="{% url 'assets:asset_create' %}" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة أصل جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات حول حساب الإهلاك الشهري
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-calculator me-2"></i>
                                عملية الحساب
                            </h6>
                            <ul class="mb-0">
                                <li>يتم حساب الإهلاك لجميع الأصول النشطة</li>
                                <li>يتم إنشاء قيد إهلاك لكل أصل</li>
                                <li>يتم تحديث مجمع الإهلاك والقيمة الدفترية</li>
                                <li>لا يتم تكرار القيد لنفس الأصل والفترة</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيهات مهمة
                            </h6>
                            <ul class="mb-0">
                                <li>تأكد من صحة الفترة المحاسبية</li>
                                <li>راجع النتائج قبل التأكيد</li>
                                <li>احتفظ بنسخة احتياطية قبل الحساب</li>
                                <li>يمكن حذف القيود الخاطئة لاحقاً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('depreciation-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            const month = document.querySelector('select[name="month"]').value;
            const year = document.querySelector('select[name="year"]').value;
            
            if (!month || !year) {
                e.preventDefault();
                alert('يجب اختيار الشهر والسنة');
                return false;
            }
            
            // تأكيد العملية
            const monthNames = {
                '1': 'يناير', '2': 'فبراير', '3': 'مارس', '4': 'أبريل',
                '5': 'مايو', '6': 'يونيو', '7': 'يوليو', '8': 'أغسطس',
                '9': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            };
            
            const confirmMessage = `هل أنت متأكد من حساب الإهلاك لشهر ${monthNames[month]} ${year}؟\n\nسيتم إنشاء قيود إهلاك لجميع الأصول النشطة.`;
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
            
            // إظهار مؤشر التحميل
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحساب...';
            submitBtn.disabled = true;
            
            // إعادة تفعيل الزر في حالة الخطأ
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });
    }
});
</script>
{% endblock %}
