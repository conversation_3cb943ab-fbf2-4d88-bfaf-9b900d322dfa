"""
مهام مجدولة للنظام
"""

import logging
import os
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from .models import SystemSettings

# استيراد currency_service بشكل آمن
try:
    from .currency_rates import currency_service
except ImportError:
    currency_service = None

logger = logging.getLogger(__name__)


class CurrencyRateUpdateTask:
    """مهمة تحديث أسعار العملات"""
    
    def __init__(self):
        self.last_update = None
        self.update_interval = 15  # دقيقة
        
    def should_update(self):
        """تحديد ما إذا كان يجب التحديث"""
        if not self.last_update:
            return True
            
        time_diff = timezone.now() - self.last_update
        return time_diff.total_seconds() >= (self.update_interval * 60)
    
    def run(self):
        """تشغيل مهمة التحديث"""
        if not self.should_update():
            logger.info("لا يحتاج تحديث أسعار العملات الآن")
            return False
            
        logger.info("بدء تحديث أسعار العملات...")
        
        try:
            if currency_service is None:
                logger.warning("خدمة العملات غير متاحة")
                return False

            result = currency_service.update_all_rates()
            
            if result['success'] > 0:
                self.last_update = timezone.now()
                logger.info(f"تم تحديث {result['success']} بنك بنجاح")
                return True
            else:
                logger.warning("فشل في تحديث جميع البنوك")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تحديث أسعار العملات: {str(e)}")
            return False


class AutoBackupTask:
    """مهمة النسخ الاحتياطي التلقائي"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def should_run_backup(self):
        """تحقق من ضرورة تشغيل النسخ الاحتياطي"""
        try:
            # التحقق من تفعيل النسخ التلقائي
            auto_backup = SystemSettings.objects.get(key='backup_auto')
            if auto_backup.value.lower() != 'true':
                return False

            # جلب تكرار النسخ
            frequency = SystemSettings.objects.get(key='backup_frequency')

            # التحقق من آخر نسخة احتياطية
            from .models import BackupRecord
            last_backup = BackupRecord.objects.filter(
                backup_type='AUTO'
            ).order_by('-created_at').first()

            if not last_backup:
                return True  # لا توجد نسخ سابقة

            now = timezone.now()
            time_diff = now - last_backup.created_at

            if frequency.value == 'hourly':
                return time_diff.total_seconds() >= 3600  # ساعة واحدة
            elif frequency.value == 'daily':
                return time_diff.days >= 1
            elif frequency.value == 'weekly':
                return time_diff.days >= 7
            elif frequency.value == 'monthly':
                return time_diff.days >= 30

            return False

        except SystemSettings.DoesNotExist:
            self.logger.warning("إعدادات النسخ الاحتياطي غير موجودة")
            return False
        except Exception as e:
            self.logger.error(f"خطأ في فحص ضرورة النسخ الاحتياطي: {str(e)}")
            return False

    def run(self):
        """تشغيل مهمة النسخ الاحتياطي"""
        try:
            if not self.should_run_backup():
                return False

            self.logger.info("بدء النسخ الاحتياطي التلقائي...")

            # جلب إعدادات النسخ الاحتياطي
            try:
                external_path_setting = SystemSettings.objects.get(key='backup_external_path')
                save_external_setting = SystemSettings.objects.get(key='backup_save_external')
                compress_setting = SystemSettings.objects.get(key='backup_compress')
                include_media_setting = SystemSettings.objects.get(key='backup_include_media')

                external_path = external_path_setting.value if save_external_setting.value.lower() == 'true' else None
                compress_backup = compress_setting.value.lower() == 'true'
                include_media = include_media_setting.value.lower() == 'true'

            except SystemSettings.DoesNotExist:
                # استخدام القيم الافتراضية
                external_path = r'D:\save'
                compress_backup = True
                include_media = True
                self.logger.warning("استخدام إعدادات النسخ الاحتياطي الافتراضية")

            # التأكد من وجود المجلد الخارجي
            if external_path and not os.path.exists(external_path):
                try:
                    os.makedirs(external_path)
                    self.logger.info(f"تم إنشاء مجلد النسخ الاحتياطي: {external_path}")
                except Exception as e:
                    self.logger.error(f"فشل في إنشاء مجلد النسخ الاحتياطي: {str(e)}")
                    external_path = None

            # إنشاء النسخة الاحتياطية
            from .views import create_database_backup
            from django.contrib.auth.models import User

            # البحث عن مستخدم النظام
            system_user = User.objects.filter(is_superuser=True).first()
            if not system_user:
                system_user = User.objects.filter(is_staff=True).first()

            backup_name = f"auto_backup_{timezone.now().strftime('%Y%m%d_%H%M%S')}"

            backup = create_database_backup(
                backup_name=backup_name,
                backup_type='AUTO',
                include_media=include_media,
                include_logs=False,
                compress_backup=compress_backup,
                description="نسخة احتياطية تلقائية كل ساعة",
                created_by=system_user,
                custom_path=external_path
            )

            if backup:
                backup_location = external_path if external_path else "المجلد الافتراضي"
                self.logger.info(f"تم إنشاء النسخة الاحتياطية التلقائية: {backup_name} في {backup_location}")

                # تنظيف النسخ القديمة
                self.cleanup_old_backups(external_path)

                return True
            else:
                self.logger.error("فشل في إنشاء النسخة الاحتياطية التلقائية")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")
            return False

    def cleanup_old_backups(self, backup_path=None):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            # جلب مدة الاحتفاظ
            retention_setting = SystemSettings.objects.get(key='backup_retention_days')
            retention_days = int(retention_setting.value)

            if retention_days <= 0:
                return  # لا تحذف إذا كانت القيمة 0 أو أقل

            # تحديد مجلد البحث
            if backup_path and os.path.exists(backup_path):
                search_path = backup_path
            else:
                search_path = os.path.join(settings.BASE_DIR, 'backups')

            if not os.path.exists(search_path):
                return

            # البحث عن الملفات القديمة
            cutoff_date = timezone.now() - timedelta(days=retention_days)
            deleted_count = 0

            for filename in os.listdir(search_path):
                if filename.startswith('auto_backup_') and (filename.endswith('.zip') or filename.endswith('.db')):
                    file_path = os.path.join(search_path, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    file_time = timezone.make_aware(file_time)

                    if file_time < cutoff_date:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                            self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {filename}")
                        except Exception as e:
                            self.logger.error(f"فشل في حذف النسخة الاحتياطية القديمة {filename}: {str(e)}")

            if deleted_count > 0:
                self.logger.info(f"تم حذف {deleted_count} نسخة احتياطية قديمة")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")


class TaskScheduler:
    """مجدول المهام"""

    def __init__(self):
        self.currency_task = CurrencyRateUpdateTask()
        self.backup_task = AutoBackupTask()
        self.is_running = False

    def start(self):
        """بدء المجدول"""
        if self.is_running:
            logger.warning("المجدول يعمل بالفعل")
            return

        self.is_running = True
        logger.info("تم بدء مجدول المهام")

    def stop(self):
        """إيقاف المجدول"""
        self.is_running = False
        logger.info("تم إيقاف مجدول المهام")

    def run_currency_update(self):
        """تشغيل مهمة تحديث العملات"""
        if not self.is_running:
            return False

        return self.currency_task.run()

    def run_auto_backup(self):
        """تشغيل مهمة النسخ الاحتياطي التلقائي"""
        if not self.is_running:
            return False

        return self.backup_task.run()


# إنشاء instance عام للمجدول
task_scheduler = TaskScheduler()


def update_currency_rates_task():
    """دالة لتحديث أسعار العملات (للاستخدام مع Celery أو cron)"""
    return task_scheduler.run_currency_update()


def auto_backup_task():
    """دالة للنسخ الاحتياطي التلقائي (للاستخدام مع Celery أو cron)"""
    return task_scheduler.run_auto_backup()


def setup_periodic_tasks():
    """إعداد المهام الدورية"""
    try:
        # يمكن استخدام Celery Beat أو Django-crontab
        # هنا مثال بسيط للتشغيل اليدوي
        
        task_scheduler.start()
        logger.info("تم إعداد المهام الدورية")
        
        # تحديث فوري عند البدء
        task_scheduler.run_currency_update()
        
    except Exception as e:
        logger.error(f"خطأ في إعداد المهام الدورية: {str(e)}")


# Auto-start scheduler when module is imported
if getattr(settings, 'AUTO_START_TASKS', True):
    setup_periodic_tasks()
