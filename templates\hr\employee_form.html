{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if employee %}تعديل موظف{% else %}إضافة موظف جديد{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            {% if employee %}تعديل موظف{% else %}إضافة موظف جديد{% endif %}
                        </h5>
                        <div>
                            <a href="{% url 'hr:employee_list' %}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="employeeForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- البيانات الشخصية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    البيانات الشخصية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    الاسم الكامل <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.national_id.id_for_label }}" class="form-label">
                                    الرقم القومي
                                </label>
                                {{ form.national_id }}
                                {% if form.national_id.help_text %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ form.national_id.help_text }}
                                </small>
                                {% endif %}
                                {% if form.national_id.errors %}
                                <div class="text-danger small">{{ form.national_id.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.birth_date.id_for_label }}" class="form-label">
                                    تاريخ الميلاد
                                </label>
                                {{ form.birth_date }}
                                {% if form.birth_date.errors %}
                                <div class="text-danger small">{{ form.birth_date.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن يكون عمر الموظف بين 18 و 65 سنة
                                </small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.employee_number.id_for_label }}" class="form-label">
                                    الرقم الوظيفي
                                </label>
                                <div class="input-group">
                                    {{ form.employee_number }}
                                    <button type="button" class="btn btn-outline-secondary" id="generateNumber">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                                {% if form.employee_number.errors %}
                                <div class="text-danger small">{{ form.employee_number.errors.0 }}</div>
                                {% endif %}
                                {% if form.employee_number.help_text %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ form.employee_number.help_text }}
                                </small>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-phone me-2"></i>
                                    معلومات الاتصال
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    رقم الهاتف
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.mobile.id_for_label }}" class="form-label">
                                    رقم الجوال
                                </label>
                                {{ form.mobile }}
                                {% if form.mobile.errors %}
                                <div class="text-danger small">{{ form.mobile.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    البريد الإلكتروني
                                </label>
                                {{ form.email }}
                                {% if form.email.help_text %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ form.email.help_text }}
                                </small>
                                {% endif %}
                                {% if form.email.errors %}
                                <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label">
                                    المدينة
                                </label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                <div class="text-danger small">{{ form.city.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    العنوان
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="text-danger small">{{ form.address.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الوظيفة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-briefcase me-2"></i>
                                    معلومات الوظيفة
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.department.id_for_label }}" class="form-label">
                                    القسم <span class="text-danger">*</span>
                                </label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                <div class="text-danger small">{{ form.department.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم تحديث المناصب المتاحة تلقائياً
                                </small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.position.id_for_label }}" class="form-label">
                                    المنصب <span class="text-danger">*</span>
                                </label>
                                {{ form.position }}
                                {% if form.position.errors %}
                                <div class="text-danger small">{{ form.position.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.salary_system.id_for_label }}" class="form-label">
                                    نظام المرتب <span class="text-danger">*</span>
                                </label>
                                {{ form.salary_system }}
                                {% if form.salary_system.errors %}
                                <div class="text-danger small">{{ form.salary_system.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.current_salary.id_for_label }}" class="form-label">
                                    المرتب الأساسي <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.current_salary }}
                                    <span class="input-group-text">ج.م</span>
                                </div>
                                {% if form.current_salary.errors %}
                                <div class="text-danger small">{{ form.current_salary.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يمكن إضافة تفاصيل المرتبات لاحقاً من صفحة تعديل الموظف
                                </small>
                            </div>
                        </div>

                        <!-- التواريخ -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar me-2"></i>
                                    التواريخ المهمة
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.hire_date.id_for_label }}" class="form-label">
                                    تاريخ التعيين <span class="text-danger">*</span>
                                </label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                <div class="text-danger small">{{ form.hire_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contract_start_date.id_for_label }}" class="form-label">
                                    تاريخ بداية العقد
                                </label>
                                {{ form.contract_start_date }}
                                {% if form.contract_start_date.errors %}
                                <div class="text-danger small">{{ form.contract_start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.contract_end_date.id_for_label }}" class="form-label">
                                    تاريخ نهاية العقد
                                </label>
                                {{ form.contract_end_date }}
                                {% if form.contract_end_date.errors %}
                                <div class="text-danger small">{{ form.contract_end_date.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    اتركه فارغاً للعقود الدائمة
                                </small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    حالة الموظف <span class="text-danger">*</span>
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <button type="submit" class="btn btn-success" id="submitBtn">
                                        <i class="fas fa-save me-2"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري الحفظ...</span>
                </div>
                <p class="mt-2 mb-0">جاري حفظ بيانات الموظف...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('employeeForm');
    const submitBtn = document.getElementById('submitBtn');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    
    // Auto-generate employee number if empty
    const employeeNumberField = document.getElementById('{{ form.employee_number.id_for_label }}');
    const generateBtn = document.getElementById('generateNumber');
    
    function generateEmployeeNumber() {
        const timestamp = Date.now();
        const empNumber = 'EMP' + timestamp;
        employeeNumberField.value = empNumber;
    }
    
    if (employeeNumberField && employeeNumberField.value === '') {
        generateEmployeeNumber();
    }
    
    if (generateBtn) {
        generateBtn.addEventListener('click', generateEmployeeNumber);
    }
    
    // Update position options based on selected department
    const departmentField = document.getElementById('{{ form.department.id_for_label }}');
    const positionField = document.getElementById('{{ form.position.id_for_label }}');
    
    if (departmentField && positionField) {
        departmentField.addEventListener('change', function() {
            const departmentId = this.value;
            if (departmentId) {
                // Reset position selection
                positionField.value = '';
                // Here you could add AJAX call to filter positions by department
                // For now, we'll keep all positions available
            }
        });
    }
    
    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });
    
    // Set default dates
    const hireDateField = document.getElementById('{{ form.hire_date.id_for_label }}');
    const contractStartField = document.getElementById('{{ form.contract_start_date.id_for_label }}');
    
    if (hireDateField && !hireDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        hireDateField.value = today;
    }
    
    if (contractStartField && !contractStartField.value) {
        const today = new Date().toISOString().split('T')[0];
        contractStartField.value = today;
    }
});
</script>
{% endblock %}
