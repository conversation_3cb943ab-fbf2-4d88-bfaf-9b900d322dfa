#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item
from django.contrib.auth.models import User

def restore_deleted_items():
    print("=== استعادة الأصناف المحذوفة ===")
    
    # البحث عن الأصناف المحذوفة (غير النشطة)
    deleted_items = Item.objects.filter(is_active=False)
    
    if not deleted_items.exists():
        print("لا توجد أصناف محذوفة لاستعادتها")
        return
    
    print(f"تم العثور على {deleted_items.count()} صنف محذوف:")
    
    # عرض الأصناف المحذوفة
    for item in deleted_items:
        print(f"- {item.code}: {item.name}")
    
    # استعادة جميع الأصناف المحذوفة
    restored_count = deleted_items.update(is_active=True)
    
    print(f"\nتم استعادة {restored_count} صنف بنجاح!")
    
    # عرض الإحصائيات النهائية
    active_items = Item.objects.filter(is_active=True).count()
    inactive_items = Item.objects.filter(is_active=False).count()
    total_items = Item.objects.count()
    
    print(f"\nالإحصائيات النهائية:")
    print(f"الأصناف النشطة: {active_items}")
    print(f"الأصناف المحذوفة: {inactive_items}")
    print(f"إجمالي الأصناف: {total_items}")

def restore_specific_items(item_codes):
    """استعادة أصناف محددة بالكود"""
    print(f"=== استعادة أصناف محددة ===")
    
    restored_count = 0
    for code in item_codes:
        try:
            item = Item.objects.get(code=code, is_active=False)
            item.is_active = True
            item.save()
            print(f"تم استعادة: {item.code} - {item.name}")
            restored_count += 1
        except Item.DoesNotExist:
            print(f"لم يتم العثور على صنف محذوف بالكود: {code}")
    
    print(f"\nتم استعادة {restored_count} صنف")

if __name__ == "__main__":
    # استعادة جميع الأصناف المحذوفة
    restore_deleted_items()
    
    # إذا كنت تريد استعادة أصناف محددة فقط، استخدم هذا:
    # specific_codes = ['TEST001', 'OLD001', 'OLD002']  # ضع أكواد الأصناف المطلوب استعادتها
    # restore_specific_items(specific_codes)