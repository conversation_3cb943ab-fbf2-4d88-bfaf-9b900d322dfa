#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item

def restore_all_deleted_items():
    print("=== Restoring deleted items ===")
    
    # Find deleted items (inactive)
    deleted_items = Item.objects.filter(is_active=False)
    
    if not deleted_items.exists():
        print("No deleted items found to restore")
        return
    
    print(f"Found {deleted_items.count()} deleted items:")
    
    # Show deleted items
    for item in deleted_items:
        try:
            print(f"- {item.code}: {item.name}")
        except UnicodeEncodeError:
            print(f"- {item.code}: [Name contains special characters]")
    
    # Restore all deleted items
    restored_count = deleted_items.update(is_active=True)
    
    print(f"\nRestored {restored_count} items successfully!")
    
    # Show final statistics
    active_items = Item.objects.filter(is_active=True).count()
    inactive_items = Item.objects.filter(is_active=False).count()
    total_items = Item.objects.count()
    
    print(f"\nFinal statistics:")
    print(f"Active items: {active_items}")
    print(f"Deleted items: {inactive_items}")
    print(f"Total items: {total_items}")

if __name__ == "__main__":
    restore_all_deleted_items()