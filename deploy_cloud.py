#!/usr/bin/env python3
"""
سكريبت النشر السحابي
Cloud Deployment Script

نشر التطبيق على الخدمات السحابية المختلفة
Deploy the application to various cloud services
"""

import os
import sys
import subprocess
import json
from datetime import datetime

class CloudDeployer:
    """أداة النشر السحابي"""
    
    def __init__(self):
        self.project_name = "osaric-accounting"
        self.python_version = "3.11"
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ERROR: {message}")
    
    def run_command(self, cmd, description):
        """تشغيل أمر"""
        self.log_info(f"{description}...")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_info(f"✅ {description} - نجح")
                return True, result.stdout
            else:
                self.log_error(f"❌ {description} - فشل: {result.stderr}")
                return False, result.stderr
        except Exception as e:
            self.log_error(f"❌ {description} - خطأ: {e}")
            return False, str(e)
    
    def create_requirements_txt(self):
        """إنشاء ملف requirements.txt للإنتاج"""
        requirements = """Django>=4.2,<5.0
psutil>=5.9.0
gunicorn>=20.1.0
whitenoise>=6.2.0
dj-database-url>=1.0.0
python-decouple>=3.6
"""
        
        try:
            with open('requirements.txt', 'w') as f:
                f.write(requirements)
            self.log_info("✅ تم إنشاء ملف requirements.txt")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء requirements.txt: {e}")
            return False
    
    def create_procfile(self):
        """إنشاء Procfile لـ Heroku"""
        procfile_content = f"web: gunicorn {self.project_name.replace('-', '_')}.wsgi:application --bind 0.0.0.0:$PORT"
        
        try:
            with open('Procfile', 'w') as f:
                f.write(procfile_content)
            self.log_info("✅ تم إنشاء Procfile")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء Procfile: {e}")
            return False
    
    def create_runtime_txt(self):
        """إنشاء runtime.txt"""
        try:
            with open('runtime.txt', 'w') as f:
                f.write(f"python-{self.python_version}")
            self.log_info("✅ تم إنشاء runtime.txt")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء runtime.txt: {e}")
            return False
    
    def create_railway_json(self):
        """إنشاء railway.json لـ Railway"""
        railway_config = {
            "build": {
                "builder": "NIXPACKS"
            },
            "deploy": {
                "startCommand": f"gunicorn {self.project_name.replace('-', '_')}.wsgi:application --bind 0.0.0.0:$PORT",
                "restartPolicyType": "ON_FAILURE",
                "restartPolicyMaxRetries": 10
            }
        }
        
        try:
            with open('railway.json', 'w') as f:
                json.dump(railway_config, f, indent=2)
            self.log_info("✅ تم إنشاء railway.json")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء railway.json: {e}")
            return False
    
    def create_dockerfile(self):
        """إنشاء Dockerfile"""
        dockerfile_content = f"""FROM python:{self.python_version}-slim

WORKDIR /app

# تثبيت المتطلبات النظام
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY requirements.txt .

# تثبيت المتطلبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ التطبيق
COPY . .

# جمع الملفات الثابتة
RUN python manage.py collectstatic --noinput

# تشغيل الترحيلات
RUN python manage.py migrate

# تعريف المنفذ
EXPOSE 8000

# تشغيل التطبيق
CMD ["gunicorn", "{self.project_name.replace('-', '_')}.wsgi:application", "--bind", "0.0.0.0:8000"]
"""
        
        try:
            with open('Dockerfile', 'w') as f:
                f.write(dockerfile_content)
            self.log_info("✅ تم إنشاء Dockerfile")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء Dockerfile: {e}")
            return False
    
    def create_docker_compose(self):
        """إنشاء docker-compose.yml"""
        compose_content = f"""version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - ALLOWED_HOSTS=*
    volumes:
      - ./media:/app/media
      - ./db.sqlite3:/app/db.sqlite3
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/app/static
    depends_on:
      - web
    restart: unless-stopped
"""
        
        try:
            with open('docker-compose.yml', 'w') as f:
                f.write(compose_content)
            self.log_info("✅ تم إنشاء docker-compose.yml")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء docker-compose.yml: {e}")
            return False
    
    def create_nginx_config(self):
        """إنشاء إعدادات Nginx"""
        nginx_config = """events {
    worker_connections 1024;
}

http {
    upstream django {
        server web:8000;
    }
    
    server {
        listen 80;
        server_name _;
        
        location /static/ {
            alias /app/static/;
        }
        
        location /media/ {
            alias /app/media/;
        }
        
        location / {
            proxy_pass http://django;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
"""
        
        try:
            with open('nginx.conf', 'w') as f:
                f.write(nginx_config)
            self.log_info("✅ تم إنشاء nginx.conf")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء nginx.conf: {e}")
            return False
    
    def update_settings_for_production(self):
        """تحديث الإعدادات للإنتاج"""
        settings_content = """
# إعدادات الإنتاج الإضافية
import os
from decouple import config
import dj_database_url

# إعدادات قاعدة البيانات للإنتاج
if 'DATABASE_URL' in os.environ:
    DATABASES['default'] = dj_database_url.parse(os.environ.get('DATABASE_URL'))

# إعدادات الملفات الثابتة
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# إعدادات الأمان للإنتاج
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SECURE_HSTS_SECONDS = ********
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'

# إعدادات WhiteNoise
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
"""
        
        try:
            settings_file = 'osaric_accounts/settings_production.py'
            with open(settings_file, 'w') as f:
                f.write(settings_content)
            self.log_info("✅ تم إنشاء إعدادات الإنتاج")
            return True
        except Exception as e:
            self.log_error(f"❌ خطأ في إنشاء إعدادات الإنتاج: {e}")
            return False
    
    def deploy_to_heroku(self):
        """النشر على Heroku"""
        self.log_info("🚀 بدء النشر على Heroku...")
        
        # فحص Heroku CLI
        success, _ = self.run_command("heroku --version", "فحص Heroku CLI")
        if not success:
            self.log_error("يرجى تثبيت Heroku CLI أولاً")
            return False
        
        # تسجيل الدخول
        self.run_command("heroku login", "تسجيل الدخول لـ Heroku")
        
        # إنشاء التطبيق
        self.run_command(f"heroku create {self.project_name}", "إنشاء تطبيق Heroku")
        
        # إعداد متغيرات البيئة
        self.run_command("heroku config:set DEBUG=False", "إعداد DEBUG")
        self.run_command("heroku config:set ALLOWED_HOSTS=*", "إعداد ALLOWED_HOSTS")
        
        # رفع التطبيق
        self.run_command("git add .", "إضافة الملفات لـ Git")
        self.run_command('git commit -m "Deploy to Heroku"', "إنشاء commit")
        self.run_command("git push heroku main", "رفع التطبيق لـ Heroku")
        
        # تشغيل الترحيلات
        self.run_command("heroku run python manage.py migrate", "تشغيل الترحيلات")
        
        # فتح التطبيق
        self.run_command("heroku open", "فتح التطبيق")
        
        self.log_info("✅ تم النشر على Heroku بنجاح!")
        return True
    
    def deploy_to_railway(self):
        """النشر على Railway"""
        self.log_info("🚀 بدء النشر على Railway...")
        
        # فحص Railway CLI
        success, _ = self.run_command("railway --version", "فحص Railway CLI")
        if not success:
            self.log_error("يرجى تثبيت Railway CLI أولاً: npm install -g @railway/cli")
            return False
        
        # تسجيل الدخول
        self.run_command("railway login", "تسجيل الدخول لـ Railway")
        
        # إنشاء مشروع جديد
        self.run_command("railway init", "إنشاء مشروع Railway")
        
        # رفع التطبيق
        self.run_command("railway up", "رفع التطبيق لـ Railway")
        
        self.log_info("✅ تم النشر على Railway بنجاح!")
        return True
    
    def prepare_for_deployment(self):
        """تحضير الملفات للنشر"""
        self.log_info("📦 تحضير الملفات للنشر...")
        
        files_created = []
        
        if self.create_requirements_txt():
            files_created.append("requirements.txt")
        
        if self.create_procfile():
            files_created.append("Procfile")
        
        if self.create_runtime_txt():
            files_created.append("runtime.txt")
        
        if self.create_railway_json():
            files_created.append("railway.json")
        
        if self.create_dockerfile():
            files_created.append("Dockerfile")
        
        if self.create_docker_compose():
            files_created.append("docker-compose.yml")
        
        if self.create_nginx_config():
            files_created.append("nginx.conf")
        
        if self.update_settings_for_production():
            files_created.append("settings_production.py")
        
        self.log_info(f"✅ تم إنشاء {len(files_created)} ملف للنشر")
        return len(files_created) > 0
    
    def show_deployment_options(self):
        """عرض خيارات النشر"""
        print("\n" + "=" * 60)
        print("☁️ خيارات النشر السحابي")
        print("=" * 60)
        print("1. تحضير ملفات النشر")
        print("2. النشر على Heroku (مجاني)")
        print("3. النشر على Railway (مجاني)")
        print("4. إنشاء ملفات Docker")
        print("5. عرض تعليمات النشر اليدوي")
        print("0. خروج")
        print("=" * 60)
        
        choice = input("اختر رقم الخيار: ")
        
        if choice == "1":
            self.prepare_for_deployment()
        elif choice == "2":
            self.prepare_for_deployment()
            self.deploy_to_heroku()
        elif choice == "3":
            self.prepare_for_deployment()
            self.deploy_to_railway()
        elif choice == "4":
            self.create_dockerfile()
            self.create_docker_compose()
            self.create_nginx_config()
        elif choice == "5":
            self.show_manual_deployment_guide()
        elif choice == "0":
            return
        else:
            print("❌ خيار غير صحيح")
            self.show_deployment_options()
    
    def show_manual_deployment_guide(self):
        """عرض دليل النشر اليدوي"""
        print("\n" + "=" * 60)
        print("📋 دليل النشر اليدوي")
        print("=" * 60)
        print("""
🔧 للنشر على خادم VPS:

1. تحديث النظام:
   sudo apt update && sudo apt upgrade -y

2. تثبيت Python و pip:
   sudo apt install python3 python3-pip python3-venv -y

3. تثبيت Nginx:
   sudo apt install nginx -y

4. إنشاء مستخدم للتطبيق:
   sudo adduser django

5. رفع ملفات التطبيق:
   scp -r . django@your-server:/home/<USER>/app/

6. إنشاء بيئة افتراضية:
   python3 -m venv venv
   source venv/bin/activate

7. تثبيت المتطلبات:
   pip install -r requirements.txt

8. تشغيل الترحيلات:
   python manage.py migrate

9. جمع الملفات الثابتة:
   python manage.py collectstatic

10. تشغيل Gunicorn:
    gunicorn osaric_accounts.wsgi:application --bind 0.0.0.0:8000

11. إعداد Nginx (استخدم nginx.conf المُنشأ)

12. إعداد SSL مع Let's Encrypt:
    sudo certbot --nginx -d your-domain.com

🌐 للنشر على AWS EC2:
- استخدم نفس الخطوات أعلاه
- أضف Security Group للمنفذ 80 و 443
- استخدم Elastic IP للعنوان الثابت

☁️ للنشر على DigitalOcean:
- إنشاء Droplet جديد
- اتبع نفس خطوات VPS
- استخدم Floating IP
""")

def main():
    """الدالة الرئيسية"""
    deployer = CloudDeployer()
    deployer.show_deployment_options()

if __name__ == "__main__":
    main()
