{% extends 'base/base.html' %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-plus me-2"></i>{{ title }}</h4>
                </div>
                <form method="post" novalidate>
                    {% csrf_token %}
                    <div class="card-body">
                        {% for field in form %}
                        <div class="mb-3">
                            {{ field.label_tag }}
                            {{ field }}
                            {% if field.errors %}<div class="text-danger small">{{ field.errors }}</div>{% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    <div class="card-footer text-end">
                        <a href="{% url 'hr:allowance_type_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
                        <button type="submit" class="btn btn-success"><i class="fas fa-save me-2"></i>حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 