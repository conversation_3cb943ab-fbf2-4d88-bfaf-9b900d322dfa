{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .detail-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .detail-header h4 {
        margin: 0;
        font-weight: 600;
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .action-btn {
        border-radius: 25px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        text-decoration: none;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .btn-approve {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .btn-reject {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    
    .btn-back {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .info-card .card-header {
        border: none;
        padding: 20px;
        font-weight: 600;
        font-size: 16px;
    }
    
    .info-card .card-body {
        padding: 25px;
    }
    
    .employee-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .termination-card .card-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .reason-card .card-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .approval-card .card-header {
        background: linear-gradient(135deg, #d4fc79 0%, #96e6a1 100%);
        color: #333;
    }
    
    .system-card .card-header {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    
    .info-row {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }
    
    .info-row:hover {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 10px;
        padding-left: 15px;
        padding-right: 15px;
        margin-left: -15px;
        margin-right: -15px;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #333;
        min-width: 120px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .info-value {
        color: #666;
        flex: 1;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #d63384;
    }
    
    .status-approved {
        background: linear-gradient(135deg, #d4fc79 0%, #96e6a1 100%);
        color: #198754;
    }
    
    .status-rejected {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #dc3545;
    }
    
    .status-completed {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #0dcaf0;
    }
    
    .type-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .duration-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 12px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .reason-text {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        font-size: 16px;
        line-height: 1.6;
        color: #333;
    }
    
    .approval-info {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #198754;
    }
    
    .system-info {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #6c757d;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .info-item {
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .info-item strong {
        color: #667eea;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: block;
        margin-bottom: 5px;
    }
    
    .info-item span {
        color: #333;
        font-weight: 500;
    }
    
    .icon-wrapper {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        margin-right: 10px;
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            width: 100%;
        }
        
        .action-btn {
            width: 100%;
            justify-content: center;
        }
        
        .info-label {
            min-width: 100px;
            font-size: 14px;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">
                    <i class="fas fa-user-times me-2" style="color: #667eea;"></i>
                    {{ title }}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:termination_list' %}">إنهاء الخدمة</a></li>
                        <li class="breadcrumb-item active">تفاصيل الطلب</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="detail-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4>
                        <i class="fas fa-file-alt me-2"></i>
                        تفاصيل طلب إنهاء الخدمة
                    </h4>
                    <div class="action-buttons">
                        {% if termination.status == 'PENDING' %}
                        <a href="{% url 'hr:termination_edit' termination.pk %}" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{% url 'hr:termination_approve' termination.pk %}" class="action-btn btn-approve">
                            <i class="fas fa-check"></i> موافقة
                        </a>
                        <a href="{% url 'hr:termination_reject' termination.pk %}" class="action-btn btn-reject">
                            <i class="fas fa-times"></i> رفض
                        </a>
                        {% endif %}
                        <a href="{% url 'hr:termination_list' %}" class="action-btn btn-back">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الموظف -->
        <div class="col-md-6">
            <div class="card info-card employee-card">
                <div class="card-header">
                    <i class="fas fa-user me-2"></i>معلومات الموظف
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-user-circle"></i>الاسم:
                        </div>
                        <div class="info-value">{{ termination.employee.person.name }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-building"></i>القسم:
                        </div>
                        <div class="info-value">{{ termination.employee.department.name }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-briefcase"></i>المنصب:
                        </div>
                        <div class="info-value">{{ termination.employee.position.name }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-calendar-plus"></i>تاريخ التعيين:
                        </div>
                        <div class="info-value">{{ termination.employee.hire_date }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-clock"></i>مدة الخدمة:
                        </div>
                        <div class="info-value">
                            <span class="duration-badge">{{ termination.get_service_duration }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل إنهاء الخدمة -->
        <div class="col-md-6">
            <div class="card info-card termination-card">
                <div class="card-header">
                    <i class="fas fa-user-minus me-2"></i>تفاصيل إنهاء الخدمة
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-tag"></i>النوع:
                        </div>
                        <div class="info-value">
                            <span class="type-badge">{{ termination.get_termination_type_display }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-info-circle"></i>الحالة:
                        </div>
                        <div class="info-value">
                            {% if termination.status == 'PENDING' %}
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock"></i>قيد الانتظار
                                </span>
                            {% elif termination.status == 'APPROVED' %}
                                <span class="status-badge status-approved">
                                    <i class="fas fa-check"></i>موافق عليه
                                </span>
                            {% elif termination.status == 'REJECTED' %}
                                <span class="status-badge status-rejected">
                                    <i class="fas fa-times"></i>مرفوض
                                </span>
                            {% elif termination.status == 'COMPLETED' %}
                                <span class="status-badge status-completed">
                                    <i class="fas fa-check-double"></i>مكتمل
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-calendar-alt"></i>تاريخ إنهاء الخدمة:
                        </div>
                        <div class="info-value">{{ termination.termination_date }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-calendar-check"></i>آخر يوم عمل:
                        </div>
                        <div class="info-value">{{ termination.last_working_day }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">
                            <i class="fas fa-calendar-day"></i>فترة الإشعار:
                        </div>
                        <div class="info-value">{{ termination.notice_period }} يوم</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- السبب -->
    <div class="row">
        <div class="col-12">
            <div class="card info-card reason-card">
                <div class="card-header">
                    <i class="fas fa-comment me-2"></i>سبب إنهاء الخدمة
                </div>
                <div class="card-body">
                    <div class="reason-text">
                        {{ termination.reason }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الموافقة -->
    {% if termination.approved_by %}
    <div class="row">
        <div class="col-12">
            <div class="card info-card approval-card">
                <div class="card-header">
                    <i class="fas fa-check-circle me-2"></i>معلومات الموافقة
                </div>
                <div class="card-body">
                    <div class="approval-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>تمت الموافقة من</strong>
                                <span>{{ termination.approved_by.get_full_name|default:termination.approved_by.username }}</span>
                            </div>
                            <div class="info-item">
                                <strong>تاريخ الموافقة</strong>
                                <span>{{ termination.approved_date|date:"Y-m-d H:i" }}</span>
                            </div>
                            <div class="info-item">
                                <strong>الحالة</strong>
                                <span>
                                    {% if termination.status == 'APPROVED' %}
                                        <span class="status-badge status-approved">
                                            <i class="fas fa-check"></i>موافق عليه
                                        </span>
                                    {% elif termination.status == 'REJECTED' %}
                                        <span class="status-badge status-rejected">
                                            <i class="fas fa-times"></i>مرفوض
                                        </span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        {% if termination.approval_notes %}
                        <div class="mt-4">
                            <strong>ملاحظات الموافقة:</strong>
                            <div class="reason-text mt-2">
                                {{ termination.approval_notes }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card info-card system-card">
                <div class="card-header">
                    <i class="fas fa-cog me-2"></i>معلومات النظام
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>أنشئ بواسطة</strong>
                                <span>{{ termination.created_by.get_full_name|default:termination.created_by.username }}</span>
                            </div>
                            <div class="info-item">
                                <strong>تاريخ الإنشاء</strong>
                                <span>{{ termination.created_at|date:"Y-m-d H:i" }}</span>
                            </div>
                            <div class="info-item">
                                <strong>آخر تحديث</strong>
                                <span>{{ termination.updated_at|date:"Y-m-d H:i" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 