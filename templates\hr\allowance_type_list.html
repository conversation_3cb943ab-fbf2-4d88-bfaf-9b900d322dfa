{% extends 'base/base.html' %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{% url 'hr:allowance_deduction_list' %}" class="btn btn-secondary me-2"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
            <a href="{% url 'hr:allowance_type_create' %}" class="btn btn-success"><i class="fas fa-plus me-2"></i>إضافة نوع إضافة</a>
        </div>
    </div>
    <div class="card w-100">
        <div class="card-body p-0">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الكود</th>
                        <th>الاسم</th>
                        <th>الاسم بالإنجليزية</th>
                        <th>خاضعة للضريبة؟</th>
                        <th>نشط</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for t in types %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ t.code }}</td>
                        <td>{{ t.name }}</td>
                        <td>{{ t.name_english }}</td>
                        <td>{% if t.is_taxable %}<span class="badge bg-warning">نعم</span>{% else %}<span class="badge bg-secondary">لا</span>{% endif %}</td>
                        <td>{% if t.is_active %}<span class="badge bg-success">نشط</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</td>
                        <td>
                            <a href="{% url 'hr:allowance_type_edit' t.pk %}" class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></a>
                            <a href="{% url 'hr:allowance_type_delete' t.pk %}" class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="7" class="text-center text-muted">لا توجد أنواع إضافات</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 