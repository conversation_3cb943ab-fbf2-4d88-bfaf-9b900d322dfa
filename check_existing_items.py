#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemType

def check_existing_items():
    print("=== Checking Existing Items ===")
    
    # Get all active items
    all_items = Item.objects.filter(is_active=True)
    print(f"Total active items: {all_items.count()}")
    
    # Show all items with their types
    print("\nAll existing items:")
    for item in all_items:
        print(f"- {item.code}: {item.name} (Type: {item.item_type})")
    
    # Check which ones are test items (FP001-FP005)
    test_items = Item.objects.filter(code__startswith='FP', is_active=True)
    print(f"\nTest items to delete: {test_items.count()}")
    for item in test_items:
        print(f"- {item.code}: {item.name}")
    
    return all_items, test_items

def delete_test_items():
    print("\n=== Deleting Test Items ===")
    
    test_items = Item.objects.filter(code__startswith='FP')
    deleted_count = 0
    
    for item in test_items:
        print(f"Deleting: {item.code} - {item.name}")
        item.delete()
        deleted_count += 1
    
    print(f"Deleted {deleted_count} test items")

def convert_existing_items_to_finished_products():
    print("\n=== Converting Existing Items to Finished Products ===")
    
    # Get items that are not raw materials and could be finished products
    # Exclude test items and items that are already finished products
    candidate_items = Item.objects.filter(
        is_active=True,
        item_type=ItemType.RAW_MATERIAL  # Convert some raw materials to finished products
    ).exclude(code__startswith='FP').exclude(code__startswith='TEST')
    
    print(f"Candidate items for conversion: {candidate_items.count()}")
    
    # Convert first few items to finished products
    converted_count = 0
    for item in candidate_items[:3]:  # Convert first 3 items
        old_type = item.item_type
        item.item_type = ItemType.FINISHED_PRODUCT
        item.save()
        print(f"Converted: {item.code} - {item.name} (from {old_type} to FINISHED_PRODUCT)")
        converted_count += 1
    
    print(f"Converted {converted_count} items to finished products")

if __name__ == "__main__":
    all_items, test_items = check_existing_items()
    
    if test_items.count() > 0:
        delete_test_items()
    
    # Check if we have any finished products after deletion
    finished_products = Item.objects.filter(
        item_type=ItemType.FINISHED_PRODUCT,
        is_active=True
    ).exclude(code__startswith='FP')
    
    print(f"\nFinished products after cleanup: {finished_products.count()}")
    
    if finished_products.count() == 0:
        convert_existing_items_to_finished_products()
    
    # Final check
    print("\n=== Final Status ===")
    final_finished_products = Item.objects.filter(
        item_type=ItemType.FINISHED_PRODUCT,
        is_active=True
    )
    print(f"Total finished products: {final_finished_products.count()}")
    for item in final_finished_products:
        print(f"- {item.code}: {item.name}")