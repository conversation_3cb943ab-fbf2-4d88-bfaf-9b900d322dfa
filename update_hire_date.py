#!/usr/bin/env python
import os
import sys
import django
from datetime import date, timedelta

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from hr.models import Employee

def update_hire_date():
    """تحديث تاريخ تعيين موظف واحد ليكون مؤهلاً للترقية"""
    print("=== تحديث تاريخ التعيين ===")
    
    # الحصول على أول موظف نشط
    employee = Employee.objects.filter(status='ACTIVE').first()
    
    if employee:
        print(f"الموظف المحدد: {employee.full_name} (ID: {employee.id})")
        print(f"تاريخ التعيين الحالي: {employee.hire_date}")
        
        # تحديث تاريخ التعيين ليكون قبل سنتين
        new_hire_date = date.today() - timedelta(days=2*365)
        employee.hire_date = new_hire_date
        employee.save()
        
        print(f"تم تحديث تاريخ التعيين إلى: {new_hire_date}")
        
        # حساب سنوات الخبرة الجديدة
        years_of_experience = (date.today() - new_hire_date).days / 365.25
        print(f"سنوات الخبرة الجديدة: {round(years_of_experience, 1)}")
        
        if years_of_experience >= 1.5:
            print("✅ الموظف الآن مؤهل للترقية!")
        else:
            print("❌ الموظف ما زال غير مؤهل للترقية")
    else:
        print("❌ لا يوجد موظفين نشطين")

if __name__ == '__main__':
    update_hire_date() 