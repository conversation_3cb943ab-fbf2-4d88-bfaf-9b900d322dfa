// Gemini AI Assistant Configuration
// ملف إعدادات مساعد الذكاء الاصطناعي جيميني

class GeminiAI {
    constructor() {
        this.apiKey = null; // يجب تعيين مفتاح API الحقيقي
        this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        this.isInitialized = false;
        this.conversationHistory = [];
    }

    // تهيئة Gemini AI
    async initialize(apiKey) {
        this.apiKey = apiKey;
        this.isInitialized = true;
        console.log('Gemini AI initialized successfully');
    }

    // استدعاء Gemini API الحقيقي
    async callRealGeminiAPI(message) {
        if (!this.isInitialized || !this.apiKey) {
            throw new Error('Gemini AI not initialized. Please provide API key.');
        }

        try {
            const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: this.buildContextualPrompt(message)
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 1024,
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.candidates[0].content.parts[0].text;
            
            // حفظ المحادثة
            this.conversationHistory.push({
                user: message,
                ai: aiResponse,
                timestamp: new Date()
            });

            return aiResponse;

        } catch (error) {
            console.error('Error calling Gemini API:', error);
            throw error;
        }
    }

    // بناء prompt مع السياق
    buildContextualPrompt(message) {
        const systemPrompt = `أنت مساعد ذكي متخصص في أنظمة المحاسبة والإدارة المالية. 
        تعمل في نظام محاسبة شامل يحتوي على:
        - إدارة المبيعات والفواتير
        - إدارة المشتريات والموردين
        - إدارة المخازن والمخزون
        - إدارة الخزينة والبنوك
        - التقارير المالية
        - إدارة العملاء والموردين
        - أسعار العملات
        - إدارة الفروع
        - الأصول الثابتة
        - الموارد البشرية

        يرجى الإجابة باللغة العربية وتقديم إجابات مفيدة ومفصلة حول استخدام النظام.`;

        return `${systemPrompt}\n\nسؤال المستخدم: ${message}`;
    }

    // الحصول على تاريخ المحادثة
    getConversationHistory() {
        return this.conversationHistory;
    }

    // مسح تاريخ المحادثة
    clearHistory() {
        this.conversationHistory = [];
    }

    // ردود ذكية محلية (fallback)
    getSmartLocalResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        const responses = {
            'فاتورة': `لإضافة فاتورة جديدة:
1. اذهب إلى قسم "المبيعات" أو "المشتريات"
2. اضغط على "إضافة فاتورة جديدة"
3. املأ بيانات العميل أو المورد
4. أضف الأصناف والكميات والأسعار
5. احفظ الفاتورة وطباعتها إذا لزم الأمر

هل تحتاج مساعدة في خطوة معينة؟`,

            'عملات': `أسعار العملات الحالية:
💵 الدولار الأمريكي: 49.75 ج.م
💶 اليورو: 53.45 ج.م  
💷 الجنيه الإسترليني: 63.20 ج.م
🇸🇦 الريال السعودي: 13.27 ج.م
🇦🇪 الدرهم الإماراتي: 13.55 ج.م

يتم تحديث الأسعار تلقائياً كل 15 دقيقة من البنوك المصرية.`,

            'تقرير': `التقارير المتاحة في النظام:
📊 تقارير المبيعات: المبيعات اليومية، الشهرية، السنوية
📈 تقارير المشتريات: المشتريات والموردين
💰 تقارير الخزينة: المقبوضات والمدفوعات
📋 تقارير المخزون: حركة المخزون والجرد
🏦 تقارير البنوك: كشوف الحسابات
👥 تقارير العملاء: أرصدة وحركات العملاء

أي تقرير تحتاج تحديداً؟`,

            'مخزون': `إدارة المخزون تشمل:
📦 إضافة أصناف جديدة
📊 متابعة الكميات والأرصدة
🔄 حركات الإدخال والإخراج
📋 عمليات الجرد والتسوية
⚠️ تنبيهات الحد الأدنى للمخزون
📈 تقارير حركة المخزون

ما العملية التي تريد القيام بها؟`,

            'عميل': `إدارة العملاء تتضمن:
👤 إضافة عملاء جدد
💰 متابعة أرصدة العملاء
📋 سجل المعاملات والفواتير
📞 بيانات الاتصال والعناوين
📊 تقارير حسابات العملاء
💳 إدارة طرق الدفع

هل تريد إضافة عميل جديد أم متابعة عميل موجود؟`,

            'مورد': `إدارة الموردين تشمل:
🏢 إضافة موردين جدد
💰 متابعة أرصدة الموردين
📋 سجل المشتريات والفواتير
📞 بيانات الاتصال والعناوين
📊 تقارير حسابات الموردين
💳 إدارة طرق الدفع للموردين

ما العملية المطلوبة مع الموردين؟`
        };

        // البحث عن الكلمات المفتاحية
        for (const [keyword, response] of Object.entries(responses)) {
            if (lowerMessage.includes(keyword)) {
                return response;
            }
        }

        // رد افتراضي
        return `أفهم أنك تسأل عن "${message}". 

كمساعد ذكي لنظام المحاسبة، يمكنني مساعدتك في:
• إدارة الفواتير والمبيعات
• العمليات المالية والمصرفية  
• إدارة المخزون والمخازن
• إنشاء التقارير المالية
• أسعار العملات والتحويلات
• إدارة العملاء والموردين

هل يمكنك توضيح سؤالك أكثر لأتمكن من مساعدتك بشكل أفضل؟`;
    }
}

// إنشاء مثيل عام من Gemini AI
window.geminiAI = new GeminiAI();

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiAI;
}
