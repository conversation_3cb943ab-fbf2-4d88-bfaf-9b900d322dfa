{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-times text-danger me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة طلبات الإجازات للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printDataList('قائمة طلبات الإجازات')">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLeaveModal">
                        <i class="fas fa-plus me-2"></i>
                        طلب إجازة جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ total_requests }}</h3>
                    <p class="mb-0">إجمالي الطلبات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ pending_requests }}</h3>
                    <p class="mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ approved_requests }}</h3>
                    <p class="mb-0">معتمدة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ rejected_requests }}</h3>
                    <p class="mb-0">مرفوضة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Requests Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            قائمة طلبات الإجازات
                        </h5>
                        <div>
                            <a href="{% url 'hr:leave_request_create' %}" class="btn btn-primary me-2">
                                <i class="fas fa-plus me-2"></i>
                                طلب إجازة جديد
                            </a>
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addLeaveModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة سريعة
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in leave_requests %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ request.employee.full_name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ request.leave_type.name }}</span>
                                    </td>
                                    <td>{{ request.from_date|date:"Y-m-d" }}</td>
                                    <td>{{ request.to_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ request.duration_display }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ request.status_badge_class }}">{{ request.get_status_display }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:leave_request_detail' request.id %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if request.status == 'PENDING' %}
                                            <button class="btn btn-sm btn-outline-success" title="اعتماد" onclick="approveRequest({{ request.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" title="رفض" onclick="rejectRequest({{ request.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-info" title="تعديل" onclick="editRequest({{ request.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3 d-block"></i>
                                        لا توجد طلبات إجازات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Leave Request Modal -->
<div class="modal fade" id="addLeaveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">طلب إجازة جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'hr:leave_request_create' %}" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                <select name="employee" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.employee_number }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الإجازة <span class="text-danger">*</span></label>
                                <select name="leave_type" class="form-select" required>
                                    <option value="">اختر نوع الإجازة</option>
                                    {% for leave_type in leave_types %}
                                    <option value="{{ leave_type.id }}">{{ leave_type.name }} ({{ leave_type.allowed_days }} يوم)</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من تاريخ <span class="text-danger">*</span></label>
                                <input type="date" name="from_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى تاريخ <span class="text-danger">*</span></label>
                                <input type="date" name="to_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب الإجازة <span class="text-danger">*</span></label>
                        <textarea name="reason" class="form-control" rows="3" required placeholder="اكتب سبب الإجازة بالتفصيل"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_emergency" id="is_emergency">
                                    <label class="form-check-label" for="is_emergency">
                                        إجازة طارئة
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مرفقات (اختياري)</label>
                                <input type="file" name="attachment" class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <small class="text-muted">يمكن رفع ملفات PDF, Word, أو صور</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Request Modal -->
<div class="modal fade" id="viewRequestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// اعتماد الطلب
function approveRequest(requestId) {
    if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
        // هنا يمكن إضافة كود لاعتماد الطلب عبر AJAX
        window.location.href = `/hr/leave-requests/${requestId}/approve/`;
    }
}

// رفض الطلب
function rejectRequest(requestId) {
    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
        // هنا يمكن إضافة كود لرفض الطلب عبر AJAX
        window.location.href = `/hr/leave-requests/${requestId}/reject/`;
    }
}

// تعديل الطلب
function editRequest(requestId) {
    window.location.href = `/hr/leave-requests/${requestId}/edit/`;
}

// حساب عدد الأيام تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const fromDateInput = document.querySelector('input[name="from_date"]');
    const toDateInput = document.querySelector('input[name="to_date"]');
    
    function calculateDays() {
        if (fromDateInput.value && toDateInput.value) {
            const fromDate = new Date(fromDateInput.value);
            const toDate = new Date(toDateInput.value);
            const diffTime = Math.abs(toDate - fromDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            
            // يمكن إضافة حقل لعرض عدد الأيام
            console.log(`عدد الأيام: ${diffDays}`);
        }
    }
    
    fromDateInput.addEventListener('change', calculateDays);
    toDateInput.addEventListener('change', calculateDays);
});
</script>
{% endblock %}
