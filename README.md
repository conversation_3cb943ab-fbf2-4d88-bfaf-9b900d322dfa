# 🏢 نظام حسابات أوساريك - Osaric Accounts System

نظام إدارة حسابات مالية ومخزون شامل ومتطور باللغة العربية

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template/django)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Django](https://img.shields.io/badge/Django-5.2+-green.svg)](https://djangoproject.com)

---

## 🚀 **احصل على رابط موثق مجاني الآن!**

### **👆 انقر على زر "Deploy on Railway" أعلاه**

**أو استخدم الرابط المباشر:**
```
https://railway.app/new/template/django
```

**بيانات الدخول بعد النشر:**
```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
🌐 رابطك: https://[project-name].up.railway.app
```

## المميزات الرئيسية

### 🏢 إدارة شاملة للأعمال
- **التعريفات الأساسية**: المخازن، الأصناف، العملات، البنوك، الخزائن
- **إدارة المبيعات**: فواتير المبيعات، المرتجعات، عروض الأسعار
- **إدارة المشتريات**: فواتير المشتريات، المرتجعات، أوامر الشراء
- **إدارة المخزون**: حركات المخزون، التحويلات، التسويات، الجرد
- **إدارة البنوك**: المعاملات البنكية، التسويات، الشيكات
- **إدارة الخزينة**: القبض والدفع، المصروفات والإيرادات

### 📊 لوحة تحكم متقدمة
- **إحصائيات فورية**: مبيعات اليوم والشهر، أرصدة البنوك والخزائن
- **رسوم بيانية تفاعلية**: اتجاهات المبيعات، مقارنات شهرية
- **تنبيهات ذكية**: الأصناف منخفضة المخزون، المعاملات المعلقة
- **أنشطة حديثة**: آخر المبيعات والمشتريات وحركات المخزون

### 🌐 واجهة عربية متجاوبة
- **دعم كامل للغة العربية** مع اتجاه RTL
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **واجهة حديثة** باستخدام Bootstrap 5
- **تجربة مستخدم سلسة** مع انتقالات سلسة

## التقنيات المستخدمة

### Backend
- **Python 3.13** - لغة البرمجة الأساسية
- **Django 5.2** - إطار العمل الرئيسي
- **Django REST Framework** - لبناء APIs
- **SQLite** - قاعدة البيانات (قابلة للتغيير إلى PostgreSQL/MySQL)

### Frontend
- **HTML5 & CSS3** - هيكل وتصميم الصفحات
- **Bootstrap 5 RTL** - إطار العمل للتصميم المتجاوب
- **JavaScript & Chart.js** - التفاعل والرسوم البيانية
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - خط عربي جميل

## التثبيت والتشغيل

### المتطلبات
```bash
Python 3.8+
pip (مدير الحزم)
Git
```

### التثبيت السريع (للتطوير)

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd osaric-accounts
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **تشغيل الهجرات**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **إنشاء البيانات التجريبية (اختياري)**
```bash
python create_sample_data.py
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح المتصفح**
```
http://127.0.0.1:8000/
```

### التثبيت باستخدام Docker

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd osaric-accounts
```

2. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتعديل ملف .env حسب احتياجاتك
```

3. **تشغيل Docker Compose**
```bash
docker-compose up -d
```

4. **تشغيل الهجرات**
```bash
docker-compose exec web python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
docker-compose exec web python manage.py createsuperuser
```

6. **إنشاء البيانات التجريبية**
```bash
docker-compose exec web python create_sample_data.py
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **البريد الإلكتروني**: <EMAIL>

## هيكل المشروع

```
osaric_accounts/
├── accounts/           # إدارة المستخدمين والصلاحيات
├── definitions/        # التعريفات الأساسية
├── inventory/          # إدارة المخزون
├── sales/             # إدارة المبيعات
├── purchases/         # إدارة المشتريات
├── banking/           # إدارة البنوك
├── treasury/          # إدارة الخزينة
├── dashboard/         # لوحة التحكم
├── templates/         # قوالب HTML
├── static/           # ملفات CSS/JS/Images
├── media/            # ملفات المستخدمين
└── locale/           # ملفات الترجمة
```

## الوحدات الرئيسية

### 1. التعريفات (Definitions)
- العملات والأسعار
- المخازن ومواقعها
- فئات ووحدات الأصناف
- الأصناف وتفاصيلها
- البنوك والحسابات
- الخزائن والمسؤولين

### 2. إدارة المخزون (Inventory)
- حركات الإدخال والإخراج
- التحويلات بين المخازن
- تسويات المخزون
- تتبع الأرصدة
- تقارير المخزون

### 3. المبيعات (Sales)
- إدارة العملاء
- فواتير المبيعات
- مرتجعات المبيعات
- عروض الأسعار
- تقارير المبيعات

### 4. المشتريات (Purchases)
- إدارة الموردين
- فواتير المشتريات
- مرتجعات المشتريات
- أوامر الشراء
- تقارير المشتريات

### 5. البنوك (Banking)
- المعاملات البنكية
- تسوية البنوك
- إدارة الشيكات
- دفاتر الشيكات

### 6. الخزينة (Treasury)
- إيصالات القبض والدفع
- المصروفات والإيرادات
- التحويلات بين الخزائن
- تقارير الخزينة

## المميزات التقنية

### الأمان
- **مصادقة المستخدمين** مع نظام صلاحيات متقدم
- **حماية CSRF** لجميع النماذج
- **تشفير كلمات المرور** باستخدام Django
- **تسجيل الأنشطة** لتتبع العمليات

### الأداء
- **استعلامات محسنة** لقاعدة البيانات
- **تخزين مؤقت** للبيانات المتكررة
- **ضغط الملفات الثابتة** لتحسين السرعة
- **تحميل تدريجي** للبيانات الكبيرة

### قابلية التوسع
- **هيكل معياري** يسهل إضافة وحدات جديدة
- **APIs جاهزة** للتكامل مع أنظمة أخرى
- **قاعدة بيانات قابلة للتغيير** (SQLite/PostgreSQL/MySQL)
- **دعم متعدد اللغات** جاهز للتوسع

## الدعم والمساهمة

### الإبلاغ عن المشاكل
إذا واجهت أي مشكلة، يرجى إنشاء issue جديد مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- لقطات شاشة (إن أمكن)
- معلومات البيئة

### المساهمة في التطوير
نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال

- **المطور**: فريق أوساريك للتطوير
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.osaric.com

---

**ملاحظة**: هذا النظام في مرحلة التطوير ومناسب للاستخدام التجريبي. للاستخدام في بيئة الإنتاج، يرجى مراجعة إعدادات الأمان وقاعدة البيانات.
