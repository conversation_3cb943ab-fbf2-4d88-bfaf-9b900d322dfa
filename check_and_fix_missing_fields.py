#!/usr/bin/env python
"""
Check and fix all missing fields in ManufacturingOrder model
"""

import os
import sys
import django
import sqlite3
import re

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def get_model_fields():
    """Extract all fields from the ManufacturingOrder model"""
    model_file = 'd:/osama/inventory/models.py'
    
    with open(model_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find ManufacturingOrder class
    class_match = re.search(r'class ManufacturingOrder\(.*?\):(.*?)(?=class|\Z)', content, re.DOTALL)
    if not class_match:
        return []
    
    class_content = class_match.group(1)
    
    # Extract field definitions
    field_pattern = r'(\w+)\s*=\s*models\.(\w+Field)\('
    fields = re.findall(field_pattern, class_content)
    
    return [field[0] for field in fields if not field[0].startswith('_')]

def get_database_columns():
    """Get all columns from the database table"""
    db_path = settings.DATABASES['default']['NAME']
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("PRAGMA table_info(inventory_manufacturingorder)")
    columns = cursor.fetchall()
    
    conn.close()
    
    return [col[1] for col in columns]

def find_missing_fields():
    """Find fields that exist in model but not in database"""
    model_fields = get_model_fields()
    db_columns = get_database_columns()
    
    # Exclude base model fields and foreign key fields
    exclude_fields = ['id', 'created_at', 'updated_at', 'is_active', 'created_by_id', 'updated_by_id']
    
    missing_fields = []
    for field in model_fields:
        if field not in db_columns and field not in exclude_fields:
            missing_fields.append(field)
    
    return missing_fields

def create_migration_for_missing_fields(missing_fields):
    """Create a migration file for missing fields"""
    if not missing_fields:
        print("No missing fields found!")
        return
    
    migration_content = '''# Generated automatically to add missing fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0025_add_quality_approved_field'),
    ]

    operations = [
'''
    
    for field in missing_fields:
        migration_content += f'''        migrations.AddField(
            model_name='manufacturingorder',
            name='{field}',
            field=models.BooleanField(default=False, verbose_name='{field}'),
        ),
'''
    
    migration_content += '''    ]
'''
    
    migration_file = 'd:/osama/inventory/migrations/0026_add_remaining_missing_fields.py'
    with open(migration_file, 'w', encoding='utf-8') as f:
        f.write(migration_content)
    
    print(f"Created migration file: {migration_file}")
    return migration_file

def main():
    print("Checking for missing fields in ManufacturingOrder...")
    
    model_fields = get_model_fields()
    db_columns = get_database_columns()
    missing_fields = find_missing_fields()
    
    print(f"Model fields: {len(model_fields)}")
    print(f"Database columns: {len(db_columns)}")
    print(f"Missing fields: {missing_fields}")
    
    if missing_fields:
        print(f"Found {len(missing_fields)} missing fields:")
        for field in missing_fields:
            print(f"  - {field}")
        
        migration_file = create_migration_for_missing_fields(missing_fields)
        if migration_file:
            print(f"\nRun: python manage.py migrate inventory")
    else:
        print("All fields are present in the database!")

if __name__ == '__main__':
    main()