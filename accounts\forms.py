from django import forms
from django.contrib.auth.models import User, Group, Permission
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import UserProfile, CustomPermissionGroup, UserPermission


class CustomUserCreationForm(UserCreationForm):
    """نموذج إنشاء مستخدم جديد محسن"""
    email = forms.EmailField(required=True, label='البريد الإلكتروني')
    first_name = forms.CharField(max_length=30, required=True, label='الاسم الأول')
    last_name = forms.CharField(max_length=30, required=True, label='اسم العائلة')
    
    # حقول إضافية من UserProfile
    phone = forms.CharField(max_length=20, required=False, label='رقم الهاتف')
    department = forms.CharField(max_length=100, required=False, label='القسم')
    position = forms.CharField(max_length=100, required=False, label='المنصب')
    hire_date = forms.DateField(required=False, label='تاريخ التوظيف', widget=forms.DateInput(attrs={'type': 'date'}))
    salary = forms.DecimalField(max_digits=10, decimal_places=2, required=False, label='الراتب')
    
    # صلاحيات
    is_staff = forms.BooleanField(required=False, label='موظف إداري')
    is_superuser = forms.BooleanField(required=False, label='مدير النظام')
    groups = forms.ModelMultipleChoiceField(
        queryset=Group.objects.all(),
        required=False,
        label='المجموعات',
        widget=forms.CheckboxSelectMultiple
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2', 
                 'is_staff', 'is_superuser', 'groups')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص الحقول
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'form-control'})
        
        # تخصيص labels
        self.fields['username'].label = 'اسم المستخدم'
        self.fields['password1'].label = 'كلمة المرور'
        self.fields['password2'].label = 'تأكيد كلمة المرور'

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.is_staff = self.cleaned_data.get('is_staff', False)
        user.is_superuser = self.cleaned_data.get('is_superuser', False)
        
        if commit:
            user.save()
            
            # إنشاء UserProfile
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.phone = self.cleaned_data.get('phone', '')
            profile.department = self.cleaned_data.get('department', '')
            profile.position = self.cleaned_data.get('position', '')
            profile.hire_date = self.cleaned_data.get('hire_date')
            profile.salary = self.cleaned_data.get('salary')
            profile.save()
            
            # إضافة المجموعات
            groups = self.cleaned_data.get('groups')
            if groups:
                user.groups.set(groups)
        
        return user


class CustomUserChangeForm(UserChangeForm):
    """نموذج تعديل المستخدم محسن"""
    email = forms.EmailField(required=True, label='البريد الإلكتروني')
    first_name = forms.CharField(max_length=30, required=True, label='الاسم الأول')
    last_name = forms.CharField(max_length=30, required=True, label='اسم العائلة')
    
    # حقول إضافية من UserProfile
    phone = forms.CharField(max_length=20, required=False, label='رقم الهاتف')
    department = forms.CharField(max_length=100, required=False, label='القسم')
    position = forms.CharField(max_length=100, required=False, label='المنصب')
    hire_date = forms.DateField(required=False, label='تاريخ التوظيف', widget=forms.DateInput(attrs={'type': 'date'}))
    salary = forms.DecimalField(max_digits=10, decimal_places=2, required=False, label='الراتب')
    address = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False, label='العنوان')
    notes = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False, label='ملاحظات')
    
    # صلاحيات
    is_active = forms.BooleanField(required=False, label='نشط')
    is_staff = forms.BooleanField(required=False, label='موظف إداري')
    is_superuser = forms.BooleanField(required=False, label='مدير النظام')
    groups = forms.ModelMultipleChoiceField(
        queryset=Group.objects.all(),
        required=False,
        label='المجموعات',
        widget=forms.CheckboxSelectMultiple
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'is_active', 
                 'is_staff', 'is_superuser', 'groups')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إزالة حقل كلمة المرور
        if 'password' in self.fields:
            del self.fields['password']
        
        # تخصيص الحقول
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'form-control'})
        
        # تحميل بيانات UserProfile إذا كانت موجودة
        if self.instance and hasattr(self.instance, 'profile'):
            profile = self.instance.profile
            self.fields['phone'].initial = profile.phone
            self.fields['department'].initial = profile.department
            self.fields['position'].initial = profile.position
            self.fields['hire_date'].initial = profile.hire_date
            self.fields['salary'].initial = profile.salary
            self.fields['address'].initial = profile.address
            self.fields['notes'].initial = profile.notes

    def save(self, commit=True):
        user = super().save(commit=False)
        
        if commit:
            user.save()
            
            # تحديث UserProfile
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.phone = self.cleaned_data.get('phone', '')
            profile.department = self.cleaned_data.get('department', '')
            profile.position = self.cleaned_data.get('position', '')
            profile.hire_date = self.cleaned_data.get('hire_date')
            profile.salary = self.cleaned_data.get('salary')
            profile.address = self.cleaned_data.get('address', '')
            profile.notes = self.cleaned_data.get('notes', '')
            profile.save()
            
            # تحديث المجموعات
            groups = self.cleaned_data.get('groups')
            if groups is not None:
                user.groups.set(groups)
        
        return user


class UserPermissionForm(forms.ModelForm):
    """نموذج إدارة صلاحيات المستخدم"""
    
    class Meta:
        model = UserPermission
        fields = ['user', 'module', 'permission_type', 'is_granted', 'notes']
        widgets = {
            'user': forms.Select(attrs={'class': 'form-control'}),
            'module': forms.Select(attrs={'class': 'form-control'}),
            'permission_type': forms.Select(attrs={'class': 'form-control'}),
            'is_granted': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['user'].label = 'المستخدم'
        self.fields['module'].label = 'الوحدة'
        self.fields['permission_type'].label = 'نوع الصلاحية'
        self.fields['is_granted'].label = 'ممنوحة'
        self.fields['notes'].label = 'ملاحظات'


class BulkPermissionForm(forms.Form):
    """نموذج منح صلاحيات متعددة"""
    users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_active=True),
        widget=forms.CheckboxSelectMultiple,
        label='المستخدمين'
    )
    modules = forms.MultipleChoiceField(
        choices=UserPermission.MODULE_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        label='الوحدات'
    )
    permissions = forms.MultipleChoiceField(
        choices=UserPermission.PERMISSION_TYPES,
        widget=forms.CheckboxSelectMultiple,
        label='الصلاحيات'
    )
    is_granted = forms.BooleanField(
        initial=True,
        required=False,
        label='منح الصلاحيات'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if hasattr(field.widget, 'attrs'):
                field.widget.attrs.update({'class': 'form-check-input'})


class CustomPermissionGroupForm(forms.ModelForm):
    """نموذج إدارة مجموعات الصلاحيات"""
    
    class Meta:
        model = CustomPermissionGroup
        fields = ['name', 'description', 'permissions', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'permissions': forms.CheckboxSelectMultiple(),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].label = 'اسم المجموعة'
        self.fields['description'].label = 'الوصف'
        self.fields['permissions'].label = 'الصلاحيات'
        self.fields['is_active'].label = 'نشط'


class PasswordResetForm(forms.Form):
    """نموذج إعادة تعيين كلمة المرور"""
    user = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='المستخدم'
    )
    new_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير وصغير ورقم'
        }),
        label='كلمة المرور الجديدة',
        min_length=8,
        help_text='يجب أن تحتوي كلمة المرور على: 8 أحرف على الأقل، حرف كبير واحد على الأقل (A-Z)، حرف صغير واحد على الأقل (a-z)، ورقم واحد على الأقل (0-9)'
    )
    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control'}),
        label='تأكيد كلمة المرور'
    )

    def clean(self):
        cleaned_data = super().clean()
        new_password = cleaned_data.get('new_password')
        confirm_password = cleaned_data.get('confirm_password')

        if new_password and confirm_password:
            if new_password != confirm_password:
                raise forms.ValidationError('كلمات المرور المدخلة غير متطابقة. يرجى التأكد من إدخال نفس كلمة المرور في الحقلين.')
            
            # التحقق من قوة كلمة المرور
            if len(new_password) < 8:
                raise forms.ValidationError('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل.')
                
            if not any(char.isdigit() for char in new_password):
                raise forms.ValidationError('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل.')
                
            if not any(char.isupper() for char in new_password):
                raise forms.ValidationError('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل (A-Z).')
                
            if not any(char.islower() for char in new_password):
                raise forms.ValidationError('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل (a-z).')

        return cleaned_data
