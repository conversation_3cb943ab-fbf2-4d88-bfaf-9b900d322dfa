# Generated by Django 5.2.2 on 2025-07-04 12:41

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0002_person_is_active_employee'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود القسم')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_departments', to='hr.department', verbose_name='القسم الرئيسي')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('employee_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('hire_date', models.DateField(verbose_name='تاريخ التعيين')),
                ('contract_start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ بداية العقد')),
                ('contract_end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية العقد')),
                ('termination_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الخدمة')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('INACTIVE', 'غير نشط'), ('TERMINATED', 'منتهي الخدمة'), ('SUSPENDED', 'موقوف')], default='ACTIVE', max_length=20, verbose_name='حالة الموظف')),
                ('current_salary', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المرتب الحالي')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.department', verbose_name='القسم')),
                ('person', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to='definitions.person', verbose_name='بيانات الشخص')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفين',
                'ordering': ['employee_number'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.employee', verbose_name='مدير القسم'),
        ),
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_type', models.CharField(choices=[('PERMANENT', 'دائم'), ('TEMPORARY', 'مؤقت'), ('PART_TIME', 'دوام جزئي'), ('CONSULTANT', 'استشاري'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع العقد')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية العقد')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية العقد')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='الراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='عقد نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('contract_file', models.FileField(blank=True, help_text='يسمح فقط بملفات PDF أو صور JPG/PNG', null=True, upload_to='contracts/', verbose_name='ملف العقد (PDF أو صورة)')),
                ('archived', models.BooleanField(default=False, verbose_name='مؤرشف')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contracts', to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'عقد عمل',
                'verbose_name_plural': 'عقود العمل',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='AllowanceDeduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('type', models.CharField(choices=[('allowance', 'إضافة'), ('deduction', 'خصم')], max_length=10, verbose_name='النوع')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='القيمة')),
                ('date', models.DateField(verbose_name='تاريخ الخصم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='allowance_deductions', to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إضافة/خصم',
                'verbose_name_plural': 'الإضافات والخصومات',
                'ordering': ['-date', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeSalary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='الراتب الأساسي')),
                ('housing_allowance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='بدل السكن')),
                ('transportation_allowance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='بدل النقل')),
                ('food_allowance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='بدل الطعام')),
                ('other_allowances', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='إضافات أخرى')),
                ('social_insurance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='التأمينات الاجتماعية')),
                ('tax_deduction', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='الخصم الضريبي')),
                ('other_deductions', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='خصومات أخرى')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=6, verbose_name='ساعات العمل الإضافي')),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=Decimal('1.50'), max_digits=5, verbose_name='معدل العمل الإضافي')),
                ('overtime_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='مبلغ العمل الإضافي')),
                ('total_allowances', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='إجمالي الإضافات')),
                ('total_deductions', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='إجمالي الخصومات')),
                ('net_salary', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='صافي المرتب')),
                ('effective_date', models.DateField(verbose_name='تاريخ سريان المرتب')),
                ('is_current', models.BooleanField(default=True, verbose_name='المرتب الحالي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_details', to='hr.employee', verbose_name='الموظف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تفاصيل مرتب الموظف',
                'verbose_name_plural': 'تفاصيل مرتبات الموظفين',
                'ordering': ['-effective_date'],
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('from_date', models.DateField(verbose_name='من تاريخ')),
                ('to_date', models.DateField(verbose_name='إلى تاريخ')),
                ('days_count', models.IntegerField(verbose_name='عدد الأيام')),
                ('reason', models.TextField(verbose_name='سبب الإجازة')),
                ('status', models.CharField(choices=[('PENDING', 'قيد المراجعة'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='حالة الطلب')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('is_emergency', models.BooleanField(default=False, verbose_name='إجازة طارئة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='leave_attachments/', verbose_name='مرفقات')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_leaves', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.employee', verbose_name='الموظف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'طلب إجازة',
                'verbose_name_plural': 'طلبات الإجازات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LeaveCalendar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الحدث')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('event_date', models.DateField(verbose_name='تاريخ الحدث')),
                ('event_type', models.CharField(choices=[('START', 'بداية الإجازة'), ('END', 'نهاية الإجازة'), ('FULL_DAY', 'يوم كامل')], default='FULL_DAY', max_length=20, verbose_name='نوع الحدث')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='لون الحدث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to='hr.employee', verbose_name='الموظف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to='hr.leaverequest', verbose_name='طلب الإجازة')),
            ],
            options={
                'verbose_name': 'حدث تقويم الإجازات',
                'verbose_name_plural': 'أحداث تقويم الإجازات',
                'ordering': ['event_date'],
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود نوع الإجازة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الإجازة')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('allowed_days', models.IntegerField(default=0, verbose_name='عدد الأيام المسموحة سنوياً')),
                ('category', models.CharField(choices=[('ANNUAL', 'إجازة سنوية'), ('SICK', 'إجازة مرضية'), ('EMERGENCY', 'إجازة طارئة'), ('MATERNITY', 'إجازة أمومة'), ('PATERNITY', 'إجازة أبوة'), ('STUDY', 'إجازة دراسية'), ('PILGRIMAGE', 'إجازة حج'), ('OTHER', 'أخرى')], default='OTHER', max_length=20, verbose_name='فئة الإجازة')),
                ('is_paid', models.BooleanField(default=True, verbose_name='إجازة مدفوعة')),
                ('requires_approval', models.BooleanField(default=True, verbose_name='تتطلب موافقة')),
                ('can_carry_forward', models.BooleanField(default=False, verbose_name='يمكن ترحيلها للعام التالي')),
                ('max_carry_forward_days', models.IntegerField(default=0, verbose_name='أقصى أيام للترحيل')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='لون الإجازة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نوع إجازة',
                'verbose_name_plural': 'أنواع الإجازات',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='leaverequest',
            name='leave_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='hr.leavetype', verbose_name='نوع الإجازة'),
        ),
        migrations.CreateModel(
            name='Overtime',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('hours', models.DecimalField(decimal_places=2, max_digits=4, verbose_name='عدد الساعات')),
                ('hourly_rate', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='سعر الساعة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('status', models.CharField(choices=[('PENDING', 'قيد المراجعة'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='overtimes', to='hr.employee', verbose_name='الموظف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'عمل إضافي',
                'verbose_name_plural': 'سجلات العمل الإضافي',
                'ordering': ['-date', 'employee'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المنصب')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنصب')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='hr.department', verbose_name='القسم')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'منصب',
                'verbose_name_plural': 'المناصب',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='position',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.position', verbose_name='المنصب'),
        ),
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('from_salary', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='الراتب السابق')),
                ('to_salary', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='الراتب الجديد')),
                ('promotion_date', models.DateField(verbose_name='تاريخ الترقية')),
                ('effective_date', models.DateField(verbose_name='تاريخ سريان الترقية')),
                ('reason', models.TextField(verbose_name='سبب الترقية')),
                ('status', models.CharField(choices=[('PENDING', 'قيد المراجعة'), ('APPROVED', 'معتمدة'), ('REJECTED', 'مرفوضة'), ('CANCELLED', 'ملغية')], default='PENDING', max_length=20, verbose_name='حالة الترقية')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='promotion_attachments/', verbose_name='مرفقات')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_promotions', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_promotions', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotions', to='hr.employee', verbose_name='الموظف')),
                ('from_position', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotions_from', to='hr.position', verbose_name='المنصب السابق')),
                ('to_position', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='promotions_to', to='hr.position', verbose_name='المنصب الجديد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_promotions', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'ترقية',
                'verbose_name_plural': 'الترقيات',
                'ordering': ['-promotion_date', 'employee'],
            },
        ),
        migrations.CreateModel(
            name='Qualification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المؤهل')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المؤهل')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('level', models.CharField(blank=True, choices=[('PRIMARY', 'ابتدائي'), ('PREP', 'إعدادي'), ('SECONDARY', 'ثانوي'), ('DIPLOMA', 'دبلوم'), ('BACHELOR', 'بكالوريوس'), ('MASTER', 'ماجستير'), ('PHD', 'دكتوراه')], max_length=20, verbose_name='مستوى المؤهل')),
                ('duration', models.CharField(blank=True, max_length=50, verbose_name='مدة الدراسة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مؤهل',
                'verbose_name_plural': 'المؤهلات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SalaryAddition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('addition_type', models.CharField(choices=[('HOUSING', 'بدل سكن'), ('TRANSPORTATION', 'بدل نقل'), ('FOOD', 'بدل طعام'), ('SPECIAL', 'بدل خاص'), ('PERFORMANCE', 'بدل أداء'), ('OVERTIME', 'بدل عمل إضافي'), ('OTHER', 'إضافات أخرى')], max_length=20, verbose_name='نوع الإضافة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إضافة مرتب',
                'verbose_name_plural': 'إضافات المرتبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalarySystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود النظام')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النظام')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('system_type', models.CharField(choices=[('MONTHLY', 'شهري'), ('WEEKLY', 'أسبوعي'), ('DAILY', 'يومي'), ('HOURLY', 'بالساعة'), ('PIECE_RATE', 'بالقطعة')], default='MONTHLY', max_length=20, verbose_name='نوع النظام')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الراتب الأساسي')),
                ('include_overtime', models.BooleanField(default=True, verbose_name='يشمل العمل الإضافي')),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=1.5, max_digits=5, verbose_name='معدل العمل الإضافي')),
                ('social_insurance_rate', models.DecimalField(decimal_places=2, default=14.0, max_digits=5, verbose_name='نسبة التأمينات الاجتماعية (%)')),
                ('tax_exemption', models.DecimalField(decimal_places=2, default=9000, max_digits=12, verbose_name='الإعفاء الضريبي')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نظام صرف المرتب',
                'verbose_name_plural': 'أنظمة صرف المرتبات',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='salary_system',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.salarysystem', verbose_name='نظام المرتب'),
        ),
        migrations.CreateModel(
            name='Shift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الوردية')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوردية')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('duration_hours', models.DecimalField(decimal_places=2, default=8.0, max_digits=4, verbose_name='مدة الوردية (ساعات)')),
                ('shift_type', models.CharField(choices=[('MORNING', 'صباحية'), ('EVENING', 'مسائية'), ('NIGHT', 'ليلية'), ('FLEXIBLE', 'مرنة'), ('PART_TIME', 'دوام جزئي')], default='MORNING', max_length=20, verbose_name='نوع الوردية')),
                ('include_break', models.BooleanField(default=True, verbose_name='يشمل استراحة')),
                ('break_duration', models.IntegerField(default=60, verbose_name='مدة الاستراحة (دقائق)')),
                ('overtime_allowed', models.BooleanField(default=True, verbose_name='يسمح بالعمل الإضافي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'وردية',
                'verbose_name_plural': 'الورديات',
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='Termination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('termination_type', models.CharField(choices=[('RESIGNATION', 'استقالة'), ('DISMISSAL', 'فصل من الخدمة'), ('RETIREMENT', 'تقاعد'), ('CONTRACT_END', 'انتهاء العقد'), ('DEATH', 'وفاة'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع إنهاء الخدمة')),
                ('reason', models.TextField(verbose_name='سبب إنهاء الخدمة')),
                ('notice_period', models.IntegerField(default=30, verbose_name='فترة الإشعار (بالأيام)')),
                ('last_working_day', models.DateField(verbose_name='آخر يوم عمل')),
                ('termination_date', models.DateField(verbose_name='تاريخ إنهاء الخدمة')),
                ('status', models.CharField(choices=[('PENDING', 'قيد الانتظار'), ('APPROVED', 'موافق عليه'), ('REJECTED', 'مرفوض'), ('COMPLETED', 'مكتمل')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة من')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='terminations_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إنهاء خدمة',
                'verbose_name_plural': 'إنهاء الخدمة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Transfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_type', models.CharField(choices=[('DEPARTMENT', 'نقل بين الأقسام'), ('POSITION', 'نقل منصب'), ('BRANCH', 'نقل فرع'), ('TEMPORARY', 'نقل مؤقت'), ('PERMANENT', 'نقل دائم'), ('SECONDMENT', 'إعارة'), ('OTHER', 'أخرى')], default='DEPARTMENT', max_length=20, verbose_name='نوع النقل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ النقل')),
                ('effective_date', models.DateField(verbose_name='تاريخ سريان النقل')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء النقل')),
                ('reason', models.TextField(verbose_name='سبب النقل')),
                ('status', models.CharField(choices=[('PENDING', 'قيد المراجعة'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض'), ('CANCELLED', 'ملغي'), ('COMPLETED', 'مكتمل')], default='PENDING', max_length=20, verbose_name='حالة النقل')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='transfer_attachments/', verbose_name='مرفقات')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_temporary', models.BooleanField(default=False, verbose_name='نقل مؤقت')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ العودة')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_hr_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_hr_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='hr.employee', verbose_name='الموظف')),
                ('from_department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from', to='hr.department', verbose_name='القسم السابق')),
                ('from_position', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from', to='hr.position', verbose_name='المنصب السابق')),
                ('replacement_employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replacement_transfers', to='hr.employee', verbose_name='الموظف البديل')),
                ('to_department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_to', to='hr.department', verbose_name='القسم الجديد')),
                ('to_position', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_to', to='hr.position', verbose_name='المنصب الجديد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_hr_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'نقل/تحويل',
                'verbose_name_plural': 'النقل والتحويل',
                'ordering': ['-transfer_date', 'employee'],
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('check_in', models.TimeField(verbose_name='وقت الحضور')),
                ('check_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('attendance_state', models.CharField(blank=True, choices=[('REGULAR', 'منتظم'), ('LATE', 'متأخر'), ('ABSENT', 'غائب'), ('COMPLETE', 'مكتمل')], max_length=10, null=True, verbose_name='الحالة (اختياري)')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حضور وانصراف',
                'verbose_name_plural': 'سجلات الحضور والانصراف',
                'ordering': ['-date', 'employee'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='AbsenceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('absence_type', models.CharField(choices=[('ABSENCE', 'غياب'), ('LATE', 'تأخير'), ('EARLY_LEAVE', 'انصراف مبكر'), ('ABSENCE_WITHOUT_PERMISSION', 'غياب بدون إذن'), ('LATE_WITHOUT_PERMISSION', 'تأخير بدون إذن')], max_length=30, verbose_name='نوع المخالفة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('time_from', models.TimeField(blank=True, null=True, verbose_name='من وقت')),
                ('time_to', models.TimeField(blank=True, null=True, verbose_name='إلى وقت')),
                ('duration_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4, verbose_name='المدة بالساعات')),
                ('duration_minutes', models.IntegerField(default=0, verbose_name='المدة بالدقائق')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('details', models.TextField(blank=True, verbose_name='تفاصيل إضافية')),
                ('deduction_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('status', models.CharField(choices=[('PENDING', 'قيد المراجعة'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approval_notes', models.TextField(blank=True, verbose_name='ملاحظات الموافقة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='absence_attachments/', verbose_name='مرفقات')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_absences', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_absences', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_absences', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='absence_records', to='hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'سجل غياب/تأخير',
                'verbose_name_plural': 'سجلات الغياب والتأخير',
                'ordering': ['-date', '-created_at'],
                'unique_together': {('employee', 'date', 'absence_type')},
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('total_days', models.IntegerField(default=0, verbose_name='إجمالي الأيام المخصصة')),
                ('used_days', models.IntegerField(default=0, verbose_name='الأيام المستخدمة')),
                ('remaining_days', models.IntegerField(default=0, verbose_name='الأيام المتبقية')),
                ('carried_forward_days', models.IntegerField(default=0, verbose_name='الأيام المحولة من السنة السابقة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='hr.employee', verbose_name='الموظف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_balances', to='hr.leavetype', verbose_name='نوع الإجازة')),
            ],
            options={
                'verbose_name': 'رصيد إجازة',
                'verbose_name_plural': 'أرصدة الإجازات',
                'ordering': ['-year', 'employee'],
                'unique_together': {('employee', 'leave_type', 'year')},
            },
        ),
    ]
