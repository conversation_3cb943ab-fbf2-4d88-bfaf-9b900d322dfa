from django.core.management.base import BaseCommand
from django.template import engines
from django.core.cache import cache
import os


class Command(BaseCommand):
    help = 'مسح template cache وإجبار Django على إعادة تحميل templates من القرص'

    def add_arguments(self, parser):
        parser.add_argument(
            '--template',
            type=str,
            help='اسم template محدد لمسحه من الذاكرة',
        )

    def handle(self, *args, **options):
        self.stdout.write('🧹 بدء مسح template cache...')
        
        # مسح Django cache العام
        cache.clear()
        self.stdout.write('✅ تم مسح Django cache')
        
        # مسح template loaders cache
        cleared_loaders = 0
        for engine in engines.all():
            if hasattr(engine, 'engine') and hasattr(engine.engine, 'template_loaders'):
                for loader in engine.engine.template_loaders:
                    if hasattr(loader, 'reset'):
                        loader.reset()
                        cleared_loaders += 1
                    if hasattr(loader, 'cache'):
                        loader.cache.clear()
                        cleared_loaders += 1
        
        self.stdout.write(f'✅ تم مسح {cleared_loaders} template loaders')
        
        # مسح template cache من الذاكرة
        template_name = options.get('template')
        if template_name:
            self.stdout.write(f'🎯 مسح template محدد: {template_name}')
            
            # محاولة حذف template من cache
            for engine in engines.all():
                if hasattr(engine, 'engine'):
                    if hasattr(engine.engine, 'template_cache'):
                        if template_name in engine.engine.template_cache:
                            del engine.engine.template_cache[template_name]
                            self.stdout.write(f'✅ تم حذف {template_name} من template cache')
        
        # مسح Python module cache للـ templates
        import sys
        templates_modules = [key for key in sys.modules.keys() if 'template' in key.lower()]
        for module_name in templates_modules:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        self.stdout.write(f'✅ تم مسح {len(templates_modules)} template modules من Python cache')
        
        # إعادة تحميل template engines
        from django.template import engines as template_engines
        template_engines._engines = {}
        
        self.stdout.write('🔄 تم إعادة تحميل template engines')
        
        # التحقق من وجود الملف على القرص
        if template_name:
            template_path = f'templates/{template_name}'
            if os.path.exists(template_path):
                file_size = os.path.getsize(template_path)
                self.stdout.write(f'📁 الملف موجود على القرص: {template_path} ({file_size} bytes)')
            else:
                self.stdout.write(f'❌ الملف غير موجود على القرص: {template_path}')
        
        self.stdout.write(
            self.style.SUCCESS('🎉 تم مسح template cache بنجاح! Django سيقرأ templates من القرص مباشرة.')
        )
