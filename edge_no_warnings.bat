@echo off
REM تشغيل Edge بدون تحذيرات SSL
REM Run Edge without SSL warnings

echo بدء Edge بدون تحذيرات SSL...
echo Starting Edge without SSL warnings...

REM البحث عن Edge
set EDGE_PATH=""
if exist "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" (
    set EDGE_PATH="C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
) else if exist "C:\Program Files\Microsoft\Edge\Application\msedge.exe" (
    set EDGE_PATH="C:\Program Files\Microsoft\Edge\Application\msedge.exe"
) else (
    echo Edge غير موجود
    pause
    exit
)

REM تشغيل Edge مع إعدادات خاصة
%EDGE_PATH% --ignore-certificate-errors --ignore-ssl-errors --ignore-certificate-errors-spki-list --ignore-certificate-errors-skip-list --disable-ssl-warnings --allow-running-insecure-content --disable-web-security --user-data-dir="%TEMP%\edge_dev" https://***************:8443/

pause
