# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> sagar <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2019-03-10 07:17+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "കാര്യനിർവ്വാഹകർക്കായുള്ള ഡോക്യൂമെന്റേഷൻ "

msgid "Home"
msgstr "ഹോം "

msgid "Documentation"
msgstr "സഹായക്കുറിപ്പുകള്‍"

msgid "Bookmarklets"
msgstr "ബുക്ക് മാര്‍ക്കുകള്‍"

msgid "Documentation bookmarklets"
msgstr "സഹായക്കുറിപ്പുകളുടെ ബുക്ക്മാര്‍ക്കുകള്‍"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"ബുക്ക്മാർക്കുകൾ ഇൻസ്റ്റാൾ ചെയ്യാൻ, നിങ്ങളുടെ ബുക്ക്മാർക്കുകൾ ടൂൾബാറിലേക്ക് ലിങ്ക് ഇഴയ്ക്കുക, "
"അല്ലെങ്കിൽ ലിങ്കിൽ വലത് ക്ലിക്കുചെയ്ത് നിങ്ങളുടെ ബുക്ക്മാർക്കുകളിൽ ചേർക്കുക. ഇപ്പോൾ നിങ്ങൾക്ക് "
"സൈറ്റിലെ ഏത് പേജിൽ നിന്നും ബുക്മാർക്കറ്റ് തിരഞ്ഞെടുക്കാം."

msgid "Documentation for this page"
msgstr "ഈ പേജിന്റെ സഹായക്കുറിപ്പുകള്‍"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "ഏതു പേജില്‍ നിന്നും അതിന്റെ ഉദ്ഭവമായ വ്യൂവിന്റെ സഹായക്കുറിപ്പിലേക്കു ചാടാന്‍"

msgid "Tags"
msgstr "ടാഗുകള്‍"

msgid "List of all the template tags and their functions."
msgstr "എല്ലാ ടെംപ്ലേറ്റുകളും അവയുടെ ഫംഗ്ഷനുകളുടെ പട്ടിക"

msgid "Filters"
msgstr "ഫില്‍ട്ടറുകള്‍"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"ഫിൽറ്ററുകൾ എന്നാൽ ഔട്പുട്ടിനു മാറ്റം വരുത്തുവാൻ  ടെംപ്ലേറ്റുകൾക്കു ഉള്ളില്ലേ വാരിയബിലിസിന് "
"(variables) നൽകുന്ന ആക്ഷൻസ്സാണ് "

msgid "Models"
msgstr "മോഡലുകള്‍"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "വ്യൂകള്‍"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"അഡ്മിൻ  ഫങ്ക്ഷണാലിറ്റിയിലേക്കു  പെട്ടെന്നു പ്രവേശിക്കുവാൻ  നിങ്ങളുടെ ബ്രൗസെറിനുള്ള ടൂളുകൾ ."

msgid "Please install docutils"
msgstr "ദയവായി ഡോക്യൂട്ടിൽസ്‌  ഇൻസ്റ്റാൾ ചെയ്യുക."

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr "ഫീൽഡുകൾ "

msgid "Field"
msgstr "ഫീൽഡ്."

msgid "Type"
msgstr "ടൈപ്പ് "

msgid "Description"
msgstr "വിവരണം"

msgid "Methods with arguments"
msgstr "മെതോടുകൾ ഉള്ള ആർഗുമെന്റ്സ് "

msgid "Method"
msgstr "രീതി"

msgid "Arguments"
msgstr "വാദങ്ങൾ"

msgid "Back to Model documentation"
msgstr "മോഡൽ ഡോക്യൂമെന്റന്റഷനിലേക്ക് തിരികെ പോവുക ."

msgid "Model documentation"
msgstr "മോഡൽ ഡോക്യൂമെന്റേഷൻ."

msgid "Model groups"
msgstr "മോഡൽ ഗ്രൂപ്സ്"

msgid "Templates"
msgstr "ടെമ്പ്‌ലേറ്റുകള്‍"

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: \"%(name)s\""
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr "(എക്സിസ്റ്  ചെയ്യുന്നില്ല )"

msgid "Back to Documentation"
msgstr "ഡോക്യൂമെന്റഷനിലേക്കു തിരികെ പോവുക ."

msgid "Template filters"
msgstr "ടെമ്പ്ലേറ്റ് ഫിൽറ്ററുകൾ "

msgid "Template filter documentation"
msgstr "ടെമ്പ്ലേറ്റ് ഫിൽറ്റർ ഡോക്യൂമെന്റേഷൻ "

msgid "Built-in filters"
msgstr "ബിൽട്ടിൻ  ഫിൽറ്ററുകൾ "

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr "ടെമ്പ്ലേറ്റ് റ്റാഗുകൾ "

msgid "Template tag documentation"
msgstr "ടെമ്പ്ലേറ്റ് ടാഗിന്റെ ഡോക്യൂമെന്റേഷൻ "

msgid "Built-in tags"
msgstr "ബിൽട്ടിൻ  റ്റാഗുകൾ "

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr "കോണ്ടെക്സ്റ് :"

msgid "Templates:"
msgstr "ടെമ്പ്‌ലേറ്റുകള്‍"

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr "ഡോക്യൂമെന്റെഷൻ കാണുക "

msgid "Jump to namespace"
msgstr "നെയിംസ്പേസിലേക്ക് ചാടുക ."

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "ടാഗ്:"

msgid "filter:"
msgstr "അരിപ്പ:"

msgid "view:"
msgstr "വ്യൂ"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(app_label)r എന്ന Appല്‍ %(model_name)r എന്ന മാത്രുക കണ്ടില്ല."

msgid "model:"
msgstr "മാത്രുക:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "ബന്ധപ്പെട്ട `%(app_label)s.%(data_type)s` വസ്തു"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "ബന്ധപ്പെട്ട `%(app_label)s.%(object_name)s` വസ്തുക്കള്‍"

#, python-format
msgid "all %s"
msgstr "%s എല്ലാം"

#, python-format
msgid "number of %s"
msgstr "%sന്റെ എണ്ണം"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s വിലാസ മാത്രുക (urlpattern object) ആണെന്ന് തോന്നുന്നില്ല."
