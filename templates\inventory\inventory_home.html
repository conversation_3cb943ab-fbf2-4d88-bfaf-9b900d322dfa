{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-warehouse text-primary me-2"></i>
                    {{ title }}
                </h2>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                    <i class="fas fa-warehouse fa-2x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_warehouses }}</h4>
                            <p class="card-text text-muted">المخازن</p>
                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-primary btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                    <i class="fas fa-boxes fa-2x text-success"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_items }}</h4>
                            <p class="card-text text-muted">الأصناف</p>
                            <a href="{% url 'definitions:item_list' %}" class="btn btn-outline-success btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                    <i class="fas fa-plus-circle fa-2x text-info"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_stock_increases }}</h4>
                            <p class="card-text text-muted">إذون الإضافة</p>
                            <a href="{% url 'inventory:stock_increase_list' %}" class="btn btn-outline-info btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                                    <i class="fas fa-minus-circle fa-2x text-warning"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_stock_decreases }}</h4>
                            <p class="card-text text-muted">إذون الصرف</p>
                            <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-outline-warning btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-danger bg-opacity-10 p-3">
                                    <i class="fas fa-industry fa-2x text-danger"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_manufacturing_orders }}</h4>
                            <p class="card-text text-muted">أوامر الإنتاج</p>
                            <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-outline-danger btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أوامر الإنتاج حسب الحالة -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                أوامر الإنتاج حسب الحالة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-secondary bg-opacity-10 p-2 me-3">
                                            <i class="fas fa-edit text-secondary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ manufacturing_stats.draft }}</h6>
                                            <small class="text-muted">مسودات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-info bg-opacity-10 p-2 me-3">
                                            <i class="fas fa-check text-info"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ manufacturing_stats.approved }}</h6>
                                            <small class="text-muted">معتمدة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-warning bg-opacity-10 p-2 me-3">
                                            <i class="fas fa-cogs text-warning"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ manufacturing_stats.in_production }}</h6>
                                            <small class="text-muted">قيد الإنتاج</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-success bg-opacity-10 p-2 me-3">
                                            <i class="fas fa-check-double text-success"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ manufacturing_stats.completed }}</h6>
                                            <small class="text-muted">مكتملة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                الإجراءات السريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="{% url 'inventory:stock_increase_create' %}" class="btn btn-outline-success w-100">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        إذن إضافة جديد
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="{% url 'inventory:stock_decrease_create' %}" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-minus-circle me-2"></i>
                                        إذن صرف جديد
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-industry me-2"></i>
                                        أمر إنتاج جديد
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="{% url 'inventory:warehouse_transfer_create' %}" class="btn btn-outline-info w-100">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        تحويل بين المخازن
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر العمليات -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                آخر إذون الإضافة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_stock_increases %}
                                <div class="list-group list-group-flush">
                                    {% for increase in recent_stock_increases %}
                                        <div class="list-group-item border-0 px-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ increase.reference_number }}</h6>
                                                    <p class="mb-1 text-muted">{{ increase.warehouse.name }}</p>
                                                    <small class="text-muted">{{ increase.created_at|date:"d/m/Y H:i" }}</small>
                                                </div>
                                                <span class="badge bg-success">{{ increase.get_status_display }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="text-center mt-3">
                                    <a href="{% url 'inventory:stock_increase_list' %}" class="btn btn-outline-primary btn-sm">
                                        عرض الكل
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد عمليات حديثة</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-industry me-2"></i>
                                آخر أوامر الإنتاج
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_manufacturing_orders %}
                                <div class="list-group list-group-flush">
                                    {% for order in recent_manufacturing_orders %}
                                        <div class="list-group-item border-0 px-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ order.order_number }}</h6>
                                                    <p class="mb-1 text-muted">{{ order.product_item.name }}</p>
                                                    <small class="text-muted">{{ order.created_at|date:"d/m/Y H:i" }}</small>
                                                </div>
                                                <span class="badge 
                                                    {% if order.status == 'DRAFT' %}bg-secondary
                                                    {% elif order.status == 'APPROVED' %}bg-info
                                                    {% elif order.status == 'IN_PRODUCTION' %}bg-warning
                                                    {% elif order.status == 'COMPLETED' %}bg-success
                                                    {% else %}bg-danger{% endif %}">
                                                    {{ order.get_status_display }}
                                                </span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="text-center mt-3">
                                    <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-outline-primary btn-sm">
                                        عرض الكل
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-3">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد أوامر إنتاج حديثة</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
