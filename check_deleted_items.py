#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item

def check_deleted_items():
    print("=== Checking Deleted Items ===")
    
    # Check all items
    all_items = Item.objects.all()
    active_items = Item.objects.filter(is_active=True)
    inactive_items = Item.objects.filter(is_active=False)
    
    print(f"Total items: {all_items.count()}")
    print(f"Active items: {active_items.count()}")
    print(f"Inactive (deleted) items: {inactive_items.count()}")
    
    if inactive_items.count() > 0:
        print("\nInactive items to be permanently deleted:")
        for i, item in enumerate(inactive_items, 1):
            print(f"{i:2d}. {item.code}: {item.name}")
        
        return inactive_items
    else:
        print("\nNo inactive items found.")
        return None

if __name__ == "__main__":
    deleted_items = check_deleted_items()
    if deleted_items:
        print(f"\nFound {deleted_items.count()} items to delete permanently.")
    else:
        print("\nNo items need to be deleted.")