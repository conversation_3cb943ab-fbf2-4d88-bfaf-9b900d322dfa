{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل نظام صرف المرتب{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12 d-flex align-items-center">
            <a href="{% url 'hr:salary_system_list' %}" class="btn btn-outline-secondary me-3">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <h2 class="mb-0">
                <i class="fas fa-money-bill text-success me-2"></i>
                تفاصيل نظام صرف المرتب
            </h2>
        </div>
    </div>
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> بيانات النظام</h4>
        </div>
        <div class="card-body">
            <div class="row mb-2">
                <div class="col-md-4 mb-2">
                    <strong>الكود:</strong> <span class="text-primary">{{ salary_system.code }}</span>
                </div>
                <div class="col-md-4 mb-2">
                    <strong>اسم النظام:</strong> {{ salary_system.name }}
                </div>
                <div class="col-md-4 mb-2">
                    <strong>نوع النظام:</strong> <span class="badge bg-info">{{ salary_system.get_system_type_display }}</span>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md-4 mb-2">
                    <strong>الراتب الأساسي:</strong> <span class="text-success">{{ salary_system.basic_salary|floatformat:2 }}</span>
                </div>
                <div class="col-md-4 mb-2">
                    <strong>العملة:</strong> <span class="badge bg-secondary">{{ salary_system.currency.code }}</span>
                </div>
                <div class="col-md-4 mb-2">
                    <strong>الوصف:</strong> {{ salary_system.description|default:'-' }}
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0"><i class="fas fa-users me-2"></i> الموظفون المرتبطون بالنظام</h4>
        </div>
        <div class="card-body p-0">
            {% if employees %}
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الاسم</th>
                            <th>الوظيفة</th>
                            <th>القسم</th>
                            <th>الراتب الحالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for emp in employees %}
                        <tr>
                            <td>{{ emp.person.get_full_name }}</td>
                            <td>{{ emp.position.name }}</td>
                            <td>{{ emp.department.name }}</td>
                            <td>{{ emp.current_salary.basic_salary|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4 text-muted">
                <i class="fas fa-user-slash fa-2x mb-2"></i><br>
                لا يوجد موظفون مرتبطون بهذا النظام.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
