{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-eye text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تفاصيل طلب الإجازة</p>
                </div>
                <div>
                    <a href="{% url 'hr:leave_request_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                    <a href="{% url 'hr:leave_request_edit' leave_request.id %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Request Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل طلب الإجازة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الموظف:</label>
                                <div class="form-control-plaintext">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    {{ leave_request.employee.full_name }}
                                    <small class="text-muted">({{ leave_request.employee.employee_number }})</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">نوع الإجازة:</label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-info">{{ leave_request.leave_type.name }}</span>
                                    <small class="text-muted">({{ leave_request.leave_type.allowed_days }} يوم مسموح)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">من تاريخ:</label>
                                <div class="form-control-plaintext">
                                    <i class="fas fa-calendar text-success me-2"></i>
                                    {{ leave_request.from_date|date:"Y-m-d" }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">إلى تاريخ:</label>
                                <div class="form-control-plaintext">
                                    <i class="fas fa-calendar text-danger me-2"></i>
                                    {{ leave_request.to_date|date:"Y-m-d" }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">عدد الأيام:</label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-primary">{{ leave_request.duration_display }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <div class="form-control-plaintext">
                                    <span class="badge {{ leave_request.status_badge_class }}">{{ leave_request.get_status_display }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">سبب الإجازة:</label>
                        <div class="form-control-plaintext" style="min-height: 80px;">
                            {{ leave_request.reason|linebreaks }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">نوع الطلب:</label>
                                <div class="form-control-plaintext">
                                    {% if leave_request.is_emergency %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            إجازة طارئة
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            إجازة عادية
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">المرفقات:</label>
                                <div class="form-control-plaintext">
                                    {% if leave_request.attachment %}
                                        <a href="{{ leave_request.attachment.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>
                                            تحميل المرفق
                                        </a>
                                    {% else %}
                                        <span class="text-muted">لا توجد مرفقات</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        سجل الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء الطلب</h6>
                                <p class="text-muted mb-0">{{ leave_request.created_at|date:"Y-m-d H:i" }}</p>
                                <small class="text-muted">بواسطة: {{ leave_request.created_by.username }}</small>
                            </div>
                        </div>
                        
                        {% if leave_request.approved_by %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">
                                    {% if leave_request.status == 'APPROVED' %}
                                        تم اعتماد الطلب
                                    {% else %}
                                        تم رفض الطلب
                                    {% endif %}
                                </h6>
                                <p class="text-muted mb-0">{{ leave_request.approved_at|date:"Y-m-d H:i" }}</p>
                                <small class="text-muted">بواسطة: {{ leave_request.approved_by.username }}</small>
                                {% if leave_request.approval_notes %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>ملاحظات:</strong> {{ leave_request.approval_notes }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات
                    </h5>
                </div>
                <div class="card-body">
                    {% if leave_request.status == 'PENDING' %}
                    <div class="d-grid gap-2">
                        <a href="{% url 'hr:leave_request_approve' leave_request.id %}" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>
                            اعتماد الطلب
                        </a>
                        <a href="{% url 'hr:leave_request_reject' leave_request.id %}" class="btn btn-danger">
                            <i class="fas fa-times me-2"></i>
                            رفض الطلب
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 mt-3">
                        <a href="{% url 'hr:leave_request_edit' leave_request.id %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الطلب
                        </a>
                        {% if leave_request.status != 'APPROVED' %}
                        <button class="btn btn-outline-danger" onclick="deleteRequest({{ leave_request.id }})">
                            <i class="fas fa-trash me-2"></i>
                            حذف الطلب
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا الطلب؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" action="" id="deleteForm" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: -26px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background: #dee2e6;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function deleteRequest(requestId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/hr/leave-requests/${requestId}/delete/`;
    modal.show();
}
</script>
{% endblock %} 