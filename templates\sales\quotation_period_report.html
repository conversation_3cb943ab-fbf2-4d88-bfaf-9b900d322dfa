{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-chart-bar text-secondary me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'sales:home' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للمبيعات
                </a>
            </div>

            <!-- نموذج التقرير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلترة التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">{{ form.customer.label }}</label>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.salesperson.id_for_label }}" class="form-label">{{ form.salesperson.label }}</label>
                                {{ form.salesperson }}
                                {% if form.salesperson.errors %}
                                    <div class="text-danger small">{{ form.salesperson.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {% if quotations is not None %}
                <!-- ملخص التقرير -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1">{{ quotations|length }}</h3>
                                <p class="mb-0">عدد عروض الأسعار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1">{{ total_amount|floatformat:0 }}</h3>
                                <p class="mb-0">إجمالي المبلغ (ج.م)</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1">{{ average_amount|floatformat:0 }}</h3>
                                <p class="mb-0">متوسط قيمة العرض (ج.م)</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3 class="mb-1">{{ quotations|length }}</h3>
                                <p class="mb-0">عدد العروض المعروضة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل التقرير -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            تفاصيل عروض الأسعار
                        </h5>
                        {% if quotations %}
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير Excel
                            </button>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if quotations %}
                            <div class="table-responsive">
                                <table class="table table-hover" id="quotationsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم العرض</th>
                                            <th>التاريخ</th>
                                            <th>العميل</th>
                                            <th>مندوب المبيعات</th>
                                            <th>المبلغ</th>
                                            <th>صالح حتى</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for quotation in quotations %}
                                            <tr>
                                                <td>
                                                    <strong>{{ quotation.quotation_number }}</strong>
                                                </td>
                                                <td>{{ quotation.date|date:"d/m/Y" }}</td>
                                                <td>{{ quotation.customer.name }}</td>
                                                <td>
                                                    {% if quotation.salesperson %}
                                                        {{ quotation.salesperson.get_full_name|default:quotation.salesperson.username }}
                                                    {% else %}
                                                        <span class="text-muted">غير محدد</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <strong class="text-success">{{ quotation.total_amount|floatformat:2 }} ج.م</strong>
                                                </td>
                                                <td>
                                                    {{ quotation.valid_until|date:"d/m/Y" }}
                                                    {% if quotation.is_expired %}
                                                        <br><span class="badge bg-danger">منتهي</span>
                                                    {% elif quotation.days_until_expiry <= 7 %}
                                                        <br><span class="badge bg-warning">{{ quotation.days_until_expiry }} أيام</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if quotation.status == 'DRAFT' %}
                                                        <span class="badge bg-secondary">{{ quotation.get_status_display }}</span>
                                                    {% elif quotation.status == 'SENT' %}
                                                        <span class="badge bg-primary">{{ quotation.get_status_display }}</span>
                                                    {% elif quotation.status == 'ACCEPTED' %}
                                                        <span class="badge bg-success">{{ quotation.get_status_display }}</span>
                                                    {% elif quotation.status == 'REJECTED' %}
                                                        <span class="badge bg-danger">{{ quotation.get_status_display }}</span>
                                                    {% elif quotation.status == 'EXPIRED' %}
                                                        <span class="badge bg-dark">{{ quotation.get_status_display }}</span>
                                                    {% elif quotation.status == 'CONVERTED' %}
                                                        <span class="badge bg-info">{{ quotation.get_status_display }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <a href="{% url 'sales:quotation_detail' quotation.pk %}" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد عروض أسعار في الفترة المحددة</h5>
                                <p class="text-muted">جرب تغيير معايير البحث</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <!-- رسالة البداية -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">تقرير عروض الأسعار</h5>
                        <p class="text-muted">اختر الفترة الزمنية والمعايير لإنشاء التقرير</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// تحديد تواريخ افتراضية
document.addEventListener('DOMContentLoaded', function() {
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    
    if (!startDateField.value) {
        // أول يوم في الشهر الحالي
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        startDateField.value = firstDay.toISOString().split('T')[0];
    }
    
    if (!endDateField.value) {
        // اليوم الحالي
        const today = new Date().toISOString().split('T')[0];
        endDateField.value = today;
    }
});

function exportToExcel() {
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
{% endblock %}
