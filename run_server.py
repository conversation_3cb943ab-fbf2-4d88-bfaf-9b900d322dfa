#!/usr/bin/env python3
"""
Simple Django Server Runner
"""

import os
import sys
import subprocess
from datetime import datetime

def log_message(message):
    """Log message with timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def main():
    """Main function to run Django server"""
    print("=" * 60)
    print("Django Server - Osaric Accounts System")
    print("=" * 60)
    
    log_message("Starting Django development server...")
    
    try:
        # Set environment for proper Unicode handling
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # Run Django server
        cmd = [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000']
        
        log_message("Server starting on http://127.0.0.1:8000/")
        log_message("Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Start the server
        process = subprocess.run(cmd, env=env)
        
        if process.returncode == 0:
            log_message("Server stopped normally")
        else:
            log_message(f"Server stopped with error code: {process.returncode}")
            
    except KeyboardInterrupt:
        log_message("Server stopped by user (Ctrl+C)")
    except Exception as e:
        log_message(f"Error running server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())