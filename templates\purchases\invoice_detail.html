{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل فاتورة المشتريات - {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .invoice-info-card {
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }
    
    .supplier-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .invoice-items-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }
    
    .status-pending { background-color: #ffc107; color: #000; }
    .status-confirmed { background-color: #17a2b8; color: #fff; }
    .status-completed { background-color: #28a745; color: #fff; }
    .status-cancelled { background-color: #dc3545; color: #fff; }
    
    .action-buttons .btn {
        margin: 0.25rem;
        border-radius: 20px;
        padding: 0.5rem 1.5rem;
    }
    
    .total-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        border-left: 4px solid #007bff;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    
    .info-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    
    .info-item strong {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        color: #495057;
    }
    
    .info-item .badge {
        margin-left: auto;
        font-size: 0.875rem;
    }
    
    .stock-status {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        border-left: 4px solid;
    }
    
    .stock-status.ready {
        background: #d4edda;
        border-color: #28a745;
        color: #155724;
    }
    
    .stock-status.pending {
        background: #fff3cd;
        border-color: #ffc107;
        color: #856404;
    }
    
    .stock-status.completed {
        background: #d1ecf1;
        border-color: #17a2b8;
        color: #0c5460;
    }
    
    .stock-status.error {
        background: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }
    
    .action-buttons .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
    }
    
    .action-buttons .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .action-buttons .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}
    <!-- رأس الفاتورة -->
    <div class="invoice-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-file-invoice me-2"></i>
                    فاتورة مشتريات رقم: {{ invoice.invoice_number }}
                </h2>
                <p class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    تاريخ الفاتورة: {{ invoice.date|date:"d/m/Y" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ invoice.status|lower }}">
                    {{ invoice.get_status_display }}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الفاتورة -->
        <div class="col-md-8">
            <!-- معلومات المورد -->
            <div class="card invoice-info-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        معلومات المورد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="supplier-info">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">{{ invoice.supplier.name }}</h6>
                                {% if invoice.supplier.phone %}
                                <p class="mb-1">
                                    <i class="fas fa-phone me-2"></i>
                                    {{ invoice.supplier.phone }}
                                </p>
                                {% endif %}
                                {% if invoice.supplier.email %}
                                <p class="mb-1">
                                    <i class="fas fa-envelope me-2"></i>
                                    {{ invoice.supplier.email }}
                                </p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {% if invoice.supplier.address %}
                                <p class="mb-1">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    {{ invoice.supplier.address }}
                                </p>
                                {% endif %}
                                {% if invoice.supplier.current_balance %}
                                <p class="mb-0">
                                    <i class="fas fa-wallet me-2"></i>
                                    الرصيد الحالي: {{ invoice.supplier.current_balance }} ج.م
                                </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أصناف الفاتورة -->
            <div class="card invoice-info-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        أصناف الفاتورة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped invoice-items-table mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>السعر</th>
                                    <th>الخصم</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.items.all %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ item.item.name }}</strong>
                                        {% if item.item.code %}
                                        <br><small class="text-muted">كود: {{ item.item.code }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.quantity|floatformat:3 }}</td>
                                    <td>{{ item.item.unit|default:"قطعة" }}</td>
                                    <td>{{ item.unit_cost|floatformat:2 }} ج.م</td>
                                    <td>
                                        {% if item.discount_amount > 0 %}
                                            {{ item.discount_amount|floatformat:2 }} ج.م
                                            {% if item.discount_percentage > 0 %}
                                            <br><small>({{ item.discount_percentage }}%)</small>
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="fw-bold">{{ item.total_amount|floatformat:2 }} ج.م</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <br>لا توجد أصناف في هذه الفاتورة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الملخص والإجراءات -->
        <div class="col-md-4">
            <!-- ملخص الفاتورة -->
            <div class="card invoice-info-card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="total-section">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>{{ invoice.subtotal|floatformat:2 }} ج.م</span>
                        </div>
                        {% if invoice.discount_amount > 0 %}
                        <div class="d-flex justify-content-between mb-2 text-danger">
                            <span>إجمالي الخصم:</span>
                            <span>-{{ invoice.discount_amount|floatformat:2 }} ج.م</span>
                        </div>
                        {% endif %}
                        {% if invoice.tax_amount > 0 %}
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة ({{ invoice.tax_percentage }}%):</span>
                            <span>{{ invoice.tax_amount|floatformat:2 }} ج.م</span>
                        </div>
                        {% endif %}
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5 text-primary">
                            <span>الإجمالي النهائي:</span>
                            <span>{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card invoice-info-card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    {% if invoice.warehouse %}
                    <p class="mb-2">
                        <i class="fas fa-warehouse me-2"></i>
                        <strong>المستودع:</strong> {{ invoice.warehouse.name }}
                    </p>
                    {% endif %}
                    
                    {% if invoice.currency %}
                    <p class="mb-2">
                        <i class="fas fa-coins me-2"></i>
                        <strong>العملة:</strong> {{ invoice.currency.name }}
                    </p>
                    {% endif %}
                    
                    {% if invoice.payment_method %}
                    <p class="mb-2">
                        <i class="fas fa-credit-card me-2"></i>
                        <strong>طريقة الدفع:</strong> {{ invoice.get_payment_method_display }}
                    </p>
                    {% endif %}
                    
                    {% if invoice.due_date %}
                    <p class="mb-2">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date|date:"d/m/Y" }}
                    </p>
                    {% endif %}
                    
                    <p class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        <strong>أنشئت بواسطة:</strong> {{ invoice.created_by.get_full_name|default:invoice.created_by.username }}
                    </p>
                </div>
            </div>

            <!-- معلومات المخزون -->
            <div class="card invoice-info-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        معلومات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if invoice.warehouse %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-warehouse me-2 text-primary"></i>
                                <strong>المخزن المستهدف:</strong>
                                <span class="badge bg-primary">{{ invoice.warehouse.name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-box me-2 text-success"></i>
                                <strong>عدد الأصناف:</strong>
                                <span class="badge bg-success">{{ invoice.items.count }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-weight-hanging me-2 text-info"></i>
                                <strong>إجمالي الكمية:</strong>
                                <span class="badge bg-info">{{ invoice.items.all|length }} صنف</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="fas fa-money-bill-wave me-2 text-warning"></i>
                                <strong>إجمالي القيمة:</strong>
                                <span class="badge bg-warning">{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if invoice.status == 'CONFIRMED' %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>جاهزة للتطبيق:</strong> يمكن تطبيق هذه الفاتورة على المخزون الآن.
                    </div>
                    {% elif invoice.status == 'RECEIVED' %}
                    <div class="alert alert-primary mt-3">
                        <i class="fas fa-box-open me-2"></i>
                        <strong>تم التطبيق:</strong> تم تطبيق هذه الفاتورة على المخزون بنجاح.
                    </div>
                    {% elif invoice.status == 'DRAFT' %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحتاج تأكيد:</strong> يجب تأكيد الفاتورة أولاً قبل تطبيقها على المخزون.
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> لم يتم تحديد مخزن للفاتورة. يجب تحديد مخزن قبل تطبيق الفاتورة على المخزون.
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- الإجراءات -->
            <div class="card invoice-info-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- حالة الفاتورة -->
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>حالة الفاتورة:</strong> 
                        <span class="badge bg-{% if invoice.status == 'DRAFT' %}secondary{% elif invoice.status == 'CONFIRMED' %}success{% elif invoice.status == 'RECEIVED' %}primary{% else %}danger{% endif %}">
                            {{ invoice.get_status_display }}
                        </span>
                    </div>

                    <!-- الإجراءات المتاحة حسب الحالة -->
                    <div class="action-buttons d-grid gap-2">
                        {% if invoice.status == 'DRAFT' %}
                        <!-- فاتورة مسودة -->
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> الفاتورة في حالة مسودة. يجب تأكيدها أولاً قبل تطبيقها على المخزون.
                        </div>
                        <a href="{% url 'purchases:invoice_edit' invoice.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                        </a>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#confirmModal">
                            <i class="fas fa-check me-2"></i>تأكيد الفاتورة
                        </button>
                        
                        {% elif invoice.status == 'CONFIRMED' %}
                        <!-- فاتورة مؤكدة -->
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>جاهزة للتطبيق:</strong> الفاتورة مؤكدة ويمكن تطبيقها على المخزون.
                        </div>
                        <button type="button" class="btn btn-success btn-lg" onclick="applyToStock({{ invoice.pk }})">
                            <i class="fas fa-boxes me-2"></i>تطبيق على المخزون
                        </button>
                        <div class="text-muted small mt-2">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم إضافة جميع الأصناف إلى مخزن: <strong>{{ invoice.warehouse.name|default:"غير محدد" }}</strong>
                        </div>
                        
                        {% elif invoice.status == 'RECEIVED' %}
                        <!-- فاتورة مستلمة -->
                        <div class="alert alert-primary">
                            <i class="fas fa-box-open me-2"></i>
                            <strong>تم الاستلام:</strong> الفاتورة مستلمة ومطبقة على المخزون.
                        </div>
                        <button type="button" class="btn btn-success" onclick="applyToStock({{ invoice.pk }})" disabled>
                            <i class="fas fa-boxes me-2"></i>تم التطبيق على المخزون
                        </button>
                        
                        {% elif invoice.status == 'CANCELLED' %}
                        <!-- فاتورة ملغية -->
                        <div class="alert alert-danger">
                            <i class="fas fa-ban me-2"></i>
                            <strong>ملغية:</strong> لا يمكن تطبيق فاتورة ملغية على المخزون.
                        </div>
                        {% endif %}
                        
                        <!-- إجراءات عامة -->
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-success w-100" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>طباعة الفاتورة
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-danger w-100" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                </a>
                            </div>
                        </div>
                        
                        {% if invoice.status in 'DRAFT,CONFIRMED' %}
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                            <i class="fas fa-times me-2"></i>إلغاء الفاتورة
                        </button>
                        {% endif %}
                        
                        <a href="{% url 'purchases:invoice_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if invoice.notes %}
    <!-- الملاحظات -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card invoice-info-card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ invoice.notes|linebreaks }}</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal تأكيد الفاتورة -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من تأكيد هذه الفاتورة؟</p>
                <p class="text-muted">بعد التأكيد لن يمكن تعديل الفاتورة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="confirmInvoice({{ invoice.pk }})">تأكيد الفاتورة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إلغاء الفاتورة -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إلغاء هذه الفاتورة؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="cancelInvoice({{ invoice.pk }})">إلغاء الفاتورة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحسين طباعة الفاتورة
    window.addEventListener('beforeprint', function() {
        $('.action-buttons').hide();
        $('.card-header').addClass('d-print-none');
    });

    window.addEventListener('afterprint', function() {
        $('.action-buttons').show();
        $('.card-header').removeClass('d-print-none');
    });
});

// تأكيد الفاتورة
function confirmInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من تأكيد هذه الفاتورة؟\nبعد التأكيد لن يمكن تعديل الفاتورة.')) {
        // يمكن إضافة AJAX request هنا لتأكيد الفاتورة
        fetch(`/purchases/invoices/${invoiceId}/confirm/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ في تأكيد الفاتورة: ' + data.error);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

// إلغاء الفاتورة
function cancelInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // يمكن إضافة AJAX request هنا لإلغاء الفاتورة
        fetch(`/purchases/invoices/${invoiceId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ في إلغاء الفاتورة: ' + data.error);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

// تطبيق الفاتورة على المخزون
function applyToStock(invoiceId) {
    if (confirm('هل أنت متأكد من تطبيق هذه الفاتورة على المخزون؟\n\nسيتم إضافة جميع الأصناف إلى المخزن المحدد.\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إظهار مؤشر التحميل
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التطبيق...';
        button.disabled = true;
        
        fetch(`/purchases/invoices/${invoiceId}/apply-stock/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح
                showSuccessMessage('تم تطبيق الفاتورة على المخزون بنجاح!', () => {
                    location.reload();
                });
            } else {
                // إظهار رسالة خطأ
                showErrorMessage('خطأ في تطبيق الفاتورة على المخزون: ' + data.error);
                // إعادة تفعيل الزر
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showErrorMessage('حدث خطأ في الاتصال');
            // إعادة تفعيل الزر
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message, callback) {
    const toast = document.createElement('div');
    toast.className = 'position-fixed top-0 end-0 p-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast.querySelector('.toast'));
    bsToast.show();
    
    // إزالة العنصر بعد إخفاء الرسالة
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
        if (callback) callback();
    });
}

// إظهار رسالة خطأ
function showErrorMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'position-fixed top-0 end-0 p-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="toast align-items-center text-bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-triangle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast.querySelector('.toast'));
    bsToast.show();
    
    // إزالة العنصر بعد إخفاء الرسالة
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// تصدير PDF
function exportToPDF() {
    // طباعة الصفحة كـ PDF
    window.print();
}
</script>
{% endblock %}
