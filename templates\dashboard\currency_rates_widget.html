<!-- أسعار العملات - عرض مباشر -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-gradient-primary text-white text-center"> <!-- أضف text-center هنا -->
        <div class="d-flex justify-content-between align-items-center flex-column flex-md-row">
            <h5 class="mb-0 w-100 text-center"> <!-- أضف text-center هنا -->
                <i class="fas fa-exchange-alt me-2"></i>
                أسعار العملات من البنوك المصرية
            </h5>
            <div class="d-flex gap-2 justify-content-center mt-2 mt-md-0">
                <small class="text-white-50">
                    <i class="fas fa-clock me-1"></i>
                    آخر تحديث: {{ last_update|date:"d/m/Y h:i A" }}
                </small>
                <a href="{% url 'definitions:currency_rates_list' %}" class="btn btn-sm btn-outline-light">
                    <i class="fas fa-external-link-alt me-1"></i>
                    عرض التفاصيل
                </a>
            </div>
        </div>
    </div>
        
    <div class="card-body p-0 text-center"> <!-- أضف text-center هنا -->
        {% if currency_data.currencies %}
            <div class="table-responsive">
                <table class="table table-hover mb-0 align-middle text-center"> <!-- أضف text-center هنا -->
                    <thead class="table-light">
                        <tr>
                            <th class="border-0 ps-3 text-center">العملة</th>
                            <th class="border-0 text-center">أفضل شراء</th>
                            <th class="border-0 text-center">أفضل بيع</th>
                            <th class="border-0 text-center">متوسط الشراء</th>
                            <th class="border-0 text-center">متوسط البيع</th>
                            <th class="border-0 text-center">آخر تحديث</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for currency in currency_data.currencies %}
                            <tr class="currency-row" data-currency="{{ currency.code }}">
                                <td class="ps-3 text-center">
                                    <div class="d-flex align-items-center justify-content-center"> <!-- أضف justify-content-center هنا -->
                                        <div class="currency-flag me-2">
                                            {% if currency.code == 'USD' %}
                                                <span class="flag-icon">🇺🇸</span>
                                            {% elif currency.code == 'EUR' %}
                                                <span class="flag-icon">🇪🇺</span>
                                            {% elif currency.code == 'GBP' %}
                                                <span class="flag-icon">🇬🇧</span>
                                            {% elif currency.code == 'EGP' %}
                                                <span class="flag-icon">🇸🇦</span>
                                            {% elif currency.code == 'AED' %}
                                                <span class="flag-icon">🇦🇪</span>
                                            {% else %}
                                                <i class="fas fa-coins text-warning"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <strong>{{ currency.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ currency.code }} - {{ currency.symbol }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="rate-info">
                                        {% if currency.best_buy %}
                                            <span class="badge bg-success fs-6">{{ currency.best_buy.rate|floatformat:2 }}</span>
                                            <br>
                                            <small class="text-muted">{{ currency.best_buy.bank|truncatechars:12 }}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="rate-info">
                                        {% if currency.best_sell %}
                                            <span class="badge bg-danger fs-6">{{ currency.best_sell.rate|floatformat:2 }}</span>
                                            <br>
                                            <small class="text-muted">{{ currency.best_sell.bank|truncatechars:12 }}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info fs-6">{{ currency.avg_buy|floatformat:2 }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning fs-6">{{ currency.avg_sell|floatformat:2 }}</span>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {% if currency.last_updated %}
                                            {{ currency.last_updated|date:"H:i" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- معلومات إضافية -->
            <div class="card-footer bg-light">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-coins text-primary me-2"></i>
                            <div>
                                <strong>{{ currency_data.currencies|length }}</strong>
                                <br>
                                <small class="text-muted">عملات متاحة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-university text-success me-2"></i>
                            <div>
                                <strong>6</strong>
                                <br>
                                <small class="text-muted">بنوك مصرية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-chart-line text-warning me-2"></i>
                            <div>
                                <strong>EGP</strong>
                                <br>
                                <small class="text-muted">العملة الأساسية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-sync-alt text-info me-2"></i>
                            <div>
                                <strong>15 دقيقة</strong>
                                <br>
                                <small class="text-muted">فترة التحديث</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- No Currency Data Available -->
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>لا توجد أسعار عملات متاحة</h5>
                <p class="text-muted">يرجى تحديث أسعار العملات أولاً</p>
                <a href="{% url 'definitions:currency_rates_list' %}" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>
                    تحديث الأسعار
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
.currency-row {
    transition: all 0.3s ease;
}

.currency-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
}

.flag-icon {
    font-size: 1.5rem;
    display: inline-block;
    width: 24px;
    text-align: center;
}

.rate-info {
    transition: transform 0.2s ease;
}

.rate-info:hover {
    transform: scale(1.05);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.currency-row td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.badge.fs-6 {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .currency-row td {
        padding: 0.75rem 0.5rem;
    }

    .flag-icon {
        font-size: 1.2rem;
    }
}

/* تحديث تلقائي للألوان */
.currency-row[data-currency="USD"] .badge {
    animation: pulse-green 2s infinite;
}

.currency-row[data-currency="EUR"] .badge {
    animation: pulse-blue 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-blue {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}
</style>

<script>
// تحديث تلقائي لأسعار العملات كل 5 دقائق
setInterval(function() {
    // يمكن إضافة AJAX لتحديث الأسعار دون إعادة تحميل الصفحة
    console.log('تحديث أسعار العملات...');
}, 300000); // 5 دقائق

// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    const currencyRows = document.querySelectorAll('.currency-row');

    currencyRows.forEach(row => {
        row.addEventListener('click', function() {
            const currency = this.dataset.currency;
            // يمكن إضافة المزيد من التفاعل هنا
            console.log('تم النقر على عملة:', currency);
        });
    });
});
</script>
