{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-exchange-alt text-info me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'treasury:home' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للخزينة
                    </a>
                    <a href="{% url 'treasury:transfer_create' %}" class="btn btn-info">
                        <i class="fas fa-plus me-1"></i>
                        إضافة تحويل
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="{{ search_query }}"
                                       placeholder="البحث في التحويلات (رقم التحويل، البيان)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="{% url 'treasury:transfer_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة التحويلات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة التحويلات بين الخزائن
                        {% if page_obj %}
                            <span class="badge bg-info ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم التحويل</th>
                                        <th>التاريخ</th>
                                        <th>من خزينة</th>
                                        <th>إلى خزينة</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإكمال</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transfer in page_obj %}
                                        <tr>
                                            <td><strong>{{ transfer.transfer_number }}</strong></td>
                                            <td>{{ transfer.date|date:"d/m/Y" }}</td>
                                            <td>
                                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                                    <i class="fas fa-arrow-left me-1"></i>
                                                    {{ transfer.from_treasury.name }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success bg-opacity-10 text-success">
                                                    <i class="fas fa-arrow-right me-1"></i>
                                                    {{ transfer.to_treasury.name }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-info fw-bold">
                                                    {{ transfer.amount|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                            <td>{{ transfer.description|truncatechars:40 }}</td>
                                            <td>
                                                {% if transfer.status == 'PENDING' %}
                                                    <span class="badge bg-warning">{{ transfer.get_status_display }}</span>
                                                {% elif transfer.status == 'COMPLETED' %}
                                                    <span class="badge bg-success">{{ transfer.get_status_display }}</span>
                                                {% elif transfer.status == 'CANCELLED' %}
                                                    <span class="badge bg-danger">{{ transfer.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if transfer.completion_date %}
                                                    {{ transfer.completion_date|date:"d/m/Y" }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    {% if transfer.status == 'PENDING' %}
                                                        <a href="{% url 'treasury:transfer_edit' transfer.pk %}" 
                                                           class="btn btn-outline-primary" 
                                                           title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-outline-success complete-btn" 
                                                                data-id="{{ transfer.pk }}"
                                                                data-name="{{ transfer.transfer_number }}"
                                                                title="إكمال التحويل">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger delete-btn" 
                                                                data-id="{{ transfer.pk }}"
                                                                data-name="{{ transfer.transfer_number }}"
                                                                title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type="button" 
                                                                class="btn btn-outline-info print-btn" 
                                                                data-id="{{ transfer.pk }}"
                                                                title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تحويلات</h5>
                            <p class="text-muted">لم يتم العثور على أي تحويلات مطابقة لمعايير البحث</p>
                            <a href="{% url 'treasury:transfer_create' %}" class="btn btn-info">
                                <i class="fas fa-plus me-2"></i>
                                إضافة تحويل جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إكمال تحويل
document.addEventListener('DOMContentLoaded', function() {
    const completeButtons = document.querySelectorAll('.complete-btn');
    completeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transferId = this.getAttribute('data-id');
            const transferNumber = this.getAttribute('data-name');
            
            if (confirm(`هل تريد إكمال التحويل "${transferNumber}"؟ سيتم تطبيق التحويل على الخزائن.`)) {
                fetch(`/treasury/transfers/${transferId}/complete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء إكمال التحويل');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء إكمال التحويل');
                });
            }
        });
    });

    // حذف تحويل
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transferId = this.getAttribute('data-id');
            const transferNumber = this.getAttribute('data-name');
            
            if (confirm(`هل أنت متأكد من حذف التحويل "${transferNumber}"؟`)) {
                fetch(`/treasury/transfers/${transferId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        });
    });

    // طباعة تحويل
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transferId = this.getAttribute('data-id');
            // يمكن إضافة رابط الطباعة هنا
            alert('سيتم إضافة وظيفة الطباعة قريباً');
        });
    });
});
</script>
{% endblock %}
