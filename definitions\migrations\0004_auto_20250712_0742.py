# Generated by Django 5.2.4 on 2025-07-12 04:42

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0003_remove_companysettings_address_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # حذف الجدول القديم
        migrations.DeleteModel(
            name='CompanySettings',
        ),

        # إعادة إنشاء الجدول الجديد
        migrations.CreateModel(
            name='CompanySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('company_name', models.CharField(default='حسابات أوساريك', max_length=200, verbose_name='اسم الشركة')),
                ('company_subtitle', models.CharField(default='نظام إدارة الحسابات المتكامل والاحترافي', max_length=300, verbose_name='العنوان الفرعي')),
                ('company_logo', models.ImageField(blank=True, help_text='يفضل أن يكون الشعار مربع الشكل (مثل 200x200 بكسل)', null=True, upload_to='company_logos/', verbose_name='شعار الشركة')),
                ('company_address', models.TextField(blank=True, null=True, verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد الشركة الإلكتروني')),
                ('company_website', models.URLField(blank=True, null=True, verbose_name='موقع الشركة الإلكتروني')),
                ('header_background_color', models.CharField(default='#667eea', help_text='كود اللون بصيغة HEX (مثل #667eea)', max_length=7, verbose_name='لون خلفية الشريط العلوي')),
                ('show_logo_in_header', models.BooleanField(default=True, verbose_name='إظهار الشعار في الشريط العلوي')),
                ('show_company_name_in_header', models.BooleanField(default=True, verbose_name='إظهار اسم الشركة في الشريط العلوي')),
                ('show_subtitle_in_header', models.BooleanField(default=True, verbose_name='إظهار العنوان الفرعي في الشريط العلوي')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات الشركة',
                'verbose_name_plural': 'إعدادات الشركة',
            },
        ),
    ]
