#!/usr/bin/env python3
"""
أداة إصلاح الأخطاء التلقائية
Automatic Error Fixing Tool

تشخيص وإصلاح جميع الأخطاء تلقائياً
Diagnose and fix all errors automatically
"""

import os
import sys
import time
import psutil
import subprocess
import socket
from datetime import datetime

class ErrorFixer:
    """أداة إصلاح الأخطاء"""
    
    def __init__(self):
        self.fixed_issues = []
        self.remaining_issues = []
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
        self.fixed_issues.append(message)
    
    def log_error(self, message):
        """تسجيل خطأ"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
        self.remaining_issues.append(message)
    
    def run_command(self, cmd, description="تشغيل أمر"):
        """تشغيل أمر بأمان"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return True, result.stdout
            else:
                return False, result.stderr
        except subprocess.TimeoutExpired:
            return False, "انتهت المهلة الزمنية"
        except Exception as e:
            return False, str(e)
    
    def kill_processes_on_port(self, port=8000):
        """قتل العمليات المستخدمة للمنفذ"""
        self.log_info(f"البحث عن العمليات المستخدمة للمنفذ {port}...")
        
        killed_processes = []
        
        try:
            # البحث عن العمليات المستخدمة للمنفذ
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    connections = proc.info['connections']
                    if connections:
                        for conn in connections:
                            if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                                pid = proc.info['pid']
                                name = proc.info['name']
                                
                                # قتل العملية
                                try:
                                    process = psutil.Process(pid)
                                    process.terminate()
                                    
                                    # انتظار الإنهاء
                                    try:
                                        process.wait(timeout=5)
                                    except psutil.TimeoutExpired:
                                        process.kill()
                                    
                                    killed_processes.append(f"{name} (PID: {pid})")
                                    self.log_success(f"تم إنهاء العملية {name} (PID: {pid})")
                                    
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    pass
                                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if killed_processes:
                self.log_success(f"تم إنهاء {len(killed_processes)} عملية")
                time.sleep(2)  # انتظار قصير
                return True
            else:
                self.log_info("لم يتم العثور على عمليات تستخدم المنفذ")
                return True
                
        except Exception as e:
            self.log_error(f"خطأ في قتل العمليات: {e}")
            return False
    
    def check_port_availability(self, port=8000):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                return False  # المنفذ مستخدم
            else:
                return True   # المنفذ متاح
        except Exception:
            return True  # نفترض أنه متاح
    
    def fix_port_issue(self, port=8000):
        """إصلاح مشكلة المنفذ"""
        self.log_info(f"إصلاح مشكلة المنفذ {port}...")
        
        if self.check_port_availability(port):
            self.log_success(f"المنفذ {port} متاح بالفعل")
            return True
        
        # محاولة قتل العمليات
        if self.kill_processes_on_port(port):
            # فحص مرة أخرى
            time.sleep(2)
            if self.check_port_availability(port):
                self.log_success(f"تم تحرير المنفذ {port} بنجاح")
                return True
            else:
                self.log_error(f"فشل في تحرير المنفذ {port}")
                return False
        else:
            return False
    
    def fix_django_warnings(self):
        """إصلاح تحذيرات Django"""
        self.log_info("إصلاح تحذيرات Django...")
        
        try:
            # البحث عن ملفات models.py المكررة
            models_files = []
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file == 'models.py':
                        models_files.append(os.path.join(root, file))
            
            # فحص التطبيقات المكررة في INSTALLED_APPS
            settings_file = 'osaric_accounts/settings.py'
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إزالة التطبيقات المكررة (إذا وجدت)
                # هذا مجرد مثال - يمكن تخصيصه حسب الحاجة
                
                self.log_success("تم فحص إعدادات Django")
                return True
            else:
                self.log_error("ملف الإعدادات غير موجود")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في إصلاح تحذيرات Django: {e}")
            return False
    
    def fix_database_issues(self):
        """إصلاح مشاكل قاعدة البيانات"""
        self.log_info("فحص وإصلاح مشاكل قاعدة البيانات...")
        
        try:
            # تشغيل الترحيلات
            success, output = self.run_command("python manage.py migrate", "تشغيل الترحيلات")
            if success:
                self.log_success("تم تشغيل الترحيلات بنجاح")
            else:
                self.log_error(f"فشل في الترحيلات: {output}")
                return False
            
            # فحص سلامة قاعدة البيانات
            if os.path.exists('db.sqlite3'):
                import sqlite3
                try:
                    conn = sqlite3.connect('db.sqlite3', timeout=10)
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    result = cursor.fetchone()
                    conn.close()
                    
                    if result[0] == 'ok':
                        self.log_success("قاعدة البيانات سليمة")
                        return True
                    else:
                        self.log_error(f"مشكلة في قاعدة البيانات: {result[0]}")
                        return False
                        
                except Exception as e:
                    self.log_error(f"خطأ في فحص قاعدة البيانات: {e}")
                    return False
            else:
                self.log_info("قاعدة البيانات غير موجودة - سيتم إنشاؤها")
                return True
                
        except Exception as e:
            self.log_error(f"خطأ في إصلاح قاعدة البيانات: {e}")
            return False
    
    def fix_static_files(self):
        """إصلاح مشاكل الملفات الثابتة"""
        self.log_info("إصلاح مشاكل الملفات الثابتة...")
        
        try:
            # إنشاء مجلدات الملفات الثابتة
            static_dirs = ['static', 'staticfiles', 'media']
            for dir_name in static_dirs:
                if not os.path.exists(dir_name):
                    os.makedirs(dir_name)
                    self.log_success(f"تم إنشاء مجلد {dir_name}")
            
            # جمع الملفات الثابتة
            success, output = self.run_command("python manage.py collectstatic --noinput", "جمع الملفات الثابتة")
            if success:
                self.log_success("تم جمع الملفات الثابتة بنجاح")
                return True
            else:
                self.log_info(f"تحذير في جمع الملفات الثابتة: {output}")
                return True  # لا نعتبرها مشكلة حرجة
                
        except Exception as e:
            self.log_error(f"خطأ في إصلاح الملفات الثابتة: {e}")
            return False
    
    def fix_permissions(self):
        """إصلاح مشاكل الصلاحيات"""
        self.log_info("فحص وإصلاح مشاكل الصلاحيات...")
        
        try:
            important_files = ['manage.py', 'db.sqlite3']
            
            for file_path in important_files:
                if os.path.exists(file_path):
                    # فحص الصلاحيات
                    if os.access(file_path, os.R_OK):
                        self.log_success(f"{file_path}: قابل للقراءة")
                    else:
                        self.log_error(f"{file_path}: غير قابل للقراءة")
                        return False
                    
                    if file_path == 'db.sqlite3':
                        if os.access(file_path, os.W_OK):
                            self.log_success(f"{file_path}: قابل للكتابة")
                        else:
                            self.log_error(f"{file_path}: غير قابل للكتابة")
                            return False
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في فحص الصلاحيات: {e}")
            return False
    
    def test_server_startup(self, port=8001):
        """اختبار تشغيل الخادم على منفذ بديل"""
        self.log_info(f"اختبار تشغيل الخادم على المنفذ {port}...")
        
        try:
            # تشغيل الخادم لفترة قصيرة
            cmd = [sys.executable, 'manage.py', 'runserver', f'127.0.0.1:{port}', '--noreload']
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # انتظار 3 ثوانٍ
            time.sleep(3)
            
            # فحص إذا كان الخادم ما زال يعمل
            if process.poll() is None:
                self.log_success(f"الخادم يعمل بنجاح على المنفذ {port}")
                
                # اختبار الاتصال
                try:
                    import urllib.request
                    response = urllib.request.urlopen(f'http://127.0.0.1:{port}/', timeout=5)
                    if response.getcode() == 200:
                        self.log_success("الخادم يستجيب للطلبات")
                        success = True
                    else:
                        self.log_error(f"الخادم يرد بكود: {response.getcode()}")
                        success = False
                except Exception as e:
                    self.log_error(f"الخادم لا يستجيب: {e}")
                    success = False
                
                # إيقاف الخادم
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                
                return success
            else:
                # الخادم توقف
                stdout, stderr = process.communicate()
                self.log_error(f"الخادم توقف فوراً: {stderr}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في اختبار الخادم: {e}")
            return False
    
    def run_comprehensive_fix(self):
        """تشغيل إصلاح شامل لجميع الأخطاء"""
        print("=" * 60)
        print("🔧 أداة إصلاح الأخطاء التلقائية")
        print("Automatic Error Fixing Tool")
        print("=" * 60)
        
        self.log_info("بدء الإصلاح الشامل للأخطاء...")
        
        # 1. إصلاح مشكلة المنفذ
        if self.fix_port_issue():
            self.log_success("تم إصلاح مشكلة المنفذ")
        
        # 2. إصلاح مشاكل قاعدة البيانات
        if self.fix_database_issues():
            self.log_success("تم إصلاح مشاكل قاعدة البيانات")
        
        # 3. إصلاح مشاكل الملفات الثابتة
        if self.fix_static_files():
            self.log_success("تم إصلاح مشاكل الملفات الثابتة")
        
        # 4. إصلاح مشاكل الصلاحيات
        if self.fix_permissions():
            self.log_success("تم فحص الصلاحيات")
        
        # 5. إصلاح تحذيرات Django
        if self.fix_django_warnings():
            self.log_success("تم فحص تحذيرات Django")
        
        # 6. اختبار تشغيل الخادم
        if self.test_server_startup():
            self.log_success("اختبار تشغيل الخادم نجح")
        
        # عرض النتائج النهائية
        self.show_final_results()
    
    def show_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("📊 نتائج الإصلاح")
        print("=" * 60)
        
        print(f"✅ المشاكل المحلولة ({len(self.fixed_issues)}):")
        for issue in self.fixed_issues:
            print(f"  • {issue}")
        
        if self.remaining_issues:
            print(f"\n❌ المشاكل المتبقية ({len(self.remaining_issues)}):")
            for issue in self.remaining_issues:
                print(f"  • {issue}")
        else:
            print("\n🎉 تم حل جميع المشاكل بنجاح!")
        
        print("\n" + "=" * 60)
        print("🚀 الخطوات التالية:")
        print("1. تشغيل الخادم المستقر: python ultra_stable_server.py")
        print("2. أو تشغيل خادم الشبكة: python network_server.py")
        print("3. أو استخدام السكريبت: start_network.bat")
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    fixer = ErrorFixer()
    fixer.run_comprehensive_fix()

if __name__ == "__main__":
    main()
