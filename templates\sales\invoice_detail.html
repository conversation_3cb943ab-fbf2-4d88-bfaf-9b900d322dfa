{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice text-success me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    {% if invoice.status == 'DRAFT' %}
                        <a href="{% url 'sales:invoice_edit' invoice.pk %}" class="btn btn-success me-2">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-primary me-2" onclick="printInvoice()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle text-success me-2"></i>
                                معلومات الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>رقم الفاتورة:</strong></div>
                                <div class="col-sm-8">{{ invoice.invoice_number }}</div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>التاريخ:</strong></div>
                                <div class="col-sm-8">{{ invoice.date|date:"d/m/Y" }}</div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>تاريخ الاستحقاق:</strong></div>
                                <div class="col-sm-8">
                                    {% if invoice.due_date %}
                                        {{ invoice.due_date|date:"d/m/Y" }}
                                        {% if invoice.due_date < today %}
                                            <span class="badge bg-danger ms-2">متأخر</span>
                                        {% elif invoice.days_until_due <= 7 %}
                                            <span class="badge bg-warning ms-2">{{ invoice.days_until_due }} أيام متبقية</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>الحالة:</strong></div>
                                <div class="col-sm-8">
                                    {% if invoice.status == 'DRAFT' %}
                                        <span class="badge bg-secondary">{{ invoice.get_status_display }}</span>
                                    {% elif invoice.status == 'CONFIRMED' %}
                                        <span class="badge bg-primary">{{ invoice.get_status_display }}</span>
                                    {% elif invoice.status == 'DELIVERED' %}
                                        <span class="badge bg-info">{{ invoice.get_status_display }}</span>
                                    {% elif invoice.status == 'PAID' %}
                                        <span class="badge bg-success">{{ invoice.get_status_display }}</span>
                                    {% elif invoice.status == 'CANCELLED' %}
                                        <span class="badge bg-danger">{{ invoice.get_status_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>العملة:</strong></div>
                                <div class="col-sm-8">{{ invoice.currency.name }}</div>
                            </div>
                            {% if invoice.exchange_rate != 1 %}
                                <hr>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>سعر الصرف:</strong></div>
                                    <div class="col-sm-8">{{ invoice.exchange_rate }}</div>
                                </div>
                            {% endif %}
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>المخزن:</strong></div>
                                <div class="col-sm-8">{{ invoice.warehouse.name }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user text-primary me-2"></i>
                                معلومات العميل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>اسم العميل:</strong></div>
                                <div class="col-sm-8">{{ invoice.customer.name }}</div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>كود العميل:</strong></div>
                                <div class="col-sm-8">{{ invoice.customer.code }}</div>
                            </div>
                            {% if invoice.customer.phone %}
                                <hr>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>الهاتف:</strong></div>
                                    <div class="col-sm-8">{{ invoice.customer.phone }}</div>
                                </div>
                            {% endif %}
                            {% if invoice.customer.email %}
                                <hr>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>البريد الإلكتروني:</strong></div>
                                    <div class="col-sm-8">{{ invoice.customer.email }}</div>
                                </div>
                            {% endif %}
                            {% if invoice.salesperson %}
                                <hr>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>مندوب المبيعات:</strong></div>
                                    <div class="col-sm-8">{{ invoice.salesperson.get_full_name|default:invoice.salesperson.username }}</div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- أصناف الفاتورة -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list text-info me-2"></i>
                                أصناف الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if items %}
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية</th>
                                                <th>سعر الوحدة</th>
                                                <th>الخصم</th>
                                                <th>الضريبة</th>
                                                <th>الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in items %}
                                                <tr>
                                                    <td>
                                                        <strong>{{ item.item.name }}</strong>
                                                        {% if item.item.code %}
                                                            <br><small class="text-muted">كود: {{ item.item.code }}</small>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ item.quantity|floatformat:3 }}</td>
                                                    <td>{{ item.unit_price|floatformat:2 }} {{ invoice.currency.symbol }}</td>
                                                    <td>
                                                        {% if item.discount_percentage > 0 %}
                                                            {{ item.discount_percentage|floatformat:2 }}%
                                                            <br><small>({{ item.discount_amount|floatformat:2 }} {{ invoice.currency.symbol }})</small>
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if item.tax_percentage > 0 %}
                                                            {{ item.tax_percentage|floatformat:2 }}%
                                                            <br><small>({{ item.tax_amount|floatformat:2 }} {{ invoice.currency.symbol }})</small>
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                    <td><strong>{{ item.total_amount|floatformat:2 }} {{ invoice.currency.symbol }}</strong></td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">لا توجد أصناف في هذه الفاتورة</h6>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- المجاميع -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator text-primary me-2"></i>
                                المجاميع
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-6"><strong>المجموع الفرعي:</strong></div>
                                <div class="col-6 text-end">{{ invoice.subtotal|floatformat:2 }} {{ invoice.currency.symbol }}</div>
                            </div>
                            {% if invoice.discount_amount > 0 %}
                                <div class="row mb-2">
                                    <div class="col-6"><strong>الخصم:</strong></div>
                                    <div class="col-6 text-end text-danger">-{{ invoice.discount_amount|floatformat:2 }} {{ invoice.currency.symbol }}</div>
                                </div>
                            {% endif %}
                            {% if invoice.tax_amount > 0 %}
                                <div class="row mb-2">
                                    <div class="col-6"><strong>الضريبة:</strong></div>
                                    <div class="col-6 text-end">{{ invoice.tax_amount|floatformat:2 }} {{ invoice.currency.symbol }}</div>
                                </div>
                            {% endif %}
                            <hr>
                            <div class="row mb-2">
                                <div class="col-6"><strong class="h5">الإجمالي:</strong></div>
                                <div class="col-6 text-end"><strong class="h5 text-success">{{ invoice.total_amount|floatformat:2 }} {{ invoice.currency.symbol }}</strong></div>
                            </div>
                            <hr>
                            <div class="row mb-2">
                                <div class="col-6"><strong>المبلغ المدفوع:</strong></div>
                                <div class="col-6 text-end text-success">{{ invoice.paid_amount|floatformat:2 }} {{ invoice.currency.symbol }}</div>
                            </div>
                            <div class="row">
                                <div class="col-6"><strong>المبلغ المتبقي:</strong></div>
                                <div class="col-6 text-end">
                                    {% if invoice.remaining_amount > 0 %}
                                        <span class="text-warning">{{ invoice.remaining_amount|floatformat:2 }} {{ invoice.currency.symbol }}</span>
                                    {% else %}
                                        <span class="text-success">{{ invoice.remaining_amount|floatformat:2 }} {{ invoice.currency.symbol }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الملاحظات -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-sticky-note text-secondary me-2"></i>
                                الملاحظات
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if invoice.notes %}
                                <p>{{ invoice.notes|linebreaks }}</p>
                            {% else %}
                                <p class="text-muted">لا توجد ملاحظات</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printInvoice() {
    window.print();
}
</script>
{% endblock %} 