#!/usr/bin/env python3
"""
سكريبت للتحقق من بيانات المخازن وإصلاح مشكلة عرض الإحصائيات
"""

import os
import sys
import django

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.models import Warehouse, StockMovement, Item
from django.db.models import Sum, Count, Q
from decimal import Decimal

def check_warehouse_data():
    """التحقق من بيانات المخازن"""
    print("=== فحص بيانات المخازن ===\n")
    
    try:
        warehouses = Warehouse.objects.all()
        
        for warehouse in warehouses:
            print(f"المخزن: {warehouse.name}")
            print(f"الوصف: {warehouse.description}")
            print("-" * 50)
            
            # حساب المخزون الفعلي
            stock_movements = StockMovement.objects.filter(warehouse=warehouse)
            
            # إجمالي الكميات
            total_in = stock_movements.filter(movement_type='in').aggregate(
                total=Sum('quantity')
            )['total'] or Decimal('0')
            
            total_out = stock_movements.filter(movement_type='out').aggregate(
                total=Sum('quantity')
            )['total'] or Decimal('0')
            
            current_stock = total_in - total_out
            
            # عدد الأصناف المختلفة
            unique_items = stock_movements.values('item').distinct().count()
            
            # الأصناف التي لديها مخزون فعلي
            items_with_stock = []
            for movement in stock_movements.values('item').distinct():
                item_id = movement['item']
                try:
                    item = Item.objects.get(id=item_id)
                    
                    item_in = stock_movements.filter(item=item, movement_type='in').aggregate(
                        total=Sum('quantity')
                    )['total'] or Decimal('0')
                    
                    item_out = stock_movements.filter(item=item, movement_type='out').aggregate(
                        total=Sum('quantity')
                    )['total'] or Decimal('0')
                    
                    item_stock = item_in - item_out
                    
                    if item_stock > 0:
                        items_with_stock.append({
                            'item': item,
                            'stock': item_stock
                        })
                except Item.DoesNotExist:
                    print(f"  تحذير: الصنف برقم {item_id} غير موجود")
                    continue
            
            print(f"إجمالي الكميات الواردة: {total_in}")
            print(f"إجمالي الكميات الصادرة: {total_out}")
            print(f"المخزون الحالي: {current_stock}")
            print(f"عدد الأصناف المختلفة: {unique_items}")
            print(f"عدد الأصناف التي لديها مخزون فعلي: {len(items_with_stock)}")
            
            if items_with_stock:
                print("\nالأصناف المتوفرة:")
                for item_data in items_with_stock:
                    print(f"  - {item_data['item'].name}: {item_data['stock']}")
            else:
                print("\nلا توجد أصناف متوفرة في المخزون")
            
            print("\n" + "=" * 60 + "\n")
    
    except Exception as e:
        print(f"خطأ في فحص البيانات: {e}")
        import traceback
        traceback.print_exc()

def fix_warehouse_stats():
    """إصلاح إحصائيات المخازن في قاعدة البيانات"""
    print("=== إصلاح إحصائيات المخازن ===\n")
    
    try:
        warehouses = Warehouse.objects.all()
        
        for warehouse in warehouses:
            print(f"معالجة المخزن: {warehouse.name}")
            
            # حساب الإحصائيات الصحيحة
            stock_movements = StockMovement.objects.filter(warehouse=warehouse)
            
            total_in = stock_movements.filter(movement_type='in').aggregate(
                total=Sum('quantity')
            )['total'] or Decimal('0')
            
            total_out = stock_movements.filter(movement_type='out').aggregate(
                total=Sum('quantity')
            )['total'] or Decimal('0')
            
            current_stock = total_in - total_out
            
            # عدد الأصناف التي لديها مخزون فعلي
            items_with_stock = 0
            for movement in stock_movements.values('item').distinct():
                item_id = movement['item']
                
                item_in = stock_movements.filter(item_id=item_id, movement_type='in').aggregate(
                    total=Sum('quantity')
                )['total'] or Decimal('0')
                
                item_out = stock_movements.filter(item_id=item_id, movement_type='out').aggregate(
                    total=Sum('quantity')
                )['total'] or Decimal('0')
                
                if (item_in - item_out) > 0:
                    items_with_stock += 1
            
            # تحديث المخزن إذا كان لديه حقول للإحصائيات
            if hasattr(warehouse, 'total_stock'):
                warehouse.total_stock = current_stock
                warehouse.save()
                print(f"  - تم تحديث إجمالي المخزون: {current_stock}")
            
            if hasattr(warehouse, 'items_count'):
                warehouse.items_count = items_with_stock
                warehouse.save()
                print(f"  - تم تحديث عدد الأصناف: {items_with_stock}")
            
            print(f"  - المخزون الحالي: {current_stock}")
            print(f"  - عدد الأصناف المتوفرة: {items_with_stock}")
            print()
    
    except Exception as e:
        print(f"خطأ في إصلاح الإحصائيات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_warehouse_data()
    fix_warehouse_stats()
    print("تم الانتهاء من فحص وإصلاح بيانات المخازن") 