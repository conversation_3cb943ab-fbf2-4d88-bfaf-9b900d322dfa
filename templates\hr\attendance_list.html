{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar-check text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تسجيل ومتابعة حضور وانصراف الموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printDataList('سجل الحضور والانصراف')">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ present_count }}</h3>
                    <p class="mb-0">حاضر</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ late_count }}</h3>
                    <p class="mb-0">متأخر</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ absent_count }}</h3>
                    <p class="mb-0">غائب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ total_count }}</h3>
                    <p class="mb-0">إجمالي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Form -->
    <div class="row mb-4">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-plus me-2"></i>إدخال حضور/انصراف جديد
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <div class="row g-3 align-items-end">
                            <div class="col-md-3">{{ form.employee.label_tag }}{{ form.employee }}</div>
                            <div class="col-md-2">{{ form.date.label_tag }}{{ form.date }}</div>
                            <div class="col-md-2">{{ form.check_in.label_tag }}{{ form.check_in }}</div>
                            <div class="col-md-2">{{ form.check_out.label_tag }}{{ form.check_out }}</div>
                            <div class="col-md-2">{{ form.notes.label_tag }}{{ form.notes }}</div>
                            <div class="col-md-1">{{ form.attendance_state.label_tag }}{{ form.attendance_state }}</div>
                        </div>
                        <div class="mt-3 text-end">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        حضور اليوم - {{ "now"|date:"Y-m-d" }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>الحالة التفصيلية</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in today_attendance %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ attendance.employee }}
                                    </td>
                                    <td>
                                        <span>
                                            {% if attendance.attendance_state %}
                                                {{ attendance.get_attendance_state_display }}
                                            {% else %}
                                                {{ attendance.status }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if attendance.check_in != '-' %}
                                            <span class="badge bg-success">{{ attendance.check_in }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.check_out != '-' %}
                                            <span class="badge bg-info">{{ attendance.check_out }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ attendance.duration }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:attendance_edit' attendance.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'hr:attendance_delete' attendance.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3 d-block"></i>
                                        لا توجد سجلات حضور لليوم
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
