"""
Django settings for osaric_accounts project.

Generated by 'django-admin startproject' using Django 5.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os

# Try to import deployment packages
try:
    import dj_database_url
    from decouple import config
    DEPLOYMENT_PACKAGES_AVAILABLE = True
except ImportError:
    DEPLOYMENT_PACKAGES_AVAILABLE = False

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
if DEPLOYMENT_PACKAGES_AVAILABLE:
    SECRET_KEY = config('SECRET_KEY', default='django-insecure-3t^u0@fc!j2bp_zmhi$o#ai8#od^j@tk0kx$m+u)ocbq+dzr7$')
    DEBUG = config('DEBUG', default=True, cast=bool)  # تفعيل DEBUG لرؤية الأخطاء
    ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='*').split(',')
else:
    SECRET_KEY = 'django-insecure-3t^u0@fc!j2bp_zmhi$o#ai8#od^j@tk0kx$m+u)ocbq+dzr7$'
    DEBUG = True  # تفعيل DEBUG لرؤية الأخطاء
    ALLOWED_HOSTS = ['*', '127.0.0.1', 'localhost', 'testserver']

# إعدادات إضافية لضمان ظهور تفاصيل الأخطاء
INTERNAL_IPS = ['127.0.0.1', 'localhost']

# تفعيل عرض الأخطاء التفصيلية
ADMINS = [('Admin', '<EMAIL>')]
MANAGERS = ADMINS

# إعدادات Logging لعرض الأخطاء
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third party apps
    'rest_framework',
    'corsheaders',
    'django_extensions',  # للـ HTTPS

    # Local apps
    'core',
    'accounts',
    'definitions',
    'inventory',
    'sales',
    'purchases',
    'banking',
    'treasury',
    'assets',
    'dashboard',
    'hr',
    'reports',
    'branches',
    'accounting',
    'services',
    'windows',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'services.middleware.SystemSettingsMiddleware',
    'services.middleware.CompanyInfoMiddleware',
    'services.middleware.UISettingsMiddleware',
    'services.middleware.FinancialSettingsMiddleware',
    'services.middleware.PrintSettingsMiddleware',
]

ROOT_URLCONF = 'osaric_accounts.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
                'services.context_processors.taskbar_settings',
                'services.context_processors.license_info',
                'services.context_processors.ui_settings',
                'definitions.context_processors.company_settings',
            ],
        },
    },
]

WSGI_APPLICATION = 'osaric_accounts.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 30,  # مهلة زمنية أطول للاستقرار
            'check_same_thread': False,  # للاستقرار في البيئات المتعددة الخيوط
        }
    }
}

# إعدادات قاعدة البيانات للنشر
if DEPLOYMENT_PACKAGES_AVAILABLE and 'DATABASE_URL' in os.environ:
    DATABASES['default'] = dj_database_url.parse(os.environ['DATABASE_URL'])


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'ar'

TIME_ZONE = 'Africa/Cairo'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Date and Time formatting - Gregorian Calendar (Miladي)
DATE_FORMAT = 'd/m/Y'  # DD/MM/YYYY format (ميلادي)
DATETIME_FORMAT = 'd/m/Y h:i A'  # DD/MM/YYYY HH:MM AM/PM
TIME_FORMAT = 'h:i A'  # HH:MM AM/PM
SHORT_DATE_FORMAT = 'd/m/Y'  # DD/MM/YYYY
SHORT_DATETIME_FORMAT = 'd/m/Y h:i A'  # DD/MM/YYYY HH:MM AM/PM

# Use Gregorian calendar (التقويم الميلادي)
USE_L10N = False  # Disable localization to force Gregorian formats
FIRST_DAY_OF_WEEK = 6  # Saturday (0=Sunday, 6=Saturday)
DATE_INPUT_FORMATS = [
    '%d/%m/%Y', '%d-%m-%Y',             # '25/10/2006', '25-10-2006' (DD/MM/YYYY)
    '%Y-%m-%d',                         # '2006-10-25' (ISO format)
    '%d/%m/%y', '%d-%m-%y',             # '25/10/06', '25-10-06'
    '%d %m %Y', '%d.%m.%Y',             # '25 10 2006', '25.10.2006'
    '%d %b %Y', '%d %b, %Y',            # '25 Oct 2006', '25 Oct, 2006'
    '%d %B %Y', '%d %B, %Y',            # '25 October 2006', '25 October, 2006'
]

DATETIME_INPUT_FORMATS = [
    '%d/%m/%Y %H:%M:%S',     # '25/10/2006 14:30:59'
    '%d/%m/%Y %H:%M',        # '25/10/2006 14:30'
    '%d/%m/%Y',              # '25/10/2006'
    '%d-%m-%Y %H:%M:%S',     # '25-10-2006 14:30:59'
    '%d-%m-%Y %H:%M',        # '25-10-2006 14:30'
    '%d-%m-%Y',              # '25-10-2006'
    '%Y-%m-%d %H:%M:%S',     # '2006-10-25 14:30:59' (ISO)
    '%Y-%m-%d %H:%M',        # '2006-10-25 14:30'
    '%Y-%m-%d',              # '2006-10-25'
    '%d/%m/%Y %I:%M:%S %p',  # '25/10/2006 02:30:59 PM'
    '%d/%m/%Y %I:%M %p',     # '25/10/2006 02:30 PM'
    '%d-%m-%Y %I:%M:%S %p',  # '25-10-2006 02:30:59 PM'
    '%d-%m-%Y %I:%M %p',     # '25-10-2006 02:30 PM'
]

TIME_INPUT_FORMATS = [
    '%H:%M:%S',     # '14:30:59'
    '%H:%M',        # '14:30'
    '%I:%M:%S %p',  # '02:30:59 PM'
    '%I:%M %p',     # '02:30 PM'
]

# Arabic language support
LANGUAGES = [
    ('ar', 'العربية'),
    ('en', 'English'),
]

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Whitenoise settings
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

CORS_ALLOW_CREDENTIALS = True

# Custom user model (if needed)
# AUTH_USER_MODEL = 'accounts.User'

# Login/Logout URLs
LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/accounts/login/'

# Session settings - محسنة للاستقرار
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # للاستقرار
SESSION_SAVE_EVERY_REQUEST = True

# إعدادات الاستقرار والأداء
# Stability and Performance Settings

# Database connection settings - للاستقرار
CONN_MAX_AGE = 600  # 10 دقائق

# إعدادات المهلة الزمنية
TIMEOUT_SECONDS = 300  # 5 دقائق

# إعدادات الذاكرة والأداء
DATA_UPLOAD_MAX_MEMORY_SIZE = ********  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = ********  # 10MB

# إعدادات الأمان للاستقرار
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True

# Cache settings - لتحسين الأداء والاستقرار
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# إعدادات HTTPS
SECURE_SSL_REDIRECT = False  # للتطوير المحلي
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 0  # للتطوير المحلي
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
CSRF_COOKIE_SECURE = False  # للتطوير المحلي
SESSION_COOKIE_SECURE = False  # للتطوير المحلي


# إعدادات HTTPS محسنة - Enhanced HTTPS Settings
SECURE_SSL_REDIRECT = False  # للتطوير المحلي
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 0  # للتطوير المحلي
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'SAMEORIGIN'
CSRF_COOKIE_SECURE = False  # للتطوير المحلي
SESSION_COOKIE_SECURE = False  # للتطوير المحلي
CSRF_TRUSTED_ORIGINS = [
    'https://***************:8443',
    'https://localhost:8443',
    'https://osaric.local:8443',
    'https://accounting.local:8443',
]
ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    '***************',
    'osaric.local',
    'accounting.local',
    'DESKTOP-H8H1ID4',
]

# إعدادات الأمان المحسنة - Enhanced Security Settings
SECURE_REFERRER_POLICY = 'same-origin'
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin-allow-popups'
