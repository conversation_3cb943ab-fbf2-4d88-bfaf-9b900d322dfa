#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, ItemType
from decimal import Decimal

def create_sample_products():
    """إنشاء منتجات نهائية عينة"""
    
    # إنشاء أو الحصول على فئة المنتجات النهائية
    category, created = ItemCategory.objects.get_or_create(
        code='FP',
        defaults={
            'name': 'منتجات نهائية',
            'description': 'المنتجات النهائية الجاهزة للبيع'
        }
    )
    
    if created:
        print(f'Category created: {category.name}')
    else:
        print(f'Category exists: {category.name}')
    
    # إنشاء بعض المنتجات النهائية
    products = [
        {'code': 'FP001', 'name': 'منتج أ', 'selling_price': 100},
        {'code': 'FP002', 'name': 'منتج ب', 'selling_price': 150},
        {'code': 'FP003', 'name': 'منتج ج', 'selling_price': 200},
        {'code': 'FP004', 'name': 'منتج د', 'selling_price': 250},
        {'code': 'FP005', 'name': 'منتج هـ', 'selling_price': 300},
    ]
    
    created_count = 0
    for product_data in products:
        item, created = Item.objects.get_or_create(
            code=product_data['code'],
            defaults={
                'name': product_data['name'],
                'item_type': ItemType.FINISHED_PRODUCT,
                'category': category,
                'selling_price': Decimal(str(product_data['selling_price'])),
                'unit': 'قطعة',
                'is_active': True
            }
        )
        if created:
            print(f'Product created: {item.name}')
            created_count += 1
        else:
            print(f'Product exists: {item.name}')
    
    total_products = Item.objects.filter(item_type=ItemType.FINISHED_PRODUCT, is_active=True).count()
    print(f'\nTotal finished products: {total_products}')
    print(f'New products created: {created_count}')

if __name__ == '__main__':
    create_sample_products()
