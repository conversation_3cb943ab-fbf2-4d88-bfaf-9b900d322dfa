#!/usr/bin/env python3
"""
خادم Django المستقر المبسط
Simple Stable Django Server

يضمن تشغيل الخادم بأقصى استقرار ولا يتوقف أبداً
Ensures maximum server stability and never stops
"""

import os
import sys
import time
import signal
import subprocess
import threading
from datetime import datetime

class SimpleStableServer:
    """خادم Django المستقر المبسط"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 1000
        self.restart_delay = 3
        self.port = 8000
        self.host = '127.0.0.1'
        
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] تم استلام إشارة الإيقاف...")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] بدء تشغيل الخادم على {self.host}:{self.port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}',
                '--noreload'
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.is_running = True
            print(f"[{datetime.now().strftime('%H:%M:%S')}] تم بدء الخادم بنجاح! PID: {self.server_process.pid}")
            return True
            
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] خطأ في بدء الخادم: {e}")
            return False
    
    def monitor_server_output(self):
        """مراقبة مخرجات الخادم"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    if 'ERROR' in line or 'Exception' in line:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] خطأ: {line}")
                    elif 'Starting development server' in line:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم جاهز: {line}")
                    elif 'Quit the server' in line:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم متاح للاستخدام")
                        
                if self.server_process.poll() is not None:
                    break
                    
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] خطأ في مراقبة المخرجات: {e}")
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            import urllib.request
            import urllib.error
            
            url = f"http://{self.host}:{self.port}/"
            
            try:
                response = urllib.request.urlopen(url, timeout=5)
                if response.getcode() == 200:
                    return True
                else:
                    return False
            except urllib.error.URLError:
                return False
                
        except Exception:
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] إعادة تشغيل الخادم...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        if self.restart_count < self.max_restarts:
            self.restart_count += 1
            print(f"[{datetime.now().strftime('%H:%M:%S')}] محاولة إعادة التشغيل رقم {self.restart_count}")
            return self.start_server()
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] تم الوصول للحد الأقصى من إعادة التشغيل")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] إيقاف الخادم...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] تم إيقاف الخادم بنجاح")
                
            except Exception as e:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def run_health_monitor(self):
        """مراقب الصحة المستمر"""
        while self.is_running:
            time.sleep(30)  # فحص كل 30 ثانية
            
            if not self.is_running:
                break
                
            # فحص حالة العملية
            if self.server_process and self.server_process.poll() is not None:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم توقف بشكل غير متوقع!")
                if not self.restart_server():
                    break
                continue
            
            # فحص الاستجابة
            if not self.check_server_health():
                print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم لا يستجيب، إعادة تشغيل...")
                if not self.restart_server():
                    break
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم يعمل بصحة جيدة")
    
    def run(self):
        """تشغيل الخادم المستقر"""
        print("=" * 60)
        print("🚀 خادم Django المستقر المبسط")
        print("Simple Stable Django Server")
        print("=" * 60)
        print(f"[{datetime.now().strftime('%H:%M:%S')}] بدء تشغيل النظام...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # بدء الخادم
        if not self.start_server():
            print(f"[{datetime.now().strftime('%H:%M:%S')}] فشل في بدء الخادم!")
            return False
        
        # بدء مراقبة المخرجات في خيط منفصل
        output_thread = threading.Thread(target=self.monitor_server_output)
        output_thread.daemon = True
        output_thread.start()
        
        # انتظار قليل للتأكد من بدء الخادم
        time.sleep(3)
        
        # بدء مراقب الصحة في خيط منفصل
        health_thread = threading.Thread(target=self.run_health_monitor)
        health_thread.daemon = True
        health_thread.start()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم يعمل بأقصى استقرار!")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] الموقع متاح على: http://{self.host}:{self.port}/")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] مراقبة مستمرة للصحة والاستقرار")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] اضغط Ctrl+C للإيقاف الآمن")
        print("=" * 60)
        
        try:
            # انتظار إنهاء العملية
            while self.is_running:
                if self.server_process:
                    self.server_process.wait()
                    if self.server_process.returncode != 0:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] الخادم توقف بكود خطأ: {self.server_process.returncode}")
                        if not self.restart_server():
                            break
                else:
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] تم الضغط على Ctrl+C")
        finally:
            self.stop_server()
            print(f"[{datetime.now().strftime('%H:%M:%S')}] تم إنهاء الخادم المستقر")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = SimpleStableServer()
    success = server.run()
    
    if success:
        print("✅ تم تشغيل الخادم بنجاح!")
    else:
        print("❌ فشل في تشغيل الخادم!")
        sys.exit(1)

if __name__ == "__main__":
    main()
