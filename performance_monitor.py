#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Monitor for Django Server
مراقب الأداء لخادم Django
"""

import os
import sys
import time
import json
import psutil
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

class PerformanceMonitor:
    """Performance monitoring system"""
    
    def __init__(self):
        self.db_path = Path("performance.db")
        self.stats_file = Path("performance_stats.json")
        self.init_database()
        
    def init_database(self):
        """Initialize performance database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    cpu_percent REAL,
                    memory_percent REAL,
                    memory_mb REAL,
                    disk_percent REAL,
                    network_sent_mb REAL,
                    network_recv_mb REAL,
                    active_connections INTEGER,
                    server_response_time REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    message TEXT NOT NULL,
                    severity TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def log_performance(self, data):
        """Log performance data to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO performance_log 
                (timestamp, cpu_percent, memory_percent, memory_mb, disk_percent, 
                 network_sent_mb, network_recv_mb, active_connections, server_response_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['timestamp'],
                data.get('cpu_percent', 0),
                data.get('memory_percent', 0),
                data.get('memory_mb', 0),
                data.get('disk_percent', 0),
                data.get('network_sent_mb', 0),
                data.get('network_recv_mb', 0),
                data.get('active_connections', 0),
                data.get('server_response_time', 0)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Performance logging error: {e}")
    
    def log_alert(self, alert_type, message, severity="WARNING"):
        """Log alert to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts (timestamp, alert_type, message, severity)
                VALUES (?, ?, ?, ?)
            ''', (datetime.now().isoformat(), alert_type, message, severity))
            
            conn.commit()
            conn.close()
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {severity}: {message}")
            
        except Exception as e:
            print(f"Alert logging error: {e}")
    
    def get_system_metrics(self):
        """Get comprehensive system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            # Network
            network = psutil.net_io_counters()
            network_sent_mb = network.bytes_sent / (1024 * 1024)
            network_recv_mb = network.bytes_recv / (1024 * 1024)
            
            # Active connections
            connections = len(psutil.net_connections())
            
            # Server response time
            response_time = self.check_server_response_time()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_mb': memory.used / (1024 * 1024),
                'disk_percent': disk.percent,
                'network_sent_mb': network_sent_mb,
                'network_recv_mb': network_recv_mb,
                'active_connections': connections,
                'server_response_time': response_time
            }
            
        except Exception as e:
            print(f"Error getting system metrics: {e}")
            return {}
    
    def check_server_response_time(self):
        """Check server response time"""
        try:
            import urllib.request
            import urllib.error
            
            start_time = time.time()
            url = "http://127.0.0.1:8000/"
            
            try:
                response = urllib.request.urlopen(url, timeout=10)
                if response.getcode() == 200:
                    return time.time() - start_time
                else:
                    return -1  # Error response
            except:
                return -1  # Connection failed
                
        except:
            return -1
    
    def check_thresholds(self, metrics):
        """Check performance thresholds and generate alerts"""
        alerts = []
        
        # CPU threshold
        if metrics.get('cpu_percent', 0) > 80:
            alerts.append(('CPU', f"High CPU usage: {metrics['cpu_percent']:.1f}%", "WARNING"))
        
        # Memory threshold
        if metrics.get('memory_percent', 0) > 85:
            alerts.append(('MEMORY', f"High memory usage: {metrics['memory_percent']:.1f}%", "WARNING"))
        
        # Disk threshold
        if metrics.get('disk_percent', 0) > 90:
            alerts.append(('DISK', f"High disk usage: {metrics['disk_percent']:.1f}%", "CRITICAL"))
        
        # Response time threshold
        response_time = metrics.get('server_response_time', 0)
        if response_time > 5:
            alerts.append(('RESPONSE', f"Slow server response: {response_time:.2f}s", "WARNING"))
        elif response_time == -1:
            alerts.append(('SERVER', "Server not responding", "CRITICAL"))
        
        # Log alerts
        for alert_type, message, severity in alerts:
            self.log_alert(alert_type, message, severity)
        
        return alerts
    
    def get_performance_summary(self, hours=24):
        """Get performance summary for the last N hours"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            since = (datetime.now() - timedelta(hours=hours)).isoformat()
            
            cursor.execute('''
                SELECT 
                    AVG(cpu_percent) as avg_cpu,
                    MAX(cpu_percent) as max_cpu,
                    AVG(memory_percent) as avg_memory,
                    MAX(memory_percent) as max_memory,
                    AVG(server_response_time) as avg_response,
                    MAX(server_response_time) as max_response,
                    COUNT(*) as total_records
                FROM performance_log 
                WHERE timestamp > ? AND server_response_time > 0
            ''', (since,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[6] > 0:  # total_records > 0
                return {
                    'avg_cpu': result[0] or 0,
                    'max_cpu': result[1] or 0,
                    'avg_memory': result[2] or 0,
                    'max_memory': result[3] or 0,
                    'avg_response': result[4] or 0,
                    'max_response': result[5] or 0,
                    'total_records': result[6]
                }
            else:
                return None
                
        except Exception as e:
            print(f"Error getting performance summary: {e}")
            return None
    
    def get_recent_alerts(self, hours=24):
        """Get recent alerts"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            since = (datetime.now() - timedelta(hours=hours)).isoformat()
            
            cursor.execute('''
                SELECT timestamp, alert_type, message, severity
                FROM alerts 
                WHERE timestamp > ?
                ORDER BY timestamp DESC
                LIMIT 50
            ''', (since,))
            
            results = cursor.fetchall()
            conn.close()
            
            return [{'timestamp': r[0], 'type': r[1], 'message': r[2], 'severity': r[3]} for r in results]
            
        except Exception as e:
            print(f"Error getting recent alerts: {e}")
            return []
    
    def print_dashboard(self):
        """Print performance dashboard"""
        print("\n" + "=" * 80)
        print("📊 PERFORMANCE DASHBOARD")
        print("=" * 80)
        
        # Current metrics
        metrics = self.get_system_metrics()
        if metrics:
            print(f"🖥️  CPU Usage: {metrics['cpu_percent']:.1f}%")
            print(f"💾 Memory Usage: {metrics['memory_mb']:.1f}MB ({metrics['memory_percent']:.1f}%)")
            print(f"💿 Disk Usage: {metrics['disk_percent']:.1f}%")
            print(f"🌐 Network: ↑{metrics['network_sent_mb']:.1f}MB ↓{metrics['network_recv_mb']:.1f}MB")
            print(f"🔗 Active Connections: {metrics['active_connections']}")
            
            response_time = metrics['server_response_time']
            if response_time > 0:
                print(f"⚡ Server Response: {response_time:.3f}s")
            else:
                print("⚡ Server Response: Not Available")
        
        # Performance summary
        summary = self.get_performance_summary()
        if summary:
            print(f"\n📈 24-Hour Summary ({summary['total_records']} records):")
            print(f"   CPU: Avg {summary['avg_cpu']:.1f}% | Max {summary['max_cpu']:.1f}%")
            print(f"   Memory: Avg {summary['avg_memory']:.1f}% | Max {summary['max_memory']:.1f}%")
            print(f"   Response: Avg {summary['avg_response']:.3f}s | Max {summary['max_response']:.3f}s")
        
        # Recent alerts
        alerts = self.get_recent_alerts(hours=1)
        if alerts:
            print(f"\n⚠️  Recent Alerts ({len(alerts)}):")
            for alert in alerts[:5]:  # Show last 5 alerts
                timestamp = datetime.fromisoformat(alert['timestamp']).strftime('%H:%M:%S')
                print(f"   [{timestamp}] {alert['severity']}: {alert['message']}")
        
        print("=" * 80)
    
    def run_monitoring(self, interval=60):
        """Run continuous monitoring"""
        print("🚀 Starting Performance Monitor...")
        print(f"📊 Monitoring interval: {interval} seconds")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                # Get metrics
                metrics = self.get_system_metrics()
                if metrics:
                    # Log to database
                    self.log_performance(metrics)
                    
                    # Check thresholds
                    self.check_thresholds(metrics)
                    
                    # Print dashboard every 5 minutes
                    if int(time.time()) % 300 == 0:
                        self.print_dashboard()
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Performance monitoring stopped")
        except Exception as e:
            print(f"Monitoring error: {e}")

def main():
    """Main function"""
    monitor = PerformanceMonitor()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "dashboard":
            monitor.print_dashboard()
        elif sys.argv[1] == "summary":
            hours = int(sys.argv[2]) if len(sys.argv) > 2 else 24
            summary = monitor.get_performance_summary(hours)
            if summary:
                print(f"Performance Summary ({hours} hours):")
                print(json.dumps(summary, indent=2))
            else:
                print("No performance data available")
        elif sys.argv[1] == "alerts":
            hours = int(sys.argv[2]) if len(sys.argv) > 2 else 24
            alerts = monitor.get_recent_alerts(hours)
            print(f"Recent Alerts ({len(alerts)}):")
            for alert in alerts:
                print(f"[{alert['timestamp']}] {alert['severity']}: {alert['message']}")
        else:
            print("Usage: python performance_monitor.py [dashboard|summary|alerts] [hours]")
    else:
        monitor.run_monitoring()

if __name__ == "__main__":
    main()