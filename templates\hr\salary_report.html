{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-money-check-alt text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقارير شاملة عن المرتبات والبدلات</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printSalaryReport()">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقرير
                    </button>
                    <button class="btn btn-primary" onclick="generateSalaryReport()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء تقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Salary Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ total_salary_cost }}</h3>
                    <p class="mb-0">إجمالي تكلفة المرتبات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ avg_salary }}</h3>
                    <p class="mb-0">متوسط الراتب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ total_allowances }}</h3>
                    <p class="mb-0">إجمالي البدلات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ total_deductions }}</h3>
                    <p class="mb-0">إجمالي الخصومات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Salary Report Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تقرير المرتبات التفصيلي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الخصومات</th>
                                    <th>صافي الراتب</th>
                                    <th>تاريخ آخر راتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in salary_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <strong>{{ salary.employee_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ salary.employee_id }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ salary.department }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ salary.basic_salary }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">{{ salary.allowances }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ salary.deductions }} ج.م</span>
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ salary.net_salary }} ج.م</strong>
                                    </td>
                                    <td>{{ salary.last_payment_date }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        لا توجد بيانات مرتبات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateSalaryReport() {
    alert('سيتم إنشاء تقرير المرتبات');
}

function printSalaryReport() {
    // جمع بيانات المرتبات من الجدول
    const salaryData = [];
    const rows = document.querySelectorAll('table tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            salaryData.push({
                employee_name: cells[1]?.textContent?.trim() || 'غير محدد',
                department: cells[2]?.textContent?.trim() || 'غير محدد',
                basic_salary: cells[3]?.textContent?.trim() || '0',
                allowances: cells[4]?.textContent?.trim() || '0',
                deductions: cells[5]?.textContent?.trim() || '0',
                net_salary: cells[6]?.textContent?.trim() || '0',
                last_payment_date: cells[7]?.textContent?.trim() || 'غير محدد'
            });
        }
    });

    // استخدام القالب المخصص للطباعة
    if (typeof printPayrollReport !== 'undefined') {
        printPayrollReport(salaryData, 'تقرير المرتبات التفصيلي');
    } else {
        // استخدام الطباعة العادية كبديل
        printReport('تقرير المرتبات التفصيلي');
    }
}
</script>
{% endblock %}
