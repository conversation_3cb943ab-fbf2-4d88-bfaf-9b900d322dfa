import os
import django
from django.conf import settings
from django.db import connection

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

def delete_users_except_admin():
    try:
        with connection.cursor() as cursor:
            # Disable foreign key constraints temporarily
            cursor.execute("PRAGMA foreign_keys = OFF")
            
            # Delete users from auth_user table
            cursor.execute("DELETE FROM auth_user WHERE username != 'admin'")
            deleted_count = cursor.rowcount
            
            # Re-enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys = ON")
            
            print(f"Deleted {deleted_count} users from auth_user table")
            
    except Exception as e:
        print(f"Error deleting users: {e}")

if __name__ == "__main__":
    delete_users_except_admin()