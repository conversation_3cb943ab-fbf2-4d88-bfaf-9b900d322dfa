#!/usr/bin/env python
"""
Direct SQL deletion of inactive items
"""

import os
import sys
import django
import sqlite3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def direct_delete_inactive():
    """Delete inactive items directly using SQL"""
    
    # Get database path
    db_path = settings.DATABASES['default']['NAME']
    
    print("Direct Database Cleanup")
    print("=" * 40)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get inactive items count
        cursor.execute("SELECT COUNT(*) FROM definitions_item WHERE is_active = 0")
        items_count = cursor.fetchone()[0]
        
        # Get inactive categories count
        cursor.execute("SELECT COUNT(*) FROM definitions_itemcategory WHERE is_active = 0")
        categories_count = cursor.fetchone()[0]
        
        # Get inactive units count
        cursor.execute("SELECT COUNT(*) FROM definitions_unit WHERE is_active = 0")
        units_count = cursor.fetchone()[0]
        
        print(f"Found {items_count} inactive items")
        print(f"Found {categories_count} inactive categories")
        print(f"Found {units_count} inactive units")
        
        if items_count == 0 and categories_count == 0 and units_count == 0:
            print("No inactive data found to delete")
            return
        
        # Show inactive items
        if items_count > 0:
            print("\nInactive items:")
            cursor.execute("SELECT name, code FROM definitions_item WHERE is_active = 0")
            items = cursor.fetchall()
            for name, code in items:
                print(f"  - {name} ({code})")
        
        # Show inactive categories
        if categories_count > 0:
            print("\nInactive categories:")
            cursor.execute("SELECT name FROM definitions_itemcategory WHERE is_active = 0")
            categories = cursor.fetchall()
            for (name,) in categories:
                print(f"  - {name}")
        
        # Show inactive units
        if units_count > 0:
            print("\nInactive units:")
            cursor.execute("SELECT name FROM definitions_unit WHERE is_active = 0")
            units = cursor.fetchall()
            for (name,) in units:
                print(f"  - {name}")
        
        # Confirm deletion
        confirm = input(f"\nDelete all inactive data permanently? (y/N): ").strip().lower()
        
        if confirm not in ['y', 'yes']:
            print("Operation cancelled")
            return
        
        # Delete inactive data
        deleted_total = 0
        
        if items_count > 0:
            cursor.execute("DELETE FROM definitions_item WHERE is_active = 0")
            deleted_items = cursor.rowcount
            print(f"Deleted {deleted_items} items")
            deleted_total += deleted_items
        
        if categories_count > 0:
            cursor.execute("DELETE FROM definitions_itemcategory WHERE is_active = 0")
            deleted_categories = cursor.rowcount
            print(f"Deleted {deleted_categories} categories")
            deleted_total += deleted_categories
        
        if units_count > 0:
            cursor.execute("DELETE FROM definitions_unit WHERE is_active = 0")
            deleted_units = cursor.rowcount
            print(f"Deleted {deleted_units} units")
            deleted_total += deleted_units
        
        # Commit changes
        conn.commit()
        
        print(f"\nTotal deleted: {deleted_total} records")
        print("Database cleanup completed successfully!")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    direct_delete_inactive()