{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">تفاصيل المخزن</h2>
    <div class="card mb-4">
        <div class="card-body">
            <h4 class="card-title">{{ warehouse.name|default:'-' }}</h4>
            <p class="card-text"><strong>الكود:</strong> {{ warehouse.code|default:'-' }}</p>
            <p class="card-text"><strong>العنوان:</strong> {{ warehouse.address|default:'-' }}</p>
            <p class="card-text"><strong>الوصف:</strong> {{ warehouse.description|default:'-' }}</p>
            <p class="card-text"><strong>الحالة:</strong> 
                {% if warehouse.is_active %}
                    <span class="badge bg-success">نشط</span>
                {% else %}
                    <span class="badge bg-danger">غير نشط</span>
                {% endif %}
            </p>
            <p class="card-text"><strong>تاريخ الإنشاء:</strong> {% if warehouse.created_at %}{{ warehouse.created_at|date:'Y-m-d H:i' }}{% else %}-{% endif %}</p>
        </div>
    </div>
    <h5 class="mb-3">الأصناف في هذا المخزن</h5>
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="table-light">
                <tr>
                    <th>م</th>
                    <th>اسم الصنف</th>
                    <th>الكود</th>
                    <th>الوحدة</th>
                    <th>الكمية</th>
                    <th>متوسط التكلفة</th>
                </tr>
            </thead>
            <tbody>
                {% for stock in stock_items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ stock.item.name|default:'-' }}</td>
                    <td>{{ stock.item.code|default:'-' }}</td>
                    <td>{{ stock.item.unit.name|default:'-' }}</td>
                    <td>{{ stock.quantity|default:'-' }}</td>
                    <td>{{ stock.average_cost|floatformat:2|default:'-' }}</td>
                    <td>{{ stock.item.selling_price|floatformat:2|default:'-' }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center">لا توجد أصناف في هذا المخزن.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <a href="{% url 'inventory:warehouse_analytics' %}" class="btn btn-secondary mt-3">رجوع إلى تحليلات المخازن</a>
</div>
{% endblock %}
