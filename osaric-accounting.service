[Unit]
Description=Osaric Accounting Stable Server
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=DJANGO_SETTINGS_MODULE=osaric_accounts.production_settings
ExecStart=/usr/bin/python3 /path/to/your/project/start_stable_server.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=osaric-accounting

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/path/to/your/project

[Install]
WantedBy=multi-user.target
