{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-graduate text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة تسجيل الموظفين في البرامج التدريبية</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addEnrollmentModal">
                        <i class="fas fa-user-plus me-2"></i>
                        تسجيل متدرب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollment Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>85</h3>
                    <p class="mb-0">إجمالي المسجلين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>68</h3>
                    <p class="mb-0">المكملين للبرنامج</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>12</h3>
                    <p class="mb-0">قيد التدريب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>5</h3>
                    <p class="mb-0">منسحبين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">البرنامج التدريبي:</label>
                            <select class="form-select" onchange="filterByProgram()">
                                <option value="">جميع البرامج</option>
                                <option value="leadership">القيادة والإدارة</option>
                                <option value="communication">مهارات التواصل</option>
                                <option value="excel">Excel المتقدم</option>
                                <option value="project">إدارة المشاريع</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة:</label>
                            <select class="form-select" onchange="filterByStatus()">
                                <option value="">جميع الحالات</option>
                                <option value="enrolled">مسجل</option>
                                <option value="in_progress">قيد التدريب</option>
                                <option value="completed">مكتمل</option>
                                <option value="withdrawn">منسحب</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="sales">المبيعات</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportEnrollments()">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                                <button class="btn btn-info" onclick="printEnrollments()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة المتدربين المسجلين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>البرنامج التدريبي</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>تاريخ البداية</th>
                                    <th>نسبة الإكمال</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <strong>{{ enrollment.employee }}</strong>
                                        <br>
                                        <small class="text-muted">{{ enrollment.position }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ enrollment.department }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-graduation-cap text-success me-2"></i>
                                        {{ enrollment.program }}
                                    </td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>{{ enrollment.start_date }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if enrollment.completion >= 80 %}bg-success
                                                {% elif enrollment.completion >= 50 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ enrollment.completion }}%">
                                                {{ enrollment.completion }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if enrollment.status == 'مكتمل' %}
                                            <span class="badge bg-success">{{ enrollment.status }}</span>
                                        {% elif enrollment.status == 'قيد التدريب' %}
                                            <span class="badge bg-warning">{{ enrollment.status }}</span>
                                        {% elif enrollment.status == 'مسجل' %}
                                            <span class="badge bg-primary">{{ enrollment.status }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ enrollment.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="تحديث التقدم">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="شهادة">
                                                <i class="fas fa-certificate"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-user-graduate fa-3x mb-3 d-block"></i>
                                        لا توجد تسجيلات للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Completions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        المكملين مؤخراً (آخر 7 أيام)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar bg-success text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">أحمد محمد</h6>
                                    <small class="text-muted">القيادة والإدارة - 100%</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar bg-success text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">فاطمة علي</h6>
                                    <small class="text-muted">مهارات التواصل - 95%</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar bg-success text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">محمد أحمد</h6>
                                    <small class="text-muted">Excel المتقدم - 100%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Enrollment Modal -->
<div class="modal fade" id="addEnrollmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل متدرب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <option value="1">أحمد محمد - الموارد البشرية</option>
                                    <option value="2">فاطمة علي - المالية</option>
                                    <option value="3">محمد أحمد - تقنية المعلومات</option>
                                    <option value="4">سارة علي - المبيعات</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البرنامج التدريبي</label>
                                <select class="form-select" required>
                                    <option value="">اختر البرنامج</option>
                                    <option value="leadership">القيادة والإدارة</option>
                                    <option value="communication">مهارات التواصل</option>
                                    <option value="excel">Excel المتقدم</option>
                                    <option value="project">إدارة المشاريع</option>
                                    <option value="marketing">التسويق الرقمي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ التسجيل</label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">أولوية التسجيل</label>
                                <select class="form-select" required>
                                    <option value="">اختر الأولوية</option>
                                    <option value="high">عالية</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="low">منخفضة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب التسجيل</label>
                        <textarea class="form-control" rows="3" placeholder="اذكر سبب تسجيل الموظف في هذا البرنامج"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="mandatory">
                            <label class="form-check-label" for="mandatory">
                                تدريب إجباري
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notification">
                            <label class="form-check-label" for="notification">
                                إرسال إشعار للموظف
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success">تسجيل المتدرب</button>
            </div>
        </div>
    </div>
</div>

<script>
function filterByProgram() {
    alert('تم تطبيق فلتر البرنامج التدريبي');
}

function filterByStatus() {
    alert('تم تطبيق فلتر الحالة');
}

function filterByDepartment() {
    alert('تم تطبيق فلتر القسم');
}

function exportEnrollments() {
    alert('سيتم تصدير قائمة المتدربين');
}

function printEnrollments() {
    alert('سيتم طباعة قائمة المتدربين');
}
</script>
{% endblock %}
