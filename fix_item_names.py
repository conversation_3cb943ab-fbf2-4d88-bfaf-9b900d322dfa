#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.contrib.auth.models import User

def fix_item_names():
    print("=== Fixing item names with Unicode issues ===")
    
    # Get admin user
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    
    # Get categories and units
    general_category = ItemCategory.objects.get(code='GEN')
    pcs_unit = Unit.objects.get(code='PCS')
    
    # Items with problematic names - update them with proper names
    items_to_fix = [
        {'code': 'OS022', 'new_name': 'قلم حبر أزرق'},
        {'code': 'OS015', 'new_name': 'دفتر ملاحظات'},
        {'code': 'OS019', 'new_name': 'مسطرة 30 سم'},
        {'code': 'OS011', 'new_name': 'ممحاة بيضاء'},
        {'code': 'OS012', 'new_name': 'قلم رصاص'},
        {'code': 'OS06', 'new_name': 'مبراة معدنية'},
        {'code': 'OS05', 'new_name': 'دبوس ورق'},
        {'code': 'OS07', 'new_name': 'مشبك ورق'},
        {'code': 'OS04', 'new_name': 'لاصق شفاف'},
        {'code': 'OS03', 'new_name': 'مقص صغير'},
        {'code': 'OS021', 'new_name': 'كراسة رسم'},
        {'code': 'OS020', 'new_name': 'ألوان خشبية'},
        {'code': 'OS010', 'new_name': 'صمغ أبيض'},
        {'code': '004', 'new_name': 'ورق A4'},
        {'code': 'OS014', 'new_name': 'حافظة ملفات'},
        {'code': 'OS09', 'new_name': 'دباسة صغيرة'},
        {'code': 'OS02', 'new_name': 'قلم تحديد'},
        {'code': 'OS08', 'new_name': 'مجلد بلاستيك'},
        {'code': 'OS01', 'new_name': 'كشكول سلك'},
        {'code': 'OS013', 'new_name': 'آلة حاسبة'},
        {'code': 'OS017', 'new_name': 'لوحة كتابة'},
        {'code': 'OS016', 'new_name': 'حقيبة مدرسية'},
        {'code': 'OS023', 'new_name': 'قاموس عربي'},
        {'code': 'OS018', 'new_name': 'كتاب تمارين'},
    ]
    
    fixed_count = 0
    for item_data in items_to_fix:
        try:
            item = Item.objects.get(code=item_data['code'])
            item.name = item_data['new_name']
            
            # Make sure it has proper category and unit
            if not item.category:
                item.category = general_category
            if not item.unit:
                item.unit = pcs_unit
            if not item.created_by:
                item.created_by = admin_user
                
            item.save()
            print(f"Fixed: {item.code} -> {item.name}")
            fixed_count += 1
        except Item.DoesNotExist:
            print(f"Item not found: {item_data['code']}")
        except Exception as e:
            print(f"Error fixing {item_data['code']}: {e}")
    
    print(f"\nFixed {fixed_count} items")
    
    # Show final count
    active_items = Item.objects.filter(is_active=True).count()
    print(f"Total active items: {active_items}")

if __name__ == "__main__":
    fix_item_names()