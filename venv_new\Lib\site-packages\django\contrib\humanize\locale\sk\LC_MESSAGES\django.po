# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012-2013
# <PERSON> <<EMAIL>>, 2017-2019,2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-04 18:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017-2019,2023\n"
"Language-Team: Slovak (http://app.transifex.com/django/django/language/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

msgid "Humanize"
msgstr "Poľudštenie"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milión"
msgstr[1] "%(value)s milióny"
msgstr[2] "%(value)s miliónov"
msgstr[3] "%(value)s miliónov"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliarda"
msgstr[1] "%(value)s miliardy"
msgstr[2] "%(value)s miliárd"
msgstr[3] "%(value)s miliárd"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s bilión"
msgstr[1] "%(value)s bilióny"
msgstr[2] "%(value)s biliónov"
msgstr[3] "%(value)s biliónov"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s biliarda"
msgstr[1] "%(value)s biliardy"
msgstr[2] "%(value)s biliárd"
msgstr[3] "%(value)s biliárd"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trilión"
msgstr[1] "%(value)s trilióny"
msgstr[2] "%(value)s triliónov"
msgstr[3] "%(value)s triliónov"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s triliarda"
msgstr[1] "%(value)s triliardy"
msgstr[2] "%(value)s triliárd"
msgstr[3] "%(value)s triliárd"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s kvadrilión"
msgstr[1] "%(value)s kvadrilióny"
msgstr[2] "%(value)s kvadriliónov"
msgstr[3] "%(value)s kvadriliónov"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kvadriliarda"
msgstr[1] "%(value)s kvadriliardy"
msgstr[2] "%(value)s kvadriliárd"
msgstr[3] "%(value)s kvadriliárd"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kvintilión"
msgstr[1] "%(value)s kvintilióny"
msgstr[2] "%(value)s kvintiliónov"
msgstr[3] "%(value)s kvintiliónov"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kvintiliarda"
msgstr[1] "%(value)s kvintiliardy"
msgstr[2] "%(value)s kvintiliárd"
msgstr[3] "%(value)s kvintiliárd"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s googol"
msgstr[3] "%(value)s googol"

msgid "one"
msgstr "jeden"

msgid "two"
msgstr "dva"

msgid "three"
msgstr "tri"

msgid "four"
msgstr "štyri"

msgid "five"
msgstr "päť"

msgid "six"
msgstr "šesť"

msgid "seven"
msgstr "sedem"

msgid "eight"
msgstr "osem"

msgid "nine"
msgstr "deväť"

msgid "today"
msgstr "dnes"

msgid "tomorrow"
msgstr "zajtra"

msgid "yesterday"
msgstr "včera"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "pred %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "pred hodinou"
msgstr[1] "pred %(count)s hodinami"
msgstr[2] "pred %(count)s hodinami"
msgstr[3] "pred %(count)s hodinami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "pred minútou"
msgstr[1] "pred %(count)s minútami"
msgstr[2] "pred %(count)s minútami"
msgstr[3] "pred %(count)s minútami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "pred sekundou"
msgstr[1] "pred %(count)s sekundami"
msgstr[2] "pred %(count)s sekundami"
msgstr[3] "pred %(count)s sekundami"

msgid "now"
msgstr "teraz"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "o sekundu"
msgstr[1] "o %(count)s sekundy"
msgstr[2] "o %(count)s sekúnd"
msgstr[3] "o %(count)s sekúnd"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "o minútu"
msgstr[1] "o %(count)s minúty"
msgstr[2] "o %(count)s minút"
msgstr[3] "o %(count)s minút"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "o hodinu"
msgstr[1] "o %(count)s hodiny"
msgstr[2] "o %(count)s hodín"
msgstr[3] "o %(count)s hodín"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "o %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rokom"
msgstr[1] "%(num)d rokmi"
msgstr[2] "%(num)d rokmi"
msgstr[3] "%(num)d rokmi"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mesiacom"
msgstr[1] "%(num)d mesiacmi"
msgstr[2] "%(num)d mesiacmi"
msgstr[3] "%(num)d mesiacmi"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d týždňom"
msgstr[1] "%(num)d týždňami"
msgstr[2] "%(num)d týždňami"
msgstr[3] "%(num)d týždňami"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dňom"
msgstr[1] "%(num)d dňami"
msgstr[2] "%(num)d dňami"
msgstr[3] "%(num)d dňami"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hodinou"
msgstr[1] "%(num)d hodinami"
msgstr[2] "%(num)d hodinami"
msgstr[3] "%(num)d hodinami"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minútou"
msgstr[1] "%(num)d minútami"
msgstr[2] "%(num)d minútami"
msgstr[3] "%(num)d minútami"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rok"
msgstr[1] "%(num)d roky"
msgstr[2] "%(num)d rokov"
msgstr[3] "%(num)d rokov"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mesiac"
msgstr[1] "%(num)d mesiace"
msgstr[2] "%(num)d mesiacov"
msgstr[3] "%(num)d mesiacov"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d týždeň"
msgstr[1] "%(num)d týždne"
msgstr[2] "%(num)d týždňov"
msgstr[3] "%(num)d týždňov"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d deň"
msgstr[1] "%(num)d dni"
msgstr[2] "%(num)d dní"
msgstr[3] "%(num)d dní"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hodinu"
msgstr[1] "%(num)d hodiny"
msgstr[2] "%(num)d hodín"
msgstr[3] "%(num)d hodín"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minútu"
msgstr[1] "%(num)d minúty"
msgstr[2] "%(num)d minút"
msgstr[3] "%(num)d minút"
