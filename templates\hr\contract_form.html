{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-file-contract me-2"></i> {{ title }}
                </div>
                <div class="card-body">
                    {% if renew_message %}
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-redo me-2"></i> {{ renew_message }}
                        </div>
                    {% endif %}
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>حدثت أخطاء في البيانات:</strong>
                            <ul class="mb-0">
                                {% for field in form %}
                                    {% for error in field.errors %}
                                        <li>{{ field.label }}: {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_employee" class="form-label">{{ form.employee.label }}</label>
                                {{ form.employee }}
                            </div>
                            <div class="col-md-6">
                                <label for="id_contract_type" class="form-label">{{ form.contract_type.label }}</label>
                                {{ form.contract_type }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_start_date" class="form-label">{{ form.start_date.label }}</label>
                                <input type="date" name="start_date" id="id_start_date" class="form-control" value="{{ form.start_date.value|default_if_none:'' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="id_end_date" class="form-label">{{ form.end_date.label }}</label>
                                <input type="date" name="end_date" id="id_end_date" class="form-control" value="{{ form.end_date.value|default_if_none:'' }}">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_salary" class="form-label">{{ form.salary.label }}</label>
                                {{ form.salary }}
                            </div>
                            <div class="col-md-6">
                                <label for="id_is_active" class="form-label">{{ form.is_active.label }}</label>
                                {{ form.is_active }} <span class="ms-2">نشط</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="id_contract_file" class="form-label">{{ form.contract_file.label }}</label>
                            {{ form.contract_file }}
                            <div class="form-text">يسمح فقط بملفات PDF أو صور JPG/PNG.</div>
                        </div>
                        <div class="mb-3">
                            <label for="id_notes" class="form-label">{{ form.notes.label }}</label>
                            {{ form.notes }}
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'hr:contract_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> إلغاء</a>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 