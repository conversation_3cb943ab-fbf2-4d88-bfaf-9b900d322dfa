#!/usr/bin/env python
"""
ملف لتعطيل Django autoreload نهائياً
"""

import os
import sys

# تعطيل Django autoreload
os.environ['DJANGO_AUTORELOAD'] = 'False'
os.environ['RUN_MAIN'] = 'true'

# تعطيل مراقبة الملفات
def disable_autoreload():
    """تعطيل Django autoreload"""
    try:
        from django.utils import autoreload
        autoreload.USE_INOTIFY = False
        autoreload.RUN_RELOADER = False
        print("✅ تم تعطيل Django autoreload بنجاح")
    except ImportError:
        print("⚠️ لم يتم العثور على Django autoreload")

if __name__ == "__main__":
    disable_autoreload()
    print("🔧 تم تعطيل مراقبة الملفات")
