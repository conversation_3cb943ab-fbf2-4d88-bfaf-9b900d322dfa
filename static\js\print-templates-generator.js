/**
 * مولد قوالب الطباعة
 * Print Templates Generator
 */

// دالة إنشاء HTML لفاتورة المبيعات
function generateSalesInvoiceHTML() {
    const data = currentTemplateData;
    const settings = getCurrentDesignSettings();
    
    return `
        <div class="invoice-container">
            ${settings.showLogo ? `
                <div class="invoice-header">
                    <div class="company-logo">
                        <img src="/static/images/logo.png" alt="شعار الشركة" style="max-height: 80px;">
                    </div>
                    <div class="company-info">
                        <h1>شركة [اسم الشركة]</h1>
                        <p>العنوان: [عنوان الشركة]</p>
                        <p>الهاتف: [رقم الهاتف] | البريد الإلكتروني: [البريد الإلكتروني]</p>
                    </div>
                </div>
            ` : ''}
            
            <div class="invoice-title" style="background-color: ${settings.headerColor || '#007bff'};">
                <h2>${settings.invoiceTitle || 'فاتورة مبيعات'}</h2>
            </div>
            
            <div class="invoice-details">
                <div class="invoice-info">
                    <div class="info-section">
                        <h4>بيانات الفاتورة</h4>
                        <p><strong>رقم الفاتورة:</strong> ${data.invoiceNumber}</p>
                        <p><strong>التاريخ:</strong> ${data.date}</p>
                    </div>
                    <div class="customer-info">
                        <h4>بيانات العميل</h4>
                        <p><strong>اسم العميل:</strong> ${data.customer.name}</p>
                        <p><strong>العنوان:</strong> ${data.customer.address}</p>
                        <p><strong>الهاتف:</strong> ${data.customer.phone}</p>
                        <p><strong>الرقم الضريبي:</strong> ${data.customer.taxNumber}</p>
                    </div>
                </div>
            </div>
            
            <div class="invoice-items">
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${item.price.toFixed(2)}</td>
                                <td>${item.total.toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="invoice-totals">
                <div class="totals-section">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>${data.subtotal.toFixed(2)} ج.م</span>
                    </div>
                    <div class="total-row">
                        <span>الضريبة (15%):</span>
                        <span>${data.tax.toFixed(2)} ج.م</span>
                    </div>
                    <div class="total-row final-total">
                        <span>الإجمالي النهائي:</span>
                        <span>${data.total.toFixed(2)} ج.م</span>
                    </div>
                </div>
            </div>
            
            ${settings.showFooter ? `
                <div class="invoice-footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>هذه الفاتورة مُنشأة إلكترونياً ولا تحتاج إلى توقيع</p>
                </div>
            ` : ''}
        </div>
    `;
}

// دالة إنشاء CSS لفاتورة المبيعات
function getSalesInvoiceCSS() {
    const settings = getCurrentDesignSettings();
    
    return `
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            body {
                font-family: 'Arial', sans-serif;
                font-size: ${settings.fontSize || '12pt'};
                line-height: 1.4;
                color: #000;
                margin: 0;
                padding: 20px;
                direction: rtl;
                text-align: right;
            }
            
            .invoice-container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
            }
            
            .invoice-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #ddd;
            }
            
            .company-logo img {
                max-height: 80px;
            }
            
            .company-info {
                text-align: left;
            }
            
            .company-info h1 {
                margin: 0 0 10px 0;
                font-size: 24pt;
                color: #333;
            }
            
            .invoice-title {
                text-align: center;
                padding: 15px;
                margin-bottom: 30px;
                border-radius: 8px;
                color: white;
            }
            
            .invoice-title h2 {
                margin: 0;
                font-size: 20pt;
            }
            
            .invoice-details {
                margin-bottom: 30px;
            }
            
            .invoice-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }
            
            .info-section, .customer-info {
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 5px;
            }
            
            .info-section h4, .customer-info h4 {
                margin: 0 0 15px 0;
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 5px;
            }
            
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            
            .items-table th {
                background: #f8f9fa;
                border: 1px solid #333;
                padding: 12px 8px;
                text-align: center;
                font-weight: bold;
            }
            
            .items-table td {
                border: 1px solid #333;
                padding: 10px 8px;
                text-align: center;
            }
            
            .invoice-totals {
                margin-bottom: 30px;
            }
            
            .totals-section {
                float: left;
                width: 300px;
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 5px;
            }
            
            .total-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding: 5px 0;
            }
            
            .final-total {
                border-top: 2px solid #333;
                font-weight: bold;
                font-size: 14pt;
                color: #333;
            }
            
            .invoice-footer {
                clear: both;
                text-align: center;
                margin-top: 50px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
            }
            
            @page {
                margin: 2cm;
                size: A4;
            }
        }
        
        @media screen {
            body {
                font-family: 'Arial', sans-serif;
                font-size: 12pt;
                line-height: 1.4;
                color: #000;
                direction: rtl;
                text-align: right;
            }
            
            .invoice-container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
            
            .invoice-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #ddd;
            }
            
            .company-info h1 {
                margin: 0 0 10px 0;
                font-size: 18pt;
                color: #333;
            }
            
            .invoice-title {
                text-align: center;
                padding: 15px;
                margin-bottom: 30px;
                border-radius: 8px;
                color: white;
            }
            
            .invoice-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
            }
            
            .info-section, .customer-info {
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 5px;
            }
            
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            
            .items-table th {
                background: #f8f9fa;
                border: 1px solid #ddd;
                padding: 12px 8px;
                text-align: center;
            }
            
            .items-table td {
                border: 1px solid #ddd;
                padding: 10px 8px;
                text-align: center;
            }
            
            .totals-section {
                float: left;
                width: 300px;
                border: 1px solid #ddd;
                padding: 15px;
                border-radius: 5px;
            }
            
            .total-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding: 5px 0;
            }
            
            .final-total {
                border-top: 2px solid #333;
                font-weight: bold;
                color: #333;
            }
            
            .invoice-footer {
                clear: both;
                text-align: center;
                margin-top: 50px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
            }
        }
    `;
}

// دالة إنشاء HTML لفاتورة المشتريات
function generatePurchaseInvoiceHTML() {
    const data = currentTemplateData;
    const settings = getCurrentDesignSettings();
    
    return `
        <div class="invoice-container">
            ${settings.showLogo ? `
                <div class="invoice-header">
                    <div class="company-logo">
                        <img src="/static/images/logo.png" alt="شعار الشركة" style="max-height: 80px;">
                    </div>
                    <div class="company-info">
                        <h1>شركة [اسم الشركة]</h1>
                        <p>العنوان: [عنوان الشركة]</p>
                        <p>الهاتف: [رقم الهاتف] | البريد الإلكتروني: [البريد الإلكتروني]</p>
                    </div>
                </div>
            ` : ''}
            
            <div class="invoice-title" style="background-color: ${settings.headerColor || '#28a745'};">
                <h2>${settings.invoiceTitle || 'فاتورة مشتريات'}</h2>
            </div>
            
            <div class="invoice-details">
                <div class="invoice-info">
                    <div class="info-section">
                        <h4>بيانات الفاتورة</h4>
                        <p><strong>رقم الفاتورة:</strong> ${data.invoiceNumber}</p>
                        <p><strong>التاريخ:</strong> ${data.date}</p>
                    </div>
                    <div class="supplier-info">
                        <h4>بيانات المورد</h4>
                        <p><strong>اسم المورد:</strong> ${data.supplier.name}</p>
                        <p><strong>العنوان:</strong> ${data.supplier.address}</p>
                        <p><strong>الهاتف:</strong> ${data.supplier.phone}</p>
                        <p><strong>الرقم الضريبي:</strong> ${data.supplier.taxNumber}</p>
                    </div>
                </div>
            </div>
            
            <div class="invoice-items">
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${item.price.toFixed(2)}</td>
                                <td>${item.total.toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="invoice-totals">
                <div class="totals-section">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>${data.subtotal.toFixed(2)} ج.م</span>
                    </div>
                    <div class="total-row">
                        <span>الضريبة (15%):</span>
                        <span>${data.tax.toFixed(2)} ج.م</span>
                    </div>
                    <div class="total-row final-total">
                        <span>الإجمالي النهائي:</span>
                        <span>${data.total.toFixed(2)} ج.م</span>
                    </div>
                </div>
            </div>
            
            ${settings.showFooter ? `
                <div class="invoice-footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>هذه الفاتورة مُنشأة إلكترونياً ولا تحتاج إلى توقيع</p>
                </div>
            ` : ''}
        </div>
    `;
}

// دالة إنشاء CSS لفاتورة المشتريات
function getPurchaseInvoiceCSS() {
    return getSalesInvoiceCSS(); // نفس التصميم مع اختلاف اللون
}

// دالة الحصول على إعدادات التصميم الحالية
function getCurrentDesignSettings() {
    return {
        invoiceTitle: document.getElementById('invoiceTitle')?.value || 'فاتورة',
        headerColor: document.getElementById('headerColor')?.value || '#007bff',
        fontSize: document.getElementById('fontSize')?.value || '12pt',
        showLogo: document.getElementById('showLogo')?.checked !== false,
        showFooter: document.getElementById('showFooter')?.checked !== false
    };
}

// دالة إنشاء HTML لتقرير المخزون
function generateInventoryReportHTML() {
    const data = currentTemplateData;
    const settings = getCurrentDesignSettings();

    return `
        <div class="report-container">
            <div class="report-header">
                <div class="company-info">
                    <h1>شركة [اسم الشركة]</h1>
                    <p>نظام إدارة المخزون</p>
                </div>
                <div class="report-title" style="background-color: ${settings.headerColor || '#ffc107'};">
                    <h2>${settings.invoiceTitle || 'تقرير المخزون'}</h2>
                    <p>تاريخ التقرير: ${data.reportDate}</p>
                    <p>المخزن: ${data.warehouse}</p>
                </div>
            </div>

            <div class="report-summary">
                <div class="summary-card">
                    <h4>ملخص المخزون</h4>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">عدد الأصناف:</span>
                            <span class="stat-value">${data.items.length}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي القيمة:</span>
                            <span class="stat-value">${data.totalValue.toFixed(2)} ج.م</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-items">
                <table class="inventory-table">
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الوحدة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>القيمة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.items.map(item => `
                            <tr>
                                <td>${item.code}</td>
                                <td>${item.name}</td>
                                <td>${item.unit}</td>
                                <td>${item.quantity}</td>
                                <td>${item.price.toFixed(2)}</td>
                                <td>${item.value.toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="5"><strong>الإجمالي</strong></td>
                            <td><strong>${data.totalValue.toFixed(2)} ج.م</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="report-footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون</p>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>
        </div>
    `;
}

// دالة إنشاء CSS لتقرير المخزون
function getInventoryReportCSS() {
    const settings = getCurrentDesignSettings();

    return `
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                font-family: 'Arial', sans-serif;
                font-size: ${settings.fontSize || '11pt'};
                line-height: 1.4;
                color: #000;
                margin: 0;
                padding: 15px;
                direction: rtl;
                text-align: right;
            }

            .report-container {
                max-width: 100%;
                margin: 0 auto;
                background: white;
            }

            .report-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #ddd;
            }

            .company-info h1 {
                margin: 0 0 10px 0;
                font-size: 20pt;
                color: #333;
            }

            .report-title {
                padding: 15px;
                margin: 20px 0;
                border-radius: 8px;
                color: white;
            }

            .report-title h2 {
                margin: 0 0 10px 0;
                font-size: 18pt;
            }

            .report-summary {
                margin-bottom: 30px;
            }

            .summary-card {
                border: 1px solid #ddd;
                padding: 20px;
                border-radius: 5px;
                background: #f8f9fa;
            }

            .summary-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-top: 15px;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                padding: 10px;
                background: white;
                border-radius: 3px;
                border: 1px solid #eee;
            }

            .stat-value {
                font-weight: bold;
                color: #333;
            }

            .inventory-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
                font-size: 10pt;
            }

            .inventory-table th {
                background: #f0f0f0;
                border: 1px solid #333;
                padding: 10px 6px;
                text-align: center;
                font-weight: bold;
            }

            .inventory-table td {
                border: 1px solid #333;
                padding: 8px 6px;
                text-align: center;
            }

            .inventory-table tbody tr:nth-child(even) {
                background: #f9f9f9;
            }

            .total-row {
                background: #e9ecef !important;
                font-weight: bold;
            }

            .report-footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
                font-size: 10pt;
            }

            @page {
                margin: 1.5cm;
                size: A4 landscape;
            }
        }

        @media screen {
            body {
                font-family: 'Arial', sans-serif;
                font-size: 12pt;
                direction: rtl;
                text-align: right;
            }

            .report-container {
                max-width: 1000px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
            }

            .report-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #ddd;
            }

            .report-title {
                padding: 15px;
                margin: 20px 0;
                border-radius: 8px;
                color: white;
            }

            .summary-card {
                border: 1px solid #ddd;
                padding: 20px;
                border-radius: 5px;
                background: #f8f9fa;
                margin-bottom: 30px;
            }

            .summary-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-top: 15px;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                padding: 10px;
                background: white;
                border-radius: 3px;
                border: 1px solid #eee;
            }

            .inventory-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }

            .inventory-table th {
                background: #f8f9fa;
                border: 1px solid #ddd;
                padding: 10px 6px;
                text-align: center;
            }

            .inventory-table td {
                border: 1px solid #ddd;
                padding: 8px 6px;
                text-align: center;
            }

            .total-row {
                background: #e9ecef !important;
                font-weight: bold;
            }

            .report-footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                color: #666;
            }
        }
    `;
}

// دوال إضافية للمعاينة المباشرة
function addLivePreviewListeners() {
    const inputs = ['invoiceTitle', 'headerColor', 'fontSize', 'showLogo', 'showFooter'];

    inputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', updateLivePreview);
            element.addEventListener('change', updateLivePreview);
        }
    });
}

function updateLivePreview() {
    const previewElement = document.getElementById('livePreview');
    if (previewElement && currentTemplate) {
        let content = '';

        switch (currentTemplate) {
            case 'salesInvoice':
                content = generateSalesInvoiceHTML();
                break;
            case 'purchaseInvoice':
                content = generatePurchaseInvoiceHTML();
                break;
            case 'inventoryReport':
                content = generateInventoryReportHTML();
                break;
        }

        previewElement.innerHTML = content;
    }
}

// دوال حفظ التصميم والطباعة من المعاينة
function saveDesign() {
    const settings = getCurrentDesignSettings();
    localStorage.setItem(`template_${currentTemplate}`, JSON.stringify(settings));

    // إظهار رسالة نجاح
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.textContent = 'تم حفظ التصميم بنجاح';
    document.body.appendChild(toast);

    setTimeout(() => {
        document.body.removeChild(toast);
    }, 3000);
}

function previewFromDesign() {
    updateLivePreview();
    const content = document.getElementById('livePreview').innerHTML;
    document.getElementById('previewContent').innerHTML = content;

    // إغلاق modal التصميم وفتح modal المعاينة
    bootstrap.Modal.getInstance(document.getElementById('designModal')).hide();
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function printFromPreview() {
    const content = document.getElementById('previewContent').innerHTML;
    const printWindow = window.open('', '_blank');

    let css = '';
    switch (currentTemplate) {
        case 'salesInvoice':
            css = getSalesInvoiceCSS();
            break;
        case 'purchaseInvoice':
            css = getPurchaseInvoiceCSS();
            break;
        case 'inventoryReport':
            css = getInventoryReportCSS();
            break;
    }

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة ${currentTemplate}</title>
            <style>${css}</style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);

    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => printWindow.close(), 1000);
    }, 500);
}
