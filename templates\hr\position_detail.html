{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل المنصب - {{ position.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-user-tie text-primary me-2"></i>
                            تفاصيل المنصب
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                                <li class="breadcrumb-item"><a href="{% url 'hr:job_title_list' %}">المسميات الوظيفية</a></li>
                                <li class="breadcrumb-item active">{{ position.name }}</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div>
                    <a href="{% url 'hr:position_edit' position.pk %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="{% url 'hr:job_title_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Info -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات المنصب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted">كود المنصب:</td>
                                    <td><span class="badge bg-primary fs-6">{{ position.code }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">اسم المنصب:</td>
                                    <td class="fs-5">{{ position.name }}</td>
                                </tr>
                                {% if position.name_english %}
                                <tr>
                                    <td class="fw-bold text-muted">الاسم بالإنجليزية:</td>
                                    <td>{{ position.name_english }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold text-muted">القسم:</td>
                                    <td>
                                        <a href="{% url 'hr:department_detail' position.department.pk %}" class="text-decoration-none">
                                            <span class="badge bg-info">{{ position.department.name }}</span>
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted">الحالة:</td>
                                    <td>
                                        {% if position.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                                    <td>{{ position.created_at|date:"Y/m/d" }}</td>
                                </tr>
                                {% if position.updated_at %}
                                <tr>
                                    <td class="fw-bold text-muted">آخر تحديث:</td>
                                    <td>{{ position.updated_at|date:"Y/m/d" }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold text-muted">عدد الموظفين:</td>
                                    <td>
                                        <span class="badge bg-success fs-6">{{ employees.count }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    {% if position.description %}
                    <div class="mt-3">
                        <h6 class="text-muted">الوصف:</h6>
                        <p class="mb-0">{{ position.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-md-4">
            <div class="row g-3">
                <div class="col-12">
                    <div class="card bg-primary text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ employees.count }}</h3>
                            <small>موظف</small>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card bg-success text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-user-check fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ employees.count }}</h3>
                            <small>نشط</small>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card bg-info text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-sitemap fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ position.department.name|truncatechars:15 }}</h3>
                            <small>القسم</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees in this Position -->
    {% if employees %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        الموظفين في هذا المنصب ({{ employees.count }})
                    </h5>
                    <a href="{% url 'hr:employee_create' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i> إضافة موظف
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>القسم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التعيين</th>
                                    <th>الراتب الحالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td><strong>{{ employee.employee_number }}</strong></td>
                                    <td>
                                        <a href="{% url 'hr:employee_detail' employee.pk %}" class="text-decoration-none">
                                            {{ employee.full_name }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ employee.department.name }}</span>
                                    </td>
                                    <td>
                                        {% if employee.status == 'ACTIVE' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif employee.status == 'INACTIVE' %}
                                            <span class="badge bg-warning text-dark">غير نشط</span>
                                        {% elif employee.status == 'TERMINATED' %}
                                            <span class="badge bg-danger">منتهي</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ employee.hire_date|date:"Y/m/d" }}</td>
                                    <td>
                                        {% if employee.current_salary %}
                                            <span class="fw-bold text-success">{{ employee.current_salary|floatformat:2 }}</span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:employee_edit' employee.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Position Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات المنصب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="border rounded p-3">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 class="mb-1">{{ employees.count }}</h4>
                                <small class="text-muted">إجمالي الموظفين</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border rounded p-3">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h4 class="mb-1">{{ employees.count }}</h4>
                                <small class="text-muted">الموظفين النشطين</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border rounded p-3">
                                <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                                <h4 class="mb-1">{{ position.created_at|date:"Y" }}</h4>
                                <small class="text-muted">سنة الإنشاء</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border rounded p-3">
                                <i class="fas fa-sitemap fa-2x text-warning mb-2"></i>
                                <h4 class="mb-1">{{ position.department.name|truncatechars:10 }}</h4>
                                <small class="text-muted">القسم التابع</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    {% if not employees %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد موظفين في هذا المنصب</h5>
                    <p class="text-muted mb-3">لم يتم تعيين أي موظف لهذا المنصب بعد</p>
                    <div class="d-flex justify-content-center gap-2">
                        <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إضافة موظف جديد
                        </a>
                        <a href="{% url 'hr:position_edit' position.pk %}" class="btn btn-success">
                            <i class="fas fa-edit me-1"></i> تعديل المنصب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Related Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{% url 'hr:position_edit' position.pk %}" class="btn btn-primary w-100">
                                <i class="fas fa-edit me-2"></i>
                                تعديل المنصب
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'hr:employee_create' %}" class="btn btn-success w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'hr:department_detail' position.department.pk %}" class="btn btn-info w-100">
                                <i class="fas fa-sitemap me-2"></i>
                                عرض القسم
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'hr:job_title_list' %}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
