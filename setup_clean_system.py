#!/usr/bin/env python
"""
Setup clean system - Remove deleted data visibility
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

def setup_clean_system():
    """Setup system to hide deleted data"""
    
    print("=" * 50)
    print("Setting up Clean System")
    print("=" * 50)
    
    # 1. Clean existing deleted data
    print("1. Cleaning existing deleted data...")
    os.system("python simple_delete_inactive.py")
    
    print("\n2. System configuration updated:")
    print("   - Item list view now shows only active items")
    print("   - Removed 'Show Deleted' option from interface")
    print("   - Deleted data is permanently removed from database")
    
    print("\n3. Available cleanup tools:")
    print("   - run_cleanup.bat - Manual cleanup")
    print("   - auto_cleanup_scheduler.py - Automatic cleanup")
    print("   - simple_delete_inactive.py - Direct cleanup")
    
    print("\n✅ Clean system setup completed!")
    print("   Deleted items will no longer appear in the interface")
    
    return True

if __name__ == '__main__':
    setup_clean_system()