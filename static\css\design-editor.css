/**
 * تصميمات محرر التصميم المتقدم
 * Advanced Design Editor Styles
 */

/* تحسين واجهة المحرر */
.design-editor-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.editor-toolbar {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e1e8ed;
}

.toolbar-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toolbar-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 2px 2px 0;
}

.toolbar-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.toolbar-section h6 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
    position: relative;
    font-size: 1.1rem;
}

.toolbar-section h6::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
}

/* تحسين عناصر النموذج */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-control[type="color"] {
    height: 45px;
    padding: 5px;
    cursor: pointer;
}

.form-control[type="file"] {
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.form-check {
    margin-bottom: 10px;
}

.form-check-input {
    width: 1.3em;
    height: 1.3em;
    margin-top: 0.1em;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
    transform: scale(1.1);
}

.form-check-label {
    font-weight: 500;
    margin-right: 10px;
    color: #495057;
}

/* تحسين أزرار الأدوات */
.toolbar-actions {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    border: none;
}

.toolbar-actions .btn {
    margin: 8px;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.toolbar-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.toolbar-actions .btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
}

.toolbar-actions .btn-success {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    color: white;
}

.toolbar-actions .btn-info {
    background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%);
    border-color: #6f42c1;
    color: white;
}

.toolbar-actions .btn-warning {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border-color: #fd7e14;
    color: white;
}

/* تحسين منطقة المعاينة */
.editor-preview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e1e8ed;
    overflow: hidden;
}

.preview-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h6 {
    margin: 0;
    font-weight: 700;
    font-size: 1.1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.preview-controls {
    display: flex;
    gap: 8px;
}

.preview-controls .btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preview-controls .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: white;
    transform: scale(1.1);
}

.preview-content {
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 500px;
    overflow: auto;
}

#designPreview {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

/* تحسين عرض الفاتورة في المعاينة */
.invoice-preview {
    max-width: 100%;
    margin: 0 auto;
    position: relative;
}

.invoice-preview .invoice-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.invoice-preview .company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
}

.invoice-preview .company-info h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.invoice-preview .invoice-title {
    text-align: center;
    border-radius: 10px;
    padding: 20px;
    margin: 25px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.invoice-preview .sample-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.invoice-preview table {
    border-radius: 10px;
    overflow: hidden;
}

.invoice-preview .invoice-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-top: 25px;
}

/* تأثيرات التحميل والانتقال */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* تحسين الاستجابة */
@media (max-width: 1200px) {
    .design-editor-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .toolbar-section {
        padding: 15px;
    }
    
    .toolbar-actions .btn {
        margin: 5px;
        padding: 8px 15px;
        font-size: 0.8rem;
    }
    
    .preview-content {
        padding: 15px;
        min-height: 400px;
    }
}

@media (max-width: 768px) {
    .design-editor-container {
        padding: 15px;
    }
    
    .editor-toolbar {
        padding: 15px;
    }
    
    .toolbar-section {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .toolbar-actions {
        padding: 15px;
    }
    
    .toolbar-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
    
    .preview-header {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }
    
    .preview-controls {
        justify-content: center;
    }
}

/* تحسين التمرير */
.editor-toolbar::-webkit-scrollbar,
.preview-content::-webkit-scrollbar {
    width: 8px;
}

.editor-toolbar::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.editor-toolbar::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

.editor-toolbar::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
