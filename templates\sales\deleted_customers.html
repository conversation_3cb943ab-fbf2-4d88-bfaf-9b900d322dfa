{% extends 'base/base.html' %}
{% load static %}

{% block title %}العملاء المحذوفين{% endblock %}

{% block extra_css %}
<style>
.deleted-customer-card {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
}

.restore-btn {
    background-color: #28a745;
    border-color: #28a745;
}

.restore-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.permanent-delete-btn {
    background-color: #dc3545;
    border-color: #dc3545;
}

.permanent-delete-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-trash-restore"></i>
                        العملاء المحذوفين
                    </h3>
                    <div>
                        <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            العودة لقائمة العملاء
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    {% if deleted_customers %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            يوجد {{ deleted_customers|length }} عميل محذوف. يمكنك استعادة أي عميل أو حذفه نهائياً.
                        </div>

                        <div class="row">
                            {% for customer in deleted_customers %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card deleted-customer-card">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-user-slash text-danger"></i>
                                            {{ customer.name }}
                                        </h5>
                                        <p class="card-text">
                                            <strong>الكود:</strong> {{ customer.code }}<br>
                                            <strong>الهاتف:</strong> {{ customer.phone|default:"غير محدد" }}<br>
                                            <strong>البريد:</strong> {{ customer.email|default:"غير محدد" }}<br>
                                            <strong>تاريخ الحذف:</strong> {{ customer.updated_at|date:"Y-m-d H:i" }}
                                        </p>
                                        
                                        <!-- معلومات المعاملات المرتبطة -->
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-file-invoice"></i>
                                                المعاملات المرتبطة:
                                                <span class="badge badge-info">{{ customer.invoices_count }} فاتورة</span>
                                                <span class="badge badge-warning">{{ customer.returns_count }} مرتجع</span>
                                                <span class="badge badge-secondary">{{ customer.quotations_count }} عرض سعر</span>
                                            </small>
                                        </div>

                                        <div class="btn-group w-100" role="group">
                                            <button type="button" 
                                                    class="btn btn-sm restore-btn"
                                                    onclick="restoreCustomer({{ customer.id }}, '{{ customer.name }}')"
                                                    title="استعادة العميل">
                                                <i class="fas fa-undo"></i>
                                                استعادة
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-sm permanent-delete-btn"
                                                    onclick="permanentDeleteCustomer({{ customer.id }}, '{{ customer.name }}')"
                                                    title="حذف نهائي">
                                                <i class="fas fa-trash"></i>
                                                حذف نهائي
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- أزرار العمليات الجماعية -->
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>العمليات الجماعية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" 
                                                class="btn btn-success"
                                                onclick="restoreAllCustomers()"
                                                title="استعادة جميع العملاء">
                                            <i class="fas fa-undo-alt"></i>
                                            استعادة الكل
                                        </button>
                                        <button type="button" 
                                                class="btn btn-danger"
                                                onclick="permanentDeleteAllCustomers()"
                                                title="حذف جميع العملاء نهائياً">
                                            <i class="fas fa-trash-alt"></i>
                                            حذف الكل نهائياً
                                        </button>
                                    </div>
                                    <small class="form-text text-muted">
                                        تحذير: العمليات الجماعية لا يمكن التراجع عنها
                                    </small>
                                </div>
                            </div>
                        </div>

                    {% else %}
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h4>لا يوجد عملاء محذوفين</h4>
                            <p>جميع العملاء نشطين حالياً</p>
                            <a href="{% url 'sales:customer_list' %}" class="btn btn-primary">
                                <i class="fas fa-users"></i>
                                عرض قائمة العملاء
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
function restoreCustomer(customerId, customerName) {
    if (confirm(`هل أنت متأكد من استعادة العميل "${customerName}"؟`)) {
        fetch(`/sales/customers/${customerId}/restore/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showAlert('error', data.message || 'حدث خطأ أثناء الاستعادة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function permanentDeleteCustomer(customerId, customerName) {
    if (confirm(`تحذير: هل أنت متأكد من حذف العميل "${customerName}" نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
        if (confirm('تأكيد نهائي: سيتم حذف العميل وجميع بياناته نهائياً. هل تريد المتابعة؟')) {
            fetch(`/sales/customers/${customerId}/permanent-delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('error', data.message || 'حدث خطأ أثناء الحذف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ في الاتصال بالخادم');
            });
        }
    }
}

function restoreAllCustomers() {
    if (confirm('هل أنت متأكد من استعادة جميع العملاء المحذوفين؟')) {
        fetch('/sales/customers/restore-all/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showAlert('error', data.message || 'حدث خطأ أثناء الاستعادة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function permanentDeleteAllCustomers() {
    if (confirm('تحذير خطير: هل أنت متأكد من حذف جميع العملاء المحذوفين نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد نهائي: سيتم حذف جميع العملاء المحذوفين وبياناتهم نهائياً. هل تريد المتابعة؟')) {
            if (confirm('تأكيد أخير: هذا إجراء خطير جداً. اكتب "نعم" للمتابعة')) {
                const confirmation = prompt('اكتب "نعم" للتأكيد:');
                if (confirmation === 'نعم') {
                    fetch('/sales/customers/permanent-delete-all/', {
                        method: 'DELETE',
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                            'Content-Type': 'application/json',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert('success', data.message);
                            setTimeout(() => {
                                location.reload();
                            }, 2000);
                        } else {
                            showAlert('error', data.message || 'حدث خطأ أثناء الحذف');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                    });
                }
            }
        }
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
