{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">كشف المرتبات الشهري للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success me-2" onclick="printInvoice('كشف المرتبات الشهري')">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <button class="btn btn-primary" onclick="generatePayroll()">
                        <i class="fas fa-calculator me-2"></i>
                        إنشاء كشف مرتبات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>75,000 ج.م</h3>
                    <p class="mb-0">إجمالي المرتبات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>15,750 ج.م</h3>
                    <p class="mb-0">إجمالي الإضافات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>8,250 ج.م</h3>
                    <p class="mb-0">إجمالي الخصومات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>82,500 ج.م</h3>
                    <p class="mb-0">الصافي المستحق</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Month Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label">اختر الشهر:</label>
                            <select class="form-select" onchange="loadPayroll()">
                                <option value="2024-01" selected>يناير 2024</option>
                                <option value="2023-12">ديسمبر 2023</option>
                                <option value="2023-11">نوفمبر 2023</option>
                                <option value="2023-10">أكتوبر 2023</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">حالة الكشف:</label>
                            <select class="form-select">
                                <option value="draft">مسودة</option>
                                <option value="approved" selected>معتمد</option>
                                <option value="paid">مدفوع</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="approvePayroll()">
                                    <i class="fas fa-check me-1"></i>
                                    اعتماد
                                </button>
                                <button class="btn btn-info" onclick="printPayroll()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        كشف مرتبات يناير 2024
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>الراتب الأساسي</th>
                                    <th>الإضافات</th>
                                    <th>الخصومات</th>
                                    <th>الصافي</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payroll in payrolls %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ payroll.employee }}
                                    </td>
                                    <td>
                                        <strong>{{ payroll.basic_salary }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">+{{ payroll.additions }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="text-danger">-{{ payroll.deductions }} ج.م</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ payroll.net_salary }} ج.م</strong>
                                    </td>
                                    <td>
                                        {% if payroll.status == 'مدفوع' %}
                                            <span class="badge bg-success">{{ payroll.status }}</span>
                                        {% elif payroll.status == 'معتمد' %}
                                            <span class="badge bg-warning">{{ payroll.status }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payroll.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" title="عرض كشف الراتب" onclick="viewPayslip({{ forloop.counter }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="طباعة كشف الراتب">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="تأكيد الدفع" onclick="confirmPayment({{ forloop.counter }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-file-invoice-dollar fa-3x mb-3 d-block"></i>
                                        لا توجد مرتبات للشهر المحدد
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2">الإجمالي</th>
                                    <th>75,000 ج.م</th>
                                    <th class="text-success">+15,750 ج.م</th>
                                    <th class="text-danger">-8,250 ج.م</th>
                                    <th class="text-primary">82,500 ج.م</th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Status -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        المرتبات المدفوعة (15 موظف)
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>المبلغ المدفوع:</strong> 45,000 ج.م</p>
                    <p class="mb-0"><small class="text-muted">تم الدفع في 2024-01-30</small></p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        المرتبات المعلقة (10 موظف)
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>المبلغ المعلق:</strong> 37,500 ج.م</p>
                    <p class="mb-0"><small class="text-muted">في انتظار الاعتماد</small></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generatePayroll() {
    if (confirm('هل تريد إنشاء كشف مرتبات جديد للشهر الحالي؟')) {
        alert('تم إنشاء كشف المرتبات بنجاح');
        // Add payroll generation logic here
    }
}

function loadPayroll() {
    alert('تم تحميل بيانات الشهر المحدد');
    // Add month loading logic here
}

function approvePayroll() {
    if (confirm('هل تريد اعتماد كشف المرتبات؟')) {
        alert('تم اعتماد كشف المرتبات');
        // Add approval logic here
    }
}

function printPayroll() {
    printInvoice('كشف المرتبات الشهري');
}

function viewPayslip(id) {
    alert('عرض كشف راتب الموظف رقم ' + id);
    // Add payslip view logic here
}

function confirmPayment(id) {
    if (confirm('هل تم دفع راتب هذا الموظف؟')) {
        alert('تم تأكيد دفع الراتب');
        // Add payment confirmation logic here
    }
}
</script>
{% endblock %}
