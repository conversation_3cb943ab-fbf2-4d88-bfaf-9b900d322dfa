{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.logo-preview {
    max-width: 200px;
    max-height: 200px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    background-color: #f8f9fa;
}

.logo-preview img {
    max-width: 100%;
    max-height: 180px;
    border-radius: 4px;
}

.logo-upload-area {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logo-upload-area:hover {
    background-color: #e9ecef;
    border-color: #0056b3;
}

.logo-upload-area.dragover {
    background-color: #e3f2fd;
    border-color: #1976d2;
}

.settings-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-section h5 {
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog"></i> {{ title }}</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                        <li class="breadcrumb-item active">إعدادات الشركة</li>
                    </ol>
                </nav>
            </div>

            <form method="post" enctype="multipart/form-data" id="settingsForm">
                {% csrf_token %}
                
                <!-- معلومات الشركة الأساسية -->
                <div class="settings-section">
                    <h5><i class="fas fa-building"></i> معلومات الشركة الأساسية</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_company_name" class="form-label">اسم الشركة *</label>
                                <input type="text" class="form-control" id="id_company_name" name="company_name" 
                                       value="{{ settings.company_name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_company_name_english" class="form-label">اسم الشركة بالإنجليزية</label>
                                <input type="text" class="form-control" id="id_company_name_english" name="company_name_english" 
                                       value="{{ settings.company_name_english }}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- الشعار -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">شعار الشركة</label>
                                <div class="logo-upload-area" onclick="document.getElementById('logoInput').click()">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-primary mb-2"></i>
                                    <p class="mb-0">اضغط هنا لرفع الشعار</p>
                                    <small class="text-muted">أو اسحب الملف هنا</small>
                                </div>
                                <input type="file" id="logoInput" name="logo" accept="image/*" style="display: none;">
                                <small class="form-text text-muted">يفضل أن يكون الشعار بصيغة PNG أو JPG وبحجم 200x200 بكسل</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الشعار</label>
                                <div class="logo-preview" id="logoPreview">
                                    {% if settings.logo %}
                                        <img src="{{ settings.logo.url }}" alt="شعار الشركة">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo()">
                                                <i class="fas fa-trash"></i> حذف الشعار
                                            </button>
                                        </div>
                                    {% else %}
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">لا يوجد شعار</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="settings-section">
                    <h5><i class="fas fa-phone"></i> معلومات الاتصال</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_phone" class="form-label">الهاتف</label>
                                <input type="text" class="form-control" id="id_phone" name="phone" 
                                       value="{{ settings.phone }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_mobile" class="form-label">الجوال</label>
                                <input type="text" class="form-control" id="id_mobile" name="mobile" 
                                       value="{{ settings.mobile }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="id_email" name="email" 
                                       value="{{ settings.email }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_website" class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="id_website" name="website" 
                                       value="{{ settings.website }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العنوان -->
                <div class="settings-section">
                    <h5><i class="fas fa-map-marker-alt"></i> العنوان</h5>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="id_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="id_address" name="address" rows="3">{{ settings.address }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="id_city" name="city" 
                                       value="{{ settings.city }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_state" class="form-label">المحافظة</label>
                                <input type="text" class="form-control" id="id_state" name="state" 
                                       value="{{ settings.state }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_country" class="form-label">الدولة</label>
                                <input type="text" class="form-control" id="id_country" name="country" 
                                       value="{{ settings.country }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="id_postal_code" class="form-label">الرمز البريدي</label>
                                <input type="text" class="form-control" id="id_postal_code" name="postal_code" 
                                       value="{{ settings.postal_code }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المعلومات القانونية -->
                <div class="settings-section">
                    <h5><i class="fas fa-file-contract"></i> المعلومات القانونية</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="id_tax_number" name="tax_number" 
                                       value="{{ settings.tax_number }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_commercial_register" class="form-label">السجل التجاري</label>
                                <input type="text" class="form-control" id="id_commercial_register" name="commercial_register" 
                                       value="{{ settings.commercial_register }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات التطبيق -->
                <div class="settings-section">
                    <h5><i class="fas fa-cogs"></i> إعدادات التطبيق</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_app_name" class="form-label">اسم التطبيق</label>
                                <input type="text" class="form-control" id="id_app_name" name="app_name" 
                                       value="{{ settings.app_name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_app_version" class="form-label">إصدار التطبيق</label>
                                <input type="text" class="form-control" id="id_app_version" name="app_version" 
                                       value="{{ settings.app_version }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="{% url 'definitions:home' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// معالجة رفع الشعار
document.getElementById('logoInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        previewLogo(file);
    }
});

// معاينة الشعار
function previewLogo(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = document.getElementById('logoPreview');
        preview.innerHTML = `
            <img src="${e.target.result}" alt="معاينة الشعار">
            <div class="mt-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="clearLogo()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        `;
    };
    reader.readAsDataURL(file);
}

// مسح الشعار المحدد
function clearLogo() {
    document.getElementById('logoInput').value = '';
    const preview = document.getElementById('logoPreview');
    preview.innerHTML = `
        <i class="fas fa-image fa-3x text-muted mb-2"></i>
        <p class="text-muted mb-0">لا يوجد شعار</p>
    `;
}

// حذف الشعار الموجود
function removeLogo() {
    if (confirm('هل أنت متأكد من حذف الشعار؟')) {
        fetch('{% url "definitions:remove_logo" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                clearLogo();
                showAlert('تم حذف الشعار بنجاح', 'success');
            } else {
                showAlert(data.error || 'حدث خطأ أثناء حذف الشعار', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ أثناء حذف الشعار', 'danger');
        });
    }
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Drag and drop للشعار
const uploadArea = document.querySelector('.logo-upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.classList.remove('dragover');
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    this.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            document.getElementById('logoInput').files = files;
            previewLogo(file);
        } else {
            showAlert('يرجى اختيار ملف صورة', 'warning');
        }
    }
});
</script>
{% endblock %}
