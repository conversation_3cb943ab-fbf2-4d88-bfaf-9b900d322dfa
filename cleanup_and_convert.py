#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemType

def cleanup_test_items():
    print("Deleting test items...")
    
    # Delete test items (FP001-FP005)
    test_items = Item.objects.filter(code__startswith='FP')
    deleted_count = test_items.count()
    test_items.delete()
    
    print(f"Deleted {deleted_count} test items")

def convert_existing_items():
    print("Converting existing items to finished products...")
    
    # Get existing items that are not test items
    existing_items = Item.objects.filter(
        is_active=True
    ).exclude(code__startswith='FP').exclude(code__startswith='TEST')
    
    print(f"Found {existing_items.count()} existing items")
    
    # Convert some items to finished products
    converted_count = 0
    for item in existing_items[:3]:  # Convert first 3 items
        print(f"Converting: {item.code} - {item.name}")
        item.item_type = ItemType.FINISHED_PRODUCT
        item.save()
        converted_count += 1
    
    print(f"Converted {converted_count} items to finished products")

def show_final_status():
    print("\n=== Final Status ===")
    
    # Show all items by type
    for item_type in ItemType.choices:
        count = Item.objects.filter(
            item_type=item_type[0],
            is_active=True
        ).count()
        print(f"{item_type[1]}: {count} items")
    
    # Show finished products specifically
    finished_products = Item.objects.filter(
        item_type=ItemType.FINISHED_PRODUCT,
        is_active=True
    )
    
    print(f"\nFinished Products ({finished_products.count()}):")
    for item in finished_products:
        print(f"- {item.code}: {item.name}")

if __name__ == "__main__":
    cleanup_test_items()
    convert_existing_items()
    show_final_status()