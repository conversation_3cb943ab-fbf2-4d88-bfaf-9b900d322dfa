@echo off
chcp 65001 >nul
title Osaric Accounts - Advanced Stable Server

echo.
echo ========================================
echo    Osaric Accounts System
echo    Advanced Stable Server
echo ========================================
echo.

cd /d "%~dp0"

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "venv_new" (
    echo Installing requirements...
    pip install django psutil schedule >nul 2>&1
)

echo Starting Advanced Stable Server...
echo.
echo Features:
echo - Auto-restart on crashes
echo - Health monitoring every 30 seconds  
echo - Performance tracking
echo - Automatic backups every hour
echo - Memory and CPU monitoring
echo - Response time tracking
echo - Log rotation
echo.
echo Server will be available at: http://127.0.0.1:8000/
echo Press Ctrl+C to stop server safely
echo.

python advanced_stable_server.py

echo.
echo Server stopped.
pause