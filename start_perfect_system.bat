@echo off
title النظام الآمن المثالي - Perfect Secure System
color 0A

echo ============================================================
echo 🔒 النظام الآمن المثالي - بدون أخطاء
echo Perfect Secure System - Error Free
echo ============================================================
echo.

echo 🔍 فحص النظام...
echo Checking system...
echo.

REM إيقاف العمليات السابقة
echo 🛑 إيقاف العمليات السابقة...
taskkill /F /IM python.exe >nul 2>&1
timeout /t 3 /nobreak >nul

REM فحص الشهادة
if not exist "ssl\server.crt" (
    echo 🔒 إنشاء شهادة SSL...
    python create_free_certificate.py
    if not exist "ssl\server.crt" (
        echo ❌ فشل في إنشاء الشهادة
        pause
        exit /b 1
    )
)
echo ✅ شهادة SSL متوفرة

echo.
echo 🚀 بدء النظام الآمن المثالي...
echo Starting Perfect Secure System...
echo.

REM تشغيل النظام الشامل
start "النظام الآمن المثالي" python complete_secure_system.py

echo ⏳ انتظار بدء النظام...
timeout /t 10 /nobreak >nul

REM فحص حالة النظام
netstat -an | findstr ":8000" >nul
if %errorLevel% == 0 (
    echo ✅ خادم Django يعمل
) else (
    echo ❌ خادم Django لا يعمل
)

netstat -an | findstr ":8443" >nul
if %errorLevel% == 0 (
    echo ✅ خادم HTTPS يعمل
) else (
    echo ❌ خادم HTTPS لا يعمل
)

echo.
echo ============================================================
echo 🌐 معلومات الوصول الآمن
echo Secure Access Information
echo ============================================================
echo.

echo 🔒 الروابط الآمنة:
echo    https://***************:8443/dashboard/
echo    https://localhost:8443/dashboard/
echo.

echo 📋 خيارات الوصول:
echo 1. فتح Chrome بدون تحذيرات (موصى به)
echo 2. فتح Edge بدون تحذيرات
echo 3. فتح الرابط في المتصفح الافتراضي
echo 4. عرض معلومات النظام
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" (
    if exist "chrome_no_warnings.bat" (
        echo 🚀 فتح Chrome بدون تحذيرات...
        call chrome_no_warnings.bat
    ) else (
        echo ❌ ملف Chrome غير موجود
        start https://***************:8443/dashboard/
    )
) else if "%choice%"=="2" (
    if exist "edge_no_warnings.bat" (
        echo 🚀 فتح Edge بدون تحذيرات...
        call edge_no_warnings.bat
    ) else (
        echo ❌ ملف Edge غير موجود
        start https://***************:8443/dashboard/
    )
) else if "%choice%"=="3" (
    echo 🌐 فتح الرابط في المتصفح الافتراضي...
    start https://***************:8443/dashboard/
) else if "%choice%"=="4" (
    goto show_info
) else if "%choice%"=="0" (
    goto exit
) else (
    echo ❌ خيار غير صحيح
    pause
    goto menu
)

goto end

:show_info
echo.
echo ============================================================
echo 📊 معلومات النظام المفصلة
echo Detailed System Information
echo ============================================================
echo.

echo 🖥️ معلومات الجهاز:
echo    اسم الجهاز: %COMPUTERNAME%
echo    المستخدم: %USERNAME%
echo    التاريخ: %DATE%
echo    الوقت: %TIME%
echo.

echo 🌐 معلومات الشبكة:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
    echo    عنوان IP: %%a
)
echo.

echo 🔒 معلومات الشهادة:
if exist "ssl\certificate_info.txt" (
    type ssl\certificate_info.txt | findstr "صالحة"
) else (
    echo    الشهادة: متوفرة
)
echo.

echo 🚀 حالة الخوادم:
netstat -an | findstr ":8000" >nul && echo    ✅ Django: يعمل || echo    ❌ Django: لا يعمل
netstat -an | findstr ":8443" >nul && echo    ✅ HTTPS: يعمل || echo    ❌ HTTPS: لا يعمل
echo.

echo 💡 نصائح الاستخدام:
echo    • استخدم Chrome أو Edge بدون تحذيرات
echo    • الاتصال آمن ومشفر بالكامل
echo    • يمكن الوصول من أي جهاز في الشبكة
echo    • النظام يعمل بشكل مستمر
echo.

pause
goto menu

:menu
echo.
echo ============================================================
goto start

:end
echo.
echo 🎉 شكراً لاستخدام النظام الآمن المثالي!
echo Thank you for using the Perfect Secure System!
echo.
echo 💡 النظام يعمل في الخلفية
echo The system is running in the background
echo.
pause

:exit
echo.
echo 👋 إنهاء النظام...
echo Shutting down system...
echo.
taskkill /F /IM python.exe >nul 2>&1
echo ✅ تم إنهاء النظام
echo System shutdown complete
echo.
pause
exit /b 0
