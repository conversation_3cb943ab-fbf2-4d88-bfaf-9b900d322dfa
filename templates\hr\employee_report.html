{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-users text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تقرير شامل بجميع الموظفين وبياناتهم</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-info" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ total_employees }}</h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ active_employees }}</h3>
                    <p class="mb-0">موظفين نشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ departments_count }}</h3>
                    <p class="mb-0">عدد الأقسام</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ positions_count }}</h3>
                    <p class="mb-0">عدد المناصب</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="hr">الموارد البشرية</option>
                                <option value="finance">المالية</option>
                                <option value="it">تقنية المعلومات</option>
                                <option value="sales">المبيعات</option>
                                <option value="production">الإنتاج</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة:</label>
                            <select class="form-select" onchange="filterByStatus()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="vacation">في إجازة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التوظيف:</label>
                            <select class="form-select" onchange="filterByEmploymentType()">
                                <option value="">جميع الأنواع</option>
                                <option value="permanent">دائم</option>
                                <option value="contract">تعاقد</option>
                                <option value="part_time">دوام جزئي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Excel
                                </button>
                                <button class="btn btn-danger" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة الموظفين التفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>رقم الموظف</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الراتب الأساسي</th>
                                    <th>نوع التوظيف</th>
                                    <th>الحالة</th>
                                    <th>معلومات الاتصال</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">
                                                {{ employee.name|first }}
                                            </div>
                                            <div>
                                                <strong>{{ employee.name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ employee.national_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ employee.employee_id }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ employee.department }}</span>
                                    </td>
                                    <td>{{ employee.position }}</td>
                                    <td>{{ employee.hire_date }}</td>
                                    <td>
                                        <strong class="text-success">{{ employee.basic_salary }} ج.م</strong>
                                    </td>
                                    <td>
                                        {% if employee.employment_type == 'دائم' %}
                                            <span class="badge bg-success">{{ employee.employment_type }}</span>
                                        {% elif employee.employment_type == 'تعاقد' %}
                                            <span class="badge bg-warning">{{ employee.employment_type }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ employee.employment_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if employee.status == 'نشط' %}
                                            <span class="badge bg-success">{{ employee.status }}</span>
                                        {% elif employee.status == 'في إجازة' %}
                                            <span class="badge bg-warning">{{ employee.status }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ employee.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            <i class="fas fa-phone text-primary me-1"></i>{{ employee.phone }}
                                            <br>
                                            <i class="fas fa-envelope text-info me-1"></i>{{ employee.email }}
                                        </small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                        لا توجد بيانات موظفين للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        ملخص الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for dept in department_summary %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">{{ dept.name }}</h6>
                                    <div class="d-flex justify-content-between">
                                        <span>عدد الموظفين:</span>
                                        <strong>{{ dept.employee_count }}</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>متوسط الراتب:</span>
                                        <strong>{{ dept.avg_salary }} ج.م</strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>إجمالي الرواتب:</span>
                                        <strong class="text-success">{{ dept.total_salary }} ج.م</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Footer -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body text-center">
                    <p class="mb-2">
                        <strong>تاريخ إنشاء التقرير:</strong> {{ "now"|date:"Y-m-d H:i" }}
                    </p>
                    <p class="mb-0 text-muted">
                        تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterByDepartment() {
    alert('تم تطبيق فلتر القسم');
}

function filterByStatus() {
    alert('تم تطبيق فلتر الحالة');
}

function filterByEmploymentType() {
    alert('تم تطبيق فلتر نوع التوظيف');
}

function exportToExcel() {
    alert('سيتم تصدير التقرير إلى Excel');
}

function exportToPDF() {
    alert('سيتم تصدير التقرير إلى PDF');
}

function printReport() {
    // جمع بيانات الموظفين من الجدول
    const employees = [];
    const rows = document.querySelectorAll('#employeeTable tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            employees.push({
                name: cells[1]?.textContent?.trim() || 'غير محدد',
                department: cells[2]?.textContent?.trim() || 'غير محدد',
                position: cells[3]?.textContent?.trim() || 'غير محدد',
                hire_date: cells[4]?.textContent?.trim() || 'غير محدد',
                salary: cells[5]?.textContent?.trim() || 'غير محدد',
                status: cells[6]?.textContent?.trim() || 'نشط'
            });
        }
    });

    // استخدام القالب المخصص للطباعة
    printEmployeeReport(employees, 'تقرير الموظفين');
}
</script>
{% endblock %}
