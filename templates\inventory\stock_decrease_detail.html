﻿{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-minus-circle text-danger me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                    {% if decrease.status == 'DRAFT' %}
                        <a href="{% url 'inventory:stock_decrease_edit' decrease.pk %}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <button type="button" class="btn btn-success me-2" onclick="approveDecrease({{ decrease.pk }})">
                            <i class="fas fa-check me-1"></i>
                            اعتماد
                        </button>
                    {% elif decrease.status == 'APPROVED' %}
                        <button type="button" class="btn btn-primary me-2" onclick="applyDecrease({{ decrease.pk }})">
                            <i class="fas fa-play me-1"></i>
                            تطبيق على المخزون
                        </button>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات الإذن الأساسية -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الإذن الأساسية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">رقم الإذن:</td>
                                            <td>{{ decrease.decrease_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">التاريخ:</td>
                                            <td>{{ decrease.date|date:"d/m/Y" }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">المخزن:</td>
                                            <td>{{ decrease.warehouse.name }} ({{ decrease.warehouse.code }})</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">السبب:</td>
                                            <td>{{ decrease.reason }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">الحالة:</td>
                                            <td>
                                                {% if decrease.status == 'DRAFT' %}
                                                    <span class="badge bg-secondary">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'APPROVED' %}
                                                    <span class="badge bg-info">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'APPLIED' %}
                                                    <span class="badge bg-danger">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'CANCELLED' %}
                                                    <span class="badge bg-dark">{{ decrease.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">إجمالي المبلغ:</td>
                                            <td class="text-danger fw-bold">{{ decrease.total_amount|floatformat:2 }} ج.م</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">أنشئ بواسطة:</td>
                                            <td>{{ decrease.created_by.get_full_name|default:decrease.created_by.username }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">تاريخ الإنشاء:</td>
                                            <td>{{ decrease.created_at|date:"d/m/Y H:i" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>
                                سجل الإجراءات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">إنشاء الإذن</h6>
                                        <p class="text-muted mb-0">{{ decrease.created_at|date:"d/m/Y H:i" }}</p>
                                        <small class="text-muted">بواسطة: {{ decrease.created_by.get_full_name|default:decrease.created_by.username }}</small>
                                    </div>
                                </div>
                                {% if decrease.approved_by %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">اعتماد الإذن</h6>
                                        <p class="text-muted mb-0">{{ decrease.approved_at|date:"d/m/Y H:i" }}</p>
                                        <small class="text-muted">بواسطة: {{ decrease.approved_by.get_full_name|default:decrease.approved_by.username }}</small>
                                    </div>
                                </div>
                                {% endif %}
                                {% if decrease.applied_by %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-danger"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">تطبيق على المخزون</h6>
                                        <p class="text-muted mb-0">{{ decrease.applied_at|date:"d/m/Y H:i" }}</p>
                                        <small class="text-muted">بواسطة: {{ decrease.applied_by.get_full_name|default:decrease.applied_by.username }}</small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الأصناف -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        تفاصيل الأصناف
                        <span class="badge bg-danger ms-2">{{ items.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>الصنف</th>
                                        <th>الكمية المطلوبة</th>
                                        <th>سعر الوحدة</th>
                                        <th>إجمالي التكلفة</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in items %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>
                                                <strong>{{ item.item.name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ item.item.code }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ item.quantity|floatformat:2 }}</strong>
                                                <br>
                                                <small class="text-muted">{{ item.item.unit.name }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ item.unit_price|floatformat:2 }} ج.م</strong>
                                            </td>
                                            <td>
                                                <strong class="text-danger">{{ item.total_cost|floatformat:2 }} ج.م</strong>
                                            </td>
                                            <td>
                                                {% if item.notes %}
                                                    {{ item.notes|truncatechars:50 }}
                                                {% else %}
                                                    <span class="text-muted">لا توجد ملاحظات</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-info">
                                    <tr>
                                        <td colspan="4" class="text-end fw-bold">إجمالي التكلفة:</td>
                                        <td colspan="2" class="fw-bold text-danger">{{ decrease.total_amount|floatformat:2 }} ج.م</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="text-muted">لا توجد أصناف في هذا الإذن</h5>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: -29px;
    top: 17px;
    width: 2px;
    height: calc(100% + 3px);
    background-color: #dee2e6;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-content p {
    margin-bottom: 5px;
}
</style>

<script>
function approveDecrease(decreaseId) {
    if (confirm('هل تريد اعتماد هذا الإذن؟')) {
        fetch(`/inventory/stock-decrease/${decreaseId}/approve/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الاعتماد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الاعتماد');
        });
    }
}

function applyDecrease(decreaseId) {
    if (confirm('هل تريد تطبيق هذا الإذن على المخزون؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/inventory/stock-decrease/${decreaseId}/apply/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء التطبيق');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء التطبيق');
        });
    }
}
</script>
{% endblock %}
