# Generated by Django 5.2.2 on 2025-06-28 03:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        ('purchases', '0001_initial'),
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustodyReceiptIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('depositor', models.CharField(max_length=200, verbose_name='المودع')),
                ('depositor_id', models.CharField(blank=True, max_length=50, verbose_name='رقم الهوية')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('purpose', models.CharField(max_length=200, verbose_name='الغرض من الإيداع')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('RETURNED', 'مرجع'), ('CANCELLED', 'ملغي')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع الفعلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إيصال أمانة وارد',
                'verbose_name_plural': 'إيصالات الأمانة الواردة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='CustodyReceiptOut',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('custodian', models.CharField(max_length=200, verbose_name='أمين العهدة')),
                ('custodian_id', models.CharField(blank=True, max_length=50, verbose_name='رقم الهوية')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('purpose', models.CharField(max_length=200, verbose_name='الغرض من الأمانة')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('RETURNED', 'مرجع'), ('CANCELLED', 'ملغي')], default='ACTIVE', max_length=20, verbose_name='الحالة')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع الفعلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إيصال أمانة صادر',
                'verbose_name_plural': 'إيصالات الأمانة الصادرة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود نوع المصروف')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع المصروف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نوع مصروف',
                'verbose_name_plural': 'أنواع المصروفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('expense_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المصروف')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('beneficiary', models.CharField(blank=True, max_length=200, verbose_name='المستفيد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('expense_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='treasury.expensetype', verbose_name='نوع المصروف')),
            ],
            options={
                'verbose_name': 'مصروف',
                'verbose_name_plural': 'المصروفات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('payment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('CASH', 'نقدي'), ('CHECK', 'شيك'), ('BANK_TRANSFER', 'تحويل بنكي')], default='CASH', max_length=20, verbose_name='طريقة الدفع')),
                ('check_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الشيك')),
                ('check_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشيك')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.supplier', verbose_name='المورد')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إيصال دفع',
                'verbose_name_plural': 'إيصالات الدفع',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='PaymentNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السند')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('PAID', 'مدفوع'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('payment_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.supplier', verbose_name='المورد')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'ورقة دفع',
                'verbose_name_plural': 'أوراق الدفع',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحصيل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('CASH', 'نقدي'), ('CHECK', 'شيك'), ('BANK_TRANSFER', 'تحويل بنكي'), ('CREDIT_CARD', 'بطاقة ائتمان')], default='CASH', max_length=20, verbose_name='طريقة الدفع')),
                ('check_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الشيك')),
                ('check_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشيك')),
                ('check_bank', models.CharField(blank=True, max_length=100, verbose_name='البنك المسحوب عليه')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.customer', verbose_name='العميل')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحصيل نقدي',
                'verbose_name_plural': 'التحصيل النقدي',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='ReceiptNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note_number', models.CharField(max_length=50, unique=True, verbose_name='رقم السند')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('COLLECTED', 'محصل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('collection_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التحصيل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.customer', verbose_name='العميل')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'ورقة قبض',
                'verbose_name_plural': 'أوراق القبض',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='RevenueType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود نوع الإيراد')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الإيراد')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نوع إيراد',
                'verbose_name_plural': 'أنواع الإيرادات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Revenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('revenue_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيراد')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('source', models.CharField(blank=True, max_length=200, verbose_name='المصدر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('revenue_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='treasury.revenuetype', verbose_name='نوع الإيراد')),
            ],
            options={
                'verbose_name': 'إيراد',
                'verbose_name_plural': 'الإيرادات',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='TreasuryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المعاملة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('transaction_type', models.CharField(choices=[('RECEIPT', 'قبض'), ('PAYMENT', 'دفع'), ('TRANSFER_IN', 'تحويل وارد'), ('TRANSFER_OUT', 'تحويل صادر'), ('EXPENSE', 'مصروف'), ('REVENUE', 'إيراد')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='الوصف')),
                ('balance_after', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='sales.customer', verbose_name='العميل')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='purchases.supplier', verbose_name='المورد')),
                ('to_treasury', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='transaction_transfers_in', to='definitions.treasury', verbose_name='إلى خزينة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.treasury', verbose_name='الخزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة خزينة',
                'verbose_name_plural': 'معاملات الخزينة',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='TreasuryTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(max_length=200, verbose_name='البيان')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_out', to='definitions.treasury', verbose_name='من خزينة')),
                ('to_treasury', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_in', to='definitions.treasury', verbose_name='إلى خزينة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تحويل بين خزائن',
                'verbose_name_plural': 'تحويلات بين الخزائن',
                'ordering': ['-date', '-id'],
            },
        ),
    ]
