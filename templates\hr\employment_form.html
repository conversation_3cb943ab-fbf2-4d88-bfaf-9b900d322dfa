{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                    <form method="post">
                        {% csrf_token %}
                        {{ form.as_p }}
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i> حفظ التعيين
                            </button>
                            <a href="{% url 'hr:employment_list' %}" class="btn btn-secondary ms-2">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 