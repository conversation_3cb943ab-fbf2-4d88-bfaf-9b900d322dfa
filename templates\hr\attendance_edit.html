{% extends 'base/base.html' %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <i class="fas fa-edit me-2"></i>تعديل سجل الحضور والانصراف
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        {{ form.as_p }}
                        <div class="mt-3 text-end">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                            <a href="{% url 'hr:attendance_list' %}" class="btn btn-secondary ms-2">العودة</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 