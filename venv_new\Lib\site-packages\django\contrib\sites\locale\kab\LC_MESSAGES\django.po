# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-10-06 11:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/django/django/language/"
"kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Ismal"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Isem n taɣult ur izmir ara ad yegber isekkilen ilmawen n tirigliwin."

msgid "domain name"
msgstr "isem n taɣult"

msgid "display name"
msgstr "mefffer isem"

msgid "site"
msgstr "asmel"

msgid "sites"
msgstr "ismal"
