/* تنسيق التاريخ والوقت */

.datetime-display {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: left;
}

.date-display {
    font-weight: 500;
    color: #495057;
}

.time-display {
    font-weight: 400;
    color: #6c757d;
    font-size: 0.9em;
}

.datetime-full {
    display: flex;
    align-items: center;
    gap: 8px;
}

.datetime-full .date-part {
    font-weight: 500;
    color: #495057;
}

.datetime-full .time-part {
    font-weight: 400;
    color: #6c757d;
    font-size: 0.9em;
}

.datetime-full .separator {
    color: #adb5bd;
    margin: 0 4px;
}

/* تنسيق التاريخ في الجداول */
.table .datetime-cell {
    white-space: nowrap;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.table .date-cell {
    font-weight: 500;
    color: #495057;
}

.table .time-cell {
    font-size: 0.85em;
    color: #6c757d;
}

/* تنسيق التاريخ في البطاقات */
.card .datetime-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.card .datetime-info .date-line {
    font-weight: 500;
    color: #495057;
}

.card .datetime-info .time-line {
    font-size: 0.85em;
    color: #6c757d;
}

/* تنسيق التاريخ في النماذج */
.form-control.datetime-input {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
}

/* تنسيق التاريخ في التقارير */
.report-datetime {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

.report-date {
    color: #495057;
}

.report-time {
    color: #6c757d;
    font-size: 0.9em;
}

/* تنسيق الوقت النسبي */
.time-ago {
    font-size: 0.85em;
    color: #6c757d;
    font-style: italic;
}

.time-ago.recent {
    color: #28a745;
}

.time-ago.today {
    color: #007bff;
}

.time-ago.this-week {
    color: #ffc107;
}

.time-ago.old {
    color: #6c757d;
}

/* تنسيق أيام الأسبوع */
.weekday-display {
    font-weight: 500;
    color: #495057;
}

.weekday-display.friday {
    color: #dc3545;
}

.weekday-display.saturday {
    color: #fd7e14;
}

/* تنسيق الشهور */
.month-display {
    font-weight: 500;
    color: #495057;
}

/* تنسيق التاريخ في الإشعارات */
.notification .datetime {
    font-size: 0.8em;
    color: #6c757d;
}

/* تنسيق التاريخ في الشريط الجانبي */
.sidebar .datetime {
    font-size: 0.85em;
    color: #adb5bd;
}

/* تنسيق التاريخ في الرأس */
.header .current-datetime {
    font-size: 0.9em;
    color: #6c757d;
    font-weight: 400;
}

/* تنسيق التاريخ في الأزرار */
.btn .datetime {
    font-size: 0.85em;
}

/* تنسيق التاريخ في الشارات */
.badge .datetime {
    font-size: 0.8em;
}

/* تنسيق التاريخ في القوائم */
.list-group-item .datetime {
    font-size: 0.85em;
    color: #6c757d;
}

/* تنسيق التاريخ في الخطوط الزمنية */
.timeline .datetime {
    font-weight: 500;
    color: #495057;
}

.timeline .date-marker {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
}

/* تنسيق التاريخ في الرسوم البيانية */
.chart .datetime-label {
    font-size: 0.8em;
    color: #6c757d;
}

/* تنسيق التاريخ في الطباعة */
@media print {
    .datetime-display,
    .date-display,
    .time-display {
        color: #000 !important;
    }
    
    .report-datetime {
        font-weight: bold;
    }
}

/* تنسيق التاريخ للشاشات الصغيرة */
@media (max-width: 768px) {
    .datetime-full {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .table .datetime-cell {
        font-size: 0.85em;
    }
    
    .card .datetime-info {
        font-size: 0.9em;
    }
}

/* تنسيق التاريخ للشاشات الكبيرة */
@media (min-width: 1200px) {
    .datetime-display {
        font-size: 1.1em;
    }
    
    .report-datetime {
        font-size: 1.2em;
    }
}

/* تنسيق خاص للتقويم الهجري */
.hijri-date {
    font-family: 'Traditional Arabic', 'Amiri', serif;
    direction: rtl;
    color: #495057;
}

/* تنسيق خاص للتقويم الميلادي */
.gregorian-date {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    color: #495057;
}

/* تنسيق مختلط للتقويمين */
.dual-calendar {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.dual-calendar .gregorian {
    font-weight: 500;
}

.dual-calendar .hijri {
    font-size: 0.9em;
    color: #6c757d;
}
