#!/usr/bin/env python3
"""
بروكسي HTTPS مستقر ومحسن
Stable and Enhanced HTTPS Proxy

بروكسي HTTPS محسن مع معالجة أفضل للأخطاء وأداء محسن
Enhanced HTTPS proxy with better error handling and improved performance
"""

import os
import sys
import time
import socket
import ssl
import threading
import http.server
import socketserver
import urllib.request
import urllib.parse
from pathlib import Path
from datetime import datetime

class StableHTTPSProxy:
    """بروكسي HTTPS مستقر"""
    
    def __init__(self):
        self.django_port = 8000  # منفذ الخادم الأبدي
        self.https_port = 8443   # منفذ HTTPS
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.ssl_cert = None
        self.ssl_key = None
        self.proxy_server = None
        self.is_running = False
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_error("لا توجد شهادة SSL")
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def check_django_server(self):
        """فحص إذا كان الخادم الأبدي يعمل"""
        try:
            response = urllib.request.urlopen(f"http://127.0.0.1:{self.django_port}/", timeout=3)
            return response.getcode() == 200
        except Exception:
            return False
    
    def create_proxy_handler(self):
        """إنشاء معالج البروكسي المحسن"""
        
        class StableProxyHandler(http.server.BaseHTTPRequestHandler):
            def __init__(self, django_port, *args, **kwargs):
                self.django_port = django_port
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                self.proxy_request()
            
            def do_POST(self):
                self.proxy_request()
            
            def do_PUT(self):
                self.proxy_request()
            
            def do_DELETE(self):
                self.proxy_request()
            
            def do_PATCH(self):
                self.proxy_request()
            
            def proxy_request(self):
                try:
                    # إنشاء URL للخادم Django
                    django_url = f"http://127.0.0.1:{self.django_port}{self.path}"
                    
                    # إنشاء الطلب مع timeout قصير
                    req = urllib.request.Request(django_url, method=self.command)
                    
                    # نسخ الهيدرز المهمة فقط
                    important_headers = [
                        'content-type', 'content-length', 'authorization', 
                        'cookie', 'user-agent', 'accept', 'accept-language'
                    ]
                    
                    for header, value in self.headers.items():
                        if header.lower() in important_headers:
                            req.add_header(header, value)
                    
                    # إضافة هيدرز إضافية
                    req.add_header('Host', f'127.0.0.1:{self.django_port}')
                    req.add_header('X-Forwarded-Proto', 'https')
                    req.add_header('X-Forwarded-For', self.client_address[0])
                    
                    # إضافة البيانات للطلبات POST/PUT/PATCH
                    if self.command in ['POST', 'PUT', 'PATCH']:
                        try:
                            content_length = int(self.headers.get('Content-Length', 0))
                            if content_length > 0 and content_length < 10485760:  # حد أقصى 10MB
                                req.data = self.rfile.read(content_length)
                        except (ValueError, OverflowError):
                            pass
                    
                    # إرسال الطلب مع timeout قصير ومعالجة أفضل للأخطاء
                    try:
                        response = urllib.request.urlopen(req, timeout=5)
                    except urllib.error.HTTPError as e:
                        # معالجة أخطاء HTTP
                        self.send_response(e.code)
                        for header, value in e.headers.items():
                            if header.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(header, value)
                        self.end_headers()
                        if hasattr(e, 'read'):
                            self.wfile.write(e.read())
                        return
                    except urllib.error.URLError as e:
                        # معالجة أخطاء الشبكة
                        self.send_error(502, f"Bad Gateway: {e}")
                        return
                    except socket.timeout:
                        # معالجة timeout
                        self.send_error(504, "Gateway Timeout")
                        return
                    
                    # إرسال الاستجابة
                    self.send_response(response.getcode())
                    
                    # نسخ هيدرز الاستجابة
                    for header, value in response.headers.items():
                        if header.lower() not in ['connection', 'transfer-encoding']:
                            self.send_header(header, value)
                    
                    # إضافة هيدرز أمان
                    self.send_header('Strict-Transport-Security', 'max-age=31536000')
                    self.send_header('X-Content-Type-Options', 'nosniff')
                    self.send_header('X-Frame-Options', 'SAMEORIGIN')
                    self.send_header('X-XSS-Protection', '1; mode=block')
                    
                    self.end_headers()
                    
                    # نسخ محتوى الاستجابة
                    try:
                        content = response.read()
                        if content:
                            self.wfile.write(content)
                    except Exception:
                        pass  # تجاهل أخطاء القراءة
                    
                except Exception as e:
                    # معالجة شاملة للأخطاء
                    try:
                        self.send_error(500, f"Internal Server Error: {str(e)[:100]}")
                    except:
                        pass  # تجاهل أخطاء الإرسال
            
            def log_message(self, format, *args):
                # تجاهل رسائل اللوج لتقليل الضوضاء
                pass
        
        # إنشاء معالج مع django_port
        def handler_factory(*args, **kwargs):
            return StableProxyHandler(self.django_port, *args, **kwargs)
        
        return handler_factory
    
    def start_proxy(self):
        """بدء تشغيل البروكسي المستقر"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # فحص إذا كان الخادم الأبدي يعمل
            if not self.check_django_server():
                self.log_error(f"الخادم الأبدي لا يعمل على المنفذ {self.django_port}")
                self.log_info("تأكد من تشغيل الخادم الأبدي أولاً")
                return False
            
            # البحث عن منفذ متاح لـ HTTPS
            https_port = self.find_available_port(8443)
            if not https_port:
                self.log_error("لا يمكن العثور على منفذ متاح لـ HTTPS")
                return False
            
            self.https_port = https_port
            
            # إنشاء معالج البروكسي
            handler = self.create_proxy_handler()
            
            # إنشاء الخادم مع إعدادات محسنة
            self.proxy_server = socketserver.ThreadingTCPServer(('0.0.0.0', https_port), handler)
            self.proxy_server.allow_reuse_address = True
            self.proxy_server.timeout = 30
            
            # إعداد SSL مع إعدادات محسنة
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.minimum_version = ssl.TLSVersion.TLSv1_2
            context.load_cert_chain(str(self.ssl_cert), str(self.ssl_key))
            
            self.proxy_server.socket = context.wrap_socket(
                self.proxy_server.socket,
                server_side=True
            )
            
            self.is_running = True
            self.log_success(f"تم بدء بروكسي HTTPS المستقر على المنفذ {https_port}")
            self.display_access_info()
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في بدء البروكسي: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🔒 بروكسي HTTPS المستقر")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 منفذ Django (HTTP): {self.django_port}")
        print(f"🔒 منفذ HTTPS: {self.https_port}")
        print("\n🔒 للوصول الآمن (HTTPS):")
        print(f"   https://{self.local_ip}:{self.https_port}/dashboard/")
        print(f"   https://localhost:{self.https_port}/dashboard/")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   https://{self.local_ip}:{self.https_port}/dashboard/")
        print("\n✅ مميزات البروكسي المستقر:")
        print("   • معالجة محسنة للأخطاء")
        print("   • timeout قصير لتجنب التعليق")
        print("   • دعم Threading للأداء الأفضل")
        print("   • هيدرز أمان محسنة")
        print("=" * 60)
    
    def run(self):
        """تشغيل البروكسي المستقر"""
        print("=" * 60)
        print("🔒 بروكسي HTTPS المستقر")
        print("Stable HTTPS Proxy")
        print("=" * 60)
        
        self.log_info("بدء تشغيل بروكسي HTTPS المستقر...")
        
        if not self.start_proxy():
            self.log_error("فشل في بدء بروكسي HTTPS!")
            return False
        
        self.log_success("بروكسي HTTPS المستقر يعمل بنجاح!")
        self.log_info("اضغط Ctrl+C للإيقاف")
        
        try:
            self.proxy_server.serve_forever()
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في البروكسي: {e}")
        finally:
            if self.proxy_server:
                self.proxy_server.shutdown()
                self.proxy_server.server_close()
            self.log_info("تم إنهاء بروكسي HTTPS المستقر")
        
        return True

def main():
    """الدالة الرئيسية"""
    proxy = StableHTTPSProxy()
    success = proxy.run()
    
    if success:
        print("✅ تم تشغيل بروكسي HTTPS المستقر بنجاح!")
    else:
        print("❌ فشل في تشغيل بروكسي HTTPS المستقر!")
        sys.exit(1)

if __name__ == "__main__":
    main()
