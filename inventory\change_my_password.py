from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse

@login_required
def change_my_password(request):
    if request.user.is_authenticated:
        user = request.user
        user.set_password('OsamaOsama010@')
        user.save()
        return HttpResponse('تم تغيير كلمة المرور بنجاح!')
    return HttpResponse('يجب تسجيل الدخول أولاً.', status=403)
