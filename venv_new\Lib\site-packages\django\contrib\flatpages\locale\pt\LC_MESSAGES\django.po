# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2011-2012,2015
# <PERSON><PERSON>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Portuguese (http://app.transifex.com/django/django/language/"
"pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opções avançadas"

msgid "Flat Pages"
msgstr "Páginas Simples"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Exemplo: “/about/contact/”. Certifique-se que tem traços oblíquos no início "
"e no final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor apenas poderá conter letras, números, pontos, underscores, "
"hífenes, barras ou tils."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Exemplo: “/about/contact”. Certifique-se que tem um traço obliquo no inicio."

msgid "URL is missing a leading slash."
msgstr "Está a faltar a barra inicial no URL."

msgid "URL is missing a trailing slash."
msgstr "Está a faltar a barra final no URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "A página plana com o url %(url)s já existe no site %(site)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "conteúdo"

msgid "enable comments"
msgstr "permitir comentários"

msgid "template name"
msgstr "nome da template"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Exemplo: “flatpages/contact_page.html”. Se isto não for fornecido, o sistema "
"utilizará “flatpages/default.html”."

msgid "registration required"
msgstr "é necessário registo"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Se estiver selecionado, apenas utilizadores autenticados poderão ver esta "
"página."

msgid "sites"
msgstr "site"

msgid "flat page"
msgstr "página plana"

msgid "flat pages"
msgstr "páginas planas"
