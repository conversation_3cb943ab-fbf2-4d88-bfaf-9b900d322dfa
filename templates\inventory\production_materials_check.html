{% extends 'base/base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
.material-card {
    transition: all 0.3s ease;
    border-radius: 10px;
}

.material-card.sufficient {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}

.material-card.insufficient {
    border-left: 4px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

.status-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.status-sufficient {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-insufficient {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.quantity-display {
    font-size: 1.1rem;
    font-weight: 600;
}

.shortage-alert {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 8px;
    padding: 0.75rem;
}

.summary-card {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.material-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.material-placeholder {
    width: 60px;
    height: 60px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-search text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">
                        فحص توفر المواد الخام المطلوبة للإنتاج
                    </p>
                </div>
                <div>
                    <a href="{% url 'inventory:manufacturing_order_detail' order.pk %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة لأمر الإنتاج
                    </a>
                </div>
            </div>

            <!-- Summary -->
            <div class="summary-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">أمر الإنتاج: {{ order.order_number }}</h3>
                        <p class="mb-1"><strong>المنتج:</strong> {{ order.product_item.name }}</p>
                        <p class="mb-1"><strong>الكمية المطلوبة:</strong> {{ order.quantity_to_produce|floatformat:3 }} {{ order.product_item.unit.symbol }}</p>
                        <p class="mb-0"><strong>المخزن:</strong> {{ order.warehouse.name }}</p>
                    </div>
                    <div class="col-md-4 text-center">
                        {% if can_start_production %}
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h5 class="mb-0">جاهز للإنتاج</h5>
                                <small>جميع المواد متوفرة</small>
                            </div>
                        {% else %}
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h5 class="mb-0">غير جاهز</h5>
                                <small>نقص في المواد الخام</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Materials Status -->
            <div class="row">
                {% for status in materials_status %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card material-card {% if status.is_sufficient %}sufficient{% else %}insufficient{% endif %}">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    {% if status.material.material.image %}
                                        <img src="{{ status.material.material.image.url }}" alt="{{ status.material.material.name }}" class="material-image">
                                    {% else %}
                                        <div class="material-placeholder">
                                            <i class="fas fa-box"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-9">
                                    <h6 class="card-title mb-1">{{ status.material.material.name }}</h6>
                                    <p class="text-muted small mb-2">{{ status.material.material.code }}</p>
                                    
                                    <div class="mb-2">
                                        <span class="status-badge {% if status.is_sufficient %}status-sufficient{% else %}status-insufficient{% endif %}">
                                            {% if status.is_sufficient %}
                                                <i class="fas fa-check me-1"></i>متوفر
                                            {% else %}
                                                <i class="fas fa-times me-1"></i>غير كافي
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-3">
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted d-block">المطلوب</small>
                                    <div class="quantity-display text-primary">
                                        {{ status.required_quantity|floatformat:3 }}
                                    </div>
                                    <small class="text-muted">{{ status.material.material.unit.symbol }}</small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">المتوفر</small>
                                    <div class="quantity-display {% if status.is_sufficient %}text-success{% else %}text-danger{% endif %}">
                                        {{ status.available_quantity|floatformat:3 }}
                                    </div>
                                    <small class="text-muted">{{ status.material.material.unit.symbol }}</small>
                                </div>
                            </div>
                            
                            {% if not status.is_sufficient %}
                            <div class="shortage-alert mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>نقص:</strong> {{ status.shortage|floatformat:3 }} {{ status.material.material.unit.symbol }}
                            </div>
                            {% endif %}
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <strong>التكلفة:</strong> {{ status.material.total_cost|currency }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>لا توجد مواد خام محددة</h5>
                        <p class="mb-0">يجب إضافة المواد الخام المطلوبة لأمر الإنتاج أولاً</p>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Action Buttons -->
            {% if materials_status %}
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if can_start_production %}
                                <span class="text-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    جميع المواد الخام متوفرة ويمكن بدء الإنتاج
                                </span>
                            {% else %}
                                <span class="text-danger">
                                    <i class="fas fa-times-circle me-2"></i>
                                    يوجد نقص في بعض المواد الخام
                                </span>
                            {% endif %}
                        </div>
                        <div>
                            {% if can_start_production and order.can_be_started %}
                                <form method="post" action="{% url 'inventory:start_production' order.pk %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-success" onclick="return confirm('هل أنت متأكد من بدء الإنتاج؟ سيتم حجز المواد الخام من المخزن.')">
                                        <i class="fas fa-play me-1"></i>
                                        بدء الإنتاج
                                    </button>
                                </form>
                            {% endif %}
                            <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-1"></i>
                                تعديل أمر الإنتاج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات تفاعلية للبطاقات
    const materialCards = document.querySelectorAll('.material-card');
    materialCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>
{% endblock %}
