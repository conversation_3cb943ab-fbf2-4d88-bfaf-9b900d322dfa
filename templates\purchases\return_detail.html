{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-undo text-danger me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'purchases:return_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للقائمة
                    </a>
                    {% if return_obj.status == 'PENDING' %}
                        <a href="#" class="btn btn-warning me-2" title="تعديل">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-primary me-2" onclick="printReturn({{ return_obj.pk }})" title="طباعة">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    {% if return_obj.status == 'PENDING' %}
                        <button type="button" class="btn btn-success me-2" onclick="approveReturn({{ return_obj.pk }})" title="اعتماد">
                            <i class="fas fa-check me-1"></i>
                            اعتماد
                        </button>
                    {% endif %}
                </div>
            </div>

            <!-- Return Details -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                تفاصيل المرتجع
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">رقم المرتجع:</td>
                                            <td>{{ return_obj.return_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">التاريخ:</td>
                                            <td>{{ return_obj.date|date:"d/m/Y" }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">المورد:</td>
                                            <td>
                                                <a href="#" class="text-decoration-none">
                                                    {{ return_obj.supplier.name }}
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">الفاتورة الأصلية:</td>
                                            <td>
                                                {% if return_obj.original_invoice %}
                                                    <a href="{% url 'purchases:invoice_detail' return_obj.original_invoice.pk %}" 
                                                       class="text-decoration-none">
                                                        {{ return_obj.original_invoice.invoice_number }}
                                                    </a>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">الحالة:</td>
                                            <td>
                                                {% if return_obj.status == 'PENDING' %}
                                                    <span class="badge bg-warning">{{ return_obj.get_status_display }}</span>
                                                {% elif return_obj.status == 'APPROVED' %}
                                                    <span class="badge bg-success">{{ return_obj.get_status_display }}</span>
                                                {% elif return_obj.status == 'COMPLETED' %}
                                                    <span class="badge bg-primary">{{ return_obj.get_status_display }}</span>
                                                {% elif return_obj.status == 'REJECTED' %}
                                                    <span class="badge bg-danger">{{ return_obj.get_status_display }}</span>
                                                {% else %}
                                                    <span class="badge bg-light text-dark">{{ return_obj.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">إجمالي المبلغ:</td>
                                            <td>
                                                <strong class="text-danger fs-5">{{ return_obj.total_amount|floatformat:2 }} ج.م</strong>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">سبب الإرجاع:</td>
                                            <td>
                                                {% if return_obj.reason %}
                                                    <span class="text-muted">{{ return_obj.reason }}</span>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">أنشئ بواسطة:</td>
                                            <td>{{ return_obj.created_by.get_full_name|default:return_obj.created_by.username }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                ملخص المرتجع
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <span>عدد الأصناف:</span>
                                <strong>{{ return_items.count }}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>إجمالي الكميات:</span>
                                <strong>{{ return_items|length }}</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>إجمالي المبلغ:</span>
                                <strong class="text-danger">{{ return_obj.total_amount|floatformat:2 }} ج.م</strong>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span>تاريخ الإنشاء:</span>
                                <small class="text-muted">{{ return_obj.created_at|date:"d/m/Y H:i" }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Return Items -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        الأصناف المرتجعة
                    </h5>
                </div>
                <div class="card-body">
                    {% if return_items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>الصنف</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>إجمالي السعر</th>
                                        <th>الوحدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in return_items %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>
                                                <strong>{{ item.item.name }}</strong>
                                                {% if item.item.code %}
                                                    <br><small class="text-muted">كود: {{ item.item.code }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">{{ item.quantity|floatformat:2 }}</span>
                                            </td>
                                            <td>{{ item.unit_cost|floatformat:2 }} ج.م</td>
                                            <td>
                                                <strong class="text-danger">{{ item.total_amount|floatformat:2 }} ج.م</strong>
                                            </td>
                                            <td>
                                                {% if item.item.unit %}
                                                    <span class="badge bg-secondary">{{ item.item.unit.symbol }}</span>
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="4" class="text-end fw-bold">الإجمالي:</td>
                                        <td class="fw-bold text-danger fs-5">{{ return_obj.total_amount|floatformat:2 }} ج.م</td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أصناف مرتجعة</h5>
                            <p class="text-muted">لم يتم تحديد أي أصناف في هذا المرتجع</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Notes Section -->
            {% if return_obj.notes %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            الملاحظات
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">{{ return_obj.notes }}</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printReturn(returnId) {
    // فتح نافذة طباعة جديدة
    window.open(`/purchases/returns/${returnId}/print/`, '_blank');
}

function approveReturn(returnId) {
    if (confirm('هل أنت متأكد من اعتماد هذا المرتجع؟')) {
        // إرسال طلب اعتماد المرتجع
        fetch(`/purchases/returns/${returnId}/approve/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء اعتماد المرتجع');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء اعتماد المرتجع');
        });
    }
}
</script>
{% endblock %} 