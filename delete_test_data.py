#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit, Currency, Warehouse

def delete_test_items():
    print("=== Deleting test items ===")
    
    # Delete items that look like test data
    test_patterns = [
        'test', 'Test', 'TEST',
        'sample', 'Sample', 'SAMPLE',
        'demo', 'Demo', 'DEMO',
        'Product 1', 'Product 2', 'Product 3', 'Product 4', 'Product 5',
        'FP001', 'FP002', 'FP003', 'FP004', 'FP005',
        'MAT002', 'MAT003', 'MAT004',
        'PP4', 'K.D'
    ]
    
    deleted_count = 0
    
    for pattern in test_patterns:
        items = Item.objects.filter(is_active=True, name__icontains=pattern)
        for item in items:
            print(f"Deleting: {item.code} - {item.name}")
            item.is_active = False
            item.save()
            deleted_count += 1
    
    # Also delete by code patterns
    code_patterns = ['OS', 'FP', 'MAT', 'PP']
    for pattern in code_patterns:
        items = Item.objects.filter(is_active=True, code__startswith=pattern)
        for item in items:
            print(f"Deleting: {item.code} - {item.name}")
            item.is_active = False
            item.save()
            deleted_count += 1
    
    print(f"\nTotal items deleted: {deleted_count}")
    
    # Check remaining items
    remaining_items = Item.objects.filter(is_active=True)
    print(f"Remaining active items: {remaining_items.count()}")

def delete_test_categories():
    print("\n=== Deleting test categories ===")
    
    test_categories = ItemCategory.objects.filter(
        is_active=True,
        name__in=['Test Category', 'Sample Category', 'Demo Category']
    )
    
    deleted_count = 0
    for category in test_categories:
        print(f"Deleting category: {category.name}")
        category.is_active = False
        category.save()
        deleted_count += 1
    
    print(f"Categories deleted: {deleted_count}")

def delete_test_warehouses():
    print("\n=== Deleting test warehouses ===")
    
    test_warehouses = Warehouse.objects.filter(
        is_active=True,
        name__icontains='test'
    )
    
    deleted_count = 0
    for warehouse in test_warehouses:
        print(f"Deleting warehouse: {warehouse.name}")
        warehouse.is_active = False
        warehouse.save()
        deleted_count += 1
    
    print(f"Warehouses deleted: {deleted_count}")

if __name__ == "__main__":
    print("Starting cleanup of test data...")
    delete_test_items()
    delete_test_categories()
    delete_test_warehouses()
    print("\nCleanup completed!")