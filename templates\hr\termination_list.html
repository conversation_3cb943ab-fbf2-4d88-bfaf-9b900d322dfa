{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .termination-stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .termination-stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .termination-stats-card .card-body {
        color: white;
    }
    
    .termination-stats-card .text-muted {
        color: rgba(255,255,255,0.8) !important;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
    }
    
    .pending-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .approved-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .rejected-card {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .total-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333 !important;
    }
    
    .total-card .text-muted {
        color: #666 !important;
    }
    
    .type-stats-card {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }
    
    .type-stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }
    
    .type-stats-card .card-body {
        color: #333;
    }
    
    .type-stats-card .text-muted {
        color: #666 !important;
    }
    
    .resignation-card {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }
    
    .dismissal-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
    
    .retirement-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }
    
    .main-table-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .main-table-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table thead th {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border: none;
        color: #333;
        font-weight: 600;
        padding: 15px 10px;
        text-align: center;
    }
    
    .table tbody tr {
        transition: all 0.3s ease;
    }
    
    .table tbody tr:hover {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        transform: scale(1.01);
    }
    
    .table tbody td {
        padding: 12px 10px;
        vertical-align: middle;
        border: none;
        border-bottom: 1px solid #eee;
    }
    
    .status-badge {
        padding: 8px 12px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #d63384;
    }
    
    .status-approved {
        background: linear-gradient(135deg, #d4fc79 0%, #96e6a1 100%);
        color: #198754;
    }
    
    .status-rejected {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #dc3545;
    }
    
    .status-completed {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #0dcaf0;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
    }
    
    .action-btn {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .btn-view {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .btn-approve {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    
    .btn-reject {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #dc3545;
    }
    
    .add-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .add-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
    
    .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    }
    
    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px 15px 0 0;
    }
    
    .modal-title {
        font-weight: 600;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 20px;
    }
    
    .employee-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .employee-link:hover {
        color: #5a6fd8;
        text-decoration: underline;
    }
    
    .type-badge {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .type-resignation {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #d63384;
    }
    
    .type-dismissal {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #dc3545;
    }
    
    .type-retirement {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #0dcaf0;
    }
    
    .type-contract {
        background: linear-gradient(135deg, #d4fc79 0%, #96e6a1 100%);
        color: #198754;
    }
    
    .type-death {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .type-other {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .duration-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            gap: 3px;
        }
        
        .action-btn {
            width: 30px;
            height: 30px;
            font-size: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-primary me-3" style="border-radius: 25px; padding: 8px 20px; transition: all 0.3s ease; border: 2px solid #667eea; color: #667eea; font-weight: 500;">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <h4 class="mb-0">
                        <i class="fas fa-user-times me-2" style="color: #667eea;"></i>
                        {{ title }}
                    </h4>
                </div>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">إنهاء الخدمة</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card termination-stats-card total-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">إجمالي الطلبات</p>
                            <h3 class="mb-0 fw-bold">{{ total_terminations }}</h3>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card termination-stats-card pending-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">قيد الانتظار</p>
                            <h3 class="mb-0 fw-bold">{{ pending_terminations }}</h3>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card termination-stats-card approved-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">موافق عليه</p>
                            <h3 class="mb-0 fw-bold">{{ approved_terminations }}</h3>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card termination-stats-card rejected-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">مرفوض</p>
                            <h3 class="mb-0 fw-bold">{{ rejected_terminations }}</h3>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات حسب النوع -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card type-stats-card resignation-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">استقالات</p>
                            <h4 class="mb-0 fw-bold">{{ resignation_count }}</h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon" style="background: rgba(214, 51, 132, 0.2);">
                                <i class="fas fa-user-minus" style="color: #d63384;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card type-stats-card dismissal-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">فصل من الخدمة</p>
                            <h4 class="mb-0 fw-bold">{{ dismissal_count }}</h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon" style="background: rgba(220, 53, 69, 0.2);">
                                <i class="fas fa-user-x" style="color: #dc3545;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card type-stats-card retirement-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium mb-1">تقاعد</p>
                            <h4 class="mb-0 fw-bold">{{ retirement_count }}</h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="stats-icon" style="background: rgba(13, 202, 240, 0.2);">
                                <i class="fas fa-user-check" style="color: #0dcaf0;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card main-table-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            طلبات إنهاء الخدمة
                        </h5>
                        <button type="button" class="btn add-btn" data-bs-toggle="modal" data-bs-target="#addTerminationModal">
                            <i class="fas fa-plus me-2"></i> إضافة طلب جديد
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="text-center">الموظف</th>
                                    <th class="text-center">النوع</th>
                                    <th class="text-center">القسم</th>
                                    <th class="text-center">السبب</th>
                                    <th class="text-center">تاريخ إنهاء الخدمة</th>
                                    <th class="text-center">مدة الخدمة</th>
                                    <th class="text-center">الحالة</th>
                                    <th class="text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for termination in terminations %}
                                <tr>
                                    <td class="text-center">
                                        <a href="{% url 'hr:termination_detail' termination.pk %}" class="employee-link">
                                            <i class="fas fa-user me-1"></i>
                                            {{ termination.employee.person.name }}
                                        </a>
                                    </td>
                                    <td class="text-center">
                                        <span class="type-badge type-{{ termination.termination_type|lower }}">
                                            {{ termination.get_termination_type_display }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <i class="fas fa-building me-1" style="color: #667eea;"></i>
                                        {{ termination.employee.department.name }}
                                    </td>
                                    <td class="text-center">
                                        <span title="{{ termination.reason }}">
                                            {{ termination.reason|truncatechars:50 }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <i class="fas fa-calendar me-1" style="color: #667eea;"></i>
                                        {{ termination.termination_date }}
                                    </td>
                                    <td class="text-center">
                                        <span class="duration-badge">
                                            {{ termination.get_service_duration }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        {% if termination.status == 'PENDING' %}
                                            <span class="status-badge status-pending">
                                                <i class="fas fa-clock me-1"></i>قيد الانتظار
                                            </span>
                                        {% elif termination.status == 'APPROVED' %}
                                            <span class="status-badge status-approved">
                                                <i class="fas fa-check me-1"></i>موافق عليه
                                            </span>
                                        {% elif termination.status == 'REJECTED' %}
                                            <span class="status-badge status-rejected">
                                                <i class="fas fa-times me-1"></i>مرفوض
                                            </span>
                                        {% elif termination.status == 'COMPLETED' %}
                                            <span class="status-badge status-completed">
                                                <i class="fas fa-check-double me-1"></i>مكتمل
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="action-buttons">
                                            <a href="{% url 'hr:termination_detail' termination.pk %}" class="action-btn btn-view" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if termination.status == 'PENDING' %}
                                            <a href="{% url 'hr:termination_edit' termination.pk %}" class="action-btn btn-edit" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'hr:termination_approve' termination.pk %}" class="action-btn btn-approve" title="موافقة">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="{% url 'hr:termination_reject' termination.pk %}" class="action-btn btn-reject" title="رفض">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            {% endif %}
                                            <form method="post" action="{% url 'hr:termination_delete' termination.pk %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="action-btn btn-delete" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8">
                                        <div class="empty-state">
                                            <i class="fas fa-inbox"></i>
                                            <h5>لا توجد طلبات إنهاء خدمة</h5>
                                            <p>لم يتم إضافة أي طلبات إنهاء خدمة بعد</p>
                                            <button type="button" class="btn add-btn" data-bs-toggle="modal" data-bs-target="#addTerminationModal">
                                                <i class="fas fa-plus me-2"></i> إضافة أول طلب
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة طلب جديد -->
<div class="modal fade" id="addTerminationModal" tabindex="-1" aria-labelledby="addTerminationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTerminationModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة طلب إنهاء خدمة جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'hr:termination_create' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_employee" class="form-label">
                                    <i class="fas fa-user me-1"></i>الموظف
                                </label>
                                <select name="employee" id="id_employee" class="form-control" required>
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.pk }}">{{ employee.person.name }} - {{ employee.department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_termination_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>نوع إنهاء الخدمة
                                </label>
                                <select name="termination_type" id="id_termination_type" class="form-control" required>
                                    <option value="">اختر النوع</option>
                                    <option value="RESIGNATION">استقالة</option>
                                    <option value="DISMISSAL">فصل من الخدمة</option>
                                    <option value="RETIREMENT">تقاعد</option>
                                    <option value="CONTRACT_END">انتهاء العقد</option>
                                    <option value="DEATH">وفاة</option>
                                    <option value="OTHER">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_notice_period" class="form-label">
                                    <i class="fas fa-calendar-day me-1"></i>فترة الإشعار (بالأيام)
                                </label>
                                <input type="number" name="notice_period" id="id_notice_period" class="form-control" value="30" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_termination_date" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>تاريخ إنهاء الخدمة
                                </label>
                                <input type="date" name="termination_date" id="id_termination_date" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_last_working_day" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>آخر يوم عمل
                                </label>
                                <input type="date" name="last_working_day" id="id_last_working_day" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="id_reason" class="form-label">
                            <i class="fas fa-comment me-1"></i>سبب إنهاء الخدمة
                        </label>
                        <textarea name="reason" id="id_reason" class="form-control" rows="4" placeholder="اكتب سبب إنهاء الخدمة هنا..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn add-btn">
                        <i class="fas fa-save me-1"></i>حفظ الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث آخر يوم عمل تلقائياً عند تغيير تاريخ إنهاء الخدمة
    $('#id_termination_date').change(function() {
        var terminationDate = new Date($(this).val());
        var noticePeriod = parseInt($('#id_notice_period').val()) || 30;
        
        var lastWorkingDay = new Date(terminationDate);
        lastWorkingDay.setDate(lastWorkingDay.getDate() - noticePeriod);
        
        $('#id_last_working_day').val(lastWorkingDay.toISOString().split('T')[0]);
    });
    
    // تحديث آخر يوم عمل عند تغيير فترة الإشعار
    $('#id_notice_period').change(function() {
        var terminationDate = $('#id_termination_date').val();
        if (terminationDate) {
            $('#id_termination_date').trigger('change');
        }
    });
    
    // إضافة تأثيرات بصرية للجداول
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-hover');
        },
        function() {
            $(this).removeClass('table-hover');
        }
    );
    
    // تحسين تجربة المستخدم للنماذج
    $('.form-control').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        $(this).parent().removeClass('focused');
    });
});
</script>
{% endblock %}
