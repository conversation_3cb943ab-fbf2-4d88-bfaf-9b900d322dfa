# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-15 00:08+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Хуччатгузории рохбарият"

msgid "Home"
msgstr "Асосӣ"

msgid "Documentation"
msgstr "Хуҷҷатгузорӣ"

msgid "Bookmarklets"
msgstr "Гузориш"

msgid "Documentation bookmarklets"
msgstr "Хуҷҷатгузорӣ"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Барои ба панели гузоришот ворид сохтани сурогаи сомонаи мазкур ба равзанаи "
"гузоришот ,бо пахши тугмаи чапи муш сурогаро дохил кунед.Акнун шумо аз "
"тамоми саҳифаҳои сомона ба ин суроға дастрасӣ пайдо кардед."

msgid "Documentation for this page"
msgstr "Хуҷҷатгузорӣ барои саҳифаи мазкур"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Аз тамоми саҳифаҳо бв ҳуҷҷатгузории view, ки ин саҳифаро табдил медиҳад, "
"раҳнамоӣ месозад"

msgid "Tags"
msgstr "Тегҳо"

msgid "List of all the template tags and their functions."
msgstr "Руйхати хама template -ҳо ва функқияи онхо"

msgid "Filters"
msgstr "Поло-ҳо (Filters)"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Полоҳо(Filters) - ин амалиётест ,ки барои тағйирдиҳии инъикоси шаблонҳо иҷро "
"мешавад"

msgid "Models"
msgstr "Моделҳо"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Моделҳо - ин рӯйхати ҳамаи объектҳо ва майдонҳои ба онҳо алоқандӣ дар "
"система мавҷудбуда аст.Ҳар як модел руйхати майдонҳои худро дорад, ки дар "
"шаблонҳо ба сифати тағйирёбанда истифода бурда мешаванд."

msgid "Views"
msgstr "View"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Ҳар як саҳифаи сомона тавассути view сохта мешавад. View муайян месозад, ки "
"кадом шаблон барои сохтанисаҳифа истифода мешавад ва кадом кадом объектҳо "
"дар он дастрасанд."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Асбобҳо барои браузери шумо, барои дастрасии зуд ба қисми маъмурияти "
"сомона(Гузоришот)"

msgid "Please install docutils"
msgstr "Хоҳиш менамоем, docutil-ро танзим(install) кунед"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Барои фаъолияти маъмурияти системаи ҳуҷҷатгузорӣ, бояд пакетҳои <a href="
"\"%(link)s\">docutils</a> -b Pythonтанзим карда шуда бошанд."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Хоҳишмандем аз маъмурият дархости танзими <a href=\"%(link)s\">docutils</a> -"
"ро кунед."

#, python-format
msgid "Model: %(name)s"
msgstr "Модел: %(name)s"

msgid "Fields"
msgstr "Майдонҳо"

msgid "Field"
msgstr "Майдон"

msgid "Type"
msgstr "Намуд"

msgid "Description"
msgstr "Тавсиф"

msgid "Methods with arguments"
msgstr "Усулҳо бо санадҳо"

msgid "Method"
msgstr "Усул"

msgid "Arguments"
msgstr "Санадҳо"

msgid "Back to Model documentation"
msgstr "Ба қафо, ба қисми ҳуҷҷатгузории моделҳо"

msgid "Model documentation"
msgstr "Ҳуҷҷатгузории моделҳо"

msgid "Model groups"
msgstr "Гурӯҳы моделҳо"

msgid "Templates"
msgstr "Нусхаҳо"

#, python-format
msgid "Template: %(name)s"
msgstr "Нусхаи: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

msgid "(does not exist)"
msgstr "(вуҷуд надорад)"

msgid "Back to Documentation"
msgstr "Ба қафо, ба қисми ҳуҷҷатгузорӣ"

msgid "Template filters"
msgstr "Полоҳо(Filters)-и нусхаҳо"

msgid "Template filter documentation"
msgstr "Нусхаи ҳуҷҷатгузории полоҳо"

msgid "Built-in filters"
msgstr "Бо полоҳо(filters) сохташуда."

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Барои истифодаи ин поло(filter), <code>%(code)s</code> - ро дар нусхаи худ "
"бо истифода аз полоҳо (Filters) ҷой диҳед."

msgid "Template tags"
msgstr "Теги шаблонов"

msgid "Template tag documentation"
msgstr "Ҳуҷатгузории нусхаи тегҳо"

msgid "Built-in tags"
msgstr "Тегҳои сохташуда"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Нусхаҳо:"

msgid "Back to View documentation"
msgstr "Ба қафо ба қисми намоиш(View)"

msgid "View documentation"
msgstr "Ҳуҷҷатгузории view"

msgid "Jump to namespace"
msgstr "Гузариш ба майдони номҳо"

msgid "Empty namespace"
msgstr "Майдони номҳои фарох"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "View дар майдони номҳо %(name)s"

msgid "Views by empty namespace"
msgstr "View дар майдони номҳои фарох"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"View-функсия: <code>%(full_name)s</code>. Номи url-нусхаҳо: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "тег:"

msgid "filter:"
msgstr "Поло(filter):"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Барномаи «%(app_label)r» ёфт нашуд"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модели %(model_name)r дар барномаи %(app_label)r ёфт нашуд"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "Объектҳои вобаста `%(app_label)s.%(data_type)s` "

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "Объектҳои алоқаманд `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "ҳамаи %s"

#, python-format
msgid "number of %s"
msgstr "миқдори %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ба объекти urlpattern монанд нест."
