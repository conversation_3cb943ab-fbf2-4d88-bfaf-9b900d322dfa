#!/usr/bin/env python
"""
Check table structure
"""

import os
import sys
import django
import sqlite3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def check_table_structure():
    """Check table structure"""
    
    # Get database path
    db_path = settings.DATABASES['default']['NAME']
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("ManufacturingOrder table structure:")
        print("=" * 50)
        
        # Get table info
        cursor.execute("PRAGMA table_info(inventory_manufacturingorder)")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"{column[1]} - {column[2]} - {'NOT NULL' if column[3] else 'NULL'}")
        
        print("\nManufacturingOrderMaterial table structure:")
        print("=" * 50)
        
        cursor.execute("PRAGMA table_info(inventory_manufacturingordermaterial)")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"{column[1]} - {column[2]} - {'NOT NULL' if column[3] else 'NULL'}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    check_table_structure()