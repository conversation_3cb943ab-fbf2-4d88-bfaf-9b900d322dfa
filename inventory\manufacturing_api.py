"""
Manufacturing API Views
واجهات برمجة التطبيقات للتصنيع
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from definitions.models import Item, Warehouse
from .models import Stock


@login_required
@require_http_methods(["GET"])
def warehouse_items(request):
    """
    جلب الأصناف الموجودة في مخزن معين
    """
    try:
        warehouse_id = request.GET.get('warehouse_id')
        item_type = request.GET.get('item_type', 'RAW_MATERIAL')
        
        if not warehouse_id:
            return JsonResponse({'error': 'معرف المخزن مطلوب'}, status=400)
        
        # جلب الأصناف الموجودة في المخزن
        stock_items = Stock.objects.filter(
            warehouse_id=warehouse_id,
            item__item_type=item_type,
            item__is_active=True,
            quantity__gt=0
        ).select_related('item').values(
            'item__id',
            'item__name',
            'item__code',
            'quantity'
        )
        
        items = []
        for stock in stock_items:
            items.append({
                'id': stock['item__id'],
                'name': stock['item__name'],
                'code': stock['item__code'],
                'available_quantity': float(stock['quantity'])
            })
        
        return JsonResponse({'items': items})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def material_data(request):
    """
    جلب بيانات مادة خام من مخزن معين
    """
    try:
        warehouse_id = request.GET.get('warehouse_id')
        material_id = request.GET.get('material_id')
        
        if not warehouse_id or not material_id:
            return JsonResponse({'error': 'معرف المخزن والمادة مطلوبان'}, status=400)
        
        # جلب بيانات المخزون
        try:
            stock = Stock.objects.get(
                warehouse_id=warehouse_id,
                item_id=material_id
            )
            available_quantity = float(stock.quantity)
            unit_cost = float(stock.average_cost) if stock.average_cost else 0
        except Stock.DoesNotExist:
            available_quantity = 0
            unit_cost = 0
        
        # جلب سعر المادة من جدول الأصناف
        try:
            item = Item.objects.get(id=material_id)
            if not unit_cost:
                unit_cost = float(item.cost_price) if item.cost_price else 0
        except Item.DoesNotExist:
            unit_cost = 0
        
        data = {
            'available_quantity': available_quantity,
            'unit_cost': unit_cost
        }
        
        return JsonResponse(data)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def item_cost(request):
    """
    جلب سعر صنف معين
    """
    try:
        item_id = request.GET.get('item_id')
        
        if not item_id:
            return JsonResponse({'error': 'معرف الصنف مطلوب'}, status=400)
        
        try:
            item = Item.objects.get(id=item_id, is_active=True)
            cost_price = float(item.cost_price) if item.cost_price else 0
            
            data = {
                'cost_price': cost_price
            }
            
            return JsonResponse(data)
        
        except Item.DoesNotExist:
            return JsonResponse({'error': 'الصنف غير موجود'}, status=404)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500) 