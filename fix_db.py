#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import connection

cursor = connection.cursor()

# List of missing columns to add
missing_columns = [
    ("actual_start_date", "DATE"),
    ("actual_completion_date", "DATE"),
    ("approved_at", "DATETIME"),
    ("completed_at", "DATETIME"),
    ("materials_reserved", "BOOLEAN DEFAULT 0"),
    ("materials_consumed", "BOOLEAN DEFAULT 0"),
    ("production_completed", "BOOLEAN DEFAULT 0"),
    ("quality_approved", "BOOLEAN DEFAULT 0"),
    ("stock_updated", "BOOLEAN DEFAULT 0"),
]

for column_name, column_type in missing_columns:
    try:
        cursor.execute(f"ALTER TABLE inventory_manufacturingorder ADD COLUMN {column_name} {column_type}")
        print(f"Added {column_name} column")
    except Exception as e:
        print(f"{column_name} error: {e}")

# Mark migration as applied
try:
    cursor.execute("UPDATE django_migrations SET applied = datetime('now') WHERE app = 'inventory' AND name = '0020_auto_20250619_1150'")
    print("Marked migration as applied")
except Exception as e:
    print(f"Migration mark error: {e}")

print("Database fix completed!")
