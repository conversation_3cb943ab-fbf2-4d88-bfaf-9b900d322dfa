#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item

def delete_test_items():
    # Delete items by specific codes
    test_codes = ['OS022', 'OS015', 'FP001', 'FP002', 'FP003', 'FP004', 'FP005', 'MAT004', 'MAT002', 'MAT003']
    
    deleted_count = 0
    for code in test_codes:
        try:
            item = Item.objects.get(code=code, is_active=True)
            item.is_active = False
            item.save()
            deleted_count += 1
            print(f"Deleted: {code}")
        except Item.DoesNotExist:
            print(f"Not found: {code}")
    
    print(f"Total deleted: {deleted_count}")
    
    # Check remaining
    remaining = Item.objects.filter(is_active=True).count()
    print(f"Remaining items: {remaining}")

if __name__ == "__main__":
    delete_test_items()