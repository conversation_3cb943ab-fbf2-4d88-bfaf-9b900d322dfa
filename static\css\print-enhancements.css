/**
 * تحسينات إضافية لتصميمات الطباعة
 * Additional Print Design Enhancements
 */

/* تحسينات خاصة بالطباعة */
@media print {
    /* تحسين عرض الشعارات والصور */
    .company-logo,
    .print-logo {
        max-width: 150px !important;
        max-height: 80px !important;
        object-fit: contain !important;
        border: 2px solid #34495e !important;
        border-radius: 8px !important;
        padding: 5px !important;
        background: white !important;
    }
    
    /* تحسين عرض الأيقونات */
    .fas, .far, .fab {
        font-size: 12pt !important;
        margin-left: 5px !important;
    }
    
    /* تحسين عرض التواريخ والأرقام */
    .date-field,
    .number-field {
        font-family: 'Cairo', 'Courier New', monospace !important;
        font-weight: 600 !important;
        background: #f8f9fa !important;
        padding: 3px 6px !important;
        border-radius: 3px !important;
        border: 1px solid #dee2e6 !important;
    }
    
    /* تحسين عرض العملات */
    .currency {
        font-weight: 700 !important;
        color: #27ae60 !important;
    }
    
    .currency::after {
        content: " ج.م" !important;
        font-size: 9pt !important;
        color: #7f8c8d !important;
    }
    
    /* تحسين عرض النسب المئوية */
    .percentage {
        font-weight: 600 !important;
        color: #3498db !important;
    }
    
    .percentage::after {
        content: "%" !important;
        font-size: 9pt !important;
    }
    
    /* تحسين عرض الحالات */
    .status-active {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        color: white !important;
        padding: 3px 8px !important;
        border-radius: 12px !important;
        font-size: 9pt !important;
        font-weight: 500 !important;
    }
    
    .status-inactive {
        background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
        color: white !important;
        padding: 3px 8px !important;
        border-radius: 12px !important;
        font-size: 9pt !important;
        font-weight: 500 !important;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
        color: white !important;
        padding: 3px 8px !important;
        border-radius: 12px !important;
        font-size: 9pt !important;
        font-weight: 500 !important;
    }
    
    /* تحسين عرض الأولويات */
    .priority-high {
        background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 8px !important;
        font-size: 8pt !important;
        font-weight: 600 !important;
    }
    
    .priority-medium {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 8px !important;
        font-size: 8pt !important;
        font-weight: 600 !important;
    }
    
    .priority-low {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 8px !important;
        font-size: 8pt !important;
        font-weight: 600 !important;
    }
    
    /* تحسين عرض التقييمات */
    .rating-excellent {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        color: white !important;
        padding: 4px 10px !important;
        border-radius: 15px !important;
        font-size: 9pt !important;
        font-weight: 600 !important;
    }
    
    .rating-good {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
        color: white !important;
        padding: 4px 10px !important;
        border-radius: 15px !important;
        font-size: 9pt !important;
        font-weight: 600 !important;
    }
    
    .rating-average {
        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
        color: white !important;
        padding: 4px 10px !important;
        border-radius: 15px !important;
        font-size: 9pt !important;
        font-weight: 600 !important;
    }
    
    .rating-poor {
        background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
        color: white !important;
        padding: 4px 10px !important;
        border-radius: 15px !important;
        font-size: 9pt !important;
        font-weight: 600 !important;
    }
    
    /* تحسين عرض الجداول المعقدة */
    .complex-table {
        font-size: 8pt !important;
        line-height: 1.3 !important;
    }
    
    .complex-table th {
        padding: 8px 4px !important;
        font-size: 9pt !important;
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    .complex-table td {
        padding: 6px 4px !important;
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    /* تحسين عرض الملاحظات والتعليقات */
    .notes-section {
        border: 2px dashed #7f8c8d !important;
        border-radius: 8px !important;
        padding: 15px !important;
        margin-top: 20px !important;
        background: #f8f9fa !important;
    }
    
    .notes-title {
        font-size: 12pt !important;
        font-weight: 600 !important;
        color: #2c3e50 !important;
        margin-bottom: 10px !important;
    }
    
    .notes-content {
        font-size: 10pt !important;
        line-height: 1.4 !important;
        color: #34495e !important;
    }
    
    /* تحسين عرض التوقيعات */
    .signature-section {
        margin-top: 50px !important;
        border-top: 2px solid #34495e !important;
        padding-top: 30px !important;
    }
    
    .signature-box {
        border: 1px solid #bdc3c7 !important;
        border-radius: 5px !important;
        padding: 20px !important;
        text-align: center !important;
        background: #f8f9fa !important;
        margin-bottom: 20px !important;
    }
    
    .signature-label {
        font-size: 11pt !important;
        font-weight: 600 !important;
        color: #2c3e50 !important;
        margin-bottom: 30px !important;
    }
    
    .signature-line {
        border-bottom: 2px solid #34495e !important;
        width: 200px !important;
        margin: 0 auto 10px auto !important;
    }
    
    .signature-date {
        font-size: 9pt !important;
        color: #7f8c8d !important;
    }
    
    /* تحسين عرض الرؤوس والتذييلات */
    .print-header-enhanced {
        background: linear-gradient(135deg, #2c3e50, #34495e, #2c3e50) !important;
        color: white !important;
        padding: 25px !important;
        border-radius: 15px !important;
        margin-bottom: 40px !important;
        text-align: center !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    }
    
    .print-footer-enhanced {
        background: linear-gradient(135deg, #ecf0f1, #bdc3c7) !important;
        padding: 20px !important;
        border-radius: 10px !important;
        margin-top: 40px !important;
        text-align: center !important;
        border: 2px solid #95a5a6 !important;
    }
    
    /* تحسين عرض الإحصائيات */
    .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
        gap: 15px !important;
        margin: 20px 0 !important;
    }
    
    .stat-card-enhanced {
        border: 2px solid #3498db !important;
        border-radius: 10px !important;
        padding: 15px !important;
        text-align: center !important;
        background: linear-gradient(135deg, #ebf3fd, #d6eaf8) !important;
    }
    
    .stat-value {
        font-size: 18pt !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 5px !important;
    }
    
    .stat-label {
        font-size: 10pt !important;
        color: #34495e !important;
        font-weight: 500 !important;
    }
    
    /* تحسين عرض الجداول الزمنية */
    .timeline-table th {
        background: linear-gradient(135deg, #8e44ad, #9b59b6) !important;
        color: white !important;
    }
    
    .timeline-item {
        border-left: 3px solid #3498db !important;
        padding-left: 10px !important;
        margin-bottom: 10px !important;
    }
    
    /* تحسين عرض الرسوم البيانية البديلة */
    .chart-replacement {
        border: 3px dashed #34495e !important;
        border-radius: 10px !important;
        padding: 30px !important;
        text-align: center !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        margin: 20px 0 !important;
    }
    
    .chart-title {
        font-size: 14pt !important;
        font-weight: 600 !important;
        color: #2c3e50 !important;
        margin-bottom: 15px !important;
    }
    
    .chart-data {
        font-size: 12pt !important;
        color: #34495e !important;
        line-height: 1.6 !important;
    }
}

/* تحسينات للطباعة الملونة */
@media print and (color) {
    .color-enhanced {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        color: white !important;
        padding: 10px !important;
        border-radius: 8px !important;
    }
}

/* تحسينات للطباعة بالأبيض والأسود */
@media print and (monochrome) {
    .monochrome-friendly {
        background: #f8f9fa !important;
        color: #2c3e50 !important;
        border: 2px solid #34495e !important;
    }
}
