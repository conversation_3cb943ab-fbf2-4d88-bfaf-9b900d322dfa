# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON>, 2012-2016,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-21 12:57+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina) (http://www.transifex.com/django/django/"
"language/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentación administrativa"

msgid "Home"
msgstr "Inicio"

msgid "Documentation"
msgstr "Documentación"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Bookmarklets de documentación"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar bookmarklets, arrastre el enlace a su barra\n"
"de favoritos, o pulse con el botón derecho el enlace y añádalo a sus "
"favoritos.\n"
"Ahora puede seleccionar el bookmarklet desde cualquier página en el sitio."

msgid "Documentation for this page"
msgstr "Documentación de esta página"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Le lleva desde cualquier página a la documentación de la vista que la genera."

msgid "Tags"
msgstr "Etiquetas"

msgid "List of all the template tags and their functions."
msgstr "Lista de todas las etiquetas de plantilla y sus funciones."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Los filtros son acciones que pueden ser aplicadas a variables en una "
"plantilla para alterar el resultado final."

msgid "Models"
msgstr "Modelos"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Los modelos son descripciones de todos los objetos en el sistema y sus "
"campos asociados. Cada modelo contiene una lista de campos accesibles como "
"variables de plantilla"

msgid "Views"
msgstr "Vistas"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada página en el sitio público es generada por una vista. La vista define "
"qué plantilla es utilizada para generar la página y qué objetos están "
"disponibles para esta plantilla."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Herramientas para su navegador para acceder rápidamente a la funcionalidad "
"de administración."

msgid "Please install docutils"
msgstr "Por favor instale docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"El sistema de documentación de admin requiere la biblioteca Python <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor solicite a sus administradores de sistemas que instalen <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelo: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descripción"

msgid "Methods with arguments"
msgstr "Métodos con argumentos"

msgid "Method"
msgstr "Método"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Volver a documentación de modelos"

msgid "Model documentation"
msgstr "Documentación de modelos"

msgid "Model groups"
msgstr "Grupos de modelos"

msgid "Templates"
msgstr "Plantillas"

#, python-format
msgid "Template: %(name)s"
msgstr "Plantilla: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Plantilla: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Ruta de búsqueda para la plantilla <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(no existe)"

msgid "Back to Documentation"
msgstr "Volver a la documentación"

msgid "Template filters"
msgstr "Filtros de plantilla"

msgid "Template filter documentation"
msgstr "Documentación de filtros de plantilla"

msgid "Built-in filters"
msgstr "Filtros Django 'de fábrica'"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para usar estos filtros, escriba <code>%(code)s</code> en su plantilla antes "
"de usar el filtro."

msgid "Template tags"
msgstr "Etiquetas de plantilla"

msgid "Template tag documentation"
msgstr "Documentación de etiquetas de plantilla"

msgid "Built-in tags"
msgstr "Etiquetas Django 'de fábrica'"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para usar estas etiquetas, escriba <code>%(code)s</code> en su plantilla "
"antes de usar la etiqueta."

#, python-format
msgid "View: %(name)s"
msgstr "Vista: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Plantillas:"

msgid "Back to View documentation"
msgstr "Volver a documentación de vistas"

msgid "View documentation"
msgstr "Documentación de vistas"

msgid "Jump to namespace"
msgstr "Ir a espacio de nombres"

msgid "Empty namespace"
msgstr "Espacio de nombres vacío"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vistas por espacio de nombres %(name)s"

msgid "Views by empty namespace"
msgstr "Vistas por espacio de nombres vacío"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Función vista: <code>%(full_name)s</code>. Nombre: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiqueta:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "vista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplicación %(app_label)r no encontrada"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelo %(model_name)r no encontrado en aplicación %(app_label)r"

msgid "model:"
msgstr "modelo:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "el objeto `%(app_label)s.%(data_type)s` relacionado"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objetos `%(app_label)s.%(object_name)s` relacionados"

#, python-format
msgid "all %s"
msgstr "todos los %s"

#, python-format
msgid "number of %s"
msgstr "número de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s no parece ser un objeto urlpattern"
