/**
 * UI Enhancements Final
 * تحسينات واجهة المستخدم النهائية
 */

class UIEnhancementsFinal {
    constructor() {
        this.sessionStartTime = Date.now();
        this.loadingSteps = [
            'التحقق من البيانات',
            'حساب التكاليف',
            'التحقق من المخزون',
            'إنشاء الأمر'
        ];
        this.currentLoadingStep = 0;
        this.init();
    }

    init() {
        this.setupUserPanel();
        this.setupLoadingSystem();
        this.setupSuccessModal();
        this.setupSessionTimer();
        this.setupAdvancedInteractions();
        this.setupStatusBar();
        this.setupShortcutsPanel();
        this.setupConnectionMonitor();
    }

    setupUserPanel() {
        // تحديث وقت الجلسة كل دقيقة
        setInterval(() => {
            this.updateSessionTime();
        }, 60000);

        // إضافة وظائف لوحة المستخدم
        window.toggleUserPanel = () => {
            const panel = $('#userInfoPanel');
            if (panel.is(':visible')) {
                panel.fadeOut(300);
            } else {
                panel.fadeIn(300);
            }
        };

        // إظهار لوحة المستخدم بعد التحميل
        setTimeout(() => {
            $('#userInfoPanel').fadeIn(500);
        }, 1000);
    }

    updateSessionTime() {
        const elapsed = Math.floor((Date.now() - this.sessionStartTime) / 60000);
        $('#sessionTime').text(`${elapsed} دقيقة`);
    }

    setupLoadingSystem() {
        window.showLoadingOverlay = (title = 'جاري المعالجة...', subtitle = 'يرجى الانتظار، لا تغلق النافذة') => {
            $('#loadingOverlay .loading-title').text(title);
            $('#loadingOverlay .loading-subtitle').text(subtitle);
            $('#loadingOverlay').fadeIn(300);
            
            // إعادة تعيين خطوات التحميل
            this.currentLoadingStep = 0;
            this.updateLoadingSteps();
            
            // بدء شريط التقدم
            this.startLoadingProgress();
        };

        window.hideLoadingOverlay = () => {
            $('#loadingOverlay').fadeOut(300);
        };

        window.updateLoadingStep = (stepIndex) => {
            this.currentLoadingStep = stepIndex;
            this.updateLoadingSteps();
        };
    }

    updateLoadingSteps() {
        const steps = $('#loadingSteps .step');
        steps.each((index, step) => {
            const $step = $(step);
            if (index < this.currentLoadingStep) {
                $step.removeClass('active').addClass('completed');
            } else if (index === this.currentLoadingStep) {
                $step.removeClass('completed').addClass('active');
            } else {
                $step.removeClass('active completed');
            }
        });
    }

    startLoadingProgress() {
        let progress = 0;
        const progressBar = $('#loadingProgressBar');
        
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            progressBar.css('width', progress + '%');
        }, 200);

        // تحديث الخطوات تلقائياً
        setTimeout(() => this.updateLoadingStep(1), 1000);
        setTimeout(() => this.updateLoadingStep(2), 2000);
        setTimeout(() => this.updateLoadingStep(3), 3000);
    }

    setupSuccessModal() {
        window.showSuccessModal = (orderNumber, message = 'تم حفظ أمر الإنتاج بنجاح!') => {
            $('#savedOrderNumber').text(orderNumber);
            $('#saveTimestamp').text(new Date().toLocaleString('ar-EG'));
            
            // إخفاء شاشة التحميل أولاً
            hideLoadingOverlay();
            
            // إظهار نافذة النجاح
            setTimeout(() => {
                $('#saveConfirmModal').modal('show');
                
                // تشغيل الصوت (اختياري)
                this.playSuccessSound();
                
                // إشعار النجاح
                if (typeof notificationSystem !== 'undefined') {
                    notificationSystem.success(message, 'تم بنجاح');
                }
            }, 500);
        };
    }

    playSuccessSound() {
        // إنشاء صوت نجاح بسيط
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            // تجاهل الأخطاء إذا لم يكن الصوت مدعوماً
        }
    }

    setupSessionTimer() {
        // تحذير من انتهاء الجلسة
        setTimeout(() => {
            if (typeof notificationSystem !== 'undefined') {
                notificationSystem.warning(
                    'ستنتهي جلستك خلال 5 دقائق. يرجى حفظ عملك.',
                    'تحذير الجلسة',
                    10000
                );
            }
        }, 25 * 60 * 1000); // 25 دقيقة

        // تحديث آخر نشاط
        $(document).on('click keypress scroll', () => {
            this.updateLastActivity();
        });
    }

    updateLastActivity() {
        localStorage.setItem('lastActivity', Date.now().toString());
    }

    setupAdvancedInteractions() {
        // تحسين تفاعل الأزرار
        $(document).on('mousedown', '.btn', function() {
            $(this).addClass('btn-pressed');
        });

        $(document).on('mouseup mouseleave', '.btn', function() {
            $(this).removeClass('btn-pressed');
        });

        // تحسين تفاعل الحقول
        $(document).on('focus', '.form-control, .form-select', function() {
            $(this).closest('.form-floating, .input-group').addClass('field-focused');
        });

        $(document).on('blur', '.form-control, .form-select', function() {
            $(this).closest('.form-floating, .input-group').removeClass('field-focused');
        });

        // تحسين تفاعل الجداول
        $(document).on('mouseenter', '.table-row', function() {
            $(this).addClass('row-hovered');
        });

        $(document).on('mouseleave', '.table-row', function() {
            $(this).removeClass('row-hovered');
        });

        // إضافة تأثيرات الضغط للعناصر التفاعلية
        $(document).on('click', '.clickable', function() {
            $(this).addClass('element-clicked');
            setTimeout(() => {
                $(this).removeClass('element-clicked');
            }, 150);
        });

        // تحسين التمرير السلس
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    }

    // وظائف مساعدة للتفاعل مع النموذج
    highlightField(fieldId, duration = 3000) {
        const field = $(`#${fieldId}`);
        field.addClass('field-highlighted');
        setTimeout(() => {
            field.removeClass('field-highlighted');
        }, duration);
    }

    showFieldError(fieldId, message) {
        const field = $(`#${fieldId}`);
        const errorDiv = $(`<div class="field-error-message">${message}</div>`);
        
        field.closest('.form-floating, .input-group').append(errorDiv);
        field.addClass('is-invalid');
        
        setTimeout(() => {
            errorDiv.fadeOut(300, function() {
                $(this).remove();
            });
            field.removeClass('is-invalid');
        }, 5000);
    }

    animateValue(elementId, startValue, endValue, duration = 1000) {
        const element = $(`#${elementId}`);
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = startValue + (endValue - startValue) * progress;
            element.text(currentValue.toFixed(2));
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    showTooltip(elementId, message, position = 'top') {
        const element = $(`#${elementId}`);
        const tooltip = $(`
            <div class="custom-tooltip ${position}">
                ${message}
                <div class="tooltip-arrow"></div>
            </div>
        `);
        
        $('body').append(tooltip);
        
        const elementOffset = element.offset();
        const elementWidth = element.outerWidth();
        const elementHeight = element.outerHeight();
        const tooltipWidth = tooltip.outerWidth();
        const tooltipHeight = tooltip.outerHeight();
        
        let top, left;
        
        switch (position) {
            case 'top':
                top = elementOffset.top - tooltipHeight - 10;
                left = elementOffset.left + (elementWidth / 2) - (tooltipWidth / 2);
                break;
            case 'bottom':
                top = elementOffset.top + elementHeight + 10;
                left = elementOffset.left + (elementWidth / 2) - (tooltipWidth / 2);
                break;
            case 'left':
                top = elementOffset.top + (elementHeight / 2) - (tooltipHeight / 2);
                left = elementOffset.left - tooltipWidth - 10;
                break;
            case 'right':
                top = elementOffset.top + (elementHeight / 2) - (tooltipHeight / 2);
                left = elementOffset.left + elementWidth + 10;
                break;
        }
        
        tooltip.css({ top, left }).fadeIn(300);
        
        setTimeout(() => {
            tooltip.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // تحسين الأداء
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    setupStatusBar() {
        // تحديث الوقت كل ثانية
        setInterval(() => {
            const now = new Date();
            $('#currentTime').text(now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
            }));
        }, 1000);

        // تحديث إحصائيات شريط الحالة
        this.updateStatusBarStats();

        // تحديث دوري للإحصائيات
        setInterval(() => {
            this.updateStatusBarStats();
        }, 5000);
    }

    updateStatusBarStats() {
        // عدد المواد
        const materialCount = $('.material-row').length;
        $('#materialCount').text(`${materialCount} مواد`);

        // إجمالي التكلفة
        const totalCost = $('#totalProductionCost').text() || '0.00 ج.م';
        $('#totalCostStatus').text(totalCost);

        // حالة الحفظ
        const lastSave = localStorage.getItem('lastAutoSave');
        if (lastSave) {
            const saveTime = new Date(parseInt(lastSave));
            const now = new Date();
            const diffMinutes = Math.floor((now - saveTime) / 60000);

            if (diffMinutes < 1) {
                $('#saveStatus').html('<i class="fas fa-check text-success"></i> محفوظ الآن');
            } else {
                $('#saveStatus').html(`<i class="fas fa-clock text-warning"></i> آخر حفظ: ${diffMinutes} د`);
            }
        }
    }

    setupShortcutsPanel() {
        // إضافة اختصار Ctrl+K لإظهار لوحة الاختصارات
        $(document).on('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.toggleShortcutsPanel();
            }
        });

        // وظيفة إظهار/إخفاء لوحة الاختصارات
        window.toggleShortcutsPanel = () => {
            const panel = $('#shortcutsPanel');
            if (panel.is(':visible')) {
                panel.fadeOut(300);
            } else {
                panel.fadeIn(300);

                // إخفاء تلقائي بعد 10 ثوان
                setTimeout(() => {
                    if (panel.is(':visible')) {
                        panel.fadeOut(300);
                    }
                }, 10000);
            }
        };

        // إظهار لوحة الاختصارات عند التحميل لأول مرة
        setTimeout(() => {
            if (!localStorage.getItem('shortcutsPanelShown')) {
                $('#shortcutsPanel').fadeIn(500);
                localStorage.setItem('shortcutsPanelShown', 'true');

                // إخفاء بعد 5 ثوان
                setTimeout(() => {
                    $('#shortcutsPanel').fadeOut(300);
                }, 5000);
            }
        }, 3000);
    }

    setupConnectionMonitor() {
        let isOnline = navigator.onLine;
        this.updateConnectionStatus(isOnline);

        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            this.updateConnectionStatus(true);
            if (typeof notificationSystem !== 'undefined') {
                notificationSystem.success('تم استعادة الاتصال بالإنترنت', 'متصل');
            }
        });

        window.addEventListener('offline', () => {
            this.updateConnectionStatus(false);
            if (typeof notificationSystem !== 'undefined') {
                notificationSystem.warning('انقطع الاتصال بالإنترنت', 'غير متصل');
            }
        });

        // فحص دوري للاتصال
        setInterval(() => {
            this.checkConnection();
        }, 30000);
    }

    updateConnectionStatus(isOnline) {
        const indicator = $('#connectionIndicator');
        const dot = indicator.find('.connection-dot');
        const text = indicator.find('.connection-text');

        if (isOnline) {
            dot.removeClass('offline connecting').addClass('online');
            text.text('متصل');
        } else {
            dot.removeClass('online connecting').addClass('offline');
            text.text('غير متصل');
        }
    }

    checkConnection() {
        const indicator = $('#connectionIndicator');
        const dot = indicator.find('.connection-dot');
        const text = indicator.find('.connection-text');

        // إظهار حالة الفحص
        dot.removeClass('online offline').addClass('connecting');
        text.text('جاري الفحص...');

        // محاولة ping بسيط
        fetch('/ping', { method: 'HEAD', cache: 'no-cache' })
            .then(() => {
                this.updateConnectionStatus(true);
            })
            .catch(() => {
                this.updateConnectionStatus(false);
            });
    }

    // وظائف متقدمة للتفاعل
    addKeyboardShortcut(key, callback, description) {
        $(document).on('keydown', (e) => {
            if (this.matchesShortcut(e, key)) {
                e.preventDefault();
                callback();

                if (typeof notificationSystem !== 'undefined') {
                    notificationSystem.info(`تم تنفيذ: ${description}`, 'اختصار', 2000);
                }
            }
        });
    }

    matchesShortcut(event, shortcut) {
        const keys = shortcut.toLowerCase().split('+');
        let matches = true;

        if (keys.includes('ctrl') && !event.ctrlKey) matches = false;
        if (keys.includes('alt') && !event.altKey) matches = false;
        if (keys.includes('shift') && !event.shiftKey) matches = false;

        const mainKey = keys[keys.length - 1];
        if (event.key.toLowerCase() !== mainKey) matches = false;

        return matches;
    }

    showQuickStats() {
        const stats = {
            materials: $('.material-row').length,
            totalCost: $('#totalProductionCost').text(),
            progress: $('#formProgress').css('width'),
            lastSave: localStorage.getItem('lastAutoSave')
        };

        const statsHtml = `
            <div class="quick-stats-popup">
                <h6><i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة</h6>
                <div class="stats-grid">
                    <div class="stat-item">
                        <i class="fas fa-cubes text-primary"></i>
                        <span>${stats.materials} مواد خام</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-money-bill text-success"></i>
                        <span>${stats.totalCost}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-tasks text-info"></i>
                        <span>التقدم: ${stats.progress}</span>
                    </div>
                </div>
            </div>
        `;

        // إظهار النافذة المنبثقة
        const popup = $(statsHtml);
        $('body').append(popup);

        popup.css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'white',
            padding: '1.5rem',
            borderRadius: '15px',
            boxShadow: '0 20px 60px rgba(0,0,0,0.3)',
            zIndex: 10000
        }).fadeIn(300);

        // إخفاء بعد 3 ثوان
        setTimeout(() => {
            popup.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
}

// إنشاء نسخة عامة من النظام
let uiEnhancementsFinal;

$(document).ready(function() {
    uiEnhancementsFinal = new UIEnhancementsFinal();
    
    // إضافة أنماط CSS للتحسينات
    const enhancementStyles = `
        <style id="uiEnhancementStyles">
            .btn-pressed {
                transform: scale(0.95);
                transition: transform 0.1s ease;
            }
            
            .field-focused {
                transform: scale(1.02);
                transition: transform 0.2s ease;
            }
            
            .row-hovered {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                transform: translateX(5px);
                transition: all 0.2s ease;
            }
            
            .element-clicked {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
            
            .field-highlighted {
                animation: fieldHighlight 3s ease;
            }
            
            @keyframes fieldHighlight {
                0%, 100% { background: transparent; }
                50% { background: rgba(255, 235, 59, 0.3); }
            }
            
            .field-error-message {
                color: #dc3545;
                font-size: 0.875rem;
                margin-top: 0.25rem;
                animation: slideDown 0.3s ease;
            }
            
            .custom-tooltip {
                position: absolute;
                background: #2c3e50;
                color: white;
                padding: 0.5rem 0.75rem;
                border-radius: 6px;
                font-size: 0.875rem;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
            
            .tooltip-arrow {
                position: absolute;
                width: 0;
                height: 0;
                border: 5px solid transparent;
            }
            
            .custom-tooltip.top .tooltip-arrow {
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                border-top-color: #2c3e50;
            }
            
            .custom-tooltip.bottom .tooltip-arrow {
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
                border-bottom-color: #2c3e50;
            }
        </style>
    `;
    
    $('#uiEnhancementStyles').remove();
    $('head').append(enhancementStyles);
});
