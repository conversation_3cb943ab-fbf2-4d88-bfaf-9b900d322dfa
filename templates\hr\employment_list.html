{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-plus text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة التعيينات الجديدة والمرشحين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmploymentModal">
                        <i class="fas fa-plus me-2"></i>
                        تعيين جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Employment Status Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ review_count }}</h3>
                    <p class="mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ hired_count }}</h3>
                    <p class="mb-0">تم التعيين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ probation_count }}</h3>
                    <p class="mb-0">فترة تجريبية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ rejected_count }}</h3>
                    <p class="mb-0">مرفوض</p>
                </div>
            </div>
        </div>
    </div>

    <!-- New Hires Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة التعيينات الجديدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>المنصب</th>
                                    <th>القسم</th>
                                    <th>تاريخ التعيين</th>
                                    <th>الراتب</th>
                                    <th>فترة التجربة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for hire in new_hires %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user-plus text-success me-2"></i>
                                        {{ hire.name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ hire.position }}</span>
                                    </td>
                                    <td>{{ hire.department }}</td>
                                    <td>{{ hire.start_date }}</td>
                                    <td><strong>{{ hire.salary|floatformat:0 }} ج.م</strong></td>
                                    <td>
                                        <span class="badge bg-warning">3 أشهر</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ hire.status }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-warning" title="تعديل" onclick="window.location.href='{% url 'hr:employee_edit' hire.id %}'">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="post" action="{% url 'hr:employee_delete' hire.id %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف التعيين؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-user-plus fa-3x mb-3 d-block"></i>
                                        لا توجد تعيينات جديدة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Hires -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        التعيينات الأخيرة (آخر 30 يوم)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for hire in new_hires|slice:":3" %}
                        <div class="col-md-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar bg-success text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ hire.name }}</h6>
                                    <small class="text-muted">{{ hire.position }} - {{ hire.department }}</small><br>
                                    <small class="text-muted">تاريخ التعيين: {{ hire.start_date }}</small>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12 text-center text-muted py-4">
                            <i class="fas fa-user-plus fa-3x mb-3 d-block"></i>
                            لا توجد تعيينات حديثة
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Employment Modal -->
<div class="modal fade" id="addEmploymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعيين موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'hr:employment_create' %}">
                    {% csrf_token %}
                    {% for field in form %}
                        <div class="mb-3 row">
                            <label class="col-md-3 col-form-label">{{ field.label_tag }}</label>
                            <div class="col-md-9">
                                {{ field }}
                                {% if field.help_text %}<small class="form-text text-muted">{{ field.help_text }}</small>{% endif %}
                                {% for error in field.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> حفظ التعيين
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
