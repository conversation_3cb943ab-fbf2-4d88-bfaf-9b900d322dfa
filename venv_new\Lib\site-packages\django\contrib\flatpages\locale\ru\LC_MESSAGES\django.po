# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2012,2014-2015,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-14 18:19+0000\n"
"Last-Translator: crazyzubr <<EMAIL>>\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Advanced options"
msgstr "Расширенные настройки"

msgid "Flat Pages"
msgstr "Простые страницы"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Пример: “/about/contact/”. Убедитесь, что ввели начальную и конечную косые "
"черты."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Значение должно состоять только из букв, цифр и символов точки, "
"подчеркивания, тире, косой черты и тильды."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Пример: “/about/contact”. Убедитесь, что в начале адреса находится косая "
"черта."

msgid "URL is missing a leading slash."
msgstr "В начале URL отсутствует косая черта"

msgid "URL is missing a trailing slash."
msgstr "В конце URL отсутствует косая черта"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Простая страница с адресом %(url)s уже существует для сайта %(site)s"

msgid "title"
msgstr "заголовок"

msgid "content"
msgstr "содержимое"

msgid "enable comments"
msgstr "включить комментарии"

msgid "template name"
msgstr "имя шаблона"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Пример: “flatpages/contact_page.html“. Если не указано, система будет "
"использовать “flatpages/default.html“."

msgid "registration required"
msgstr "требуется регистрация"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Если отмечено, только вошедшие пользователи смогут видеть страницу."

msgid "sites"
msgstr "сайты"

msgid "flat page"
msgstr "простая страница"

msgid "flat pages"
msgstr "простые страницы"
