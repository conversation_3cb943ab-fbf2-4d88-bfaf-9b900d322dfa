#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit, Currency, Warehouse

def check_data():
    print("=== فحص البيانات الموجودة ===")
    
    # فحص الأصناف
    items = Item.objects.filter(is_active=True)
    print(f"عدد الأصناف النشطة: {items.count()}")
    
    if items.exists():
        print("\nالأصناف الموجودة:")
        for item in items[:10]:  # أول 10 أصناف
            print(f"- {item.code}: {item.name}")
        
        if items.count() > 10:
            print(f"... و {items.count() - 10} صنف آخر")
    
    # فحص الفئات
    categories = ItemCategory.objects.filter(is_active=True)
    print(f"\nعدد فئات الأصناف: {categories.count()}")
    
    # فحص الوحدات
    units = Unit.objects.filter(is_active=True)
    print(f"عدد وحدات القياس: {units.count()}")
    
    # فحص العملات
    currencies = Currency.objects.filter(is_active=True)
    print(f"عدد العملات: {currencies.count()}")
    
    # فحص المخازن
    warehouses = Warehouse.objects.filter(is_active=True)
    print(f"عدد المخازن: {warehouses.count()}")

def delete_test_data():
    print("\n=== حذف البيانات التجريبية ===")
    
    # حذف الأصناف التجريبية (يمكنك تعديل المعايير حسب الحاجة)
    test_items = Item.objects.filter(
        is_active=True,
        name__icontains='test'  # أو أي معيار آخر لتحديد البيانات التجريبية
    )
    
    if test_items.exists():
        print(f"سيتم حذف {test_items.count()} صنف تجريبي")
        for item in test_items:
            print(f"- حذف: {item.code}: {item.name}")
            item.is_active = False
            item.save()
        print("تم حذف البيانات التجريبية")
    else:
        print("لم يتم العثور على بيانات تجريبية للحذف")

if __name__ == "__main__":
    check_data()
    
    # إذا كنت تريد حذف البيانات التجريبية، قم بإلغاء التعليق عن السطر التالي
    # delete_test_data()