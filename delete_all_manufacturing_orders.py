#!/usr/bin/env python
"""
سكريبت لحذف جميع أوامر التصنيع من قاعدة البيانات
يجب تشغيل هذا السكريبت بحذر شديد لأنه سيحذف جميع البيانات المرتبطة بأوامر التصنيع
"""

import os
import sys
import django
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import transaction
from inventory.models import ManufacturingOrder, ManufacturingMaterial, ManufacturingStep
from django.contrib.auth.models import User

def delete_all_manufacturing_orders():
    """حذف جميع أوامر التصنيع والبيانات المرتبطة بها"""
    
    print("=" * 60)
    print("🚨 تحذير: هذا السكريبت سيحذف جميع أوامر التصنيع")
    print("=" * 60)
    
    # إحصائيات قبل الحذف
    total_orders = ManufacturingOrder.objects.count()
    total_materials = ManufacturingMaterial.objects.count()
    total_steps = ManufacturingStep.objects.count()
    
    print(f"📊 الإحصائيات الحالية:")
    print(f"   - عدد أوامر التصنيع: {total_orders}")
    print(f"   - عدد المواد الخام: {total_materials}")
    print(f"   - عدد خطوات التصنيع: {total_steps}")
    print()
    
    if total_orders == 0:
        print("✅ لا توجد أوامر تصنيع للحذف")
        return
    
    # طلب التأكيد من المستخدم
    confirm = input("هل أنت متأكد من حذف جميع أوامر التصنيع؟ (اكتب 'نعم' للتأكيد): ")
    
    if confirm.strip() != 'نعم':
        print("❌ تم إلغاء العملية")
        return
    
    # طلب تأكيد إضافي
    confirm2 = input("اكتب 'حذف' مرة أخرى للتأكيد النهائي: ")
    
    if confirm2.strip() != 'حذف':
        print("❌ تم إلغاء العملية")
        return
    
    try:
        with transaction.atomic():
            print("🔄 بدء عملية الحذف...")
            
            # حذف خطوات التصنيع أولاً
            deleted_steps = ManufacturingStep.objects.all().delete()
            print(f"✅ تم حذف {deleted_steps[0]} خطوة تصنيع")
            
            # حذف المواد الخام
            deleted_materials = ManufacturingMaterial.objects.all().delete()
            print(f"✅ تم حذف {deleted_materials[0]} مادة خام")
            
            # حذف أوامر التصنيع
            deleted_orders = ManufacturingOrder.objects.all().delete()
            print(f"✅ تم حذف {deleted_orders[0]} أمر تصنيع")
            
            print()
            print("🎉 تم حذف جميع أوامر التصنيع بنجاح!")
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء الحذف: {e}")
        print("تم التراجع عن جميع التغييرات")
        return

def show_manufacturing_orders_summary():
    """عرض ملخص أوامر التصنيع الحالية"""
    
    print("📋 ملخص أوامر التصنيع الحالية:")
    print("-" * 40)
    
    orders = ManufacturingOrder.objects.select_related('finished_product').all()
    
    if not orders.exists():
        print("لا توجد أوامر تصنيع")
        return
    
    for order in orders:
        print(f"🔸 أمر رقم: {order.order_number}")
        print(f"   المنتج: {order.finished_product.name}")
        print(f"   الحالة: {order.get_status_display()}")
        print(f"   الكمية: {order.quantity_to_produce}")
        print(f"   تاريخ الإنشاء: {order.created_at.strftime('%Y-%m-%d')}")
        print()

def main():
    """الدالة الرئيسية"""
    
    print("🔧 سكريبت إدارة أوامر التصنيع")
    print("=" * 40)
    
    while True:
        print("\nاختر العملية:")
        print("1. عرض ملخص أوامر التصنيع")
        print("2. حذف جميع أوامر التصنيع")
        print("3. خروج")
        
        choice = input("\nأدخل رقم العملية (1-3): ").strip()
        
        if choice == '1':
            show_manufacturing_orders_summary()
        elif choice == '2':
            delete_all_manufacturing_orders()
        elif choice == '3':
            print("👋 شكراً لاستخدام السكريبت")
            break
        else:
            print("❌ اختيار غير صحيح، حاول مرة أخرى")

if __name__ == '__main__':
    main() 