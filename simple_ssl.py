#!/usr/bin/env python3
"""
إنشاء شهادة SSL بسيطة
Simple SSL Certificate Creation
"""

import os
from pathlib import Path
from datetime import datetime, timedelta
import ipaddress

def create_ssl_certificate():
    """إنشاء شهادة SSL"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        
        print("🔒 إنشاء شهادة SSL...")
        
        # إنشاء مجلد SSL
        ssl_dir = Path('ssl')
        ssl_dir.mkdir(exist_ok=True)
        
        # إنشاء المفتاح الخاص
        print("🔑 إنشاء المفتاح الخاص...")
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # إنشاء الشهادة
        print("📄 إنشاء الشهادة...")
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "EG"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Cairo"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Cairo"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Osaric Accounting"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("127.0.0.1"),
                x509.DNSName("***************"),
                x509.DNSName("DESKTOP-H8H1ID4"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                x509.IPAddress(ipaddress.IPv4Address("***************")),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # حفظ المفتاح الخاص
        key_file = ssl_dir / 'server.key'
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # حفظ الشهادة
        cert_file = ssl_dir / 'server.crt'
        with open(cert_file, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        # إنشاء ملف PEM مدمج
        pem_file = ssl_dir / 'server.pem'
        with open(pem_file, 'w') as pem:
            with open(cert_file, 'r') as cert_f:
                pem.write(cert_f.read())
            with open(key_file, 'r') as key_f:
                pem.write(key_f.read())
        
        print("✅ تم إنشاء شهادة SSL بنجاح!")
        print(f"📄 الشهادة: {cert_file}")
        print(f"🔑 المفتاح: {key_file}")
        print(f"📋 ملف PEM: {pem_file}")
        
        return cert_file, key_file, pem_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشهادة: {e}")
        return None, None, None

if __name__ == "__main__":
    create_ssl_certificate()
