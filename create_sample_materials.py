#!/usr/bin/env python
"""
سكريبت إنشاء مواد خام تجريبية
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.models import Item, Stock, Warehouse
from definitions.models import Unit

def create_sample_materials():
    """إنشاء مواد خام تجريبية"""
    print("=== إنشاء مواد خام تجريبية ===")
    
    # الحصول على أول مخزن نشط
    warehouse = Warehouse.objects.filter(is_active=True).first()
    if not warehouse:
        print("لا يوجد مخزن نشط!")
        return
    
    print(f"المخزن المستخدم: {warehouse.name}")
    
    # إنشاء وحدة قياس
    unit, created = Unit.objects.get_or_create(
        name='قطعة',
        defaults={'code': 'PCS'}
    )
    
    if created:
        print(f"تم إنشاء وحدة قياس: {unit.name}")
    
    # قائمة المواد الخام التجريبية
    raw_materials = [
        {
            'name': 'خشب أبيض',
            'code': 'WOOD001',
            'cost_price': 50.0,
            'unit': unit
        },
        {
            'name': 'حديد صلب',
            'code': 'STEEL001',
            'cost_price': 25.0,
            'unit': unit
        },
        {
            'name': 'بلاستيك',
            'code': 'PLASTIC001',
            'cost_price': 15.0,
            'unit': unit
        },
        {
            'name': 'زجاج',
            'code': 'GLASS001',
            'cost_price': 30.0,
            'unit': unit
        },
        {
            'name': 'قماش',
            'code': 'FABRIC001',
            'cost_price': 20.0,
            'unit': unit
        },
        {
            'name': 'ألمنيوم',
            'code': 'ALUM001',
            'cost_price': 40.0,
            'unit': unit
        },
        {
            'name': 'نحاس',
            'code': 'COPPER001',
            'cost_price': 35.0,
            'unit': unit
        },
        {
            'name': 'مطاط',
            'code': 'RUBBER001',
            'cost_price': 12.0,
            'unit': unit
        }
    ]
    
    created_count = 0
    for material_data in raw_materials:
        item, created = Item.objects.get_or_create(
            code=material_data['code'],
            defaults={
                'name': material_data['name'],
                'item_type': 'RAW_MATERIAL',
                'cost_price': material_data['cost_price'],
                'unit': material_data['unit'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ تم إنشاء: {item.name} ({item.code})")
            created_count += 1
            
            # إنشاء مخزون للمادة
            stock, stock_created = Stock.objects.get_or_create(
                item=item,
                warehouse=warehouse,
                defaults={
                    'quantity': 100.0,
                    'average_cost': material_data['cost_price']
                }
            )
            
            if stock_created:
                print(f"  📦 تم إنشاء مخزون: {stock.quantity} في {warehouse.name}")
        else:
            print(f"⚠️ موجود مسبقاً: {item.name}")
    
    print(f"\n🎉 تم إنشاء {created_count} مادة خام جديدة")
    
    # عرض إجمالي المواد
    total_raw_materials = Item.objects.filter(item_type='RAW_MATERIAL', is_active=True).count()
    total_stocks = Stock.objects.filter(item__item_type='RAW_MATERIAL', item__is_active=True, quantity__gt=0).count()
    
    print(f"\n📊 الإحصائيات النهائية:")
    print(f"  إجمالي المواد الخام: {total_raw_materials}")
    print(f"  المواد الخام بالمخزون: {total_stocks}")

if __name__ == '__main__':
    create_sample_materials() 