<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>{{ title|default:"إضافة مادة خام" }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body class="container mt-4">
    
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary">
                <i class="bi bi-plus-circle"></i>
                {{ title|default:"إضافة مادة خام" }}
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_dashboard' %}">التصنيع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'inventory:raw_material_list' %}">المواد الخام</a></li>
                    <li class="breadcrumb-item active">{{ title|default:"إضافة مادة خام" }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'inventory:raw_material_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- الرسائل -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- النموذج -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-info-circle"></i>
                معلومات المادة الخام
            </h5>
        </div>
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
                
                <div class="row g-3">
                    <!-- اسم المادة -->
                    <div class="col-md-6">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            {{ form.name.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الوحدة -->
                    <div class="col-md-6">
                        <label for="{{ form.unit.id_for_label }}" class="form-label">
                            {{ form.unit.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.unit }}
                        {% if form.unit.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.unit.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الكمية المتاحة -->
                    <div class="col-md-4">
                        <label for="{{ form.quantity_available.id_for_label }}" class="form-label">
                            {{ form.quantity_available.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.quantity_available }}
                        {% if form.quantity_available.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.quantity_available.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- تكلفة الوحدة -->
                    <div class="col-md-4">
                        <label for="{{ form.unit_cost.id_for_label }}" class="form-label">
                            {{ form.unit_cost.label }}
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            {{ form.unit_cost }}
                            <span class="input-group-text">ج.م</span>
                        </div>
                        {% if form.unit_cost.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.unit_cost.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الحد الأدنى للمخزون -->
                    <div class="col-md-4">
                        <label for="{{ form.minimum_stock.id_for_label }}" class="form-label">
                            {{ form.minimum_stock.label }}
                        </label>
                        {{ form.minimum_stock }}
                        {% if form.minimum_stock.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.minimum_stock.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">سيتم التنبيه عند انخفاض المخزون عن هذا الحد</div>
                    </div>

                    <!-- المخزن -->
                    <div class="col-md-6">
                        <label for="{{ form.warehouse.id_for_label }}" class="form-label">
                            {{ form.warehouse.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.warehouse }}
                        {% if form.warehouse.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.warehouse.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- المورد -->
                    <div class="col-md-6">
                        <label for="{{ form.supplier.id_for_label }}" class="form-label">
                            {{ form.supplier.label }}
                        </label>
                        {{ form.supplier }}
                        {% if form.supplier.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.supplier.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الملاحظات -->
                    <div class="col-12">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i>
                            حفظ المادة الخام
                        </button>
                        <a href="{% url 'inventory:raw_material_list' %}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                        {% if material %}
                            <button type="button" class="btn btn-outline-info" onclick="calculateTotalValue()">
                                <i class="bi bi-calculator"></i>
                                حساب القيمة الإجمالية
                            </button>
                        {% endif %}
                    </div>
                </div>
            </form>

            <!-- عرض القيمة الإجمالية -->
            {% if material %}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6>معلومات إضافية:</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>القيمة الإجمالية:</strong>
                                    <span id="total-value" class="text-success">
                                        {{ material.quantity_available|floatformat:3 }} × {{ material.unit_cost|floatformat:2 }} = 
                                        <strong>{{ material.quantity_available|mul:material.unit_cost|floatformat:2 }} ج.م</strong>
                                    </span>
                                </div>
                                <div class="col-md-4">
                                    <strong>تاريخ الإضافة:</strong>
                                    {{ material.created_at }}
                                </div>
                                <div class="col-md-4">
                                    <strong>آخر تحديث:</strong>
                                    {{ material.updated_at }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- JavaScript لحساب القيمة الإجمالية -->
    <script>
        function calculateTotalValue() {
            const quantity = parseFloat(document.getElementById('id_quantity_available').value) || 0;
            const unitCost = parseFloat(document.getElementById('id_unit_cost').value) || 0;
            const total = quantity * unitCost;
            
            const totalValueElement = document.getElementById('total-value');
            if (totalValueElement) {
                totalValueElement.innerHTML = `
                    ${quantity.toFixed(3)} × ${unitCost.toFixed(2)} = 
                    <strong>${total.toFixed(2)} ج.م</strong>
                `;
            }
        }

        // حساب تلقائي عند تغيير القيم
        document.addEventListener('DOMContentLoaded', function() {
            const quantityInput = document.getElementById('id_quantity_available');
            const costInput = document.getElementById('id_unit_cost');
            
            if (quantityInput && costInput) {
                quantityInput.addEventListener('input', calculateTotalValue);
                costInput.addEventListener('input', calculateTotalValue);
            }
        });

        // التحقق من صحة النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
