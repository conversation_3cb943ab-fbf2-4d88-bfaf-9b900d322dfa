{% extends 'base/base.html' %}

{% block title %}{{ title }} - حسابات أوساريك{% endblock %}

{% block content %}
{% csrf_token %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">
            <i class="fas fa-tags me-2"></i>
            {{ title }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-6 text-end">
        <a href="{% url 'definitions:category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة فئة جديدة
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="البحث في فئات الأصناف (الكود، الاسم)">
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{% url 'definitions:category_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Categories Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة فئات الأصناف
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>كود الفئة</th>
                            <th>اسم الفئة</th>
                            <th>الفئة الأب</th>
                            <th>الوصف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in page_obj %}
                        <tr>
                            <td>
                                <strong>{{ category.code }}</strong>
                            </td>
                            <td>{{ category.name }}</td>
                            <td>
                                {% if category.parent %}
                                    <span class="badge bg-info">{{ category.parent.name }}</span>
                                {% else %}
                                    <span class="text-muted">فئة رئيسية</span>
                                {% endif %}
                            </td>
                            <td>{{ category.description|default:"-"|truncatechars:50 }}</td>
                            <td>{{ category.created_at|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'definitions:category_edit' category.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ category.pk }}"
                                            data-name="{{ category.name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فئات</h5>
                <p class="text-muted">لم يتم العثور على أي فئات مطابقة لمعايير البحث</p>
                <a href="{% url 'definitions:category_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة فئة جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.id;
            const categoryName = this.dataset.name;
            
            // حذف مباشر بدون تأكيد
            fetch(`/definitions/categories/${categoryId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة الصف من الجدول
                    this.closest('tr').remove();
                    
                    // عرض رسالة نجاح
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    
                    // إدراج الرسالة في أعلى الصفحة
                    const container = document.querySelector('.container-fluid');
                    container.insertBefore(alertDiv, container.firstChild);
                    
                    // إخفاء الرسالة بعد 3 ثوان
                    setTimeout(() => {
                        alertDiv.remove();
                    }, 3000);
                } else {
                    // عرض رسالة خطأ
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${data.message || 'حدث خطأ أثناء الحذف'}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    
                    const container = document.querySelector('.container-fluid');
                    container.insertBefore(alertDiv, container.firstChild);
                    
                    setTimeout(() => {
                        alertDiv.remove();
                    }, 5000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle me-2"></i>
                    حدث خطأ في الاتصال
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                const container = document.querySelector('.container-fluid');
                container.insertBefore(alertDiv, container.firstChild);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            });
        });
    });
});
</script>
{% endblock %}
