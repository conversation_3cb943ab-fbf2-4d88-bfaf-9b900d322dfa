{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice-dollar text-warning me-2"></i>
                    {{ title }}
                </h2>
                <div>
                    <a href="{% url 'treasury:home' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للخزينة
                    </a>
                    <a href="{% url 'treasury:payment_note_create' %}" class="btn btn-warning">
                        <i class="fas fa-plus me-1"></i>
                        إضافة ورقة دفع
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       name="search" 
                                       value="{{ search_query }}"
                                       placeholder="البحث في أوراق الدفع (رقم الورقة، المورد، البيان)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="{% url 'treasury:payment_note_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة أوراق الدفع -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة أوراق الدفع
                        {% if page_obj %}
                            <span class="badge bg-warning ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الورقة</th>
                                        <th>التاريخ</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الخزينة</th>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>البيان</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for note in page_obj %}
                                        <tr>
                                            <td><strong>{{ note.note_number }}</strong></td>
                                            <td>{{ note.date|date:"d/m/Y" }}</td>
                                            <td>
                                                {{ note.due_date|date:"d/m/Y" }}
                                                {% if note.status == 'PENDING' %}
                                                    {% now "Y-m-d" as today %}
                                                    {% if note.due_date|date:"Y-m-d" < today %}
                                                        <br><small class="text-danger">متأخر</small>
                                                    {% elif note.due_date|date:"Y-m-d" == today %}
                                                        <br><small class="text-warning">مستحق اليوم</small>
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                            <td>{{ note.treasury.name }}</td>
                                            <td>{{ note.supplier.name }}</td>
                                            <td>
                                                <span class="text-warning fw-bold">
                                                    {{ note.amount|floatformat:2 }} ج.م
                                                </span>
                                            </td>
                                            <td>{{ note.description|truncatechars:40 }}</td>
                                            <td>
                                                {% if note.status == 'PENDING' %}
                                                    <span class="badge bg-warning">{{ note.get_status_display }}</span>
                                                {% elif note.status == 'PAID' %}
                                                    <span class="badge bg-success">{{ note.get_status_display }}</span>
                                                    {% if note.payment_date %}
                                                        <br><small class="text-muted">{{ note.payment_date|date:"d/m/Y" }}</small>
                                                    {% endif %}
                                                {% elif note.status == 'CANCELLED' %}
                                                    <span class="badge bg-danger">{{ note.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    {% if note.status == 'PENDING' %}
                                                        <a href="{% url 'treasury:payment_note_edit' note.pk %}" 
                                                           class="btn btn-outline-primary" 
                                                           title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-outline-success pay-btn" 
                                                                data-id="{{ note.pk }}"
                                                                data-name="{{ note.note_number }}"
                                                                title="دفع">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </button>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger delete-btn" 
                                                                data-id="{{ note.pk }}"
                                                                data-name="{{ note.note_number }}"
                                                                title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type="button" 
                                                                class="btn btn-outline-info print-btn" 
                                                                data-id="{{ note.pk }}"
                                                                title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أوراق دفع</h5>
                            <p class="text-muted">لم يتم العثور على أي أوراق دفع مطابقة لمعايير البحث</p>
                            <a href="{% url 'treasury:payment_note_create' %}" class="btn btn-warning">
                                <i class="fas fa-plus me-2"></i>
                                إضافة ورقة دفع جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دفع ورقة دفع
document.addEventListener('DOMContentLoaded', function() {
    const payButtons = document.querySelectorAll('.pay-btn');
    payButtons.forEach(button => {
        button.addEventListener('click', function() {
            const noteId = this.getAttribute('data-id');
            const noteNumber = this.getAttribute('data-name');
            
            if (confirm(`هل تريد دفع ورقة الدفع "${noteNumber}"؟`)) {
                fetch(`/treasury/payment-notes/${noteId}/pay/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الدفع');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الدفع');
                });
            }
        });
    });

    // حذف ورقة دفع
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const noteId = this.getAttribute('data-id');
            const noteNumber = this.getAttribute('data-name');
            
            if (confirm(`هل أنت متأكد من حذف ورقة الدفع "${noteNumber}"؟`)) {
                fetch(`/treasury/payment-notes/${noteId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        });
    });

    // طباعة ورقة دفع
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const noteId = this.getAttribute('data-id');
            // يمكن إضافة رابط الطباعة هنا
            alert('سيتم إضافة وظيفة الطباعة قريباً');
        });
    });
});
</script>
{% endblock %}
