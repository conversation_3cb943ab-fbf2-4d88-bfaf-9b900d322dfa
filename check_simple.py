#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import ItemCategory, Unit

def check_data():
    print("Categories:")
    categories = ItemCategory.objects.filter(is_active=True)
    for cat in categories:
        print(f"ID: {cat.pk}, Code: {cat.code}")
    
    print("\nUnits:")
    units = Unit.objects.filter(is_active=True)
    for unit in units:
        print(f"ID: {unit.pk}, Code: {unit.code}")

if __name__ == "__main__":
    check_data()