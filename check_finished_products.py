#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemType

def check_finished_products():
    print("=== Checking Finished Products ===")
    
    # Check all item types
    all_items = Item.objects.filter(is_active=True)
    print(f"Total active items: {all_items.count()}")
    
    # Group by item type
    for item_type in ItemType.choices:
        count = all_items.filter(item_type=item_type[0]).count()
        print(f"{item_type[1]}: {count} items")
    
    # Show finished products specifically
    finished_products = Item.objects.filter(
        item_type=ItemType.FINISHED_PRODUCT,
        is_active=True
    )
    
    print(f"\n=== Finished Products ({finished_products.count()}) ===")
    for product in finished_products:
        print(f"- {product.code}: {product.name}")
    
    if finished_products.count() == 0:
        print("No finished products found! Creating some...")
        create_sample_finished_products()

def create_sample_finished_products():
    from django.contrib.auth.models import User
    from definitions.models import ItemCategory, Unit
    from decimal import Decimal
    
    # Get required objects
    admin_user = User.objects.get(username='admin')
    category = ItemCategory.objects.filter(is_active=True).first()
    unit = Unit.objects.filter(is_active=True).first()
    
    # Sample finished products
    finished_products_data = [
        {
            'code': 'FP001',
            'name': 'Finished Product 1',
            'cost_price': '50.00',
            'selling_price': '75.00'
        },
        {
            'code': 'FP002', 
            'name': 'Finished Product 2',
            'cost_price': '80.00',
            'selling_price': '120.00'
        },
        {
            'code': 'FP003',
            'name': 'Finished Product 3', 
            'cost_price': '100.00',
            'selling_price': '150.00'
        }
    ]
    
    created_count = 0
    for fp_data in finished_products_data:
        try:
            item, created = Item.objects.get_or_create(
                code=fp_data['code'],
                defaults={
                    'name': fp_data['name'],
                    'item_type': ItemType.FINISHED_PRODUCT,
                    'category': category,
                    'unit': unit,
                    'cost_price': Decimal(fp_data['cost_price']),
                    'selling_price': Decimal(fp_data['selling_price']),
                    'is_active': True,
                    'created_by': admin_user
                }
            )
            if created:
                print(f"Created finished product: {item.code} - {item.name}")
                created_count += 1
            else:
                # Update existing item to be finished product
                item.item_type = ItemType.FINISHED_PRODUCT
                item.is_active = True
                item.save()
                print(f"Updated existing item to finished product: {item.code} - {item.name}")
                created_count += 1
        except Exception as e:
            print(f"Error creating {fp_data['code']}: {e}")
    
    print(f"\nCreated/Updated {created_count} finished products")

if __name__ == "__main__":
    check_finished_products()