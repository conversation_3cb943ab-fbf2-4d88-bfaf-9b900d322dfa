{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'hr:shift_edit' shift.pk %}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{% url 'hr:shift_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> عودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h5 class="mb-3">المعلومات الأساسية</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">كود الوردية:</th>
                                    <td><strong>{{ shift.code }}</strong></td>
                                </tr>
                                <tr>
                                    <th>اسم الوردية:</th>
                                    <td><strong>{{ shift.name }}</strong></td>
                                </tr>
                                <tr>
                                    <th>نوع الوردية:</th>
                                    <td>
                                        {% if shift.shift_type == 'MORNING' %}
                                            <span class="badge badge-info">صباحية</span>
                                        {% elif shift.shift_type == 'EVENING' %}
                                            <span class="badge badge-warning">مسائية</span>
                                        {% elif shift.shift_type == 'NIGHT' %}
                                            <span class="badge badge-dark">ليلية</span>
                                        {% elif shift.shift_type == 'FLEXIBLE' %}
                                            <span class="badge badge-success">مرنة</span>
                                        {% elif shift.shift_type == 'PART_TIME' %}
                                            <span class="badge badge-secondary">دوام جزئي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>الحالة:</th>
                                    <td>
                                        {% if shift.is_active %}
                                            <span class="badge badge-success">نشط</span>
                                        {% else %}
                                            <span class="badge badge-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if shift.description %}
                                <tr>
                                    <th>الوصف:</th>
                                    <td>{{ shift.description }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        
                        <!-- أوقات الوردية -->
                        <div class="col-md-6">
                            <h5 class="mb-3">أوقات الوردية</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">وقت البداية:</th>
                                    <td><strong>{{ shift.start_time|time:"H:i" }}</strong></td>
                                </tr>
                                <tr>
                                    <th>وقت النهاية:</th>
                                    <td><strong>{{ shift.end_time|time:"H:i" }}</strong></td>
                                </tr>
                                <tr>
                                    <th>المدة:</th>
                                    <td><strong>{{ shift.duration_hours }} ساعة</strong></td>
                                </tr>
                                <tr>
                                    <th>يشمل استراحة:</th>
                                    <td>
                                        {% if shift.include_break %}
                                            <span class="badge badge-success">نعم</span>
                                            ({{ shift.break_duration }} دقيقة)
                                        {% else %}
                                            <span class="badge badge-danger">لا</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>يسمح بالعمل الإضافي:</th>
                                    <td>
                                        {% if shift.overtime_allowed %}
                                            <span class="badge badge-success">نعم</span>
                                        {% else %}
                                            <span class="badge badge-danger">لا</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- معلومات النظام -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">معلومات النظام</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150">تاريخ الإنشاء:</th>
                                    <td>{{ shift.created_at|date:"Y/m/d H:i" }}</td>
                                </tr>
                                {% if shift.created_by %}
                                <tr>
                                    <th>أنشئ بواسطة:</th>
                                    <td>{{ shift.created_by.get_full_name|default:shift.created_by.username }}</td>
                                </tr>
                                {% endif %}
                                {% if shift.updated_at %}
                                <tr>
                                    <th>آخر تحديث:</th>
                                    <td>{{ shift.updated_at|date:"Y/m/d H:i" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-danger" 
                                onclick="confirmDelete('{{ shift.name }}', {{ shift.pk }})">
                            <i class="fas fa-trash"></i> حذف الوردية
                        </button>
                        <div>
                            <a href="{% url 'hr:shift_edit' shift.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'hr:shift_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> عودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج حذف مخفي -->
<form id="deleteForm" method="POST" style="display: none;">
    {% csrf_token %}
</form>

<script>
function confirmDelete(shiftName, shiftId) {
    if (confirm(`هل أنت متأكد من حذف الوردية "${shiftName}"؟`)) {
        const form = document.getElementById('deleteForm');
        form.action = `/hr/shifts/${shiftId}/delete/`;
        form.submit();
    }
}
</script>
{% endblock %} 