/**
 * قوالب الطباعة المخصصة
 * Custom Print Templates for HR System
 */

// قالب طباعة تقرير الموظفين
function printEmployeeReport(employees, title = 'تقرير الموظفين') {
    const printWindow = window.open('', '_blank');
    
    const htmlContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                    
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        font-size: 11pt;
                        line-height: 1.5;
                        color: #2c3e50;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background: white;
                    }
                    
                    .report-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border: 3px solid #3498db;
                        border-radius: 15px;
                        padding: 30px;
                        background: linear-gradient(135deg, #ebf3fd, #d6eaf8);
                    }
                    
                    .company-logo {
                        max-height: 80px;
                        margin-bottom: 20px;
                    }
                    
                    .report-title {
                        font-size: 24pt;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 15px;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    }
                    
                    .report-subtitle {
                        font-size: 16pt;
                        color: #34495e;
                        margin-bottom: 10px;
                    }
                    
                    .report-date {
                        font-size: 12pt;
                        color: #7f8c8d;
                    }
                    
                    .employees-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 30px;
                        font-size: 10pt;
                    }
                    
                    .employees-table th {
                        background: linear-gradient(135deg, #3498db, #2980b9);
                        color: white;
                        border: 2px solid #2980b9;
                        padding: 12px 8px;
                        text-align: center;
                        font-weight: 600;
                    }
                    
                    .employees-table td {
                        border: 1px solid #bdc3c7;
                        padding: 10px 8px;
                        text-align: center;
                        background: white;
                    }
                    
                    .employees-table tbody tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    
                    .summary-section {
                        margin-top: 40px;
                        padding: 20px;
                        border: 2px solid #27ae60;
                        border-radius: 10px;
                        background: linear-gradient(135deg, #d5f4e6, #a9dfbf);
                    }
                    
                    .summary-title {
                        font-size: 16pt;
                        font-weight: 600;
                        color: #27ae60;
                        margin-bottom: 15px;
                        text-align: center;
                    }
                    
                    .summary-stats {
                        display: flex;
                        justify-content: space-around;
                        text-align: center;
                    }
                    
                    .stat-item {
                        padding: 10px;
                    }
                    
                    .stat-number {
                        font-size: 18pt;
                        font-weight: 700;
                        color: #2c3e50;
                    }
                    
                    .stat-label {
                        font-size: 12pt;
                        color: #34495e;
                        margin-top: 5px;
                    }
                    
                    @page {
                        margin: 1.5cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            <div class="report-header">
                <div class="report-title">نظام إدارة الموارد البشرية</div>
                <div class="report-subtitle">شركة [اسم الشركة]</div>
                <div class="report-title">${title}</div>
                <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            
            <table class="employees-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الموظف</th>
                        <th>القسم</th>
                        <th>المسمى الوظيفي</th>
                        <th>تاريخ التوظيف</th>
                        <th>الراتب</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${employees.map((emp, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${emp.name || 'غير محدد'}</td>
                            <td>${emp.department || 'غير محدد'}</td>
                            <td>${emp.position || 'غير محدد'}</td>
                            <td>${emp.hire_date || 'غير محدد'}</td>
                            <td>${emp.salary || 'غير محدد'}</td>
                            <td>${emp.status || 'نشط'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="summary-section">
                <div class="summary-title">ملخص التقرير</div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-number">${employees.length}</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${employees.filter(emp => emp.status === 'نشط').length}</div>
                        <div class="stat-label">الموظفين النشطين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${new Set(employees.map(emp => emp.department)).size}</div>
                        <div class="stat-label">عدد الأقسام</div>
                    </div>
                </div>
            </div>
            
            <div style="position: fixed; bottom: 20px; left: 0; right: 0; text-align: center; font-size: 10pt; color: #7f8c8d; border-top: 1px solid #bdc3c7; padding-top: 10px;">
                نظام إدارة الموارد البشرية - جميع الحقوق محفوظة © ${new Date().getFullYear()}
            </div>
        </body>
        </html>
    `;
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 1000);
}

// قالب طباعة تقرير الحضور
function printAttendanceReport(attendanceData, title = 'تقرير الحضور والانصراف') {
    const printWindow = window.open('', '_blank');
    
    const htmlContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                    
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        font-size: 11pt;
                        line-height: 1.5;
                        color: #2c3e50;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background: white;
                    }
                    
                    .report-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border: 3px solid #e74c3c;
                        border-radius: 15px;
                        padding: 30px;
                        background: linear-gradient(135deg, #fadbd8, #f1948a);
                    }
                    
                    .report-title {
                        font-size: 24pt;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 15px;
                    }
                    
                    .attendance-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 30px;
                        font-size: 10pt;
                    }
                    
                    .attendance-table th {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                        color: white;
                        border: 2px solid #c0392b;
                        padding: 12px 8px;
                        text-align: center;
                        font-weight: 600;
                    }
                    
                    .attendance-table td {
                        border: 1px solid #bdc3c7;
                        padding: 10px 8px;
                        text-align: center;
                        background: white;
                    }
                    
                    .attendance-table tbody tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    
                    .status-present { color: #27ae60; font-weight: 600; }
                    .status-absent { color: #e74c3c; font-weight: 600; }
                    .status-late { color: #f39c12; font-weight: 600; }
                    
                    @page {
                        margin: 1.5cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            <div class="report-header">
                <div class="report-title">نظام إدارة الموارد البشرية</div>
                <div class="report-subtitle">شركة [اسم الشركة]</div>
                <div class="report-title">${title}</div>
                <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الموظف</th>
                        <th>التاريخ</th>
                        <th>وقت الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>ساعات العمل</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${attendanceData.map((record, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${record.employee_name || 'غير محدد'}</td>
                            <td>${record.date || 'غير محدد'}</td>
                            <td>${record.check_in || 'غير محدد'}</td>
                            <td>${record.check_out || 'غير محدد'}</td>
                            <td>${record.hours_worked || 'غير محدد'}</td>
                            <td class="status-${record.status || 'present'}">${record.status_text || 'حاضر'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </body>
        </html>
    `;
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 1000);
}

// قالب طباعة كشف المرتبات
function printPayrollReport(payrollData, title = 'كشف المرتبات الشهري') {
    const printWindow = window.open('', '_blank');
    
    const totalSalaries = payrollData.reduce((sum, emp) => sum + (parseFloat(emp.net_salary) || 0), 0);
    
    const htmlContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                    
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        font-size: 11pt;
                        line-height: 1.5;
                        color: #2c3e50;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background: white;
                    }
                    
                    .report-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border: 3px solid #27ae60;
                        border-radius: 15px;
                        padding: 30px;
                        background: linear-gradient(135deg, #d5f4e6, #a9dfbf);
                    }
                    
                    .report-title {
                        font-size: 24pt;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 15px;
                    }
                    
                    .payroll-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 30px;
                        font-size: 9pt;
                    }
                    
                    .payroll-table th {
                        background: linear-gradient(135deg, #27ae60, #229954);
                        color: white;
                        border: 2px solid #229954;
                        padding: 10px 6px;
                        text-align: center;
                        font-weight: 600;
                    }
                    
                    .payroll-table td {
                        border: 1px solid #bdc3c7;
                        padding: 8px 6px;
                        text-align: center;
                        background: white;
                    }
                    
                    .payroll-table tbody tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    
                    .total-row {
                        background: linear-gradient(135deg, #f39c12, #e67e22) !important;
                        color: white !important;
                        font-weight: 700 !important;
                    }
                    
                    .total-section {
                        margin-top: 30px;
                        padding: 20px;
                        border: 3px solid #27ae60;
                        border-radius: 10px;
                        background: #d5f4e6;
                        text-align: center;
                    }
                    
                    .total-amount {
                        font-size: 20pt;
                        font-weight: 700;
                        color: #27ae60;
                    }
                    
                    @page {
                        margin: 1.2cm;
                        size: A4 landscape;
                    }
                }
            </style>
        </head>
        <body>
            <div class="report-header">
                <div class="report-title">نظام إدارة الموارد البشرية</div>
                <div class="report-subtitle">شركة [اسم الشركة]</div>
                <div class="report-title">${title}</div>
                <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            
            <table class="payroll-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم الموظف</th>
                        <th>الراتب الأساسي</th>
                        <th>البدلات</th>
                        <th>العمل الإضافي</th>
                        <th>إجمالي الاستحقاقات</th>
                        <th>التأمينات</th>
                        <th>الضرائب</th>
                        <th>الخصومات</th>
                        <th>صافي الراتب</th>
                    </tr>
                </thead>
                <tbody>
                    ${payrollData.map((emp, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${emp.employee_name || 'غير محدد'}</td>
                            <td>${emp.basic_salary || '0'}</td>
                            <td>${emp.allowances || '0'}</td>
                            <td>${emp.overtime || '0'}</td>
                            <td>${emp.gross_salary || '0'}</td>
                            <td>${emp.insurance || '0'}</td>
                            <td>${emp.tax || '0'}</td>
                            <td>${emp.deductions || '0'}</td>
                            <td>${emp.net_salary || '0'}</td>
                        </tr>
                    `).join('')}
                    <tr class="total-row">
                        <td colspan="9">إجمالي صافي المرتبات</td>
                        <td>${totalSalaries.toFixed(2)}</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="total-section">
                <div>إجمالي صافي المرتبات لهذا الشهر</div>
                <div class="total-amount">${totalSalaries.toFixed(2)} جنيه مصري</div>
            </div>
        </body>
        </html>
    `;
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 1000);
}

// تصدير الدوال
window.printEmployeeReport = printEmployeeReport;
window.printAttendanceReport = printAttendanceReport;
window.printPayrollReport = printPayrollReport;
