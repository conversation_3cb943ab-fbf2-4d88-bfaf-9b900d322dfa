# Generated by Django 5.2.2 on 2025-06-28 03:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الحساب')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحساب')),
                ('account_type', models.CharField(choices=[('ASSET', 'أصول'), ('LIABILITY', 'خصوم'), ('EQUITY', 'حقوق ملكية'), ('REVENUE', 'إيرادات'), ('EXPENSE', 'مصروفات')], max_length=20, verbose_name='نوع الحساب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.account', verbose_name='الحساب الأب')),
            ],
            options={
                'verbose_name': 'حساب محاسبي',
                'verbose_name_plural': 'الحسابات المحاسبية',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='AccountingPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_name', models.CharField(max_length=100, verbose_name='اسم الفترة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('is_current', models.BooleanField(default=False, verbose_name='الفترة الحالية')),
                ('is_closed', models.BooleanField(default=False, verbose_name='مغلقة')),
                ('closed_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('closed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='closed_periods', to=settings.AUTH_USER_MODEL, verbose_name='أغلق بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'فترة حسابات',
                'verbose_name_plural': 'فترات الحسابات',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='AccountMerge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('merge_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الدمج')),
                ('merge_date', models.DateField(verbose_name='تاريخ الدمج')),
                ('merged_balance', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد المدمج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('source_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='merges_from', to='definitions.person', verbose_name='الشخص المصدر')),
                ('target_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='merges_to', to='definitions.person', verbose_name='الشخص الهدف')),
            ],
            options={
                'verbose_name': 'دمج حسابات',
                'verbose_name_plural': 'دمج الحسابات',
                'ordering': ['-merge_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BalanceTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='definitions.person', verbose_name='من شخص')),
                ('to_person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='definitions.person', verbose_name='إلى شخص')),
            ],
            options={
                'verbose_name': 'تحويل رصيد',
                'verbose_name_plural': 'تحويلات الأرصدة',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')),
                ('entry_date', models.DateField(verbose_name='تاريخ القيد')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('total_debit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المدين')),
                ('total_credit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي الدائن')),
                ('is_balanced', models.BooleanField(default=False, verbose_name='متوازن')),
                ('is_posted', models.BooleanField(default=False, verbose_name='مرحل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'قيد محاسبي',
                'verbose_name_plural': 'القيود المحاسبية',
                'ordering': ['-entry_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=255, verbose_name='الوصف')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='دائن')),
                ('line_order', models.PositiveIntegerField(default=1, verbose_name='ترتيب السطر')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.account', verbose_name='الحساب')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entry_lines', to='accounting.journalentry', verbose_name='القيد المحاسبي')),
                ('person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.person', verbose_name='الشخص')),
            ],
            options={
                'verbose_name': 'سطر قيد محاسبي',
                'verbose_name_plural': 'أسطر القيود المحاسبية',
                'ordering': ['journal_entry', 'line_order'],
            },
        ),
        migrations.CreateModel(
            name='OpeningBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد الافتتاحي')),
                ('balance_date', models.DateField(verbose_name='تاريخ القيد الافتتاحي')),
                ('balance_type', models.CharField(choices=[('INVENTORY', 'جرد بضاعة أول المدة'), ('PERSONS', 'رصيد الأشخاص والجهات'), ('BANK', 'رصيد البنك'), ('TREASURY', 'رصيد الخزينة'), ('RECEIVABLE_PAPERS', 'أوراق قبض'), ('INCOMING_CUSTODY', 'إصالات أمانة واردة'), ('PAYABLE_PAPERS', 'أوراق دفع'), ('OUTGOING_CUSTODY', 'إيصالات أمانة صادرة'), ('FIXED_ASSETS', 'الأصول الثابتة'), ('RETAINED_EARNINGS', 'أرباح مرحلة')], max_length=20, verbose_name='نوع الرصيد')),
                ('person_type', models.CharField(blank=True, choices=[('CUSTOMERS', 'عملاء'), ('EMPLOYEE_ADVANCES', 'سلف عاملين'), ('MISC_DEBTORS', 'مدينون متنوعون'), ('SUPPLIERS', 'موردون'), ('MISC_CREDITORS', 'دائنون متنوعون'), ('PARTNERS', 'شركاء'), ('PARTNER_CURRENT', 'جاري الشركاء')], max_length=20, null=True, verbose_name='نوع الشخص')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='دائن')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.account', verbose_name='الحساب')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.person', verbose_name='الشخص')),
            ],
            options={
                'verbose_name': 'قيد افتتاحي',
                'verbose_name_plural': 'القيود الافتتاحية',
                'ordering': ['-balance_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OpeningInventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('inventory_number', models.CharField(max_length=50, unique=True, verbose_name='رقم جرد أول المدة')),
                ('date', models.DateField(verbose_name='تاريخ أول المدة')),
                ('fiscal_year', models.CharField(max_length=10, verbose_name='السنة المالية')),
                ('period_name', models.CharField(max_length=100, verbose_name='اسم الفترة')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('APPROVED', 'معتمد'), ('POSTED', 'مرحل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('total_items', models.IntegerField(default=0, verbose_name='عدد الأصناف')),
                ('total_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='إجمالي الكميات')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('posted_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الترحيل')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_opening_inventories', to=settings.AUTH_USER_MODEL, verbose_name='معتمد من')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_opening_inventories', to=settings.AUTH_USER_MODEL, verbose_name='رحل بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='عدل بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'جرد بضاعة أول المدة',
                'verbose_name_plural': 'جرد بضاعة أول المدة',
                'ordering': ['-date', '-id'],
                'unique_together': {('warehouse', 'fiscal_year')},
            },
        ),
        migrations.CreateModel(
            name='ProfitCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('center_code', models.CharField(max_length=20, unique=True, verbose_name='كود المركز')),
                ('center_name', models.CharField(max_length=100, verbose_name='اسم المركز')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مركز ربحية',
                'verbose_name_plural': 'مراكز الربحية',
                'ordering': ['center_code'],
            },
        ),
        migrations.CreateModel(
            name='ProfitDistribution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('distribution_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التوزيع')),
                ('distribution_date', models.DateField(verbose_name='تاريخ التوزيع')),
                ('total_profit', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='إجمالي الربح')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'توزيع أرباح',
                'verbose_name_plural': 'توزيعات الأرباح',
                'ordering': ['-distribution_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProfitDistributionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='النسبة %')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='الوصف')),
                ('distribution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='distribution_lines', to='accounting.profitdistribution', verbose_name='توزيع الأرباح')),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.person', verbose_name='الشخص/الشريك')),
            ],
            options={
                'verbose_name': 'سطر توزيع أرباح',
                'verbose_name_plural': 'أسطر توزيع الأرباح',
                'ordering': ['distribution', 'person'],
            },
        ),
        migrations.CreateModel(
            name='OpeningInventoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('opening_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='كمية أول المدة')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي القيمة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='الموقع في المخزن')),
                ('condition', models.CharField(choices=[('NEW', 'جديد'), ('GOOD', 'جيد'), ('FAIR', 'مقبول'), ('POOR', 'ضعيف'), ('DAMAGED', 'تالف')], default='GOOD', max_length=20, verbose_name='الحالة')),
                ('quality_grade', models.CharField(blank=True, choices=[('A', 'ممتاز'), ('B', 'جيد جداً'), ('C', 'جيد'), ('D', 'مقبول')], max_length=20, verbose_name='درجة الجودة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('recorded_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.item', verbose_name='الصنف')),
                ('opening_inventory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='accounting.openinginventory', verbose_name='جرد أول المدة')),
                ('recorded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='سجل بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='عدل بواسطة')),
            ],
            options={
                'verbose_name': 'صنف جرد أول المدة',
                'verbose_name_plural': 'أصناف جرد أول المدة',
                'ordering': ['item__name'],
                'unique_together': {('opening_inventory', 'item', 'batch_number')},
            },
        ),
    ]
