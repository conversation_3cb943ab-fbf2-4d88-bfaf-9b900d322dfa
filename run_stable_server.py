#!/usr/bin/env python3
"""
سكريبت تشغيل الخادم المستقر
Stable Server Runner Script

هذا السكريبت يضمن تشغيل الخادم بأقصى استقرار ولا يتوقف أبداً
This script ensures maximum server stability and never stops
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
from datetime import datetime
from pathlib import Path

# إعداد اللوجز
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_stability.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class StableServerRunner:
    """مدير الخادم المستقر"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 1000  # عدد مرات إعادة التشغيل المسموحة
        self.restart_delay = 5  # ثواني انتظار قبل إعادة التشغيل
        self.health_check_interval = 30  # فحص الصحة كل 30 ثانية
        self.port = 8000
        self.host = '127.0.0.1'
        
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        logger.info(f"🛑 تم استلام إشارة {signum}. إيقاف الخادم بأمان...")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def check_dependencies(self):
        """فحص المتطلبات"""
        try:
            import django
            logger.info(f"✅ Django version: {django.get_version()}")
            return True
        except ImportError:
            logger.error("❌ Django غير مثبت!")
            return False
    
    def prepare_environment(self):
        """تحضير البيئة"""
        try:
            # إنشاء المجلدات المطلوبة
            os.makedirs('logs', exist_ok=True)
            os.makedirs('static', exist_ok=True)
            os.makedirs('media', exist_ok=True)
            
            # تحديث متغيرات البيئة
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'accounting_system.settings')
            
            logger.info("✅ تم تحضير البيئة بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تحضير البيئة: {e}")
            return False
    
    def run_migrations(self):
        """تشغيل الترحيلات"""
        try:
            logger.info("🔄 تشغيل ترحيلات قاعدة البيانات...")
            result = subprocess.run([
                sys.executable, 'manage.py', 'migrate', '--noinput'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ تم تشغيل الترحيلات بنجاح")
                return True
            else:
                logger.warning(f"⚠️ تحذير في الترحيلات: {result.stderr}")
                return True  # نستمر حتى لو كان هناك تحذيرات
        except Exception as e:
            logger.error(f"❌ خطأ في الترحيلات: {e}")
            return False
    
    def collect_static(self):
        """جمع الملفات الثابتة"""
        try:
            logger.info("📁 جمع الملفات الثابتة...")
            result = subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ تم جمع الملفات الثابتة بنجاح")
            else:
                logger.warning(f"⚠️ تحذير في جمع الملفات: {result.stderr}")
            return True
        except Exception as e:
            logger.warning(f"⚠️ تحذير في جمع الملفات الثابتة: {e}")
            return True  # نستمر حتى لو فشل
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            logger.info(f"🚀 بدء تشغيل الخادم على {self.host}:{self.port}")
            
            # أوامر تشغيل الخادم مع خيارات الاستقرار
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}',
                '--noreload',  # منع إعادة التحميل التلقائي
                '--insecure'   # تقديم الملفات الثابتة في التطوير
            ]
            
            # تشغيل الخادم
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.is_running = True
            logger.info(f"✅ تم بدء الخادم بنجاح! PID: {self.server_process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء الخادم: {e}")
            return False
    
    def monitor_server_output(self):
        """مراقبة مخرجات الخادم"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    # تسجيل مخرجات الخادم
                    line = line.strip()
                    if 'ERROR' in line or 'Exception' in line:
                        logger.error(f"🔥 خطأ في الخادم: {line}")
                    elif 'WARNING' in line:
                        logger.warning(f"⚠️ تحذير: {line}")
                    else:
                        logger.info(f"📝 {line}")
                        
                if self.server_process.poll() is not None:
                    break
                    
        except Exception as e:
            logger.error(f"❌ خطأ في مراقبة المخرجات: {e}")
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            import urllib.request
            import urllib.error
            
            url = f"http://{self.host}:{self.port}/"
            
            try:
                response = urllib.request.urlopen(url, timeout=10)
                if response.getcode() == 200:
                    logger.info("💚 الخادم يعمل بصحة جيدة")
                    return True
                else:
                    logger.warning(f"⚠️ الخادم يرد بكود: {response.getcode()}")
                    return False
            except urllib.error.URLError:
                logger.warning("⚠️ الخادم لا يرد على الطلبات")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        logger.info("🔄 إعادة تشغيل الخادم...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        if self.restart_count < self.max_restarts:
            self.restart_count += 1
            logger.info(f"🔄 محاولة إعادة التشغيل رقم {self.restart_count}")
            return self.start_server()
        else:
            logger.error(f"❌ تم الوصول للحد الأقصى من إعادة التشغيل ({self.max_restarts})")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                logger.info("🛑 إيقاف الخادم...")
                self.server_process.terminate()
                
                # انتظار الإيقاف الطبيعي
                try:
                    self.server_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️ إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                
                logger.info("✅ تم إيقاف الخادم بنجاح")
                
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def run_health_monitor(self):
        """مراقب الصحة المستمر"""
        while self.is_running:
            time.sleep(self.health_check_interval)
            
            if not self.is_running:
                break
                
            # فحص حالة العملية
            if self.server_process and self.server_process.poll() is not None:
                logger.error("💀 الخادم توقف بشكل غير متوقع!")
                if not self.restart_server():
                    break
                continue
            
            # فحص الاستجابة
            if not self.check_server_health():
                logger.warning("⚠️ الخادم لا يستجيب، إعادة تشغيل...")
                if not self.restart_server():
                    break
    
    def run(self):
        """تشغيل الخادم المستقر"""
        logger.info("🎯 بدء تشغيل الخادم المستقر...")
        logger.info("=" * 50)
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # فحص المتطلبات
        if not self.check_dependencies():
            return False
        
        # تحضير البيئة
        if not self.prepare_environment():
            return False
        
        # تشغيل الترحيلات
        if not self.run_migrations():
            return False
        
        # جمع الملفات الثابتة
        self.collect_static()
        
        # بدء الخادم
        if not self.start_server():
            return False
        
        # بدء مراقبة المخرجات في خيط منفصل
        output_thread = threading.Thread(target=self.monitor_server_output)
        output_thread.daemon = True
        output_thread.start()
        
        # بدء مراقب الصحة في خيط منفصل
        health_thread = threading.Thread(target=self.run_health_monitor)
        health_thread.daemon = True
        health_thread.start()
        
        logger.info("🎉 الخادم يعمل بأقصى استقرار!")
        logger.info(f"🌐 الموقع متاح على: http://{self.host}:{self.port}/")
        logger.info("🔄 مراقبة مستمرة للصحة والاستقرار")
        logger.info("⌨️ اضغط Ctrl+C للإيقاف الآمن")
        
        try:
            # انتظار إنهاء العملية
            while self.is_running:
                if self.server_process:
                    self.server_process.wait()
                    if self.server_process.returncode != 0:
                        logger.error(f"💀 الخادم توقف بكود خطأ: {self.server_process.returncode}")
                        if not self.restart_server():
                            break
                else:
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("⌨️ تم الضغط على Ctrl+C")
        finally:
            self.stop_server()
            logger.info("👋 تم إنهاء الخادم المستقر")
        
        return True

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في الخادم المستقر!")
    print("=" * 50)
    
    runner = StableServerRunner()
    success = runner.run()
    
    if success:
        print("✅ تم تشغيل الخادم بنجاح!")
    else:
        print("❌ فشل في تشغيل الخادم!")
        sys.exit(1)

if __name__ == "__main__":
    main()
