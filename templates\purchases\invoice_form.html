{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .supplier-info-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
        border-left: 4px solid #007bff;
    }

    .supplier-info-card h6 {
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .supplier-info-card p {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .add-supplier-btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-file-invoice text-primary me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'purchases:invoice_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات فاتورة المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.invoice_number.id_for_label }}" class="form-label">{{ form.invoice_number.label }}</label>
                                {{ form.invoice_number }}
                                {% if form.invoice_number.errors %}
                                    <div class="text-danger small">{{ form.invoice_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.supplier_invoice_number.id_for_label }}" class="form-label">{{ form.supplier_invoice_number.label }}</label>
                                {{ form.supplier_invoice_number }}
                                {% if form.supplier_invoice_number.errors %}
                                    <div class="text-danger small">{{ form.supplier_invoice_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label">{{ form.due_date.label }}</label>
                                {{ form.due_date }}
                                {% if form.due_date.errors %}
                                    <div class="text-danger small">{{ form.due_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- المورد والمخزن -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-building me-1"></i>
                                    المورد والمخزن
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">
                                    {{ form.supplier.label }}
                                    <button type="button" class="btn btn-outline-primary add-supplier-btn ms-2" id="add-new-supplier-btn">
                                        <i class="fas fa-plus"></i> إضافة مورد
                                    </button>
                                </label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger small">{{ form.supplier.errors.0 }}</div>
                                {% endif %}

                                <!-- بطاقة معلومات المورد -->
                                <div id="supplier-info-card" class="supplier-info-card">
                                    <h6><i class="fas fa-user-tie me-2"></i>معلومات المورد</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>الاسم:</strong> <span id="supplier_name"></span></p>
                                            <p><strong>الهاتف:</strong> <span id="supplier_phone"></span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>البريد:</strong> <span id="supplier_email"></span></p>
                                            <p><strong>الرصيد:</strong> <span id="supplier_balance"></span></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <p><strong>العنوان:</strong> <span id="supplier_address"></span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- العملة والصرف -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-success border-bottom pb-2 mb-3">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    العملة والصرف
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">{{ form.currency.label }}</label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small">{{ form.currency.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.exchange_rate.id_for_label }}" class="form-label">{{ form.exchange_rate.label }}</label>
                                {{ form.exchange_rate }}
                                {% if form.exchange_rate.errors %}
                                    <div class="text-danger small">{{ form.exchange_rate.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أصناف الفاتورة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-list me-1"></i>
                                    أصناف الفاتورة
                                    <button type="button" class="btn btn-sm btn-success ms-2" id="add-item-btn">
                                        <i class="fas fa-plus"></i> إضافة صنف
                                    </button>
                                </h6>
                            </div>
                            <div class="col-12">
                                <div id="invoice-items">
                                    <!-- سيتم إضافة الأصناف هنا ديناميكياً -->
                                </div>

                                <div class="text-center mt-3" id="no-items-message">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لم يتم إضافة أي أصناف بعد</p>
                                    <button type="button" class="btn btn-primary" onclick="addInvoiceItem()">
                                        <i class="fas fa-plus me-2"></i>إضافة أول صنف
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-calculator me-1"></i>
                                    ملخص الفاتورة
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ج.م</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2 text-danger">
                                            <span>إجمالي الخصم:</span>
                                            <span id="total-discount">0.00 ج.م</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة:</span>
                                            <span id="total-tax">0.00 ج.م</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold fs-5 text-primary">
                                            <span>الإجمالي النهائي:</span>
                                            <span id="grand-total">0.00 ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6>معلومات إضافية</h6>
                                        <p class="mb-1"><strong>عدد الأصناف:</strong> <span id="items-count">0</span></p>
                                        <p class="mb-1"><strong>إجمالي الكمية:</strong> <span id="total-quantity">0</span></p>
                                        <p class="mb-0"><strong>متوسط سعر الصنف:</strong> <span id="avg-price">0.00 ج.م</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ملاحظات
                                </h6>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'purchases:invoice_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ملاحظة -->
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> بعد حفظ الفاتورة، يمكنك إضافة الأصناف من صفحة التفاصيل.
            </div>
        </div>
    </div>
</div>

<!-- Template لصف صنف جديد -->
<template id="item-row-template">
    <div class="card mb-3 item-row" data-item-index="">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">صنف رقم <span class="item-number"></span></h6>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeInvoiceItem(this)">
                <i class="fas fa-times"></i> حذف
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الصنف *</label>
                        <select class="form-select item-select" name="items[INDEX][item]" required>
                            <option value="">اختر الصنف...</option>
                            {% for item in items %}
                            <option value="{{ item.id }}" data-price="{{ item.cost_price|default:0 }}" data-unit="{{ item.unit }}">
                                {{ item.name }} {% if item.code %}({{ item.code }}){% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الكمية *</label>
                        <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]"
                               step="0.001" min="0.001" value="1" required>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">سعر الوحدة *</label>
                        <input type="number" class="form-control item-price" name="items[INDEX][unit_cost]"
                               step="0.01" min="0.01" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">نسبة الخصم (%)</label>
                        <input type="number" class="form-control item-discount-percent" name="items[INDEX][discount_percentage]"
                               step="0.01" min="0" max="100" value="0">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">مبلغ الخصم</label>
                        <input type="number" class="form-control item-discount-amount" name="items[INDEX][discount_amount]"
                               step="0.01" min="0" value="0" readonly>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">نسبة الضريبة (%)</label>
                        <input type="number" class="form-control item-tax-percent" name="items[INDEX][tax_percentage]"
                               step="0.01" min="0" max="100" value="0">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الإجمالي</label>
                        <input type="number" class="form-control item-total" name="items[INDEX][total_amount]"
                               step="0.01" readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/supplier_integration.js' %}"></script>
<script>
// تحديد تاريخ اليوم افتراضياً
document.addEventListener('DOMContentLoaded', function() {
    const dateField = document.getElementById('{{ form.date.id_for_label }}');
    if (!dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }

    // تحديد سعر الصرف الافتراضي
    const exchangeRateField = document.getElementById('{{ form.exchange_rate.id_for_label }}');
    if (!exchangeRateField.value) {
        exchangeRateField.value = '1.0000';
    }
});

// تحديث معلومات المورد عند تغيير الاختيار
document.getElementById('{{ form.supplier.id_for_label }}').addEventListener('change', function() {
    const supplierId = this.value;
    if (supplierId) {
        // جلب معلومات المورد
        fetch(`/purchases/api/suppliers/${supplierId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const supplier = data.data;

                    // تحديث معلومات المورد
                    document.getElementById('supplier_name').textContent = supplier.name || 'غير محدد';
                    document.getElementById('supplier_phone').textContent = supplier.phone || 'غير محدد';
                    document.getElementById('supplier_email').textContent = supplier.email || 'غير محدد';
                    document.getElementById('supplier_address').textContent = supplier.address || 'غير محدد';
                    document.getElementById('supplier_balance').textContent = (supplier.current_balance || 0) + ' ج.م';

                    // إظهار بطاقة معلومات المورد
                    document.getElementById('supplier-info-card').style.display = 'block';

                    // تحديث تاريخ الاستحقاق حسب شروط الدفع
                    if (supplier.payment_terms) {
                        const invoiceDate = new Date(dateField.value);
                        const dueDate = new Date(invoiceDate);
                        dueDate.setDate(dueDate.getDate() + supplier.payment_terms);

                        const dueDateField = document.getElementById('{{ form.due_date.id_for_label }}');
                        dueDateField.value = dueDate.toISOString().split('T')[0];
                    }

                    // تحديث العملة المفضلة إذا كانت متوفرة
                    if (supplier.preferred_currency) {
                        const currencyField = document.getElementById('{{ form.currency.id_for_label }}');
                        currencyField.value = supplier.preferred_currency;
                    }
                } else {
                    console.error('خطأ في جلب بيانات المورد:', data.error);
                }
            })
            .catch(error => {
                console.error('خطأ في الاتصال:', error);
            });
    } else {
        // إخفاء بطاقة معلومات المورد
        document.getElementById('supplier-info-card').style.display = 'none';
    }
});

// إضافة مورد جديد
document.getElementById('add-new-supplier-btn').addEventListener('click', function() {
    // فتح نافذة إضافة مورد جديد
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المورد *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewSupplier()">حفظ المورد</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // إزالة النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
});

// حفظ مورد جديد
function saveNewSupplier() {
    const form = document.getElementById('addSupplierForm');
    const formData = new FormData(form);

    // إضافة نوع الشخص كمورد
    formData.append('person_type', 'SUPPLIER');
    formData.append('is_active_supplier', 'true');

    fetch('/purchases/api/suppliers/create/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إضافة المورد الجديد للقائمة
            const supplierSelect = document.getElementById('{{ form.supplier.id_for_label }}');
            const newOption = document.createElement('option');
            newOption.value = data.data.id;
            newOption.textContent = data.data.name;
            newOption.selected = true;
            supplierSelect.appendChild(newOption);

            // تحديث معلومات المورد
            supplierSelect.dispatchEvent(new Event('change'));

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            modal.hide();

            // عرض رسالة نجاح
            showAlert('تم إضافة المورد بنجاح', 'success');
        } else {
            showAlert('خطأ في حفظ المورد: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في الاتصال:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// عرض رسائل التنبيه
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى النموذج
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alert = document.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// إدارة أصناف الفاتورة
let itemIndex = 0;

// إضافة صنف جديد
document.getElementById('add-item-btn').addEventListener('click', addInvoiceItem);

function addInvoiceItem() {
    const template = document.getElementById('item-row-template');
    const clone = template.content.cloneNode(true);

    // تحديث الفهارس
    const itemRow = clone.querySelector('.item-row');
    itemRow.setAttribute('data-item-index', itemIndex);

    // تحديث رقم الصنف
    clone.querySelector('.item-number').textContent = itemIndex + 1;

    // تحديث أسماء الحقول
    const inputs = clone.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.name) {
            input.name = input.name.replace('INDEX', itemIndex);
        }
    });

    // إضافة الصف
    document.getElementById('invoice-items').appendChild(clone);

    // إخفاء رسالة "لا توجد أصناف"
    document.getElementById('no-items-message').style.display = 'none';

    // إضافة event listeners للحقول الجديدة
    const newRow = document.querySelector(`[data-item-index="${itemIndex}"]`);
    addItemEventListeners(newRow);

    itemIndex++;
    updateInvoiceTotals();
}

function removeInvoiceItem(button) {
    const itemRow = button.closest('.item-row');
    itemRow.remove();

    // إظهار رسالة "لا توجد أصناف" إذا لم تعد هناك أصناف
    if (document.querySelectorAll('.item-row').length === 0) {
        document.getElementById('no-items-message').style.display = 'block';
    }

    // إعادة ترقيم الأصناف
    renumberItems();
    updateInvoiceTotals();
}

function addItemEventListeners(itemRow) {
    // تحديث السعر عند اختيار الصنف
    const itemSelect = itemRow.querySelector('.item-select');
    itemSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const price = selectedOption.getAttribute('data-price') || 0;
        const priceField = itemRow.querySelector('.item-price');
        priceField.value = price;
        updateItemTotal(itemRow);
        updateInvoiceTotals();
    });

    // تحديث الإجماليات عند تغيير أي قيمة
    const inputs = itemRow.querySelectorAll('.item-quantity, .item-price, .item-discount-percent, .item-tax-percent');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            updateItemTotal(itemRow);
            updateInvoiceTotals();
        });
    });
}

function updateItemTotal(itemRow) {
    const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
    const discountPercent = parseFloat(itemRow.querySelector('.item-discount-percent').value) || 0;
    const taxPercent = parseFloat(itemRow.querySelector('.item-tax-percent').value) || 0;

    // حساب المجموع الفرعي
    const subtotal = quantity * price;

    // حساب الخصم
    const discountAmount = subtotal * (discountPercent / 100);
    itemRow.querySelector('.item-discount-amount').value = discountAmount.toFixed(2);

    // حساب المبلغ بعد الخصم
    const afterDiscount = subtotal - discountAmount;

    // حساب الضريبة
    const taxAmount = afterDiscount * (taxPercent / 100);

    // حساب الإجمالي
    const total = afterDiscount + taxAmount;
    itemRow.querySelector('.item-total').value = total.toFixed(2);
}

function updateInvoiceTotals() {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;
    let itemsCount = 0;
    let totalQuantity = 0;

    document.querySelectorAll('.item-row').forEach(function(itemRow) {
        const quantity = parseFloat(itemRow.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemRow.querySelector('.item-price').value) || 0;
        const discountAmount = parseFloat(itemRow.querySelector('.item-discount-amount').value) || 0;
        const itemTotal = parseFloat(itemRow.querySelector('.item-total').value) || 0;

        const itemSubtotal = quantity * price;
        const taxAmount = itemTotal - (itemSubtotal - discountAmount);

        subtotal += itemSubtotal;
        totalDiscount += discountAmount;
        totalTax += taxAmount;
        totalQuantity += quantity;
        itemsCount++;
    });

    const grandTotal = subtotal - totalDiscount + totalTax;
    const avgPrice = itemsCount > 0 ? subtotal / totalQuantity : 0;

    // تحديث العرض
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
    document.getElementById('total-discount').textContent = totalDiscount.toFixed(2) + ' ج.م';
    document.getElementById('total-tax').textContent = totalTax.toFixed(2) + ' ج.م';
    document.getElementById('grand-total').textContent = grandTotal.toFixed(2) + ' ج.م';
    document.getElementById('items-count').textContent = itemsCount;
    document.getElementById('total-quantity').textContent = totalQuantity.toFixed(3);
    document.getElementById('avg-price').textContent = avgPrice.toFixed(2) + ' ج.م';
}

function renumberItems() {
    document.querySelectorAll('.item-row').forEach(function(itemRow, index) {
        itemRow.querySelector('.item-number').textContent = index + 1;
    });
}

// إضافة صنف أول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة صنف أول تلقائياً هنا إذا رغبت
    // addInvoiceItem();
});
</script>
{% endblock %}
