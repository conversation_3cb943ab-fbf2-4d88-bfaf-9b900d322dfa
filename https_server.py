#!/usr/bin/env python3
"""
خادم HTTPS آمن
Secure HTTPS Server

خادم Django مع دعم HTTPS وشهادة SSL
Django server with HTTPS support and SSL certificate
"""

import os
import sys
import time
import signal
import socket
import subprocess
import threading
import logging
import ssl
from pathlib import Path
from datetime import datetime

# إعداد اللوجز
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'https_server.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class HTTPSServer:
    """خادم HTTPS آمن"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = float('inf')
        self.restart_delay = 3
        self.preferred_ports = [8443, 8000, 8001, 8002, 443]
        self.current_port = None
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.health_check_interval = 15
        self.ssl_cert = None
        self.ssl_key = None
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ✅ {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_info("لا توجد شهادة SSL - إنشاء شهادة جديدة...")
            self.create_ssl_certificate()
    
    def create_ssl_certificate(self):
        """إنشاء شهادة SSL"""
        try:
            from simple_ssl import create_ssl_certificate
            cert_file, key_file, pem_file = create_ssl_certificate()
            
            if cert_file and key_file:
                self.ssl_cert = cert_file
                self.ssl_key = key_file
                self.log_success("تم إنشاء شهادة SSL جديدة")
            else:
                self.log_error("فشل في إنشاء شهادة SSL")
                
        except Exception as e:
            self.log_error(f"خطأ في إنشاء شهادة SSL: {e}")
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self):
        """البحث عن منفذ متاح"""
        self.log_info("البحث عن منفذ متاح...")
        
        for port in self.preferred_ports:
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        # البحث في نطاق أوسع
        for port in range(8000, 9000):
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        self.log_error("لم يتم العثور على منفذ متاح")
        return None
    
    def update_django_settings_for_https(self):
        """تحديث إعدادات Django لـ HTTPS"""
        try:
            settings_file = 'osaric_accounts/settings.py'
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إضافة إعدادات HTTPS إذا لم تكن موجودة
                https_settings = """
# إعدادات HTTPS
SECURE_SSL_REDIRECT = False  # للتطوير المحلي
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 0  # للتطوير المحلي
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
CSRF_COOKIE_SECURE = False  # للتطوير المحلي
SESSION_COOKIE_SECURE = False  # للتطوير المحلي
"""
                
                if 'SECURE_SSL_REDIRECT' not in content:
                    content += https_settings
                    
                    with open(settings_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.log_success("تم تحديث إعدادات Django لـ HTTPS")
                
        except Exception as e:
            self.log_error(f"خطأ في تحديث إعدادات Django: {e}")
    
    def start_https_server(self):
        """بدء تشغيل خادم HTTPS"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # البحث عن منفذ متاح
            port = self.find_available_port()
            if not port:
                self.log_error("لا يمكن العثور على منفذ متاح")
                return False
            
            self.current_port = port
            self.log_info(f"بدء تشغيل خادم HTTPS على {self.host}:{port}")
            
            # تحديث إعدادات Django
            self.update_django_settings_for_https()
            
            # تشغيل خادم Django مع HTTPS
            cmd = [
                sys.executable, 'manage.py', 'runserver_plus',
                f'{self.host}:{port}',
                '--cert-file', str(self.ssl_cert),
                '--key-file', str(self.ssl_key),
                '--insecure'
            ]
            
            # إذا لم يكن runserver_plus متوفر، استخدم runserver عادي
            try:
                # فحص إذا كان django-extensions مثبت
                import django_extensions
                self.log_info("استخدام runserver_plus للـ HTTPS")
            except ImportError:
                self.log_info("تثبيت django-extensions للـ HTTPS...")
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', 'django-extensions'], 
                                 check=True, capture_output=True)
                    self.log_success("تم تثبيت django-extensions")
                except subprocess.CalledProcessError:
                    self.log_error("فشل في تثبيت django-extensions - استخدام HTTP عادي")
                    cmd = [
                        sys.executable, 'manage.py', 'runserver',
                        f'{self.host}:{port}',
                        '--insecure'
                    ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.is_running = True
            self.log_success(f"تم بدء خادم HTTPS! PID: {self.server_process.pid}")
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(5)
            
            if self.server_process.poll() is None:
                self.log_success("خادم HTTPS يعمل بنجاح")
                self.display_access_info()
                return True
            else:
                self.log_error(f"الخادم توقف فوراً بكود: {self.server_process.returncode}")
                return False
            
        except Exception as e:
            self.log_error(f"خطأ في بدء خادم HTTPS: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🔒 خادم HTTPS الآمن")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 المنفذ: {self.current_port}")
        print("\n🔒 للوصول الآمن (HTTPS):")
        print(f"   https://{self.local_ip}:{self.current_port}/")
        print(f"   https://localhost:{self.current_port}/")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   https://{self.local_ip}:{self.current_port}/")
        print("\n⚠️ ملاحظات مهمة:")
        print("   • قد تظهر تحذيرات الأمان في المتصفح")
        print("   • اضغط 'Advanced' ثم 'Proceed to localhost'")
        print("   • أو أضف الشهادة للمتصفح كموثوقة")
        print("   • الاتصال مشفر ومحمي")
        print("=" * 60)
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            if not self.server_process or self.server_process.poll() is not None:
                return False
            
            import urllib.request
            import urllib.error
            
            # إنشاء SSL context يتجاهل التحقق من الشهادة للاختبار
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            url = f"https://127.0.0.1:{self.current_port}/"
            
            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'HTTPSServer/1.0')
                response = urllib.request.urlopen(request, timeout=10, context=ssl_context)
                
                if response.getcode() == 200:
                    return True
                else:
                    self.log_error(f"الخادم يرد بكود خطأ: {response.getcode()}")
                    return False
                    
            except urllib.error.URLError as e:
                self.log_error(f"خطأ في الاتصال بالخادم: {e}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.restart_count += 1
        self.log_info(f"إعادة تشغيل خادم HTTPS (المحاولة #{self.restart_count})...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        return self.start_https_server()
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                self.log_info("إيقاف خادم HTTPS...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=10)
                    self.log_success("تم إيقاف الخادم بنجاح")
                except subprocess.TimeoutExpired:
                    self.log_info("إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                    self.log_success("تم إجبار إيقاف الخادم")
                
            except Exception as e:
                self.log_error(f"خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة الإيقاف {signum}")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def health_monitor_loop(self):
        """حلقة مراقبة الصحة"""
        consecutive_failures = 0
        max_failures = 3
        
        while self.is_running:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("عملية خادم HTTPS توقفت!")
                    if not self.restart_server():
                        break
                    consecutive_failures = 0
                    continue
                
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_success("خادم HTTPS عاد للعمل بنجاح")
                    consecutive_failures = 0
                    self.log_info("خادم HTTPS يعمل بصحة جيدة")
                else:
                    consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.log_error("فشل متكرر في فحص الصحة - إعادة تشغيل الخادم")
                        if not self.restart_server():
                            break
                        consecutive_failures = 0
                
            except Exception as e:
                self.log_error(f"خطأ في حلقة مراقبة الصحة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل خادم HTTPS"""
        print("=" * 60)
        print("🔒 خادم HTTPS الآمن")
        print("Secure HTTPS Server")
        print("=" * 60)
        
        self.log_info("بدء تشغيل خادم HTTPS الآمن...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # بدء الخادم
        if not self.start_https_server():
            self.log_error("فشل في بدء خادم HTTPS!")
            return False
        
        # بدء مراقبة الصحة
        health_thread = threading.Thread(target=self.health_monitor_loop)
        health_thread.daemon = True
        health_thread.start()
        
        self.log_success("خادم HTTPS يعمل بأقصى أمان!")
        self.log_info("الاتصال مشفر ومحمي بشهادة SSL")
        self.log_info("اضغط Ctrl+C للإيقاف الآمن")
        
        try:
            while self.is_running:
                time.sleep(1)
                
                if self.server_process and self.server_process.poll() is not None:
                    return_code = self.server_process.returncode
                    if return_code != 0:
                        self.log_error(f"الخادم توقف بكود خطأ: {return_code}")
                        if not self.restart_server():
                            break
                    else:
                        self.log_info("الخادم توقف بشكل طبيعي")
                        break
                        
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
        finally:
            self.stop_server()
            self.log_info("تم إنهاء خادم HTTPS")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = HTTPSServer()
    success = server.run()
    
    if success:
        print("✅ تم تشغيل خادم HTTPS بنجاح!")
    else:
        print("❌ فشل في تشغيل خادم HTTPS!")
        sys.exit(1)

if __name__ == "__main__":
    main()
