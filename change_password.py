#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.contrib.auth.models import User

def change_admin_password():
    """تغيير كلمة المرور للمستخدم admin"""
    try:
        # البحث عن المستخدم admin
        admin_user = User.objects.get(username='admin')
        
        # تغيير كلمة المرور
        admin_user.set_password('OsamaOsama010@')
        admin_user.save()
        
        print("✅ تم تغيير كلمة المرور بنجاح!")
        print(f"اسم المستخدم: admin")
        print(f"كلمة المرور الجديدة: OsamaOsama010@")
        
    except User.DoesNotExist:
        print("❌ المستخدم admin غير موجود!")
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    change_admin_password() 