{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2 class="mb-0">
                <i class="fas fa-plus text-success me-2"></i>
                {{ title }}
            </h2>
            <a href="{% url 'hr:salary_addition_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">بيانات الإضافة</h4>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <div class="row g-3">
                            <!-- الموظف -->
                            <div class="col-md-6">
                                <label for="id_employee" class="form-label">{{ form.employee.label }} <span class="text-danger">*</span></label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                    <div class="invalid-feedback d-block">{{ form.employee.errors.0 }}</div>
                                {% endif %}
                                {% if form.employee.field.queryset.count == 0 %}
                                    <div class="alert alert-warning mt-2">لا يوجد موظفون متاحون لإضافتهم. يرجى إضافة موظفين أولاً.</div>
                                {% endif %}
                            </div>
                            <!-- نوع الإضافة -->
                            <div class="col-md-6">
                                <label for="id_addition_type" class="form-label">{{ form.addition_type.label }} <span class="text-danger">*</span></label>
                                {{ form.addition_type }}
                                {% if form.addition_type.errors %}
                                    <div class="invalid-feedback d-block">{{ form.addition_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <!-- المبلغ -->
                            <div class="col-md-6">
                                <label for="id_amount" class="form-label">{{ form.amount.label }} <span class="text-danger">*</span></label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="invalid-feedback d-block">{{ form.amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <!-- تاريخ البداية -->
                            <div class="col-md-6">
                                <label for="id_start_date" class="form-label">{{ form.start_date.label }} <span class="text-danger">*</span></label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="invalid-feedback d-block">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <!-- تاريخ النهاية (اختياري) -->
                            <div class="col-md-6">
                                <label for="id_end_date" class="form-label">{{ form.end_date.label }}</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="invalid-feedback d-block">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <!-- نشط -->
                            <div class="col-md-6 d-flex align-items-center mt-4">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label ms-2" for="id_is_active">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="invalid-feedback d-block ms-2">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <!-- الوصف -->
                            <div class="col-12">
                                <label for="id_description" class="form-label">{{ form.description.label }}</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-12 d-flex justify-content-end gap-2">
                                <a href="{% url 'hr:salary_addition_list' %}" class="btn btn-secondary">
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i> حفظ الإضافة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 