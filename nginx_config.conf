# إعداد Nginx لنظام حسابات أوساريك
# Nginx configuration for Osaric Accounts System

# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com app.your-domain.com;
    
    # إعادة توجيه دائمة إلى HTTPS
    return 301 https://$server_name$request_uri;
}

# إعداد HTTPS الرئيسي
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com www.your-domain.com app.your-domain.com;

    # شهادات SSL
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # إعدادات SSL الآمنة
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # رؤوس الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';" always;

    # إعدادات عامة
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;

    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # الملفات الثابتة
    location /static/ {
        alias /opt/osaric_accounts/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # ضغط إضافي للملفات الثابتة
        location ~* \.(css|js)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            gzip_static on;
        }
    }

    # ملفات الوسائط
    location /media/ {
        alias /opt/osaric_accounts/mediafiles/;
        expires 30d;
        add_header Cache-Control "public";
        
        # حماية ملفات الوسائط الحساسة
        location ~* \.(php|py|pl|sh|cgi)$ {
            deny all;
        }
    }

    # favicon
    location = /favicon.ico {
        alias /opt/osaric_accounts/staticfiles/favicon.ico;
        access_log off;
        log_not_found off;
        expires 1y;
    }

    # robots.txt
    location = /robots.txt {
        alias /opt/osaric_accounts/staticfiles/robots.txt;
        access_log off;
        log_not_found off;
    }

    # منع الوصول للملفات الحساسة
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|ini|log|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # التطبيق الرئيسي
    location / {
        proxy_pass http://unix:/opt/osaric_accounts/osaric.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        
        # إعدادات المهلة الزمنية
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # تمرير معلومات SSL
        proxy_set_header X-Forwarded-Ssl on;
        
        # دعم WebSocket (إذا كان مطلوباً)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # صفحة الصحة
    location /health/ {
        proxy_pass http://unix:/opt/osaric_accounts/osaric.sock;
        access_log off;
    }

    # API endpoints
    location /api/ {
        proxy_pass http://unix:/opt/osaric_accounts/osaric.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # إعدادات خاصة بـ API
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # سجلات الوصول والأخطاء
    access_log /var/log/nginx/osaric_access.log;
    error_log /var/log/nginx/osaric_error.log;
}

# إعداد إضافي للنطاقات الفرعية (اختياري)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin.your-domain.com;

    # نفس إعدادات SSL
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # إعادة توجيه إلى لوحة الإدارة
    location / {
        return 301 https://your-domain.com/secure-admin-panel/;
    }
}
