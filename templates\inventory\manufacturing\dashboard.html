{% extends 'base/base.html' %}
{% load static %}

{% block title %}لوحة تحكم التصنيع{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات عامة */
    .manufacturing-dashboard {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        color: white;
        font-weight: 700;
        margin: 0;
        font-size: 2.5rem;
    }
    
    .page-header p {
        color: rgba(255,255,255,0.9);
        font-size: 1.1rem;
        margin: 10px 0 0 0;
    }
    
    /* تحسين البطاقات الإحصائية */
    .stats-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
    }
    
    .stats-card.primary::before { --card-color: #667eea; --card-color-light: #764ba2; }
    .stats-card.warning::before { --card-color: #f093fb; --card-color-light: #f5576c; }
    .stats-card.info::before { --card-color: #4facfe; --card-color-light: #00f2fe; }
    .stats-card.success::before { --card-color: #43e97b; --card-color-light: #38f9d7; }
    
    .stats-card .card-body {
        padding: 30px;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 20px;
    }
    
    .stats-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .stats-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .stats-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .stats-icon.success { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 10px 0;
        color: #2c3e50;
    }
    
    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    /* تحسين أزرار الإجراءات السريعة */
    .quick-actions-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    
    .quick-actions-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 25px;
    }
    
    .quick-actions-card .card-header h6 {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.2rem;
    }
    
    .quick-action-btn {
        background: linear-gradient(135deg, var(--btn-color) 0%, var(--btn-color-light) 100%);
        border: none;
        border-radius: 15px;
        padding: 20px;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        min-height: 80px;
    }
    
    .quick-action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }
    
    .quick-action-btn.primary { --btn-color: #667eea; --btn-color-light: #764ba2; }
    .quick-action-btn.info { --btn-color: #4facfe; --btn-color-light: #00f2fe; }
    .quick-action-btn.success { --btn-color: #43e97b; --btn-color-light: #38f9d7; }
    .quick-action-btn.secondary { --btn-color: #a8edea; --btn-color-light: #fed6e3; }
    
    .quick-action-btn i {
        font-size: 1.5rem;
        margin-left: 10px;
    }
    
    /* تحسين الجداول */
    .table-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    
    .table-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 25px;
    }
    
    .table-card .card-header h6 {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.2rem;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background: #f8f9fa;
        border: none;
        padding: 15px;
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .table td {
        padding: 15px;
        border: none;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }
    
    .table tbody tr:hover {
        background: #f8f9fa;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }
    
    /* تحسين الشارات */
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .status-badge.draft { background: #95a5a6; color: white; }
    .status-badge.planned { background: #3498db; color: white; }
    .status-badge.in-progress { background: #f39c12; color: white; }
    .status-badge.completed { background: #27ae60; color: white; }
    
    /* تحسين الأزرار الصغيرة */
    .btn-sm {
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    /* تحسين الرسائل */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    /* تحسينات إضافية */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    /* تحسين شريط التقدم */
    .progress {
        background-color: #f8f9fa;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .progress-bar {
        transition: width 0.6s ease;
        font-size: 0.75rem;
        font-weight: 600;
        line-height: 20px;
    }
    
    /* تحسين الجداول الصغيرة */
    .table-sm th,
    .table-sm td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .table-sm th {
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="manufacturing-dashboard">
    <div class="container-fluid">
        <!-- عنوان الصفحة -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1>
                        <i class="fas fa-industry me-3"></i>
                        لوحة تحكم التصنيع
                    </h1>
                    <p>إدارة أوامر التصنيع والإنتاج بكفاءة عالية</p>
                </div>
                <div>
                    <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أمر تصنيع جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4 fade-in">
                <div class="stats-card primary">
                    <div class="card-body text-center">
                        <div class="stats-icon primary">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stats-number">{{ total_orders }}</div>
                        <div class="stats-label">إجمالي الأوامر</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4 fade-in">
                <div class="stats-card warning">
                    <div class="card-body text-center">
                        <div class="stats-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stats-number">{{ pending_orders }}</div>
                        <div class="stats-label">أوامر في الانتظار</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4 fade-in">
                <div class="stats-card info">
                    <div class="card-body text-center">
                        <div class="stats-icon info">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="stats-number">{{ in_progress_orders }}</div>
                        <div class="stats-label">قيد التنفيذ</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4 fade-in">
                <div class="stats-card success">
                    <div class="card-body text-center">
                        <div class="stats-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-number">{{ completed_orders }}</div>
                        <div class="stats-label">مكتملة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="quick-actions-card">
                    <div class="card-header">
                        <h6>
                            <i class="fas fa-bolt me-2"></i>
                            الإجراءات السريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{% url 'inventory:manufacturing_order_create' %}" class="quick-action-btn primary">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>إنشاء أمر تصنيع</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{% url 'inventory:manufacturing_order_list' %}" class="quick-action-btn info">
                                    <i class="fas fa-list"></i>
                                    <span>عرض الأوامر</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{% url 'inventory:manufacturing_report' %}" class="quick-action-btn success">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>تقارير التصنيع</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{% url 'definitions:item_list' %}" class="quick-action-btn secondary">
                                    <i class="fas fa-boxes"></i>
                                    <span>إدارة الأصناف</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- أحدث أوامر التصنيع -->
            <div class="col-lg-8">
                <div class="table-card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6>
                            <i class="fas fa-history me-2"></i>
                            أحدث أوامر التصنيع
                        </h6>
                        <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-light btn-sm">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body p-0">
                        {% if recent_orders %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>رقم الأمر</th>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>الحالة</th>
                                            <th>التكلفة</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for order in recent_orders %}
                                        <tr>
                                            <td>
                                                <strong class="text-primary">#{{ order.id }}</strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-box text-muted me-2"></i>
                                                    {{ order.finished_product.name|default:"غير محدد" }}
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    {{ order.quantity_to_produce|default:0 }} {{ order.finished_product.unit.name|default:"" }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge 
                                                    {% if order.status == 'DRAFT' %}draft
                                                    {% elif order.status == 'PLANNED' %}planned
                                                    {% elif order.status == 'IN_PROGRESS' %}in-progress
                                                    {% elif order.status == 'COMPLETED' %}completed
                                                    {% else %}draft{% endif %}">
                                                    {{ order.get_status_display|default:"غير محدد" }}
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    {{ order.total_production_cost|default:0|floatformat:2 }} ج.م
                                                </strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ order.created_at|date:"d/m/Y"|default:"غير محدد" }}
                                                </small>
                                            </td>
                                            <td>
                                                <a href="{% url 'inventory:manufacturing_order_detail' order.pk %}" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-clipboard-list"></i>
                                <h5>لا توجد أوامر تصنيع</h5>
                                <p>ابدأ بإنشاء أول أمر تصنيع جديد</p>
                                <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء أمر تصنيع
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- المواد منخفضة المخزون -->
                <div class="table-card mb-4">
                    <div class="card-header">
                        <h6>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            المواد منخفضة المخزون
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if low_stock_items %}
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead>
                                        <tr>
                                            <th>المادة</th>
                                            <th>المخزن</th>
                                            <th>الكمية الحالية</th>
                                            <th>الحد الأدنى</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in low_stock_items %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-box text-muted me-2"></i>
                                                    <div>
                                                        <strong>{{ item.item.name }}</strong>
                                                        {% if item.item.code %}
                                                            <br><small class="text-muted">{{ item.item.code }}</small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    {{ item.warehouse.name }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-danger fw-bold">
                                                    {{ item.available_quantity|floatformat:2 }}
                                                </span>
                                                <small class="text-muted d-block">
                                                    {{ item.item.unit.name|default:"" }}
                                                </small>
                                            </td>
                                            <td>
                                                <span class="text-warning">
                                                    {{ item.min_stock|floatformat:2 }}
                                                </span>
                                                <small class="text-muted d-block">
                                                    {{ item.item.unit.name|default:"" }}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-danger" 
                                                         role="progressbar" 
                                                         style="width: {{ item.percentage|floatformat:0 }}%"
                                                         aria-valuenow="{{ item.percentage|floatformat:0 }}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100">
                                                        {{ item.percentage|floatformat:0 }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{% url 'definitions:item_list' %}" class="btn btn-outline-warning btn-sm me-2">
                                    <i class="fas fa-cog me-1"></i>
                                    إدارة المخزون
                                </a>
                                <a href="{% url 'inventory:stock_increase_create' %}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة مخزون
                                </a>
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-check-circle text-success"></i>
                                <h6 class="text-success">جميع الأصناف في المستوى المطلوب</h6>
                                <p class="text-muted small">لا توجد مواد منخفضة المخزون</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- إحصائيات التكلفة -->
                <div class="table-card">
                    <div class="card-header">
                        <h6>
                            <i class="fas fa-chart-pie me-2"></i>
                            إحصائيات التكلفة
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="stats-icon success pulse">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stats-number text-success">
                            {{ total_production_cost|floatformat:0 }}
                        </div>
                        <div class="stats-label">إجمالي تكلفة الإنتاج</div>
                        <small class="text-muted">جميع الأوامر المكتملة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.quick-action-btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
