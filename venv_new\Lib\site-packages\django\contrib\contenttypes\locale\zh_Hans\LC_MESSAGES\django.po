# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-03-04 02:33+0000\n"
"Last-Translator: wolf ice <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "内容类型"

msgid "python model class name"
msgstr "python 模型类名"

msgid "content type"
msgstr "内容类型"

msgid "content types"
msgstr "内容类型"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "内容类型 %(ct_id)s 对象没有关联的模型"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "内容类型%(ct_id)s对象%(obj_id)s不存在"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s对象不包含get_absolute_url()方法"
