#!/usr/bin/env python3
"""
إعداد ngrok للحصول على HTTPS معتمد فوري
Setup ngrok for instant certified HTTPS

حل سريع للحصول على HTTPS معتمد بدون دومين
Quick solution for certified HTTPS without domain
"""

import os
import sys
import subprocess
import requests
import zipfile
from pathlib import Path
from datetime import datetime

class NgrokSetup:
    """إعداد ngrok"""
    
    def __init__(self):
        self.ngrok_dir = Path('ngrok')
        self.ngrok_exe = None
        self.auth_token = None
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def check_ngrok_installed(self):
        """فحص إذا كان ngrok مثبت"""
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.log_success("ngrok مثبت بالفعل")
                self.ngrok_exe = 'ngrok'
                return True
        except FileNotFoundError:
            pass
        
        # فحص في المجلد المحلي
        local_ngrok = self.ngrok_dir / 'ngrok.exe'
        if local_ngrok.exists():
            self.log_success("ngrok موجود في المجلد المحلي")
            self.ngrok_exe = str(local_ngrok)
            return True
        
        return False
    
    def download_ngrok(self):
        """تحميل ngrok"""
        print("📥 تحميل ngrok...")
        
        self.ngrok_dir.mkdir(exist_ok=True)
        
        # رابط تحميل ngrok لـ Windows
        url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
        zip_file = self.ngrok_dir / 'ngrok.zip'
        
        try:
            self.log_info("جاري التحميل...")
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(zip_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.log_success("تم تحميل ngrok")
            
            # فك الضغط
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(self.ngrok_dir)
            
            # حذف ملف الـ zip
            zip_file.unlink()
            
            self.ngrok_exe = str(self.ngrok_dir / 'ngrok.exe')
            self.log_success("تم فك ضغط ngrok")
            
            return True
            
        except Exception as e:
            self.log_error(f"فشل في تحميل ngrok: {e}")
            return False
    
    def setup_auth_token(self):
        """إعداد رمز المصادقة"""
        print("\n🔑 إعداد رمز المصادقة...")
        
        print("للحصول على رمز مصادقة مجاني:")
        print("1. اذهب إلى https://ngrok.com")
        print("2. أنشئ حساب مجاني")
        print("3. اذهب إلى Dashboard → Your Authtoken")
        print("4. انسخ الرمز")
        
        self.auth_token = input("\nأدخل رمز المصادقة (أو اتركه فارغ للتخطي): ").strip()
        
        if self.auth_token:
            try:
                result = subprocess.run([
                    self.ngrok_exe, 'config', 'add-authtoken', self.auth_token
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_success("تم إعداد رمز المصادقة")
                    return True
                else:
                    self.log_error("فشل في إعداد رمز المصادقة")
                    return False
            except Exception as e:
                self.log_error(f"خطأ في إعداد الرمز: {e}")
                return False
        else:
            self.log_info("تم تخطي رمز المصادقة (محدود بـ 2 ساعة)")
            return True
    
    def create_ngrok_config(self):
        """إنشاء ملف إعدادات ngrok"""
        config_content = """
version: "2"
authtoken: {auth_token}
tunnels:
  eternal-server:
    proto: http
    addr: 8000
    bind_tls: true
    inspect: false
  eternal-https:
    proto: http  
    addr: 8443
    bind_tls: true
    inspect: false
""".format(auth_token=self.auth_token or "")
        
        config_file = self.ngrok_dir / 'ngrok.yml'
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        self.log_success("تم إنشاء ملف الإعدادات")
        return config_file
    
    def start_tunnel(self, port=8000):
        """بدء tunnel"""
        print(f"\n🚀 بدء tunnel للمنفذ {port}...")
        
        cmd = [self.ngrok_exe, 'http', str(port), '--region', 'us']
        
        try:
            self.log_info("جاري بدء ngrok...")
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            self.log_success("تم بدء ngrok!")
            self.log_info("للحصول على الرابط، اذهب إلى: http://127.0.0.1:4040")
            
            return process
            
        except Exception as e:
            self.log_error(f"فشل في بدء ngrok: {e}")
            return None
    
    def get_tunnel_url(self):
        """الحصول على رابط tunnel"""
        try:
            import time
            time.sleep(3)  # انتظار بدء ngrok
            
            response = requests.get('http://127.0.0.1:4040/api/tunnels')
            data = response.json()
            
            for tunnel in data.get('tunnels', []):
                if tunnel.get('proto') == 'https':
                    url = tunnel.get('public_url')
                    self.log_success(f"رابط HTTPS: {url}")
                    return url
            
            return None
            
        except Exception as e:
            self.log_error(f"فشل في الحصول على الرابط: {e}")
            return None
    
    def create_start_script(self):
        """إنشاء سكريپت بدء التشغيل"""
        script_content = f"""@echo off
title ngrok HTTPS Tunnel
echo ============================================================
echo 🔒 بدء tunnel HTTPS معتمد
echo Starting Certified HTTPS Tunnel  
echo ============================================================
echo.

echo 🚀 بدء ngrok...
"{self.ngrok_exe}" http 8000 --region us

pause
"""
        
        script_file = Path('start_ngrok.bat')
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        self.log_success("تم إنشاء سكريپت البدء: start_ngrok.bat")
    
    def display_instructions(self):
        """عرض التعليمات"""
        print("\n" + "=" * 60)
        print("🎉 تم إعداد ngrok بنجاح!")
        print("=" * 60)
        
        print("\n🚀 للبدء:")
        print("1. شغل الخادم الأبدي:")
        print("   python eternal_server.py")
        print()
        print("2. في نافذة أخرى، شغل ngrok:")
        print("   start_ngrok.bat")
        print("   أو")
        print(f"   {self.ngrok_exe} http 8000")
        print()
        print("3. احصل على الرابط من:")
        print("   http://127.0.0.1:4040")
        
        print("\n🔒 المميزات:")
        print("✅ HTTPS معتمد فوري")
        print("✅ لا حاجة لدومين")
        print("✅ يعمل من أي مكان")
        print("✅ لا تحذيرات أمان")
        
        print("\n⚠️ ملاحظات:")
        print("• بدون رمز مصادقة: محدود بـ 2 ساعة")
        print("• مع رمز مصادقة: غير محدود")
        print("• الرابط يتغير عند إعادة التشغيل")
        print("• مناسب للتطوير والعرض")
    
    def run(self):
        """تشغيل إعداد ngrok"""
        print("=" * 60)
        print("🔒 إعداد ngrok للحصول على HTTPS معتمد")
        print("=" * 60)
        
        # فحص إذا كان مثبت
        if not self.check_ngrok_installed():
            if not self.download_ngrok():
                return False
        
        # إعداد رمز المصادقة
        if not self.setup_auth_token():
            return False
        
        # إنشاء ملف الإعدادات
        self.create_ngrok_config()
        
        # إنشاء سكريپت البدء
        self.create_start_script()
        
        # عرض التعليمات
        self.display_instructions()
        
        # خيار بدء فوري
        choice = input("\nهل تريد بدء tunnel الآن؟ (y/n): ").lower()
        if choice == 'y':
            process = self.start_tunnel()
            if process:
                import time
                time.sleep(3)
                url = self.get_tunnel_url()
                if url:
                    print(f"\n🎉 الموقع متاح على: {url}")
                    print("اضغط Ctrl+C لإيقاف tunnel")
                    try:
                        process.wait()
                    except KeyboardInterrupt:
                        process.terminate()
                        print("\n👋 تم إيقاف tunnel")
        
        return True

def main():
    """الدالة الرئيسية"""
    setup = NgrokSetup()
    success = setup.run()
    
    if success:
        print("\n✅ تم إعداد ngrok بنجاح!")
    else:
        print("\n❌ فشل في إعداد ngrok")

if __name__ == "__main__":
    main()
