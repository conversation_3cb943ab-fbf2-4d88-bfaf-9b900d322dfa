#!/usr/bin/env python
"""
Auto cleanup scheduler for deleted items
"""

import os
import sys
import django
import sqlite3
import schedule
import time
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def cleanup_deleted_data():
    """Clean up deleted data automatically"""
    
    try:
        # Get database path
        db_path = settings.DATABASES['default']['NAME']
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Count inactive records
        cursor.execute("SELECT COUNT(*) FROM definitions_item WHERE is_active = 0")
        items_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM definitions_itemcategory WHERE is_active = 0")
        categories_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM definitions_unit WHERE is_active = 0")
        units_count = cursor.fetchone()[0]
        
        total_count = items_count + categories_count + units_count
        
        if total_count > 0:
            print(f"[{datetime.now()}] Found {total_count} inactive records to clean")
            
            deleted_total = 0
            
            # Delete items
            if items_count > 0:
                cursor.execute("DELETE FROM definitions_item WHERE is_active = 0")
                deleted_items = cursor.rowcount
                deleted_total += deleted_items
                print(f"[{datetime.now()}] Deleted {deleted_items} items")
            
            # Delete categories
            if categories_count > 0:
                cursor.execute("DELETE FROM definitions_itemcategory WHERE is_active = 0")
                deleted_categories = cursor.rowcount
                deleted_total += deleted_categories
                print(f"[{datetime.now()}] Deleted {deleted_categories} categories")
            
            # Delete units
            if units_count > 0:
                cursor.execute("DELETE FROM definitions_unit WHERE is_active = 0")
                deleted_units = cursor.rowcount
                deleted_total += deleted_units
                print(f"[{datetime.now()}] Deleted {deleted_units} units")
            
            # Commit changes
            conn.commit()
            print(f"[{datetime.now()}] Cleanup completed - Total deleted: {deleted_total}")
        else:
            print(f"[{datetime.now()}] No inactive data found - Database is clean")
        
        conn.close()
        
    except Exception as e:
        print(f"[{datetime.now()}] Error during cleanup: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

def run_scheduler():
    """Run the cleanup scheduler"""
    
    print("=" * 50)
    print("Auto Cleanup Scheduler Started")
    print("=" * 50)
    print("Schedule: Every hour")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    # Schedule cleanup every hour
    schedule.every().hour.do(cleanup_deleted_data)
    
    # Run initial cleanup
    cleanup_deleted_data()
    
    # Keep running
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    except KeyboardInterrupt:
        print(f"\n[{datetime.now()}] Scheduler stopped by user")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        # Run cleanup once
        cleanup_deleted_data()
    else:
        # Run scheduler
        run_scheduler()