# This file is distributed under the same license as the Django package.
#
# Translators:
# alberto<PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2012-2013,2015-2017
# e4db27214f7e7544f2022c647b585925_bb0e321, 2015-2016
# e4db27214f7e7544f2022c647b585925_bb0e321, 2020
# <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2012
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# Leonardo J<PERSON>aba<PERSON> G. <<EMAIL>>, 2011
# Natalia, 2024
# U<PERSON> <<EMAIL>>, 2020-2021,2023-2024
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2024-10-07 08:09+0000\n"
"Last-Translator: Uriel Medina <<EMAIL>>, 2020-2021,2023-2024\n"
"Language-Team: Spanish (http://app.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr "Información personal"

msgid "Permissions"
msgstr "Permisos"

msgid "Important dates"
msgstr "Fechas importantes"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "el objeto %(name)s con clave primaria %(key)r no existe."

msgid "Conflicting form data submitted. Please try again."
msgstr ""
"Se enviaron datos contradictorios en el formulario. Por favor inténtalo de "
"nuevo."

msgid "Password changed successfully."
msgstr "La contraseña se ha cambiado con éxito."

msgid "Password-based authentication was disabled."
msgstr "La autenticación basada en contraseña fue deshabilitada."

#, python-format
msgid "Change password: %s"
msgstr "Cambiar contraseña: %s"

#, python-format
msgid "Set password: %s"
msgstr "Establecer contraseña: %s"

msgid "Authentication and Authorization"
msgstr "Autenticación y autorización"

msgid "password"
msgstr "contraseña"

msgid "last login"
msgstr "último inicio de sesión"

msgid "No password set."
msgstr "No se ha establecido la clave."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Formato de clave incorrecto o algoritmo de hash desconocido."

msgid "Reset password"
msgstr "Restablecer contraseña"

msgid "Set password"
msgstr "Establecer contraseña"

msgid "The two password fields didn’t match."
msgstr "Los dos campos de contraseña no coinciden."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Determina si el usuario podrá autenticarse usando una contraseña. Si está "
"deshabilitado, el usuario aún podría autenticarse mediante otros métodos, "
"como Single Sign-On o LDAP."

msgid "Password"
msgstr "Contraseña"

msgid "Password confirmation"
msgstr "Contraseña (confirmación)"

msgid "Enter the same password as before, for verification."
msgstr "Para verificar, introduzca la misma contraseña anterior."

msgid "Password-based authentication"
msgstr "Autenticación basada en contraseña"

msgid "Enabled"
msgstr "Habilitado"

msgid "Disabled"
msgstr "Deshabilitado"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Las contraseñas en texto plano no se almacenan, por lo que no se puede ver "
"la contraseña del usuario."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Habilite la autenticación por contraseña para este usuario estableciendo una "
"contraseña."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Por favor, introduzca un %(username)s y clave correctos. Observe que ambos "
"campos pueden ser sensibles a mayúsculas."

msgid "This account is inactive."
msgstr "Esta cuenta está inactiva."

msgid "Email"
msgstr "Correo electrónico"

msgid "New password"
msgstr "Contraseña nueva"

msgid "New password confirmation"
msgstr "Contraseña nueva (confirmación)"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Su contraseña antigua es incorrecta. Por favor, vuelva a introducirla. "

msgid "Old password"
msgstr "Contraseña antigua"

msgid "algorithm"
msgstr "algoritmo"

msgid "iterations"
msgstr "iteraciones"

msgid "salt"
msgstr "salto"

msgid "hash"
msgstr "función resumen"

msgid "variety"
msgstr "variedad"

msgid "version"
msgstr "versión"

msgid "memory cost"
msgstr "coste de memoria"

msgid "time cost"
msgstr "coste de tiempo"

msgid "parallelism"
msgstr "paralelismo"

msgid "work factor"
msgstr "factor trabajo"

msgid "checksum"
msgstr "suma de verificación"

msgid "block size"
msgstr "tamaño de bloque"

msgid "name"
msgstr "nombre"

msgid "content type"
msgstr "tipo de contenido"

msgid "codename"
msgstr "nombre en código"

msgid "permission"
msgstr "permiso"

msgid "permissions"
msgstr "permisos"

msgid "group"
msgstr "grupo"

msgid "groups"
msgstr "grupos"

msgid "superuser status"
msgstr "estado de superusuario"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Indica que este usuario tiene todos los permisos sin asignárselos "
"explícitamente."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Los grupos a los que pertenece este usuario. Un usuario tendrá todos los "
"permisos asignados a cada uno de sus grupos."

msgid "user permissions"
msgstr "permisos de usuario"

msgid "Specific permissions for this user."
msgstr "Permisos específicos para este usuario."

msgid "username"
msgstr "nombre de usuario"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Requerido. 150 carácteres como máximo. Únicamente letras, dígitos y @/./+/-/"
"_ "

msgid "A user with that username already exists."
msgstr "Ya existe un usuario con este nombre."

msgid "first name"
msgstr "nombre"

msgid "last name"
msgstr "apellidos"

msgid "email address"
msgstr "dirección de correo electrónico"

msgid "staff status"
msgstr "es staff"

msgid "Designates whether the user can log into this admin site."
msgstr "Indica si el usuario puede entrar en este sitio de administración."

msgid "active"
msgstr "activo"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Indica si el usuario debe ser tratado como activo. Desmarque esta opción en "
"lugar de borrar la cuenta."

msgid "date joined"
msgstr "fecha de alta"

msgid "user"
msgstr "usuario"

msgid "users"
msgstr "usuarios"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
"Esta contraseña es demasiado corta. Debe contener al menos %(min_length)d "
"caracter."
msgstr[1] ""
"Esta contraseña es demasiado corta. Debe contener al menos %(min_length)d "
"caracteres."
msgstr[2] ""
"Esta contraseña es demasiado corta. Debe contener al menos %(min_length)d "
"caracteres."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Su contraseña debe contener al menos %(min_length)d caracter."
msgstr[1] "Su contraseña debe contener al menos %(min_length)d caracteres."
msgstr[2] "Su contraseña debe contener al menos %(min_length)d caracteres."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "La contraseña es demasiado similar a la de %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr ""
"Su contraseña no puede asemejarse tanto a su otra información personal."

msgid "This password is too common."
msgstr "Esta contraseña es demasiado común."

msgid "Your password can’t be a commonly used password."
msgstr "Su contraseña no puede ser una clave utilizada comúnmente."

msgid "This password is entirely numeric."
msgstr "Esta contraseña es completamente numérica."

msgid "Your password can’t be entirely numeric."
msgstr "Su contraseña no puede ser completamente numérica."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Contraseña restablecida en %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Introduzca un nombre de usuario válido. Este valor solo puede contener "
"letras mayúsculas y minúsculas de la A la Z (excepto las acentuadas), "
"números y los caracteres @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Introduza un nombre de usuario válido. Este valor puede contener únicamente "
"letras, números y los caracteres @/./+/-/_ "

msgid "Logged out"
msgstr "Sesión terminada"

msgid "Password reset"
msgstr "Restablecer contraseña"

msgid "Password reset sent"
msgstr "Restablecimiento de contraseña enviado"

msgid "Enter new password"
msgstr "Escriba la nueva contraseña"

msgid "Password reset unsuccessful"
msgstr "Restablecimiento de contraseñas fallido"

msgid "Password reset complete"
msgstr "Restablecimiento de contraseña completado"

msgid "Password change"
msgstr "Cambiar contraseña"

msgid "Password change successful"
msgstr "Contraseña cambiada correctamente"
