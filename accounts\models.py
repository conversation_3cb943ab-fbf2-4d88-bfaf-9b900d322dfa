from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType


class UserProfile(models.Model):
    """ملف تعريف المستخدم الموسع"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    department = models.CharField(max_length=100, blank=True, null=True, verbose_name='القسم')
    position = models.CharField(max_length=100, blank=True, null=True, verbose_name='المنصب')
    hire_date = models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')
    salary = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name='الراتب')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'ملف تعريف المستخدم'
        verbose_name_plural = 'ملفات تعريف المستخدمين'

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.department or 'غير محدد'}"


class CustomPermissionGroup(models.Model):
    """مجموعات الصلاحيات المخصصة"""
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم المجموعة')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    permissions = models.ManyToManyField(Permission, blank=True, verbose_name='الصلاحيات')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'مجموعة صلاحيات'
        verbose_name_plural = 'مجموعات الصلاحيات'

    def __str__(self):
        return self.name


class UserPermission(models.Model):
    """صلاحيات المستخدمين المخصصة"""
    PERMISSION_TYPES = [
        ('view', 'عرض'),
        ('add', 'إضافة'),
        ('change', 'تعديل'),
        ('delete', 'حذف'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
        ('approve', 'موافقة'),
        ('reject', 'رفض'),
    ]

    MODULE_CHOICES = [
        ('dashboard', 'لوحة التحكم'),
        ('sales', 'المبيعات'),
        ('purchases', 'المشتريات'),
        ('inventory', 'المخزون'),
        ('treasury', 'الخزينة'),
        ('banking', 'البنوك'),
        ('accounts', 'الحسابات'),
        ('reports', 'التقارير'),
        ('definitions', 'التعريفات'),
        ('hr', 'الموارد البشرية'),
        ('assets', 'الأصول الثابتة'),
        ('branches', 'الفروع'),
        ('services', 'الخدمات'),
        ('windows', 'النوافذ'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_permissions')
    module = models.CharField(max_length=50, choices=MODULE_CHOICES, verbose_name='الوحدة')
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, verbose_name='نوع الصلاحية')
    is_granted = models.BooleanField(default=True, verbose_name='ممنوحة')
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='granted_permissions', verbose_name='منحت بواسطة')
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المنح')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'صلاحية مستخدم'
        verbose_name_plural = 'صلاحيات المستخدمين'
        unique_together = ['user', 'module', 'permission_type']

    def __str__(self):
        return f"{self.user.username} - {self.get_module_display()} - {self.get_permission_type_display()}"


class UserActivity(models.Model):
    """سجل نشاطات المستخدمين"""
    ACTION_TYPES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('view', 'عرض'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    action = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name='النشاط')
    module = models.CharField(max_length=50, verbose_name='الوحدة')
    object_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='معرف الكائن')
    object_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='اسم الكائن')
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')
    user_agent = models.TextField(blank=True, null=True, verbose_name='متصفح المستخدم')
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='الوقت')
    details = models.JSONField(blank=True, null=True, verbose_name='تفاصيل إضافية')

    class Meta:
        verbose_name = 'نشاط مستخدم'
        verbose_name_plural = 'نشاطات المستخدمين'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.username} - {self.get_action_display()} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
