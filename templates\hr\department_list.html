{% extends 'base/base.html' %}
{% load static %}

{% block title %}الأقسام{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-sitemap text-primary me-2"></i>
                            الأقسام
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                                <li class="breadcrumb-item active">الأقسام</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="printDataList('قائمة الأقسام')">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="{% url 'hr:department_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة قسم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}"
                               placeholder="البحث في الأقسام">
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأقسام
                </h5>
                <span class="badge bg-primary">{{ page_obj.paginator.count }} قسم</span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الكود</th>
                            <th>اسم القسم</th>
                            <th>القسم الرئيسي</th>
                            <th>المدير</th>
                            <th>عدد الموظفين</th>
                            <th>عدد المناصب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for department in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ department.code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ department.name }}</strong>
                                    {% if department.name_english %}
                                    <small class="text-muted d-block">{{ department.name_english }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if department.parent_department %}
                                <a href="{% url 'hr:department_detail' department.parent_department.pk %}" class="text-decoration-none">
                                    {{ department.parent_department.name }}
                                </a>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if department.manager %}
                                <a href="{% url 'hr:employee_detail' department.manager.pk %}" class="text-decoration-none">
                                    {{ department.manager.full_name }}
                                </a>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ department.employees.count }}</span>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ department.positions.count }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'hr:department_detail' department.pk %}" 
                                       class="btn btn-outline-info" 
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:department_edit' department.pk %}" 
                                       class="btn btn-outline-primary" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-btn" 
                                            data-id="{{ department.pk }}"
                                            data-name="{{ department.name }}"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابق</a>
                        </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالي</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أقسام</h5>
                <p class="text-muted">ابدأ بإضافة قسم جديد</p>
                <a href="{% url 'hr:department_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة قسم جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف القسم: <strong id="itemName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    const itemNameSpan = document.getElementById('itemName');
    let currentItemId = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            currentItemId = this.dataset.id;
            itemNameSpan.textContent = this.dataset.name;
            deleteModal.show();
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        if (currentItemId) {
            fetch(`/hr/departments/${currentItemId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message);
                }
                deleteModal.hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الحذف');
                deleteModal.hide();
            });
        }
    });
});
</script>
{% endblock %}
