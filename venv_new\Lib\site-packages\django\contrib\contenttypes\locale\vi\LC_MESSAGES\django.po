# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: Tran Van <<EMAIL>>\n"
"Language-Team: Vietnamese (http://www.transifex.com/django/django/language/"
"vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "Các loại nội dung"

msgid "python model class name"
msgstr "tên lớp mô hình python"

msgid "content type"
msgstr "kiểu nội dung"

msgid "content types"
msgstr "kiểu nội dung"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Đối tượng của kiểu nội dung %(ct_id)s không có model tương ứng"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Đối tượng %(obj_id)s của Kiểu nội dung %(ct_id)s không tồn tại."

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "Đối tượng %(ct_name)s không có phương thức get_absolute_url()"
