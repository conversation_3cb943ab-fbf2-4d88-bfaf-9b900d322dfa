#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Django Stable Server - Fixed Version
خادم Django المستقر - النسخة المحدثة
"""

import os
import sys
import time
import signal
import subprocess
import threading
from datetime import datetime

class StableServer:
    """Django Stable Server"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 1000
        self.restart_delay = 3
        self.port = 8000
        self.host = '127.0.0.1'
        
    def log_message(self, message, level="INFO"):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def signal_handler(self, signum, frame):
        """System signal handler"""
        self.log_message("Received stop signal, shutting down...")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """Setup signal handlers"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def start_server(self):
        """Start Django server"""
        try:
            self.log_message(f"Starting server on {self.host}:{self.port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}',
                '--noreload'
            ]
            
            # Set environment to handle Unicode properly
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env
            )
            
            self.is_running = True
            self.log_message(f"Server started successfully! PID: {self.server_process.pid}")
            return True
            
        except Exception as e:
            self.log_message(f"Error starting server: {e}", "ERROR")
            return False
    
    def monitor_server_output(self):
        """Monitor server output"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    if 'ERROR' in line or 'Exception' in line:
                        self.log_message(f"Server error: {line}", "ERROR")
                    elif 'Starting development server' in line:
                        self.log_message(f"Server ready: {line}")
                    elif 'Quit the server' in line:
                        self.log_message("Server is ready for use")
                        
                if self.server_process.poll() is not None:
                    break
                    
        except Exception as e:
            self.log_message(f"Error monitoring output: {e}", "ERROR")
    
    def check_server_health(self):
        """Check server health"""
        try:
            import urllib.request
            import urllib.error
            
            url = f"http://{self.host}:{self.port}/"
            
            try:
                response = urllib.request.urlopen(url, timeout=5)
                return response.getcode() == 200
            except urllib.error.URLError:
                return False
                
        except Exception:
            return False
    
    def restart_server(self):
        """Restart server"""
        self.log_message("Restarting server...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        if self.restart_count < self.max_restarts:
            self.restart_count += 1
            self.log_message(f"Restart attempt #{self.restart_count}")
            return self.start_server()
        else:
            self.log_message("Maximum restart attempts reached", "ERROR")
            return False
    
    def stop_server(self):
        """Stop server"""
        if self.server_process:
            try:
                self.log_message("Stopping server...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.log_message("Force stopping server...")
                    self.server_process.kill()
                    self.server_process.wait()
                
                self.log_message("Server stopped successfully")
                
            except Exception as e:
                self.log_message(f"Error stopping server: {e}", "ERROR")
            finally:
                self.server_process = None
                self.is_running = False
    
    def run_health_monitor(self):
        """Continuous health monitor"""
        while self.is_running:
            time.sleep(30)  # Check every 30 seconds
            
            if not self.is_running:
                break
                
            # Check process status
            if self.server_process and self.server_process.poll() is not None:
                self.log_message("Server process stopped unexpectedly!", "ERROR")
                if not self.restart_server():
                    break
                continue
            
            # Check HTTP response
            if not self.check_server_health():
                self.log_message("Server not responding, restarting...", "WARNING")
                if not self.restart_server():
                    break
            else:
                self.log_message("Server health check: OK")
    
    def run(self):
        """Run stable server"""
        print("=" * 60)
        print("Django Stable Server - Fixed Version")
        print("=" * 60)
        self.log_message("Starting system...")
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Start server
        if not self.start_server():
            self.log_message("Failed to start server!", "ERROR")
            return False
        
        # Start output monitoring thread
        output_thread = threading.Thread(target=self.monitor_server_output)
        output_thread.daemon = True
        output_thread.start()
        
        # Wait a bit for server to start
        time.sleep(3)
        
        # Start health monitoring thread
        health_thread = threading.Thread(target=self.run_health_monitor)
        health_thread.daemon = True
        health_thread.start()
        
        self.log_message("Server running with maximum stability!")
        self.log_message(f"Website available at: http://{self.host}:{self.port}/")
        self.log_message("Continuous health monitoring active")
        self.log_message("Press Ctrl+C for safe shutdown")
        print("=" * 60)
        
        try:
            # Wait for process completion
            while self.is_running:
                if self.server_process:
                    self.server_process.wait()
                    if self.server_process.returncode != 0:
                        self.log_message(f"Server stopped with error code: {self.server_process.returncode}", "ERROR")
                        if not self.restart_server():
                            break
                else:
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            self.log_message("Ctrl+C pressed")
        finally:
            self.stop_server()
            self.log_message("Stable server terminated")
        
        return True

def main():
    """Main function"""
    server = StableServer()
    success = server.run()
    
    if success:
        print("Server ran successfully!")
    else:
        print("Server failed to run!")
        sys.exit(1)

if __name__ == "__main__":
    main()