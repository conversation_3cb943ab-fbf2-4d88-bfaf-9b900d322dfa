from django.urls import path
from . import views, api_views
from .warehouse_dashboard_views import warehouse_dashboard

app_name = 'inventory'

urlpatterns = [
    # الصفحة الرئيسية للمخزون
    path('', views.inventory_home, name='inventory_home'),
    
    # ========== نظام التصنيع الجديد ==========
    path('manufacturing/', views.manufacturing_dashboard, name='manufacturing_dashboard'),
    path('manufacturing/orders/', views.manufacturing_order_list, name='manufacturing_order_list'),
    path('manufacturing/orders/create/', views.manufacturing_order_create, name='manufacturing_order_create'),
    path('manufacturing/orders/<int:pk>/', views.manufacturing_order_detail, name='manufacturing_order_detail'),
    path('manufacturing/orders/<int:pk>/edit/', views.manufacturing_order_edit, name='manufacturing_order_edit'),
    path('manufacturing/orders/<int:order_id>/plan/', views.plan_manufacturing_order, name='plan_manufacturing_order'),
    path('manufacturing/orders/<int:order_id>/start/', views.start_production, name='start_production'),
    path('manufacturing/orders/<int:order_id>/complete/', views.complete_production, name='complete_production'),
    path('manufacturing/orders/<int:order_id>/change-status/', views.change_manufacturing_order_status, name='change_manufacturing_order_status'),
    path('manufacturing/report/', views.manufacturing_report, name='manufacturing_report'),
    
    # API endpoints
    path('api/finished-products/', views.get_finished_products, name='api_finished_products'),
    path('api/raw-materials/', views.get_raw_materials, name='api_raw_materials'),
    path('api/get_material_cost/', views.get_material_cost, name='api_get_material_cost'),
    path('api/item-cost/', api_views.item_cost, name='api_item_cost'),
    
    # ========== API endpoints للمواد الخام في التصنيع ==========
    path('api/warehouse-items/', api_views.warehouse_items, name='api_warehouse_items'),
    path('api/warehouse-raw-materials/<int:warehouse_id>/', api_views.get_warehouse_raw_materials, name='api_warehouse_raw_materials'),
    path('api/all-warehouse-materials/', api_views.get_all_warehouse_materials, name='api_all_warehouse_materials'),
    path('api/material-details/<int:item_id>/<int:warehouse_id>/', api_views.get_material_details, name='api_material_details'),
    path('api/item-price/<int:item_id>/', api_views.get_item_price, name='api_item_price'),
    path('api/items-with-prices/', api_views.get_all_items_with_prices, name='api_items_with_prices'),
    
    # ========== إدارة المخزون ==========
    # أذون إضافة الزيادات
    path('stock-increase/', views.stock_increase_list, name='stock_increase_list'),
    path('stock-increase/create/', views.stock_increase_create, name='stock_increase_create'),
    path('stock-increase/<int:pk>/', views.stock_increase_detail, name='stock_increase_detail'),
    
    # أذون صرف النواقص
    path('stock-decrease/', views.stock_decrease_list, name='stock_decrease_list'),
    path('stock-decrease/create/', views.stock_decrease_create, name='stock_decrease_create'),
    path('stock-decrease/<int:pk>/', views.stock_decrease_detail, name='stock_decrease_detail'),
    
    # بضاعة مضافة سلفة من الغير
    path('goods-received-on-loan/', views.goods_received_on_loan_list, name='goods_received_on_loan_list'),
    path('goods-received-on-loan/create/', views.goods_received_on_loan_create, name='goods_received_on_loan_create'),
    path('goods-received-on-loan/<int:pk>/', views.goods_received_on_loan_detail, name='goods_received_on_loan_detail'),
    
    # بضاعة منصرفة سلفة لدى الغير
    path('goods-issued-on-loan/', views.goods_issued_on_loan_list, name='goods_issued_on_loan_list'),
    path('goods-issued-on-loan/create/', views.goods_issued_on_loan_create, name='goods_issued_on_loan_create'),
    path('goods-issued-on-loan/<int:pk>/', views.goods_issued_on_loan_detail, name='goods_issued_on_loan_detail'),
    
    # تحويلات بين المخازن
    path('warehouse-transfer/', views.warehouse_transfer_list, name='warehouse_transfer_list'),
    path('warehouse-transfer/create/', views.warehouse_transfer_create, name='warehouse_transfer_create'),
    path('warehouse-transfer/<int:pk>/', views.warehouse_transfer_detail, name='warehouse_transfer_detail'),

    # Professional Materials API
    path('api/materials/save/', api_views.save_materials_data, name='api_save_materials'),
    
    # Warehouse Dashboard
    path('warehouse-dashboard/', warehouse_dashboard, name='warehouse_dashboard'),
    path('warehouse-analytics/', views.warehouse_analytics, name='warehouse_analytics'),

    path('stock-increase/<int:pk>/edit/', views.stock_increase_edit, name='stock_increase_edit'),
    path('stock-increase/<int:pk>/approve/', views.stock_increase_approve, name='stock_increase_approve'),
    path('stock-increase/<int:pk>/apply/', views.stock_increase_apply, name='stock_increase_apply'),
    path('stock-increase/<int:pk>/delete/', views.stock_increase_delete, name='stock_increase_delete'),

    path('stock-decrease/<int:pk>/edit/', views.stock_decrease_edit, name='stock_decrease_edit'),
    path('stock-decrease/<int:pk>/approve/', views.stock_decrease_approve, name='stock_decrease_approve'),
    path('stock-decrease/<int:pk>/apply/', views.stock_decrease_apply, name='stock_decrease_apply'),
    path('stock-decrease/<int:pk>/delete/', views.stock_decrease_delete, name='stock_decrease_delete'),

    path('goods-received-on-loan/<int:pk>/edit/', views.goods_received_on_loan_edit, name='goods_received_on_loan_edit'),
    path('goods-received-on-loan/<int:pk>/return/', views.goods_received_on_loan_return, name='goods_received_on_loan_return'),
    path('goods-received-on-loan/<int:pk>/partial-return/', views.goods_received_on_loan_partial_return, name='goods_received_on_loan_partial_return'),
    path('goods-received-on-loan/<int:pk>/cancel/', views.goods_received_on_loan_cancel, name='goods_received_on_loan_cancel'),
    path('goods-received-on-loan/<int:pk>/delete/', views.goods_received_on_loan_delete, name='goods_received_on_loan_delete'),

    path('goods-issued-on-loan/<int:pk>/edit/', views.goods_issued_on_loan_edit, name='goods_issued_on_loan_edit'),
    path('goods-issued-on-loan/<int:pk>/return/', views.goods_issued_on_loan_return, name='goods_issued_on_loan_return'),
    path('goods-issued-on-loan/<int:pk>/partial-return/', views.goods_issued_on_loan_partial_return, name='goods_issued_on_loan_partial_return'),
    path('goods-issued-on-loan/<int:pk>/cancel/', views.goods_issued_on_loan_cancel, name='goods_issued_on_loan_cancel'),
    path('goods-issued-on-loan/<int:pk>/delete/', views.goods_issued_on_loan_delete, name='goods_issued_on_loan_delete'),

    path('warehouse-transfer/<int:pk>/edit/', views.warehouse_transfer_edit, name='warehouse_transfer_edit'),
    path('warehouse-transfer/<int:pk>/approve/', views.warehouse_transfer_approve, name='warehouse_transfer_approve'),
    path('warehouse-transfer/<int:pk>/ship/', views.warehouse_transfer_ship, name='warehouse_transfer_ship'),
    path('warehouse-transfer/<int:pk>/receive/', views.warehouse_transfer_receive, name='warehouse_transfer_receive'),
    path('warehouse-transfer/<int:pk>/cancel/', views.warehouse_transfer_cancel, name='warehouse_transfer_cancel'),
    path('warehouse-transfer/<int:pk>/delete/', views.warehouse_transfer_delete, name='warehouse_transfer_delete'),

    path('item-transformation/', views.item_transformation_list, name='item_transformation_list'),
    path('item-transformation/create/', views.item_transformation_create, name='item_transformation_create'),
    path('item-transformation/<int:pk>/', views.item_transformation_detail, name='item_transformation_detail'),
    path('item-transformation/<int:pk>/edit/', views.item_transformation_edit, name='item_transformation_edit'),
    path('item-transformation/<int:pk>/approve/', views.item_transformation_approve, name='item_transformation_approve'),
    path('item-transformation/<int:pk>/apply/', views.item_transformation_complete, name='item_transformation_apply'),
    path('item-transformation/<int:pk>/cancel/', views.item_transformation_cancel, name='item_transformation_cancel'),
    path('item-transformation/<int:pk>/delete/', views.item_transformation_delete, name='item_transformation_delete'),

    path('warehouse-stock/<int:warehouse_id>/', views.warehouse_stock_view, name='warehouse_stock'),

    path('warehouse/<int:pk>/', views.warehouse_detail, name='warehouse_detail'),

    path('api/material-data/', api_views.material_data, name='api_material_data'),
    path('api/update-manufacturing-availability/<int:order_id>/', api_views.update_manufacturing_order_availability, name='api_update_manufacturing_availability'),
]
