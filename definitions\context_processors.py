from .models import CompanySettings


def company_settings(request):
    """Context processor لإعدادات الشركة"""
    try:
        settings = CompanySettings.get_settings()
        return {
            'company_settings': settings,
            'company_name': settings.company_name,
            'company_subtitle': settings.company_subtitle,
            'company_logo': settings.company_logo,
            'header_background_color': settings.header_background_color,
            'show_logo_in_header': settings.show_logo_in_header,
            'show_company_name_in_header': settings.show_company_name_in_header,
            'show_subtitle_in_header': settings.show_subtitle_in_header,
        }
    except Exception:
        # في حالة عدم وجود إعدادات أو حدوث خطأ
        return {
            'company_settings': None,
            'company_name': 'حسابات أوساريك',
            'company_subtitle': 'نظام إدارة الحسابات المتكامل والاحترافي',
            'company_logo': None,
            'header_background_color': '#667eea',
            'show_logo_in_header': True,
            'show_company_name_in_header': True,
            'show_subtitle_in_header': True,
        }
