{% extends 'base/base.html' %}
{% load static %}

{% block title %}إنشاء أمر تصنيع{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="{% static 'css/manufacturing_form_enhanced.css' %}" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-industry text-primary me-2"></i>
            إنشاء أمر تصنيع جديد
        </h1>
        <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i> العودة للقائمة
        </a>
    </div>
    <form method="post" id="manufacturing-form" action="{% url 'inventory:manufacturing_order_create' %}">
        {% csrf_token %}
        <!-- بيانات أساسية -->
        <div class="form-section">
            <div class="form-section-header"><h5>بيانات المنتج النهائي</h5></div>
            <div class="form-section-body row g-4">
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.finished_product.label }} <span class="text-danger">*</span></label>
                    {{ form.finished_product }}
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.finished_goods_warehouse.label }} <span class="text-danger">*</span></label>
                    {{ form.finished_goods_warehouse }}
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.quantity_to_produce.label }} <span class="text-danger">*</span></label>
                    {{ form.quantity_to_produce }}
                    <div id="materials-availability-warning" class="alert alert-warning mt-2" style="display: none;"></div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.expected_start_date.label }}</label>
                    {{ form.expected_start_date }}
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.expected_completion_date.label }}</label>
                    {{ form.expected_completion_date }}
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ form.production_line.label }}</label>
                    {{ form.production_line }}
                </div>
            </div>
        </div>
        <!-- المواد الخام -->
        <div class="form-section">
            <div class="form-section-header"><h5>المواد الخام المطلوبة</h5></div>
            <div class="form-section-body">
                {{ materials_formset.management_form }}
                {% for form in materials_formset %}
                    <div class="material-input-row row g-3 align-items-end mb-2">
                        <div class="col-md-5">
                            {{ form.material.label_tag }}
                            {{ form.material }}
                        </div>
                        <div class="col-md-3">
                            {{ form.required_quantity.label_tag }}
                            {{ form.required_quantity }}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المتوفر</label>
                            <input type="text" class="form-control" value="{{ form.available_quantity.value }}" readonly>
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-outline-danger delete-material">حذف</button>
                        </div>
                    </div>
                {% endfor %}
                <button type="button" class="btn add-btn mt-2" id="add-material-btn">إضافة مادة</button>
            </div>
        </div>
        <!-- خطوات التصنيع -->
        <div class="form-section">
            <div class="form-section-header"><h5>خطوات التصنيع</h5></div>
            <div class="form-section-body">
                {{ steps_formset.management_form }}
                {% for form in steps_formset %}
                    <div class="step-row row g-3 align-items-end mb-2">
                        <div class="col-md-10">{{ form.description.label_tag }}{{ form.description }}</div>
                        <div class="col-md-2"><button type="button" class="btn btn-outline-danger delete-step">حذف</button></div>
                    </div>
                {% endfor %}
                <button type="button" class="btn add-btn mt-2" id="add-step-btn">إضافة خطوة</button>
            </div>
        </div>
        <!-- اختبارات الجودة -->
        <div class="form-section">
            <div class="form-section-header"><h5>اختبارات الجودة</h5></div>
            <div class="form-section-body">
                {{ quality_formset.management_form }}
                {% for form in quality_formset %}
                    <div class="quality-row row g-3 align-items-end mb-2">
                        <div class="col-md-10">{{ form.description.label_tag }}{{ form.description }}</div>
                        <div class="col-md-2"><button type="button" class="btn btn-outline-danger delete-quality">حذف</button></div>
                    </div>
                {% endfor %}
                <button type="button" class="btn add-btn mt-2" id="add-quality-btn">إضافة اختبار</button>
            </div>
        </div>
        <!-- ملاحظات -->
        <div class="form-section">
            <div class="form-section-header"><h5>ملاحظات إضافية</h5></div>
            <div class="form-section-body">
                {{ form.notes }}
            </div>
        </div>
        <div class="text-center my-4">
            <button type="submit" class="btn btn-success btn-lg px-5">حفظ أمر التصنيع</button>
        </div>
    </form>
</div>
{% endblock %} 