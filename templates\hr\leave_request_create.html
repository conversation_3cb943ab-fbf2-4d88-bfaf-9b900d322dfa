{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إنشاء طلب إجازة جديد</p>
                </div>
                <div>
                    <a href="{% url 'hr:leave_request_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة الطلبات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات طلب الإجازة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                    <select name="employee" class="form-select" required>
                                        <option value="">اختر الموظف</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.employee_number }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الإجازة <span class="text-danger">*</span></label>
                                    <select name="leave_type" class="form-select" required>
                                        <option value="">اختر نوع الإجازة</option>
                                        {% for leave_type in leave_types %}
                                        <option value="{{ leave_type.id }}">{{ leave_type.name }} ({{ leave_type.allowed_days }} يوم)</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ <span class="text-danger">*</span></label>
                                    <input type="date" name="from_date" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ <span class="text-danger">*</span></label>
                                    <input type="date" name="to_date" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">سبب الإجازة <span class="text-danger">*</span></label>
                            <textarea name="reason" class="form-control" rows="4" required placeholder="اكتب سبب الإجازة بالتفصيل"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_emergency" id="is_emergency">
                                        <label class="form-check-label" for="is_emergency">
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            إجازة طارئة
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مرفقات (اختياري)</label>
                                    <input type="file" name="attachment" class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <small class="text-muted">يمكن رفع ملفات PDF, Word, أو صور</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سيتم إرسال الطلب للمراجعة والاعتماد من قبل المدير المباشر.
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'hr:leave_request_list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                إرسال الطلب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب عدد الأيام تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const fromDateInput = document.querySelector('input[name="from_date"]');
    const toDateInput = document.querySelector('input[name="to_date"]');
    
    function calculateDays() {
        if (fromDateInput.value && toDateInput.value) {
            const fromDate = new Date(fromDateInput.value);
            const toDate = new Date(toDateInput.value);
            
            if (fromDate > toDate) {
                alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                toDateInput.value = '';
                return;
            }
            
            const diffTime = Math.abs(toDate - fromDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            
            // إظهار عدد الأيام
            const daysDisplay = document.getElementById('days-display');
            if (!daysDisplay) {
                const display = document.createElement('div');
                display.id = 'days-display';
                display.className = 'alert alert-info mt-2';
                toDateInput.parentNode.appendChild(display);
            }
            document.getElementById('days-display').innerHTML = `<i class="fas fa-calendar me-2"></i>عدد أيام الإجازة: <strong>${diffDays} يوم</strong>`;
        }
    }
    
    fromDateInput.addEventListener('change', calculateDays);
    toDateInput.addEventListener('change', calculateDays);
    
    // تعيين الحد الأدنى للتاريخ كاليوم الحالي
    const today = new Date().toISOString().split('T')[0];
    fromDateInput.min = today;
    toDateInput.min = today;
});
</script>
{% endblock %} 