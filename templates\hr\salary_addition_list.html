{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رسائل النظام -->
    {% if messages %}
      <div class="row">
        <div class="col-12">
          {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
            </div>
          {% endfor %}
        </div>
      </div>
    {% endif %}
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-plus text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة الإضافات والبدلات للموظفين</p>
                </div>
                <div>
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAdditionModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Addition Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm">
                <div class="card-body text-center">
                    <h3>{{ total_allowances|floatformat:2 }} ج.م</h3>
                    <p class="mb-0">إجمالي الإضافات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm">
                <div class="card-body text-center">
                    <h3>{{ total_employees }}</h3>
                    <p class="mb-0">عدد الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm">
                <div class="card-body text-center">
                    <h3>{{ avg_allowance|floatformat:2 }} ج.م</h3>
                    <p class="mb-0">متوسط الإضافة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white shadow-sm">
                <div class="card-body text-center">
                    <h3>{{ current_month }}</h3>
                    <p class="mb-0">الشهر الحالي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة الإضافات للمرتبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>نوع الإضافة</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for addition in addition_data %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ addition.employee_name }}</td>
                                    <td>{{ addition.department }}</td>
                                    <td>{{ addition.type_display }}</td>
                                    <td>{{ addition.amount|floatformat:2 }} ج.م</td>
                                    <td>{{ addition.start_date }}</td>
                                    <td>{{ addition.end_date|default:'-' }}</td>
                                    <td>
                                        {% if addition.is_current %}
                                            <span class="badge bg-success">سارية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">منتهية</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <!-- يمكن إضافة أزرار تعديل/حذف هنا لاحقاً -->
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-5">
                                        <i class="fas fa-plus fa-3x mb-3 d-block text-muted"></i>
                                        <h5>لا توجد إضافات مسجلة</h5>
                                        <p class="mb-0">لم يتم تسجيل أي إضافات للمرتبات بعد</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Addition Types Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        ملخص أنواع الإضافات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for t in addition_types_summary %}
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h5 class="mb-2">{{ t.total|floatformat:2 }} ج.م</h5>
                                <p class="mb-0 text-muted">{{ t.type_display }}</p>
                                <small class="text-muted">{{ t.count }} إضافة</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه الإضافات الشهرية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">إجمالي الإضافات الشهرية</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-success" style="width: 75%">
                                    {{ total_allowances|floatformat:0 }} ج.م
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">نسبة الإضافات من الراتب الأساسي</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-primary" style="width: 60%">
                                    {{ allowance_percentage|floatformat:1 }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Addition Modal -->
<div class="modal fade" id="addAdditionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة جديدة للمرتب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="">
                    {% csrf_token %}
                    <div class="row g-3">
                        <div class="col-12">
                            {{ form.as_p }}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            حفظ الإضافة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function printSalaryReport(employeeId) {
    // يمكن إضافة وظيفة الطباعة هنا
    alert('سيتم طباعة تقرير المرتب للموظف رقم: ' + employeeId);
}
</script>
{% endblock %}
