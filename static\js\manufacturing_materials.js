/**
 * JavaScript للوظائف التفاعلية في نموذج المواد الخام للتصنيع
 */

$(document).ready(function() {
    // تهيئة الوظائف عند تحميل الصفحة
    initializeManufacturingMaterials();
    
    // إعادة تهيئة الوظائف عند إضافة صف جديد
    $(document).on('formset:added', function(event, $row, formsetName) {
        if (formsetName === 'manufacturing_materials') {
            initializeRow($row);
        }
    });
});

function initializeManufacturingMaterials() {
    // تهيئة جميع الصفوف الموجودة
    $('.manufacturing-material-form').each(function() {
        initializeRow($(this));
    });
}

function initializeRow($row) {
    // الحصول على العناصر في الصف
    const $warehouseSelect = $row.find('.warehouse-select');
    const $materialSelect = $row.find('.material-select');
    const $availableQuantityInput = $row.find('.available-quantity-input');
    const $quantityRequiredInput = $row.find('.quantity-input');
    const $unitCostInput = $row.find('.cost-input');
    const $totalCostInput = $row.find('.total-cost-input');
    
    // إعادة تعيين قائمة المواد عند تغيير المخزن
    $warehouseSelect.on('change', function() {
        const warehouseId = $(this).val();
        updateMaterialsList($materialSelect, warehouseId);
        clearMaterialData($row);
    });
    
    // تحديث البيانات عند اختيار المادة
    $materialSelect.on('change', function() {
        const materialId = $(this).val();
        const warehouseId = $warehouseSelect.val();
        if (materialId && warehouseId) {
            updateMaterialData($row, warehouseId, materialId);
        }
    });
    
    // حساب إجمالي التكلفة عند تغيير الكمية أو السعر
    $quantityRequiredInput.on('input', function() {
        calculateTotalCost($row);
    });
    
    $unitCostInput.on('input', function() {
        calculateTotalCost($row);
    });
    
    // تحديث سعر الوحدة عند تغيير المادة
    $materialSelect.on('change', function() {
        const materialId = $(this).val();
        if (materialId) {
            updateUnitCost($unitCostInput, materialId);
        }
    });
}

function updateMaterialsList($materialSelect, warehouseId) {
    if (!warehouseId) {
        $materialSelect.html('<option value="">اختر المادة الخام</option>');
        return;
    }
    
    // إرسال طلب AJAX للحصول على المواد الموجودة في المخزن
    $.ajax({
        url: '/inventory/api/warehouse-items/',
        method: 'GET',
        data: {
            warehouse_id: warehouseId,
            item_type: 'RAW_MATERIAL'
        },
        success: function(data) {
            let options = '<option value="">اختر المادة الخام</option>';
            data.items.forEach(function(item) {
                options += `<option value="${item.id}">${item.name} (${item.code})</option>`;
            });
            $materialSelect.html(options);
        },
        error: function() {
            console.error('خطأ في جلب المواد من المخزن');
            $materialSelect.html('<option value="">خطأ في جلب البيانات</option>');
        }
    });
}

function updateMaterialData($row, warehouseId, materialId) {
    const $availableQuantityInput = $row.find('.available-quantity-input');
    const $unitCostInput = $row.find('.cost-input');
    
    // إرسال طلب AJAX للحصول على بيانات المادة
    $.ajax({
        url: '/inventory/api/material-data/',
        method: 'GET',
        data: {
            warehouse_id: warehouseId,
            material_id: materialId
        },
        success: function(data) {
            // تحديث الكمية المتوفرة
            $availableQuantityInput.val(data.available_quantity);
            
            // تحديث سعر الوحدة
            $unitCostInput.val(data.unit_cost);
            
            // إعادة حساب إجمالي التكلفة
            calculateTotalCost($row);
        },
        error: function() {
            console.error('خطأ في جلب بيانات المادة');
            $availableQuantityInput.val('0');
            $unitCostInput.val('0');
        }
    });
}

function updateUnitCost($unitCostInput, materialId) {
    // إرسال طلب AJAX للحصول على سعر المادة
    $.ajax({
        url: '/inventory/api/item-cost/',
        method: 'GET',
        data: {
            item_id: materialId
        },
        success: function(data) {
            $unitCostInput.val(data.cost_price);
        },
        error: function() {
            console.error('خطأ في جلب سعر المادة');
            $unitCostInput.val('0');
        }
    });
}

function calculateTotalCost($row) {
    const $quantityRequiredInput = $row.find('.quantity-input');
    const $unitCostInput = $row.find('.cost-input');
    const $totalCostInput = $row.find('.total-cost-input');
    
    const quantity = parseFloat($quantityRequiredInput.val()) || 0;
    const unitCost = parseFloat($unitCostInput.val()) || 0;
    const totalCost = quantity * unitCost;
    
    $totalCostInput.val(totalCost.toFixed(2));
}

function clearMaterialData($row) {
    const $materialSelect = $row.find('.material-select');
    const $availableQuantityInput = $row.find('.available-quantity-input');
    const $quantityRequiredInput = $row.find('.quantity-input');
    const $unitCostInput = $row.find('.cost-input');
    const $totalCostInput = $row.find('.total-cost-input');
    
    $materialSelect.val('');
    $availableQuantityInput.val('0');
    $quantityRequiredInput.val('');
    $unitCostInput.val('0');
    $totalCostInput.val('0');
}

// دالة للتحقق من كفاية الكمية
function checkQuantitySufficiency($row) {
    const $availableQuantityInput = $row.find('.available-quantity-input');
    const $quantityRequiredInput = $row.find('.quantity-input');
    const $rowElement = $row.closest('tr');
    
    const available = parseFloat($availableQuantityInput.val()) || 0;
    const required = parseFloat($quantityRequiredInput.val()) || 0;
    
    if (required > available) {
        $rowElement.addClass('table-warning');
        $rowElement.find('.quantity-input').addClass('is-invalid');
        
        // إضافة رسالة تحذير
        let $warningMsg = $rowElement.find('.quantity-warning');
        if ($warningMsg.length === 0) {
            $warningMsg = $('<div class="invalid-feedback quantity-warning">الكمية المطلوبة أكبر من الكمية المتوفرة</div>');
            $rowElement.find('.quantity-input').after($warningMsg);
        }
        $warningMsg.show();
    } else {
        $rowElement.removeClass('table-warning');
        $rowElement.find('.quantity-input').removeClass('is-invalid');
        $rowElement.find('.quantity-warning').hide();
    }
}

// إضافة مستمع لفحص الكمية
$(document).on('input', '.quantity-input', function() {
    checkQuantitySufficiency($(this).closest('.manufacturing-material-form'));
});

// دالة لحساب إجمالي تكلفة جميع المواد
function calculateTotalMaterialsCost() {
    let totalCost = 0;
    $('.total-cost-input').each(function() {
        const cost = parseFloat($(this).val()) || 0;
        totalCost += cost;
    });
    
    // تحديث حقل إجمالي تكلفة المواد في النموذج الرئيسي
    $('#id_total_material_cost').val(totalCost.toFixed(2));
    
    return totalCost;
}

// إضافة مستمع لحساب الإجمالي عند تغيير أي تكلفة
$(document).on('input', '.total-cost-input', function() {
    calculateTotalMaterialsCost();
});

// دالة للتحقق من صحة البيانات قبل الإرسال
function validateManufacturingMaterials() {
    let isValid = true;
    let errorMessages = [];
    
    $('.manufacturing-material-form').each(function(index) {
        const $row = $(this);
        const warehouse = $row.find('.warehouse-select').val();
        const material = $row.find('.material-select').val();
        const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
        const available = parseFloat($row.find('.available-quantity-input').val()) || 0;
        
        if (!warehouse) {
            errorMessages.push(`الصف ${index + 1}: يجب اختيار المخزن`);
            isValid = false;
        }
        
        if (!material) {
            errorMessages.push(`الصف ${index + 1}: يجب اختيار المادة الخام`);
            isValid = false;
        }
        
        if (quantity <= 0) {
            errorMessages.push(`الصف ${index + 1}: يجب إدخال كمية صحيحة`);
            isValid = false;
        }
        
        if (quantity > available) {
            errorMessages.push(`الصف ${index + 1}: الكمية المطلوبة أكبر من الكمية المتوفرة`);
            isValid = false;
        }
    });
    
    if (!isValid) {
        alert('يرجى تصحيح الأخطاء التالية:\n' + errorMessages.join('\n'));
    }
    
    return isValid;
}

// إضافة التحقق عند إرسال النموذج
$('form').on('submit', function(e) {
    if ($(this).find('.manufacturing-material-form').length > 0) {
        if (!validateManufacturingMaterials()) {
            e.preventDefault();
            return false;
        }
    }
}); 