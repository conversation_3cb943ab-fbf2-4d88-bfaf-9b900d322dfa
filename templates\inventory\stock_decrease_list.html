{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-minus-circle text-danger me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:stock_decrease_create' %}" class="btn btn-danger">
                    <i class="fas fa-plus me-1"></i>
                    إضافة إذن نقص جديد
                </a>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                    <i class="fas fa-list fa-2x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ total_decreases }}</h4>
                            <p class="card-text text-muted">إجمالي الأذون</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ pending_decreases }}</h4>
                            <p class="card-text text-muted">في الانتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                    <i class="fas fa-check fa-2x text-info"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ approved_decreases }}</h4>
                            <p class="card-text text-muted">معتمدة</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <div class="rounded-circle bg-danger bg-opacity-10 p-3">
                                    <i class="fas fa-check-double fa-2x text-danger"></i>
                                </div>
                            </div>
                            <h4 class="card-title">{{ applied_decreases }}</h4>
                            <p class="card-text text-muted">مطبقة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            {{ form.search.label_tag }}
                            {{ form.search }}
                        </div>
                        <div class="col-md-2">
                            {{ form.warehouse.label_tag }}
                            {{ form.warehouse }}
                        </div>
                        <div class="col-md-2">
                            {{ form.status.label_tag }}
                            {{ form.status }}
                        </div>
                        <div class="col-md-2">
                            {{ form.date_from.label_tag }}
                            {{ form.date_from }}
                        </div>
                        <div class="col-md-2">
                            {{ form.date_to.label_tag }}
                            {{ form.date_to }}
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search"></i>
                            </button>
                            <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الأذون -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة أذون صرف النواقص
                        <span class="badge bg-danger ms-2">{{ page_obj.paginator.count }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الإذن</th>
                                        <th>التاريخ</th>
                                        <th>المخزن</th>
                                        <th>السبب</th>
                                        <th>إجمالي المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for decrease in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ decrease.decrease_number }}</strong>
                                            </td>
                                            <td>{{ decrease.date|date:"d/m/Y" }}</td>
                                            <td>{{ decrease.warehouse.name }}</td>
                                            <td>{{ decrease.reason|truncatechars:50 }}</td>
                                            <td>
                                                <strong class="text-danger">{{ decrease.total_amount|floatformat:2 }} ج.م</strong>
                                            </td>
                                            <td>
                                                {% if decrease.status == 'DRAFT' %}
                                                    <span class="badge bg-secondary">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'APPROVED' %}
                                                    <span class="badge bg-info">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'APPLIED' %}
                                                    <span class="badge bg-danger">{{ decrease.get_status_display }}</span>
                                                {% elif decrease.status == 'CANCELLED' %}
                                                    <span class="badge bg-dark">{{ decrease.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'inventory:stock_decrease_detail' decrease.pk %}" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if decrease.status == 'DRAFT' %}
                                                        <a href="{% url 'inventory:stock_decrease_edit' decrease.pk %}" 
                                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="approveDecrease({{ decrease.pk }})" title="اعتماد">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="deleteDecrease({{ decrease.pk }}, '{{ decrease.decrease_number }}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    {% elif decrease.status == 'APPROVED' %}
                                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                onclick="applyDecrease({{ decrease.pk }})" title="تطبيق على المخزون">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-minus-circle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أذون نواقص</h5>
                            <p class="text-muted">ابدأ بإضافة إذن نقص جديد</p>
                            <a href="{% url 'inventory:stock_decrease_create' %}" class="btn btn-danger">
                                <i class="fas fa-plus me-1"></i>
                                إضافة إذن نقص جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function approveDecrease(decreaseId) {
    if (confirm('هل تريد اعتماد هذا الإذن؟')) {
        fetch(`/inventory/stock-decrease/${decreaseId}/approve/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الاعتماد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الاعتماد');
        });
    }
}

function applyDecrease(decreaseId) {
    if (confirm('هل تريد تطبيق هذا الإذن على المخزون؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/inventory/stock-decrease/${decreaseId}/apply/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء التطبيق');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء التطبيق');
        });
    }
}

function deleteDecrease(decreaseId, decreaseNumber) {
    if (confirm(`هل أنت متأكد من حذف إذن النقص "${decreaseNumber}"؟`)) {
        fetch(`/inventory/stock-decrease/${decreaseId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}
</script>
{% endblock %}
