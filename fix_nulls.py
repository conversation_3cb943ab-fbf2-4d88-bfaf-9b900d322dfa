#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import connection

cursor = connection.cursor()

print("Fixing NULL values in cost columns...")

# Update NULL values to 0 for cost columns
try:
    cursor.execute("UPDATE inventory_manufacturingorder SET total_material_cost = 0.00 WHERE total_material_cost IS NULL")
    print("+ Fixed total_material_cost NULL values")
except Exception as e:
    print(f"x Error fixing total_material_cost: {e}")

try:
    cursor.execute("UPDATE inventory_manufacturingorder SET total_production_cost = 0.00 WHERE total_production_cost IS NULL")
    print("+ Fixed total_production_cost NULL values")
except Exception as e:
    print(f"x Error fixing total_production_cost: {e}")

try:
    cursor.execute("UPDATE inventory_manufacturingorder SET production_cost_per_unit = 0.00 WHERE production_cost_per_unit IS NULL")
    print("+ Fixed production_cost_per_unit NULL values")
except Exception as e:
    print(f"x Error fixing production_cost_per_unit: {e}")

print("Fix completed!")
