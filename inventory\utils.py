"""
أدوات مساعدة لإدارة المخزون والتصنيع
"""
from django.db import transaction
from django.utils import timezone
from .models import Stock, StockMovement, StockMovementItem, StockMovementType
from definitions.models import Item, Warehouse
import uuid


def get_stock_quantity(warehouse, item):
    """الحصول على كمية الصنف في المخزن"""
    try:
        stock = Stock.objects.get(warehouse=warehouse, item=item)
        return stock.available_quantity
    except Stock.DoesNotExist:
        return 0


def add_stock_to_warehouse(warehouse, item, quantity, unit_cost, reference, movement_type='IN'):
    """إضافة صنف إلى المخزن"""
    with transaction.atomic():
        # إنشاء رقم مرجع فريد لتجنب التكرار
        unique_reference = f"{reference}_{timezone.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # إنشاء حركة مخزون
        movement = StockMovement.objects.create(
            movement_type=movement_type,
            reference_number=unique_reference,
            date=timezone.now().date(),
            warehouse=warehouse,
            notes=f"إضافة {quantity} {item.unit.name} من {item.name}",
            total_amount=quantity * unit_cost
        )
        
        # إضافة الصنف للحركة
        StockMovementItem.objects.create(
            movement=movement,
            item=item,
            quantity=quantity,
            unit_cost=unit_cost,
            total_cost=quantity * unit_cost
        )
        
        # تحديث أو إنشاء رصيد المخزون
        stock, created = Stock.objects.get_or_create(
            warehouse=warehouse,
            item=item,
            defaults={
                'quantity': quantity,
                'available_quantity': quantity,
                'average_cost': unit_cost,
                'last_movement_date': timezone.now()
            }
        )
        
        if not created:
            # حساب متوسط التكلفة الجديد
            total_value = (stock.quantity * stock.average_cost) + (quantity * unit_cost)
            stock.quantity += quantity
            stock.available_quantity += quantity
            stock.average_cost = total_value / stock.quantity if stock.quantity > 0 else 0
            stock.last_movement_date = timezone.now()
            stock.save()


def reduce_stock_from_warehouse(warehouse, item, quantity, reference, movement_type='OUT'):
    """خصم صنف من المخزن"""
    with transaction.atomic():
        # التحقق من توفر الكمية
        available_quantity = get_stock_quantity(warehouse, item)
        if available_quantity < quantity:
            raise ValueError(f"الكمية المتاحة ({available_quantity}) أقل من المطلوب ({quantity})")
        
        # إنشاء رقم مرجع فريد لتجنب التكرار
        unique_reference = f"{reference}_{timezone.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # إنشاء حركة مخزون
        movement = StockMovement.objects.create(
            movement_type=movement_type,
            reference_number=unique_reference,
            date=timezone.now().date(),
            warehouse=warehouse,
            notes=f"خصم {quantity} {item.unit.name} من {item.name}",
            total_amount=quantity * item.cost_price
        )
        
        # إضافة الصنف للحركة
        StockMovementItem.objects.create(
            movement=movement,
            item=item,
            quantity=quantity,
            unit_cost=item.cost_price,
            total_cost=quantity * item.cost_price
        )
        
        # تحديث رصيد المخزون
        stock = Stock.objects.get(warehouse=warehouse, item=item)
        stock.quantity -= quantity
        stock.available_quantity -= quantity
        stock.last_movement_date = timezone.now()
        stock.save()


def transfer_stock_between_warehouses(from_warehouse, to_warehouse, item, quantity, reference):
    """تحويل صنف بين مخزنين"""
    with transaction.atomic():
        # خصم من المخزن المصدر
        reduce_stock_from_warehouse(from_warehouse, item, quantity, f"{reference} - خصم")
        
        # إضافة إلى المخزن الهدف
        add_stock_to_warehouse(to_warehouse, item, quantity, item.cost_price, f"{reference} - إضافة")


def get_low_stock_items(warehouse=None, threshold_percentage=20):
    """الحصول على الأصناف منخفضة المخزون"""
    stocks = Stock.objects.filter(available_quantity__gt=0)
    
    if warehouse:
        stocks = stocks.filter(warehouse=warehouse)
    
    low_stock_items = []
    for stock in stocks:
        if stock.item.min_stock > 0:
            percentage = (stock.available_quantity / stock.item.min_stock) * 100
            if percentage <= threshold_percentage:
                low_stock_items.append({
                    'item': stock.item,
                    'warehouse': stock.warehouse,
                    'available_quantity': stock.available_quantity,
                    'min_stock': stock.item.min_stock,
                    'percentage': percentage
                })
    
    return low_stock_items


def calculate_material_requirements(finished_product, quantity):
    """حساب متطلبات المواد الخام للمنتج النهائي"""
    # هذا يمكن أن يكون أكثر تعقيداً حسب هيكل المنتج
    # حالياً نرجع قائمة فارغة - يمكن تطويرها لاحقاً
    return []


def validate_manufacturing_order(order):
    """التحقق من صحة أمر التصنيع"""
    errors = []
    
    # التحقق من توفر المواد الخام - فقط إذا كان الأمر محفوظاً
    if order.pk:
        for material in order.materials.all():
            if not material.is_sufficient:
                errors.append(f"الكمية المتاحة من {material.material.name} غير كافية")
    
    # التحقق من التواريخ
    if order.expected_start_date > order.expected_completion_date:
        errors.append("تاريخ البدء المتوقع يجب أن يكون قبل تاريخ الانتهاء المتوقع")
    
    # التحقق من الكمية
    if order.quantity_to_produce <= 0:
        errors.append("الكمية المطلوبة يجب أن تكون أكبر من صفر")
    
    return errors


def generate_manufacturing_report(start_date, end_date, warehouse=None):
    """إنشاء تقرير التصنيع"""
    from .models import ManufacturingOrder
    
    orders = ManufacturingOrder.objects.filter(
        order_date__range=[start_date, end_date]
    )
    
    if warehouse:
        orders = orders.filter(raw_materials_warehouse=warehouse)
    
    report_data = {
        'total_orders': orders.count(),
        'completed_orders': orders.filter(status='COMPLETED').count(),
        'in_progress_orders': orders.filter(status='IN_PROGRESS').count(),
        'total_production_cost': sum(order.total_production_cost for order in orders),
        'total_quantity_produced': sum(order.quantity_to_produce for order in orders.filter(status='COMPLETED')),
        'orders_by_status': {},
        'orders_by_product': {},
        'cost_breakdown': {
            'materials': sum(order.total_material_cost for order in orders),
            'labor': sum(order.labor_cost for order in orders),
            'overhead': sum(order.overhead_cost for order in orders),
            'other': sum(order.other_costs for order in orders),
        }
    }
    
    # تجميع حسب الحالة
    for status, _ in ManufacturingOrder._meta.get_field('status').choices:
        report_data['orders_by_status'][status] = orders.filter(status=status).count()
    
    # تجميع حسب المنتج
    for order in orders:
        product_name = order.finished_product.name
        if product_name not in report_data['orders_by_product']:
            report_data['orders_by_product'][product_name] = {
                'count': 0,
                'total_quantity': 0,
                'total_cost': 0
            }
        report_data['orders_by_product'][product_name]['count'] += 1
        report_data['orders_by_product'][product_name]['total_quantity'] += order.quantity_to_produce
        report_data['orders_by_product'][product_name]['total_cost'] += order.total_production_cost
    
    return report_data 