# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020-2021
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-02 11:59+0000\n"
"Last-Translator: Soyuzbek Orozbek uulu <<EMAIL>>\n"
"Language-Team: Kyrgyz (http://www.transifex.com/django/django/language/ky/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ky\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "Башкармалык түшүндүрмөсү"

msgid "Home"
msgstr "Башкы"

msgid "Documentation"
msgstr "Түшүндүрмө"

msgid "Bookmarklets"
msgstr "Букмарклеттер"

msgid "Documentation bookmarklets"
msgstr "букмарклет түшүндүрмөсү"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Букмарклетти орнотуу үчүн шилтемени кыстырмалар панелине сүйрөңүз же "
"шилтемени оң чыкылдатып андан соң кыстырмаларыңызга кошуп алыңыз. Азыр "
"букмарклетти сайттын бүт бетинен тандап алсаңыз болот."

msgid "Documentation for this page"
msgstr "Бул барактын түшүндүрмөсү"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Бул баракты жараткан көрүнүш үчүн сиз каалаган барактагы түшүндүрмөдөн "
"секирип өтүңүз."

msgid "Tags"
msgstr "Тегдер"

msgid "List of all the template tags and their functions."
msgstr "Бүт калып тегдердин жана алардын кызматынын тизмеси"

msgid "Filters"
msgstr "Чыпкалар"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Чыпкалар чыгуучу агымды өзгөртүү үчүн калыптагы өзгөрмөгө жасалуучу аракет "
"болуп саналат."

msgid "Models"
msgstr "Моделдер"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Моделдер системдеги бүт обекттер жана алардын талаалардын ачыкталышы. Ар бир "
"модел өзүнө талааларды камтыйт. Алар өз учурунда калыптуу өзгөрмөлөр катары "
"жетүүнү камсыз кылат."

msgid "Views"
msgstr "Көрүнүштөр"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Ачык саттагы ар бир барак көрүнүш аркылуу чыгарылган. Көрүнүш баракты "
"чыгаруу үчүн кайсы калып колдонууларын жана кайсы обекттер жеткиликтүү "
"экендигин аныктайт."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Браузер үчүн башкаруу кызматына тез жетүү үчүн куралдар."

msgid "Please install docutils"
msgstr "түшүндүрмө утилитасын орнотуңуз."

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Башкармалыктын түшүндүрмө системи Питондун <a href=\"%(link)s\">docutils</a> "
"китепканасын талап кылат.   "

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Башкаруучудан <a href=\"%(link)s\">түшүндүрмө утилитасын</a>орнотууну "
"сураңыз."

#, python-format
msgid "Model: %(name)s"
msgstr "Модел: %(name)s"

msgid "Fields"
msgstr "Талаалар"

msgid "Field"
msgstr "Талаа"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Ачыктоо"

msgid "Methods with arguments"
msgstr "аргументүү ыкма"

msgid "Method"
msgstr "Ыкма"

msgid "Arguments"
msgstr "Аргумент"

msgid "Back to Model documentation"
msgstr "Модел түшүндүрмөсүнө кайт"

msgid "Model documentation"
msgstr "Модел түшүндүрмөсү"

msgid "Model groups"
msgstr "Модел тайпасы"

msgid "Templates"
msgstr "Калыптар"

#, python-format
msgid "Template: %(name)s"
msgstr "Калып: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Калып: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "<q>%(name)s</q>калыбы үчүн жолду издөө:"

msgid "(does not exist)"
msgstr "(табылган жок)"

msgid "Back to Documentation"
msgstr "Түшүндүрмөгө кайт"

msgid "Template filters"
msgstr "Калып чыпкалары"

msgid "Template filter documentation"
msgstr "Калып чыпкалары түшүндүрмөсү"

msgid "Built-in filters"
msgstr "Бар чыпкалар"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Бул чыпкаларды колдонуудан мурда калыбыңызга <code>%(code)s</code>коюңуз."

msgid "Template tags"
msgstr "Калып тегдер"

msgid "Template tag documentation"
msgstr "Калып тегдер түшүндүрмөсү"

msgid "Built-in tags"
msgstr "Мурдатан бар тегдер"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Тегдерди колдонуш үчүн:  тегди колдонуудан мурда калыбыңызга<code>%(code)s</"
"code>коюңуз."

#, python-format
msgid "View: %(name)s"
msgstr "Көрүнүш: %(name)s"

msgid "Context:"
msgstr "Мазмун:"

msgid "Templates:"
msgstr "Калыптар:"

msgid "Back to View documentation"
msgstr "Көрүнүш түшүндүрмөсүнө кайт"

msgid "View documentation"
msgstr "Көрүнүш түшүндүрмөсү"

msgid "Jump to namespace"
msgstr "ысым мейкиндигине өтүү"

msgid "Empty namespace"
msgstr "бош ысым мейкиндиги"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "%(name)sысым мейкиндиги боюнча көрүү"

msgid "Views by empty namespace"
msgstr "бош ысым мейкиндиги боюнча көрүү"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Көрүнүш функциасы: <code>%(full_name)s</code>.Аты:<code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "тег:"

msgid "filter:"
msgstr "чыпка:"

msgid "view:"
msgstr "көрүнүш:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r колдонмосу табылган жок"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(app_label)r колдонмосунда %(model_name)r модели табылбады"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "байланышкан `%(app_label)s.%(data_type)s` обектиси"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "байланышкан `%(app_label)s.%(object_name)s` обекттери"

#, python-format
msgid "all %s"
msgstr "Бүт %s"

#, python-format
msgid "number of %s"
msgstr "%s саны"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s урлкалып обектисине тиешеси жок"
