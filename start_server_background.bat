@echo off
echo Starting Django Server in Background...
echo.
echo Activating virtual environment...
call venv_new\Scripts\activate
echo.
echo Starting server on http://127.0.0.1:8000/
echo Server is running in background
echo To stop the server, close this window or press Ctrl+C
echo.
start "Django Server" python manage.py runserver 127.0.0.1:8000
echo.
echo ✅ Server started successfully!
echo 🌐 Open your browser and go to: http://127.0.0.1:8000/
echo.
echo Press any key to close this window...
pause >nul 