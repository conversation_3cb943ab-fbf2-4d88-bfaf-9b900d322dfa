{% extends 'base/base.html' %}
{% load static %}

{% block title %}الورديات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <h2 class="mb-0">
                        <i class="fas fa-clock text-info me-2"></i>
                        الورديات
                    </h2>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'hr:shift_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة وردية جديدة
                    </a>
                    <button class="btn btn-outline-success" onclick="exportTableToCSV('shifts.csv')" title="تصدير إلى CSV">
                        <i class="fas fa-file-csv"></i>
                    </button>
                    <button class="btn btn-outline-dark" onclick="window.print()" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- كروت إحصائية -->
    <div class="row mb-4 g-3">
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ total_shifts|default:0 }}</div>
                        <div>إجمالي الورديات</div>
                    </div>
                    <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ active_shifts|default:0 }}</div>
                        <div>الورديات النشطة</div>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ inactive_shifts|default:0 }}</div>
                        <div>غير النشطة</div>
                    </div>
                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm h-100">
                <div class="card-body d-flex align-items-center justify-content-between">
                    <div>
                        <div class="fs-2 fw-bold">{{ types_count|default:0 }}</div>
                        <div>أنواع الورديات</div>
                    </div>
                    <i class="fas fa-layer-group fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- بحث وتصفية -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" class="row g-2 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">بحث بالاسم أو الكود</label>
                    <input type="text" name="q" class="form-control" placeholder="بحث..." value="{{ search_query|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الوردية</label>
                    <select name="shift_type" class="form-select">
                        <option value="">كل الأنواع</option>
                        <option value="MORNING" {% if shift_type_filter == 'MORNING' %}selected{% endif %}>صباحية</option>
                        <option value="EVENING" {% if shift_type_filter == 'EVENING' %}selected{% endif %}>مسائية</option>
                        <option value="NIGHT" {% if shift_type_filter == 'NIGHT' %}selected{% endif %}>ليلية</option>
                        <option value="FLEXIBLE" {% if shift_type_filter == 'FLEXIBLE' %}selected{% endif %}>مرنة</option>
                        <option value="PART_TIME" {% if shift_type_filter == 'PART_TIME' %}selected{% endif %}>دوام جزئي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex gap-2">
                    <button class="btn btn-outline-primary w-100" type="submit"><i class="fas fa-search"></i> بحث</button>
                    <a href="?" class="btn btn-outline-secondary"><i class="fas fa-times"></i></a>
                </div>
            </form>
            <div class="mt-3 d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    يمكنك استخدام أزرار التصدير والطباعة في أعلى الصفحة
                </small>
                <small class="text-muted">
                    <i class="fas fa-filter me-1"></i>
                    النتائج: {{ items|length }} وردية
                </small>
            </div>
        </div>
    </div>

    <!-- جدول الورديات -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="shiftsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>الوردية</th>
                            <th>الوقت</th>
                            <th>النوع</th>
                            <th>المدة</th>
                            <th>الراحة</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ item.name }}</div>
                                        <small class="text-muted">{{ item.code }}</small>
                                        {% if item.description %}
                                            <br><small class="text-muted">{{ item.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="badge bg-primary">{{ item.start_time|time:"H:i" }}</span>
                                    <span class="badge bg-secondary">{{ item.end_time|time:"H:i" }}</span>
                                </div>
                            </td>
                            <td>
                                {% if item.shift_type == 'MORNING' %}
                                    <span class="badge bg-info">صباحية</span>
                                {% elif item.shift_type == 'EVENING' %}
                                    <span class="badge bg-warning">مسائية</span>
                                {% elif item.shift_type == 'NIGHT' %}
                                    <span class="badge bg-dark">ليلية</span>
                                {% elif item.shift_type == 'FLEXIBLE' %}
                                    <span class="badge bg-success">مرنة</span>
                                {% elif item.shift_type == 'PART_TIME' %}
                                    <span class="badge bg-secondary">دوام جزئي</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ item.duration_hours }} ساعة</span>
                            </td>
                            <td>
                                {% if item.include_break %}
                                    <span class="badge bg-success">{{ item.break_duration }} دقيقة</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">لا توجد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ item.created_at|date:"Y/m/d"|default:"غير محدد" }}
                                </small>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'hr:shift_detail' item.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:shift_edit' item.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="حذف" 
                                            onclick="confirmDelete('{{ item.name }}', '{% url 'hr:shift_delete' item.pk %}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center text-muted py-5">
                                <i class="fas fa-clock fa-3x mb-3 text-muted"></i>
                                <h5 class="text-muted">لا توجد ورديات للعرض</h5>
                                <p class="text-muted mb-3">ابدأ بإضافة أول وردية في النظام</p>
                                <a href="{% url 'hr:shift_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة وردية جديدة
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="صفحات الورديات">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&q={{ search_query|default:'' }}&shift_type={{ shift_type_filter|default:'' }}&status={{ status_filter|default:'' }}">
                            <i class="fas fa-chevron-right"></i> السابق
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-right"></i> السابق</span>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}&q={{ search_query|default:'' }}&shift_type={{ shift_type_filter|default:'' }}&status={{ status_filter|default:'' }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&q={{ search_query|default:'' }}&shift_type={{ shift_type_filter|default:'' }}&status={{ status_filter|default:'' }}">
                            التالي <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">التالي <i class="fas fa-chevron-left"></i></span>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<script>
function confirmDelete(shiftName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف الوردية "${shiftName}"؟`)) {
        // إنشاء نموذج حذف مخفي
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        
        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function exportTableToCSV(filename) {
    var csv = [];
    var rows = document.querySelectorAll("#shiftsTable tr");
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll("td, th");
        
        for (var j = 0; j < cols.length; j++) {
            // إزالة HTML tags والحصول على النص فقط
            var text = cols[j].innerText.replace(/\s+/g, ' ').trim();
            row.push('"' + text + '"');
        }
        
        csv.push(row.join(","));
    }

    // تحميل الملف
    var csvFile = new Blob([csv.join("\n")], {type: "text/csv"});
    var downloadLink = document.createElement("a");
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
}

// تفعيل البحث المباشر
document.addEventListener('DOMContentLoaded', function() {
    var searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    }
});
</script>
{% endblock %}
