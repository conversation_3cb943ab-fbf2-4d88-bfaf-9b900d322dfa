// Modern Components JavaScript

// Utility Functions
const ModernUtils = {
    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Format currency
    formatCurrency(amount, currency = 'ج.م') {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        }).format(amount);
    },

    // Format date
    formatDate(date) {
        return new Intl.DateTimeFormat('ar-EG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },

    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert-modern alert-modern-${type} fixed top-4 right-4 z-50 max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} ml-2"></i>
                <span class="flex-1">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // Confirm dialog
    async confirm(message, title = 'تأكيد') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-modern';
            modal.innerHTML = `
                <div class="modal-modern-content">
                    <div class="modal-modern-header">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                        <button onclick="this.closest('.modal-modern').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-modern-body">
                        <p class="text-gray-700 dark:text-gray-300">${message}</p>
                    </div>
                    <div class="modal-modern-footer">
                        <button onclick="this.closest('.modal-modern').remove(); window.confirmResult = false" 
                                class="btn-modern btn-modern-secondary">
                            إلغاء
                        </button>
                        <button onclick="this.closest('.modal-modern').remove(); window.confirmResult = true" 
                                class="btn-modern btn-modern-primary">
                            تأكيد
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Handle result
            const checkResult = setInterval(() => {
                if (window.confirmResult !== undefined) {
                    clearInterval(checkResult);
                    resolve(window.confirmResult);
                    delete window.confirmResult;
                }
            }, 100);
        });
    }
};

// Data Table Component
class ModernDataTable {
    constructor(selector, options = {}) {
        this.element = document.querySelector(selector);
        this.options = {
            pageSize: 10,
            searchable: true,
            sortable: true,
            pagination: true,
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        
        this.init();
    }
    
    init() {
        if (!this.element) return;
        
        this.createTable();
        this.bindEvents();
        this.render();
    }
    
    createTable() {
        this.element.innerHTML = `
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${this.options.title || 'البيانات'}</h3>
                        ${this.options.searchable ? `
                            <div class="relative">
                                <input type="text" 
                                       placeholder="البحث..." 
                                       class="input-modern pr-10"
                                       id="table-search">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="overflow-x-auto">
                        <table class="table-modern">
                            <thead>
                                <tr>
                                    ${this.options.columns.map(col => `
                                        <th class="${col.sortable !== false ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700' : ''}"
                                            data-column="${col.key}">
                                            <div class="flex items-center justify-between">
                                                <span>${col.title}</span>
                                                ${col.sortable !== false ? '<i class="fas fa-sort text-gray-400"></i>' : ''}
                                            </div>
                                        </th>
                                    `).join('')}
                                </tr>
                            </thead>
                            <tbody id="table-body">
                                <!-- Data will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                    ${this.options.pagination ? `
                        <div class="flex items-center justify-between mt-4" id="table-pagination">
                            <!-- Pagination will be inserted here -->
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        // Search
        if (this.options.searchable) {
            const searchInput = this.element.querySelector('#table-search');
            searchInput.addEventListener('input', ModernUtils.debounce((e) => {
                this.filterData(e.target.value);
            }, 300));
        }
        
        // Sort
        if (this.options.sortable) {
            const headers = this.element.querySelectorAll('th[data-column]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.column;
                    this.sortData(column);
                });
            });
        }
    }
    
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.currentPage = 1;
        this.render();
    }
    
    filterData(query) {
        if (!query) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(item => {
                return Object.values(item).some(value => 
                    String(value).toLowerCase().includes(query.toLowerCase())
                );
            });
        }
        this.currentPage = 1;
        this.render();
    }
    
    sortData(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.filteredData.sort((a, b) => {
            const aVal = a[column];
            const bVal = b[column];
            
            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
        
        this.render();
    }
    
    render() {
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        // Render table body
        const tbody = this.element.querySelector('#table-body');
        tbody.innerHTML = pageData.map(item => `
            <tr>
                ${this.options.columns.map(col => `
                    <td>${col.render ? col.render(item[col.key], item) : item[col.key] || ''}</td>
                `).join('')}
            </tr>
        `).join('');
        
        // Render pagination
        if (this.options.pagination) {
            this.renderPagination();
        }
        
        // Update sort indicators
        this.updateSortIndicators();
    }
    
    renderPagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        const pagination = this.element.querySelector('#table-pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        const pages = [];
        const currentPage = this.currentPage;
        
        // Previous button
        pages.push(`
            <button onclick="this.closest('.modern-data-table').__instance.goToPage(${currentPage - 1})"
                    ${currentPage === 1 ? 'disabled' : ''}
                    class="btn-modern btn-modern-secondary ${currentPage === 1 ? 'disabled' : ''}">
                <i class="fas fa-chevron-right"></i>
            </button>
        `);
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                pages.push(`
                    <button onclick="this.closest('.modern-data-table').__instance.goToPage(${i})"
                            class="btn-modern ${i === currentPage ? 'btn-modern-primary' : 'btn-modern-secondary'}">
                        ${i}
                    </button>
                `);
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                pages.push('<span class="px-2 text-gray-500">...</span>');
            }
        }
        
        // Next button
        pages.push(`
            <button onclick="this.closest('.modern-data-table').__instance.goToPage(${currentPage + 1})"
                    ${currentPage === totalPages ? 'disabled' : ''}
                    class="btn-modern btn-modern-secondary ${currentPage === totalPages ? 'disabled' : ''}">
                <i class="fas fa-chevron-left"></i>
            </button>
        `);
        
        pagination.innerHTML = `
            <div class="text-sm text-gray-700 dark:text-gray-300">
                عرض ${startIndex + 1} إلى ${Math.min(endIndex, this.filteredData.length)} من ${this.filteredData.length} نتيجة
            </div>
            <div class="flex items-center space-x-1 space-x-reverse">
                ${pages.join('')}
            </div>
        `;
    }
    
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }
    
    updateSortIndicators() {
        const headers = this.element.querySelectorAll('th[data-column]');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            const column = header.dataset.column;
            
            if (this.sortColumn === column) {
                icon.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'} text-primary-600 dark:text-primary-400`;
            } else {
                icon.className = 'fas fa-sort text-gray-400';
            }
        });
    }
}

// Form Component
class ModernForm {
    constructor(selector, options = {}) {
        this.element = document.querySelector(selector);
        this.options = {
            ajax: false,
            validation: true,
            ...options
        };
        
        this.init();
    }
    
    init() {
        if (!this.element) return;
        
        this.bindEvents();
        this.setupValidation();
    }
    
    bindEvents() {
        this.element.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });
        
        // Real-time validation
        const inputs = this.element.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }
    
    setupValidation() {
        // Add validation attributes
        const requiredFields = this.element.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('invalid', (e) => {
                e.preventDefault();
                this.showFieldError(field, 'هذا الحقل مطلوب');
            });
        });
    }
    
    validateField(field) {
        const value = field.value.trim();
        
        // Required validation
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
        
        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }
        }
        
        // Min length validation
        if (field.hasAttribute('minlength')) {
            const minLength = parseInt(field.getAttribute('minlength'));
            if (value.length < minLength) {
                this.showFieldError(field, `يجب أن يكون الحقل ${minLength} أحرف على الأقل`);
                return false;
            }
        }
        
        this.clearFieldError(field);
        return true;
    }
    
    showFieldError(field, message) {
        field.classList.add('input-modern-error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-600 dark:text-red-400 text-sm mt-1';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }
    
    clearFieldError(field) {
        field.classList.remove('input-modern-error');
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    validateForm() {
        const fields = this.element.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    async handleSubmit() {
        if (!this.validateForm()) {
            return;
        }
        
        const formData = new FormData(this.element);
        const data = Object.fromEntries(formData);
        
        if (this.options.ajax) {
            try {
                const response = await fetch(this.element.action, {
                    method: this.element.method,
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    ModernUtils.showNotification(result.message || 'تم الحفظ بنجاح', 'success');
                    if (result.redirect) {
                        window.location.href = result.redirect;
                    }
                } else {
                    ModernUtils.showNotification(result.message || 'حدث خطأ', 'error');
                }
            } catch (error) {
                ModernUtils.showNotification('حدث خطأ في الاتصال', 'error');
            }
        } else {
            this.element.submit();
        }
    }
}

// Modal Component
class ModernModal {
    constructor(selector, options = {}) {
        this.element = document.querySelector(selector);
        this.options = {
            backdrop: true,
            keyboard: true,
            ...options
        };
        
        this.isOpen = false;
        this.init();
    }
    
    init() {
        if (!this.element) return;
        
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        this.element.innerHTML = `
            <div class="modal-modern" x-show="open" @click.away="close()" x-transition>
                <div class="modal-modern-content">
                    <div class="modal-modern-header">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white" id="modal-title">
                            ${this.options.title || 'النافذة المنبثقة'}
                        </h3>
                        <button @click="close()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-modern-body" id="modal-body">
                        <!-- Content will be inserted here -->
                    </div>
                    <div class="modal-modern-footer" id="modal-footer">
                        <!-- Buttons will be inserted here -->
                    </div>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        // Keyboard events
        if (this.options.keyboard) {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) {
                    this.close();
                }
            });
        }
    }
    
    open(content = '', title = '', buttons = []) {
        const modalBody = this.element.querySelector('#modal-body');
        const modalTitle = this.element.querySelector('#modal-title');
        const modalFooter = this.element.querySelector('#modal-footer');
        
        if (content) {
            modalBody.innerHTML = content;
        }
        
        if (title) {
            modalTitle.textContent = title;
        }
        
        if (buttons.length > 0) {
            modalFooter.innerHTML = buttons.map(btn => `
                <button onclick="${btn.onclick}" 
                        class="btn-modern ${btn.class || 'btn-modern-secondary'}">
                    ${btn.text}
                </button>
            `).join('');
        }
        
        this.isOpen = true;
        document.body.style.overflow = 'hidden';
    }
    
    close() {
        this.isOpen = false;
        document.body.style.overflow = '';
    }
}

// Chart Component (Simple)
class ModernChart {
    constructor(selector, data, options = {}) {
        this.element = document.querySelector(selector);
        this.data = data;
        this.options = {
            type: 'bar',
            colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
            ...options
        };
        
        this.init();
    }
    
    init() {
        if (!this.element) return;
        
        this.render();
    }
    
    render() {
        if (this.options.type === 'bar') {
            this.renderBarChart();
        } else if (this.options.type === 'line') {
            this.renderLineChart();
        } else if (this.options.type === 'pie') {
            this.renderPieChart();
        }
    }
    
    renderBarChart() {
        const maxValue = Math.max(...this.data.map(d => d.value));
        
        this.element.innerHTML = `
            <div class="space-y-2">
                ${this.data.map((item, index) => `
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="flex-1">
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-700 dark:text-gray-300">${item.label}</span>
                                <span class="font-medium text-gray-900 dark:text-white">${item.value}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full transition-all duration-300"
                                     style="width: ${(item.value / maxValue) * 100}%; background-color: ${this.options.colors[index % this.options.colors.length]}"></div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderLineChart() {
        // Simple line chart implementation
        this.element.innerHTML = `
            <div class="h-64 flex items-end justify-between space-x-2 space-x-reverse">
                ${this.data.map((item, index) => `
                    <div class="flex-1 flex flex-col items-center">
                        <div class="w-full bg-primary-200 dark:bg-primary-800 rounded-t" 
                             style="height: ${item.value}%"></div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">${item.label}</p>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderPieChart() {
        const total = this.data.reduce((sum, item) => sum + item.value, 0);
        
        this.element.innerHTML = `
            <div class="flex items-center justify-center">
                <div class="relative w-32 h-32">
                    ${this.data.map((item, index) => {
                        const percentage = (item.value / total) * 100;
                        const rotation = this.data.slice(0, index).reduce((sum, d) => sum + (d.value / total) * 360, 0);
                        
                        return `
                            <div class="absolute inset-0 rounded-full border-8"
                                 style="border-color: ${this.options.colors[index % this.options.colors.length]}; 
                                        clip-path: polygon(50% 50%, 50% 0%, ${50 + Math.cos((rotation + percentage * 3.6) * Math.PI / 180) * 50}% ${50 + Math.sin((rotation + percentage * 3.6) * Math.PI / 180) * 50}%, ${50 + Math.cos(rotation * Math.PI / 180) * 50}% ${50 + Math.sin(rotation * Math.PI / 180) * 50}%)"></div>
                        `;
                    }).join('')}
                </div>
                <div class="mr-4 space-y-1">
                    ${this.data.map((item, index) => `
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <div class="w-3 h-3 rounded-full" 
                                 style="background-color: ${this.options.colors[index % this.options.colors.length]}"></div>
                            <span class="text-sm text-gray-700 dark:text-gray-300">${item.label}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
}

// Export components
window.ModernComponents = {
    DataTable: ModernDataTable,
    Form: ModernForm,
    Modal: ModernModal,
    Chart: ModernChart,
    Utils: ModernUtils
};

// Auto-initialize components
document.addEventListener('DOMContentLoaded', function() {
    // Auto-initialize data tables
    document.querySelectorAll('[data-modern-table]').forEach(element => {
        const options = JSON.parse(element.dataset.modernTable || '{}');
        new ModernDataTable(element, options);
    });
    
    // Auto-initialize forms
    document.querySelectorAll('[data-modern-form]').forEach(element => {
        const options = JSON.parse(element.dataset.modernForm || '{}');
        new ModernForm(element, options);
    });
    
    // Auto-initialize modals
    document.querySelectorAll('[data-modern-modal]').forEach(element => {
        const options = JSON.parse(element.dataset.modernModal || '{}');
        new ModernModal(element, options);
    });
}); 