#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.contrib.auth.models import User
from decimal import Decimal

def fix_item_names():
    print("=== Fixing item names with English names ===")
    
    # Get admin user
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    
    # Get categories and units
    general_category = ItemCategory.objects.get(code='GEN')
    pcs_unit = Unit.objects.get(code='PCS')
    
    # Items with problematic names - update them with English names
    items_to_fix = [
        {'code': 'OS022', 'new_name': 'Blue Pen', 'cost': '2.50', 'selling': '3.00'},
        {'code': 'OS015', 'new_name': 'Notebook', 'cost': '15.00', 'selling': '20.00'},
        {'code': 'OS019', 'new_name': 'Ruler 30cm', 'cost': '5.00', 'selling': '7.00'},
        {'code': 'OS011', 'new_name': 'White Eraser', 'cost': '1.50', 'selling': '2.00'},
        {'code': 'OS012', 'new_name': 'Pencil', 'cost': '1.00', 'selling': '1.50'},
        {'code': 'OS06', 'new_name': 'Metal Sharpener', 'cost': '3.00', 'selling': '4.00'},
        {'code': 'OS05', 'new_name': 'Paper Clip', 'cost': '0.50', 'selling': '1.00'},
        {'code': 'OS07', 'new_name': 'Stapler', 'cost': '25.00', 'selling': '35.00'},
        {'code': 'OS04', 'new_name': 'Clear Tape', 'cost': '8.00', 'selling': '12.00'},
        {'code': 'OS03', 'new_name': 'Small Scissors', 'cost': '15.00', 'selling': '20.00'},
        {'code': 'OS021', 'new_name': 'Drawing Book', 'cost': '12.00', 'selling': '18.00'},
        {'code': 'OS020', 'new_name': 'Colored Pencils', 'cost': '30.00', 'selling': '45.00'},
        {'code': 'OS010', 'new_name': 'White Glue', 'cost': '6.00', 'selling': '9.00'},
        {'code': '004', 'new_name': 'A4 Paper', 'cost': '50.00', 'selling': '65.00'},
        {'code': 'OS014', 'new_name': 'File Folder', 'cost': '4.00', 'selling': '6.00'},
        {'code': 'OS09', 'new_name': 'Small Stapler', 'cost': '20.00', 'selling': '28.00'},
        {'code': 'OS02', 'new_name': 'Highlighter', 'cost': '3.50', 'selling': '5.00'},
        {'code': 'OS08', 'new_name': 'Plastic Folder', 'cost': '2.00', 'selling': '3.50'},
        {'code': 'OS01', 'new_name': 'Spiral Notebook', 'cost': '10.00', 'selling': '15.00'},
        {'code': 'OS013', 'new_name': 'Calculator', 'cost': '45.00', 'selling': '65.00'},
        {'code': 'OS017', 'new_name': 'Writing Board', 'cost': '35.00', 'selling': '50.00'},
        {'code': 'OS016', 'new_name': 'School Bag', 'cost': '80.00', 'selling': '120.00'},
        {'code': 'OS023', 'new_name': 'Dictionary', 'cost': '25.00', 'selling': '40.00'},
        {'code': 'OS018', 'new_name': 'Exercise Book', 'cost': '8.00', 'selling': '12.00'},
    ]
    
    fixed_count = 0
    for item_data in items_to_fix:
        try:
            item = Item.objects.get(code=item_data['code'])
            item.name = item_data['new_name']
            item.cost_price = Decimal(item_data['cost'])
            item.selling_price = Decimal(item_data['selling'])
            
            # Make sure it has proper category and unit
            if not item.category:
                item.category = general_category
            if not item.unit:
                item.unit = pcs_unit
            if not item.created_by:
                item.created_by = admin_user
                
            item.save()
            print(f"Fixed: {item.code} -> {item.name}")
            fixed_count += 1
        except Item.DoesNotExist:
            print(f"Item not found: {item_data['code']}")
        except Exception as e:
            print(f"Error fixing {item_data['code']}: {e}")
    
    print(f"\nFixed {fixed_count} items")
    
    # Show final count
    active_items = Item.objects.filter(is_active=True).count()
    print(f"Total active items: {active_items}")

if __name__ == "__main__":
    fix_item_names()