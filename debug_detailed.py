#!/usr/bin/env python
"""
اختبار مفصل مع عرض تفاصيل الأخطاء
"""

import os
import sys
import django
import traceback

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.test import Client
from django.conf import settings

def debug_detailed():
    """اختبار مفصل مع عرض تفاصيل الأخطاء"""
    
    print("🔍 اختبار مفصل مع عرض تفاصيل الأخطاء...")
    
    # التحقق من إعدادات DEBUG
    print(f"DEBUG = {settings.DEBUG}")
    print(f"ALLOWED_HOSTS = {settings.ALLOWED_HOSTS}")
    
    client = Client()
    
    try:
        print("\n📊 اختبار صفحة dashboard...")
        response = client.get('/dashboard/', HTTP_HOST='127.0.0.1:8000')
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 500:
            print('❌ خطأ 500!')
            
            # محاولة عرض محتوى الخطأ
            try:
                content = response.content.decode('utf-8', errors='ignore')
                print("\n📋 محتوى الخطأ:")
                print("=" * 50)
                print(content)
                print("=" * 50)
            except Exception as e:
                print(f"لا يمكن قراءة محتوى الخطأ: {e}")
            
            return False
            
        elif response.status_code == 200:
            print('✅ الصفحة تعمل بنجاح!')
            
            # التحقق من المحتوى
            content = response.content.decode('utf-8', errors='ignore')
            if 'حسابات أوساريك' in content:
                print('✅ اسم "حسابات أوساريك" موجود!')
            else:
                print('⚠️ اسم "حسابات أوساريك" غير موجود')
            
            return True
            
        else:
            print(f'Status غير متوقع: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_detailed()
    if success:
        print("\n🎉 الموقع يعمل بشكل مثالي!")
        print("🌐 الرابط: http://127.0.0.1:8000/dashboard/")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
        print("💡 تم عرض تفاصيل الخطأ أعلاه")
