#!/usr/bin/env python
"""
سكريبت حذف البيانات التجريبية المحذوفة نهائياً من الأصناف
Script to permanently delete inactive (soft-deleted) items
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.db import transaction

def delete_inactive_items():
    """حذف الأصناف غير النشطة نهائياً"""
    
    print("🔍 البحث عن الأصناف المحذوفة (غير النشطة)...")
    
    # العثور على الأصناف غير النشطة
    inactive_items = Item.objects.filter(is_active=False)
    count = inactive_items.count()
    
    if count == 0:
        print("✅ لا توجد أصناف محذوفة للحذف النهائي")
        return
    
    print(f"📊 تم العثور على {count} صنف محذوف")
    print("\n📋 قائمة الأصناف المحذوفة:")
    print("-" * 60)
    
    for item in inactive_items:
        print(f"• {item.name} ({item.code})")
    
    print("-" * 60)
    
    # طلب التأكيد
    confirm = input(f"\n⚠️  هل تريد حذف هذه الأصناف نهائياً؟ (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # حذف الأصناف نهائياً
    try:
        with transaction.atomic():
            deleted_count = 0
            for item in inactive_items:
                item_name = item.name
                item_code = item.code
                item.delete()  # حذف نهائي
                deleted_count += 1
                print(f"🗑️  تم حذف: {item_name} ({item_code})")
            
            print(f"\n✅ تم حذف {deleted_count} صنف نهائياً من قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء الحذف: {str(e)}")
        return

def delete_inactive_categories():
    """حذف فئات الأصناف غير النشطة نهائياً"""
    
    print("\n🔍 البحث عن فئات الأصناف المحذوفة...")
    
    inactive_categories = ItemCategory.objects.filter(is_active=False)
    count = inactive_categories.count()
    
    if count == 0:
        print("✅ لا توجد فئات محذوفة للحذف النهائي")
        return
    
    print(f"📊 تم العثور على {count} فئة محذوفة")
    print("\n📋 قائمة الفئات المحذوفة:")
    print("-" * 60)
    
    for category in inactive_categories:
        print(f"• {category.name}")
    
    print("-" * 60)
    
    # طلب التأكيد
    confirm = input(f"\n⚠️  هل تريد حذف هذه الفئات نهائياً؟ (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # حذف الفئات نهائياً
    try:
        with transaction.atomic():
            deleted_count = 0
            for category in inactive_categories:
                category_name = category.name
                category.delete()  # حذف نهائي
                deleted_count += 1
                print(f"🗑️  تم حذف الفئة: {category_name}")
            
            print(f"\n✅ تم حذف {deleted_count} فئة نهائياً من قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء حذف الفئات: {str(e)}")
        return

def delete_inactive_units():
    """حذف وحدات القياس غير النشطة نهائياً"""
    
    print("\n🔍 البحث عن وحدات القياس المحذوفة...")
    
    inactive_units = Unit.objects.filter(is_active=False)
    count = inactive_units.count()
    
    if count == 0:
        print("✅ لا توجد وحدات قياس محذوفة للحذف النهائي")
        return
    
    print(f"📊 تم العثور على {count} وحدة قياس محذوفة")
    print("\n📋 قائمة وحدات القياس المحذوفة:")
    print("-" * 60)
    
    for unit in inactive_units:
        print(f"• {unit.name}")
    
    print("-" * 60)
    
    # طلب التأكيد
    confirm = input(f"\n⚠️  هل تريد حذف هذه الوحدات نهائياً؟ (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return
    
    # حذف الوحدات نهائياً
    try:
        with transaction.atomic():
            deleted_count = 0
            for unit in inactive_units:
                unit_name = unit.name
                unit.delete()  # حذف نهائي
                deleted_count += 1
                print(f"🗑️  تم حذف وحدة القياس: {unit_name}")
            
            print(f"\n✅ تم حذف {deleted_count} وحدة قياس نهائياً من قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء حذف وحدات القياس: {str(e)}")
        return

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🗑️  أداة حذف البيانات التجريبية المحذوفة نهائياً")
    print("   Permanent Deletion Tool for Inactive Data")
    print("=" * 60)
    
    print("\n📋 الخيارات المتاحة:")
    print("1. حذف الأصناف المحذوفة نهائياً")
    print("2. حذف فئات الأصناف المحذوفة نهائياً")
    print("3. حذف وحدات القياس المحذوفة نهائياً")
    print("4. حذف جميع البيانات المحذوفة نهائياً")
    print("0. خروج")
    
    while True:
        choice = input("\n🔢 اختر رقم الخيار: ").strip()
        
        if choice == '1':
            delete_inactive_items()
        elif choice == '2':
            delete_inactive_categories()
        elif choice == '3':
            delete_inactive_units()
        elif choice == '4':
            delete_inactive_items()
            delete_inactive_categories()
            delete_inactive_units()
        elif choice == '0':
            print("👋 شكراً لاستخدام الأداة!")
            break
        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
            continue
        
        # سؤال عن المتابعة
        continue_choice = input("\n🔄 هل تريد تنفيذ عملية أخرى؟ (y/N): ").strip().lower()
        if continue_choice not in ['y', 'yes', 'نعم']:
            print("👋 شكراً لاستخدام الأداة!")
            break

if __name__ == '__main__':
    main()