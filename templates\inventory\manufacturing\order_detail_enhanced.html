{% extends 'base/base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}تفاصيل أمر التصنيع - {{ order.finished_product.name|default:"غير محدد" }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسين الألوان العامة */
    .material-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .material-card:hover {
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .step-card {
        border-left: 4px solid #ffc107;
        background: linear-gradient(135deg, #ffffff 0%, #fff8e1 100%);
    }

    .quality-card {
        border-left: 4px solid #17a2b8;
        background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
    }

    /* تحسين رؤوس البطاقات - تحسين كبير للوضوح */
    .card-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-bottom: 3px solid #1a252f;
        padding: 1.25rem 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60);
    }

    .card-header h6 {
        color: #ffffff;
        font-weight: 800;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        font-size: 1.1rem;
        margin: 0;
        letter-spacing: 0.5px;
        position: relative;
        z-index: 2;
    }

    .card-header h6 i {
        margin-right: 0.75rem;
        color: #3498db;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        font-size: 1.2rem;
    }

    /* تحسين العناوين الرئيسية */
    .page-title {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ffffff;
        padding: 1.5rem 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        position: relative;
        overflow: hidden;
    }

    .page-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6);
    }

    .page-title h1 {
        color: #ffffff;
        font-weight: 900;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        margin: 0;
        font-size: 1.8rem;
        letter-spacing: 1px;
    }

    .page-title .breadcrumb {
        background: transparent;
        margin: 0.5rem 0 0 0;
        padding: 0;
        box-shadow: none;
    }

    .page-title .breadcrumb-item a {
        color: #3498db;
        font-weight: 600;
        text-decoration: none;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .page-title .breadcrumb-item.active {
        color: #ecf0f1;
        font-weight: 600;
    }

    /* تحسين عناوين الأقسام */
    .section-title {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: #ffffff;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }

    .section-title::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3498db, #e74c3c);
    }

    .section-title h5 {
        color: #ffffff;
        font-weight: 800;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
        margin: 0;
        font-size: 1.2rem;
        letter-spacing: 0.5px;
    }

    .section-title h5 i {
        margin-right: 0.75rem;
        color: #3498db;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* تحسين عناوين الجداول */
    .table-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ffffff;
        padding: 1rem 1.5rem;
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .table-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3498db, #e74c3c);
    }

    .table-header h6 {
        color: #ffffff;
        font-weight: 800;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
        margin: 0;
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .table-header h6 i {
        margin-right: 0.75rem;
        color: #3498db;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* تحسين الجداول */
    .table-dark th {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: #ffffff;
        border: none;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 1rem 0.75rem;
    }

    /* تحسين أزرار الإجراءات */
    .btn-lg {
        font-size: 1.1rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    }

    .btn-success.btn-lg {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: #ffffff;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .btn-success.btn-lg:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        color: #ffffff;
    }

    .btn-info.btn-lg {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        border: none;
        color: #ffffff;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .btn-info.btn-lg:hover {
        background: linear-gradient(135deg, #138496 0%, #5a32a3 100%);
        color: #ffffff;
    }

    .btn-warning.btn-lg {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        border: none;
        color: #212529;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
    }

    .btn-warning.btn-lg:hover {
        background: linear-gradient(135deg, #e0a800 0%, #e8590c 100%);
        color: #212529;
    }

    /* تحسين رسائل الحالة */
    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 2px solid #28a745;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
    }

    .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 2px solid #ffc107;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(255, 193, 7, 0.2);
    }

    .table-dark td {
        font-size: 0.95rem;
        letter-spacing: 0.5px;
    }

    .table-dark th i {
        opacity: 0.9;
        margin-right: 0.5rem;
        color: #3498db;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(248, 249, 250, 0.8);
    }

    .table-striped tbody tr:nth-of-type(even) {
        background-color: rgba(255, 255, 255, 0.9);
    }

    /* تحسين الصفوف حسب الحالة */
    .table-warning {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%) !important;
        border-left: 3px solid #ffc107;
    }

    .table-success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%) !important;
        border-left: 3px solid #28a745;
    }

    .table-danger {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%) !important;
        border-left: 3px solid #dc3545;
    }

    /* تحسين البادجات */
    .badge {
        font-weight: 600;
        letter-spacing: 0.5px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .badge.fs-6 {
        font-size: 0.875rem !important;
        padding: 0.6rem 0.9rem;
        border-radius: 6px;
    }

    .badge.bg-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }

    .badge.bg-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }

    .badge.bg-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    }

    .badge.bg-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
        color: #212529 !important;
    }

    .badge.bg-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    }

    .badge.bg-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
    }

    .badge.bg-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        color: #495057 !important;
        border: 1px solid #dee2e6;
    }

    /* تحسين النصوص */
    .text-primary {
        color: #0056b3 !important;
        font-weight: 600;
    }

    .text-success {
        color: #1e7e34 !important;
        font-weight: 600;
    }

    .text-danger {
        color: #c82333 !important;
        font-weight: 600;
    }

    .text-warning {
        color: #e0a800 !important;
        font-weight: 600;
    }

    .text-info {
        color: #138496 !important;
        font-weight: 600;
    }

    .text-muted {
        color: #6c757d !important;
        font-weight: 500;
    }

    /* تحسين العناوين */
    .fs-5 {
        font-size: 1.2rem !important;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .fs-6 {
        font-size: 1rem !important;
        font-weight: 600;
    }

    /* تحسين الحقول */
    .material-status .badge {
        min-width: 110px;
        padding: 0.7rem 1rem;
        border-radius: 8px;
        font-size: 0.8rem;
    }

    /* تحسين المسافات */
    .gap-2 {
        gap: 0.75rem;
    }

    /* تأثيرات hover للصفوف */
    .table-hover tbody tr:hover {
        background: linear-gradient(135deg, rgba(0,123,255,0.08) 0%, rgba(0,123,255,0.04) 100%) !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* تحسين عرض الكميات */
    .available-quantity {
        font-weight: 700;
        min-width: 90px;
        display: inline-block;
        text-align: center;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* تحسين footer الجدول */
    .table-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
        border-top: 2px solid #17a2b8;
    }

    .table-info td {
        font-weight: 600;
        color: #0c5460;
    }

    /* تحسين الأزرار */
    .btn {
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
    }

    .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: none;
        color: #212529;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
    }

    /* تحسين التنبيهات */
    .alert {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    /* تحسين النصوص العامة */
    strong {
        font-weight: 700;
        color: #212529;
    }

    .text-dark {
        color: #212529 !important;
        font-weight: 600;
    }

    /* تحسين الألوان في البطاقات */
    .card {
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border-radius: 10px;
        overflow: hidden;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* تحسين الألوان في الجدول */
    .table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table td {
        vertical-align: middle;
        padding: 1rem 0.75rem;
        border-color: rgba(0,0,0,0.05);
    }

    .table th {
        padding: 1rem 0.75rem;
        border: none;
    }

    /* تحسين الألوان في الصفوف */
    tbody tr {
        transition: all 0.3s ease;
    }

    tbody tr:hover {
        background-color: rgba(0,123,255,0.05) !important;
    }

    /* تحسين الألوان في النصوص الصغيرة */
    small {
        color: #6c757d;
        font-weight: 500;
    }

    /* تحسين الألوان في الأيقونات */
    .fas, .fa {
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    /* تحسين الألوان في التصنيفات */
    .breadcrumb {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .breadcrumb-item a {
        color: #007bff;
        font-weight: 600;
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 600;
    }

    /* تحسين الأيقونات */
    .material-icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .material-icon:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* تحسين قسم التكاليف */
    .cost-section {
        background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%) !important;
        border: none !important;
        box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3) !important;
        border-radius: 15px !important;
        overflow: hidden;
        position: relative;
    }

    .cost-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ffffff, #ffd700, #ffffff);
    }

    .cost-section .card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .cost-section h6 {
        color: #ffffff !important;
        font-weight: 800 !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
        font-size: 1.2rem !important;
        letter-spacing: 0.5px !important;
    }

    .cost-section .text-white-50 {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 600 !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2) !important;
    }

    .cost-section .h5 {
        color: #ffffff !important;
        font-weight: 900 !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.4) !important;
        font-size: 1.5rem !important;
    }

    .cost-section .h3 {
        color: #ffffff !important;
        font-weight: 900 !important;
        text-shadow: 3px 3px 6px rgba(0,0,0,0.5) !important;
        font-size: 2.2rem !important;
    }

    .cost-section hr {
        border-color: rgba(255, 255, 255, 0.3) !important;
        border-width: 2px !important;
        margin: 1.5rem 0 !important;
    }

    .cost-section .col-6 {
        padding: 1rem 0.5rem;
    }

    .cost-section .col-12 {
        padding: 1rem 0;
    }

    /* تأثيرات hover للقسم */
    .cost-section:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(255, 105, 180, 0.4) !important;
        transition: all 0.3s ease;
    }

    /* تحسين مظهر تكلفة الوحدة */
    .unit-cost-highlight {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 1.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        margin-top: 1rem;
    }

    .unit-cost-highlight:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .unit-cost-highlight .h4 {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        font-weight: 800;
        letter-spacing: 1px;
        color: #ffffff !important;
    }

    .unit-cost-highlight .text-white-50 {
        font-weight: 500;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .unit-cost-highlight .small {
        color: rgba(255, 255, 255, 0.8) !important;
        font-weight: 400;
    }
</style>
{% endblock %}

{% block content %}
<!-- CSRF Token for AJAX requests -->
{% csrf_token %}

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="page-title">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1>
                    <i class="fas fa-industry me-3"></i>
                    تفاصيل أمر التصنيع #{{ order.id }}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_dashboard' %}">التصنيع</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_order_list' %}">أوامر التصنيع</a></li>
                        <li class="breadcrumb-item active">#{{ order.id }}</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-column gap-2">
                    <div>
                        <span class="badge fs-6 
                            {% if order.status == 'DRAFT' %}bg-secondary
                            {% elif order.status == 'PLANNED' %}bg-warning
                            {% elif order.status == 'IN_PROGRESS' %}bg-info
                            {% elif order.status == 'COMPLETED' %}bg-success
                            {% elif order.status == 'CANCELLED' %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {% if order.status == 'DRAFT' %}
                                <i class="fas fa-edit me-1"></i>مسودة
                            {% elif order.status == 'PLANNED' %}
                                <i class="fas fa-calendar-check me-1"></i>مخطط
                            {% elif order.status == 'IN_PROGRESS' %}
                                <i class="fas fa-cogs me-1"></i>قيد التنفيذ
                            {% elif order.status == 'COMPLETED' %}
                                <i class="fas fa-check-circle me-1"></i>مكتمل
                            {% elif order.status == 'CANCELLED' %}
                                <i class="fas fa-times-circle me-1"></i>ملغي
                            {% else %}
                                {{ order.get_status_display }}
                            {% endif %}
                        </span>
                    </div>
                    <div>
                        <span class="badge fs-6
                            {% if order.priority == 'HIGH' %}bg-danger
                            {% elif order.priority == 'MEDIUM' %}bg-warning
                            {% else %}bg-success{% endif %}">
                            {% if order.priority == 'HIGH' %}
                                <i class="fas fa-exclamation-triangle me-1"></i>أولوية عالية
                            {% elif order.priority == 'MEDIUM' %}
                                <i class="fas fa-minus me-1"></i>أولوية متوسطة
                            {% else %}
                                <i class="fas fa-arrow-down me-1"></i>أولوية منخفضة
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- أخطاء التحقق -->
    {% if validation_errors %}
        <div class="alert alert-danger validation-error">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء في التحقق:</h6>
            <ul class="mb-0">
                {% for error in validation_errors %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    <div class="row">
        <!-- المعلومات الأساسية -->
        <div class="col-lg-8">
            <!-- بيانات الأمر الأساسية -->
            <div class="card info-card shadow mb-4">
                <div class="card-header">
                    <h6>
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات أمر التصنيع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="w-50">رقم الأمر:</th>
                                    <td><strong>#{{ order.id }}</strong></td>
                                </tr>
                                <tr>
                                    <th>المنتج النهائي:</th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-box text-primary me-2"></i>
                                            <div>
                                                <strong>{{ order.finished_product.name|default:"غير محدد" }}</strong>
                                                {% if order.finished_product.code %}
                                                    <br><small class="text-muted">{{ order.finished_product.code }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>الكمية المطلوبة:</th>
                                    <td><span class="badge bg-info fs-6">{{ order.quantity_to_produce|default:0 }}</span></td>
                                </tr>
                                <tr>
                                    <th>مخزن المواد الخام:</th>
                                    <td>
                                        <i class="fas fa-warehouse text-secondary me-1"></i>
                                        {{ order.raw_materials_warehouse.name|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>مخزن المنتجات النهائية:</th>
                                    <td>
                                        <i class="fas fa-warehouse text-secondary me-1"></i>
                                        {{ order.finished_goods_warehouse.name|default:"غير محدد" }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="w-50">تاريخ الأمر:</th>
                                    <td>
                                        <i class="fas fa-calendar text-secondary me-1"></i>
                                        {{ order.order_date|date:"Y/m/d" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنتهاء المتوقع:</th>
                                    <td>
                                        <i class="fas fa-calendar-check text-secondary me-1"></i>
                                        {{ order.expected_completion_date|date:"Y/m/d"|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنتهاء الفعلي:</th>
                                    <td>
                                        <i class="fas fa-calendar-day text-secondary me-1"></i>
                                        {{ order.actual_completion_date|date:"Y/m/d"|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>المشرف:</th>
                                    <td>
                                        <i class="fas fa-user text-secondary me-1"></i>
                                        {% if order.supervisor %}
                                            {% if order.supervisor.get_full_name %}
                                                {{ order.supervisor.get_full_name }}
                                            {% elif order.supervisor.username %}
                                                {{ order.supervisor.username }}
                                            {% else %}
                                                غير محدد
                                            {% endif %}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>أنشئ بواسطة:</th>
                                    <td>
                                        <i class="fas fa-user-plus text-secondary me-1"></i>
                                        {% if order.created_by %}
                                            {% if order.created_by.get_full_name %}
                                                {{ order.created_by.get_full_name }}
                                            {% elif order.created_by.username %}
                                                {{ order.created_by.username }}
                                            {% else %}
                                                غير محدد
                                            {% endif %}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    {% if order.notes %}
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <h6><i class="fas fa-sticky-note me-2"></i>الملاحظات:</h6>
                                <p class="text-muted">{{ order.notes }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- المواد الخام المطلوبة -->
            <div class="card material-card shadow mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>
                            <i class="fas fa-box-seam me-2"></i>
                            المواد الخام المطلوبة
                        </h6>
                        <div class="d-flex gap-2">
                            <span class="badge bg-primary">
                                <i class="fas fa-list me-1"></i>
                                {{ materials|length }} مادة
                            </span>
                            {% if materials %}
                                <span class="badge {% if order.materials_sufficient %}bg-success{% else %}bg-warning{% endif %}">
                                    <i class="fas fa-check me-1"></i>
                                    {% if order.materials_sufficient %}
                                        جميع المواد متوفرة
                                    {% else %}
                                        بعض المواد غير كافية
                                    {% endif %}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if materials %}
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th><i class="fas fa-cube me-1"></i>المادة الخام</th>
                                        <th><i class="fas fa-arrow-up me-1"></i>المطلوب</th>
                                        <th><i class="fas fa-check-circle me-1"></i>المتوفر</th>
                                        <th><i class="fas fa-minus-circle me-1"></i>المستهلك</th>
                                        <th><i class="fas fa-ruler me-1"></i>الوحدة</th>
                                        <th><i class="fas fa-money-bill me-1"></i>تكلفة الوحدة</th>
                                        <th><i class="fas fa-calculator me-1"></i>الإجمالي</th>
                                        <th><i class="fas fa-info-circle me-1"></i>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for material in materials %}
                                    <tr data-material-id="{{ material.id }}" class="{% if not material.is_sufficient %}table-warning{% elif material.quantity_consumed >= material.quantity_required %}table-success{% endif %}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="material-icon me-2">
                                                    <i class="fas fa-cube text-{% if material.is_sufficient %}success{% else %}danger{% endif %}"></i>
                                                </div>
                                                <div>
                                                    <strong class="text-dark">{{ material.material.name }}</strong>
                                                    {% if material.material.code %}
                                                        <br><small class="text-muted">{{ material.material.code }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary fs-6">{{ material.quantity_required|floatformat:3 }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="available-quantity badge {% if material.is_sufficient %}bg-success{% else %}bg-danger{% endif %} fs-6">
                                                {{ material.available_quantity|floatformat:3 }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary fs-6">{{ material.quantity_consumed|floatformat:3 }}</span>
                                        </td>
                                        <td class="text-center">
                                            {% if material.material.unit %}
                                                <span class="badge bg-light text-dark">{{ material.material.unit.name }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <span class="text-primary fw-bold">{{ material.unit_cost|floatformat:2 }} ج.م</span>
                                        </td>
                                        <td class="text-center">
                                            <strong class="text-success">{{ material.total_cost|floatformat:2 }} ج.م</strong>
                                        </td>
                                        <td class="text-center">
                                            <div class="material-status">
                                                {% if material.is_sufficient %}
                                                    {% if material.quantity_consumed >= material.quantity_required %}
                                                        <span class="badge bg-success fs-6">
                                                            <i class="fas fa-check me-1"></i>مستهلكة
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-info fs-6">
                                                            <i class="fas fa-check me-1"></i>متوفرة
                                                        </span>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger fs-6">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>غير كافية
                                                        <br><small>(نقص: {{ material.shortage_quantity|floatformat:3 }})</small>
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-info">
                                    <tr>
                                        <td colspan="6" class="text-end">
                                            <strong class="fs-5">إجمالي تكلفة المواد:</strong>
                                        </td>
                                        <td class="text-center">
                                            <strong class="text-success fs-5">{{ order.total_material_cost|default:0|floatformat:2 }} ج.م</strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge {% if order.materials_sufficient %}bg-success{% else %}bg-warning{% endif %} fs-6">
                                                <i class="fas fa-{% if order.materials_sufficient %}check{% else %}exclamation-triangle{% endif %} me-1"></i>
                                                {% if order.materials_sufficient %}
                                                    جاهز للإنتاج
                                                {% else %}
                                                    يحتاج مواد إضافية
                                                {% endif %}
                                            </span>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي مواد خام لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- خطوات التصنيع -->
            <div class="card step-card shadow mb-4">
                <div class="card-header">
                    <h6>
                        <i class="fas fa-tasks me-2"></i>
                        خطوات التصنيع
                    </h6>
                </div>
                <div class="card-body">
                    {% if steps %}
                        <div class="timeline">
                            {% for step in steps %}
                            <div class="timeline-item">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ step.name }}</h6>
                                                <p class="text-muted mb-2">{{ step.description|default:"لا توجد وصف" }}</p>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user me-1"></i>
                                                            {{ step.assigned_to.get_full_name|default:step.assigned_to.username|default:"غير محدد" }}
                                                        </small>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {{ step.estimated_duration|default:0 }} ساعة
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge 
                                                    {% if step.is_completed %}bg-success
                                                    {% elif step.is_in_progress %}bg-info
                                                    {% else %}bg-secondary{% endif %}">
                                                    {% if step.is_completed %}
                                                        <i class="fas fa-check me-1"></i>مكتمل
                                                    {% elif step.is_in_progress %}
                                                        <i class="fas fa-cogs me-1"></i>قيد التنفيذ
                                                    {% else %}
                                                        <i class="fas fa-clock me-1"></i>في الانتظار
                                                    {% endif %}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-tasks fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي خطوات تصنيع لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- فحوصات الجودة -->
            <div class="card quality-card shadow mb-4">
                <div class="card-header">
                    <h6>
                        <i class="fas fa-clipboard-check me-2"></i>
                        فحوصات الجودة
                    </h6>
                </div>
                <div class="card-body">
                    {% if quality_checks %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>نوع الفحص</th>
                                        <th>المواصفات</th>
                                        <th>النتيجة</th>
                                        <th>المفتش</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for check in quality_checks %}
                                    <tr>
                                        <td><strong>{{ check.check_type }}</strong></td>
                                        <td>{{ check.specifications|default:"-" }}</td>
                                        <td>{{ check.result|default:"-" }}</td>
                                        <td>
                                            <i class="fas fa-user text-secondary me-1"></i>
                                            {{ check.checked_by.get_full_name|default:check.checked_by.username|default:"غير محدد" }}
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar text-secondary me-1"></i>
                                            {{ check.check_date|date:"Y/m/d"|default:"-" }}
                                        </td>
                                        <td>
                                            <span class="badge 
                                                {% if check.is_passed %}bg-success
                                                {% elif check.is_failed %}bg-danger
                                                {% else %}bg-warning{% endif %}">
                                                {% if check.is_passed %}
                                                    <i class="fas fa-check me-1"></i>مقبول
                                                {% elif check.is_failed %}
                                                    <i class="fas fa-times me-1"></i>مرفوض
                                                {% else %}
                                                    <i class="fas fa-clock me-1"></i>في الانتظار
                                                {% endif %}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-check fa-4x text-muted mb-3"></i>
                            <p class="text-muted">لم يتم تحديد أي فحوصات جودة لهذا الأمر</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- التكاليف -->
            <div class="card cost-section shadow mb-4">
                <div class="card-body">
                    <h6>
                        <i class="fas fa-calculator me-2"></i>
                        تفاصيل التكاليف
                    </h6>
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="text-white-50">تكلفة المواد</div>
                            <div class="h5 text-white">{{ order.total_material_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-white-50">تكلفة التشغيل</div>
                            <div class="h5 text-white">{{ order.total_operating_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="col-12">
                            <hr class="border-white">
                            <div class="text-white-50">التكلفة الإجمالية</div>
                            <div class="h3 text-white">{{ order.total_production_cost|default:0|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="col-12 mt-3">
                            <div class="unit-cost-highlight">
                                <div class="text-white-50 mb-1">
                                    <i class="fas fa-tag me-1"></i>
                                    تكلفة الوحدة
                                </div>
                                <div class="h4 text-white fw-bold">
                                    {{ order.unit_cost|default:0|floatformat:2 }} ج.م
                                </div>
                                <div class="text-white-50 small">
                                    لكل {{ order.finished_product.unit.name|default:"وحدة" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإجراءات -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6>
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات المتاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <!-- زر العودة -->
                            <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary mb-3">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات حسب حالة الأمر -->
                    <div class="row g-3">
                        {% if order.status == 'DRAFT' or order.status == 'PENDING' %}
                            <div class="col-md-6">
                                <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" class="btn btn-warning w-100">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل الأمر
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="{% url 'inventory:plan_manufacturing_order' order.id %}" 
                                   class="btn btn-info w-100"
                                   onclick="return confirm('هل أنت متأكد من تخطيط هذا الأمر؟')">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    تخطيط الأمر
                                </a>
                            </div>
                        {% endif %}
                        
                        {% if order.status == 'PLANNED' %}
                            <div class="col-md-4">
                                <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" class="btn btn-warning w-100">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل الأمر
                                </a>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-info w-100" id="update-availability-btn">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    تحديث الكميات
                                </button>
                            </div>
                            <div class="col-md-4">
                                {% if not validation_errors %}
                                    <a href="{% url 'inventory:start_production' order.id %}" 
                                       class="btn btn-success w-100 btn-lg"
                                       onclick="return confirm('هل أنت متأكد من بدء عملية التصنيع؟ سيتم خصم المواد الخام من المخزون.')">
                                        <i class="fas fa-play me-2"></i>
                                        <strong>بدء التصنيع</strong>
                                    </a>
                                {% else %}
                                    <button type="button" class="btn btn-success w-100 btn-lg" disabled title="يجب تصحيح الأخطاء أولاً">
                                        <i class="fas fa-play me-2"></i>
                                        <strong>بدء التصنيع</strong>
                                    </button>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        {% if order.status == 'IN_PROGRESS' %}
                            <div class="col-md-6">
                                <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" class="btn btn-warning w-100">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل الأمر
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="{% url 'inventory:complete_production' order.id %}" 
                                   class="btn btn-success w-100 btn-lg"
                                   onclick="return confirm('هل أنت متأكد من إكمال عملية التصنيع؟')">
                                    <i class="fas fa-check-double me-2"></i>
                                    <strong>إكمال التصنيع</strong>
                                </a>
                            </div>
                        {% endif %}
                        
                        {% if order.status == 'COMPLETED' %}
                            <div class="col-12">
                                <div class="alert alert-success text-center p-4">
                                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                    <h5 class="mb-2"><strong>تم إكمال التصنيع بنجاح</strong></h5>
                                    <p class="mb-0">تاريخ الإكمال: {{ order.actual_completion_date|date:"Y/m/d" }}</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- رسائل الحالة -->
                    {% if validation_errors %}
                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات:</h6>
                            <ul class="mb-0">
                                {% for error in validation_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <div>{{ order.created_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">آخر تحديث:</small>
                        <div>{{ order.updated_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    {% if order.is_active %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i>نشط</span>
                    {% else %}
                        <span class="badge bg-danger"><i class="fas fa-times me-1"></i>غير نشط</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث الكميات المتاحة تلقائياً عند تحميل الصفحة
    if ('{{ order.status }}' === 'PLANNED') {
        updateAvailabilityOnLoad();
    }
    
    // زر تحديث الكميات المتاحة
    $('#update-availability-btn').on('click', function() {
        const btn = $(this);
        const originalText = btn.html();
        
        // تعطيل الزر وإظهار حالة التحميل
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...');
        
        // إرسال طلب AJAX لتحديث الكميات
        $.ajax({
            url: `/inventory/api/update-manufacturing-availability/{{ order.id }}/`,
            method: 'POST',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.status === 'success') {
                    // إظهار رسالة نجاح
                    showAlert('success', 'تم تحديث الكميات المتاحة بنجاح');
                    
                    // تحديث الجدول
                    updateMaterialsTable(response.materials);
                    
                    // إعادة تحميل الصفحة بعد ثانيتين
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('error', response.message || 'حدث خطأ أثناء التحديث');
                }
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء التحديث';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert('error', message);
            },
            complete: function() {
                // إعادة تفعيل الزر
                btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    // دالة تحديث الكميات المتاحة عند التحميل
    function updateAvailabilityOnLoad() {
        $.ajax({
            url: `/inventory/api/update-manufacturing-availability/{{ order.id }}/`,
            method: 'POST',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.status === 'success') {
                    updateMaterialsTable(response.materials);
                }
            },
            error: function() {
                console.log('فشل في تحديث الكميات المتاحة عند التحميل');
            }
        });
    }
    
    // دالة إظهار التنبيهات
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى الصفحة
        $('.container-fluid').prepend(alertHtml);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
    
    // دالة تحديث جدول المواد
    function updateMaterialsTable(materials) {
        materials.forEach(function(material) {
            const row = $(`tr[data-material-id="${material.id}"]`);
            if (row.length > 0) {
                // تحديث الكمية المتاحة
                row.find('.available-quantity').text(material.available_quantity);
                
                // تحديث حالة الكفاية
                const statusCell = row.find('.material-status');
                if (material.is_sufficient) {
                    statusCell.html('<span class="badge bg-success">كافية</span>');
                } else {
                    statusCell.html(`<span class="badge bg-danger">غير كافية (نقص: ${material.shortage_quantity})</span>`);
                }
            }
        });
    }
});
</script>
{% endblock %} 