#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_management.settings')
django.setup()

from definitions.models import CompanySettings

def fix_company_settings():
    """إصلاح إعدادات الشركة"""
    print("🔍 فحص إعدادات الشركة...")
    
    # الحصول على الإعدادات أو إنشاؤها
    settings = CompanySettings.get_settings()
    
    print(f"📊 معلومات الإعدادات الحالية:")
    print(f"   - ID: {settings.id}")
    print(f"   - اسم الشركة: {settings.company_name}")
    print(f"   - العنوان الفرعي: {settings.company_subtitle}")
    print(f"   - الشعار: {settings.company_logo}")
    print(f"   - لون الشريط العلوي: {settings.header_background_color}")
    
    # التأكد من وجود القيم الافتراضية
    updated = False
    
    if not settings.company_name:
        settings.company_name = "حسابات أوساريك"
        updated = True
        print("✅ تم تعيين اسم الشركة الافتراضي")
    
    if not settings.company_subtitle:
        settings.company_subtitle = "نظام إدارة الحسابات المتكامل والاحترافي"
        updated = True
        print("✅ تم تعيين العنوان الفرعي الافتراضي")
    
    if not settings.header_background_color:
        settings.header_background_color = "#667eea"
        updated = True
        print("✅ تم تعيين لون الشريط العلوي الافتراضي")
    
    if updated:
        settings.save()
        print("💾 تم حفظ التحديثات")
    else:
        print("✅ جميع الإعدادات موجودة")
    
    print(f"\n📊 معلومات الإعدادات النهائية:")
    print(f"   - اسم الشركة: {settings.company_name}")
    print(f"   - العنوان الفرعي: {settings.company_subtitle}")
    print(f"   - الشعار: {settings.company_logo}")
    print(f"   - لون الشريط العلوي: {settings.header_background_color}")
    
    return settings

if __name__ == "__main__":
    try:
        settings = fix_company_settings()
        print("\n✅ تم إصلاح إعدادات الشركة بنجاح!")
    except Exception as e:
        print(f"❌ خطأ: {e}")
