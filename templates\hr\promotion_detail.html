{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-eye text-info me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">تفاصيل كاملة للترقية</p>
                </div>
                <div>
                    <a href="{% url 'hr:promotion_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة الترقيات
                    </a>
                    <a href="{% url 'hr:promotion_edit' promotion.id %}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل الترقية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted">الموظف:</th>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ promotion.employee.full_name }}
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">رقم الموظف:</th>
                                    <td>{{ promotion.employee.employee_number }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">القسم:</th>
                                    <td>{{ promotion.employee.department.name|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">تاريخ الترقية:</th>
                                    <td>{{ promotion.promotion_date|date:"Y-m-d" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted">المنصب السابق:</th>
                                    <td>
                                        <span class="badge bg-secondary">{{ promotion.from_position.name }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">المنصب الجديد:</th>
                                    <td>
                                        <span class="badge bg-success">{{ promotion.to_position.name }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-muted">الراتب السابق:</th>
                                    <td>{{ promotion.from_salary|floatformat:2 }} ج.م</td>
                                </tr>
                                <tr>
                                    <th class="text-muted">الراتب الجديد:</th>
                                    <td>{{ promotion.to_salary|floatformat:2 }} ج.م</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">زيادة الراتب</h6>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-arrow-up text-success fa-2x"></i>
                                </div>
                                <div>
                                    <h4 class="text-success mb-0">+{{ promotion.salary_increase|floatformat:2 }} ج.م</h4>
                                    <small class="text-muted">{{ promotion.salary_increase_percentage|floatformat:1 }}% زيادة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">حالة الترقية</h6>
                            {% if promotion.status == 'PENDING' %}
                                <span class="badge bg-warning fs-6">قيد المراجعة</span>
                            {% elif promotion.status == 'APPROVED' %}
                                <span class="badge bg-success fs-6">موافق عليها</span>
                            {% elif promotion.status == 'REJECTED' %}
                                <span class="badge bg-danger fs-6">مرفوضة</span>
                            {% endif %}
                        </div>
                    </div>

                    {% if promotion.reason %}
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">سبب الترقية</h6>
                            <p class="mb-0">{{ promotion.reason }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if promotion.notes %}
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">ملاحظات إضافية</h6>
                            <p class="mb-0">{{ promotion.notes }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if promotion.effective_date %}
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">تاريخ التنفيذ</h6>
                            <p class="mb-0">{{ promotion.effective_date|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Approval Information -->
            {% if promotion.status == 'APPROVED' or promotion.status == 'REJECTED' %}
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        معلومات الموافقة
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th class="text-muted">تمت الموافقة بواسطة:</th>
                            <td>{% if promotion.approved_by %}{{ promotion.approved_by.get_full_name|default:promotion.approved_by.username }}{% else %}غير محدد{% endif %}</td>
                        </tr>
                        <tr>
                            <th class="text-muted">تاريخ الموافقة:</th>
                            <td>{{ promotion.approved_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        {% if promotion.approval_notes %}
                        <tr>
                            <th class="text-muted">ملاحظات:</th>
                            <td>{{ promotion.approval_notes }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- System Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        معلومات النظام
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th class="text-muted">تم الإنشاء بواسطة:</th>
                            <td>{% if promotion.created_by %}{{ promotion.created_by.get_full_name|default:promotion.created_by.username }}{% else %}غير محدد{% endif %}</td>
                        </tr>
                        <tr>
                            <th class="text-muted">تاريخ الإنشاء:</th>
                            <td>{{ promotion.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th class="text-muted">آخر تحديث:</th>
                            <td>{{ promotion.updated_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Actions -->
            {% if promotion.status == 'PENDING' %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'hr:promotion_approve' promotion.id %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success btn-sm w-100 mb-2" 
                                onclick="return confirm('هل أنت متأكد من الموافقة على هذه الترقية؟')">
                            <i class="fas fa-check me-2"></i>
                            موافقة
                        </button>
                    </form>
                    
                    <button type="button" class="btn btn-danger btn-sm w-100" 
                            onclick="rejectPromotion({{ promotion.id }})">
                        <i class="fas fa-times me-2"></i>
                        رفض
                    </button>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function rejectPromotion(promotionId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason !== null) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/hr/promotions/${promotionId}/reject/`;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'rejection_reason';
        reasonInput.value = reason;
        
        form.appendChild(csrfInput);
        form.appendChild(reasonInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %} 