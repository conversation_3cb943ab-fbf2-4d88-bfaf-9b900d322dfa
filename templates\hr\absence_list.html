{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-times text-secondary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">متابعة حالات الغياب والتأخير للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:absence_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تسجيل غياب/تأخير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ total_absences }}</h3>
                    <p class="mb-0">حالات غياب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ total_lates }}</h3>
                    <p class="mb-0">حالات تأخير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ total_early_leaves }}</h3>
                    <p class="mb-0">انصراف مبكر</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3>{{ total_violations }}</h3>
                    <p class="mb-0">إجمالي المخالفات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Absence Records Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        سجل الغياب والتأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>نوع المخالفة</th>
                                    <th>المدة</th>
                                    <th>السبب</th>
                                    <th>الخصم</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in absence_records %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ record.employee.full_name }}
                                    </td>
                                    <td>{{ record.date|date:"Y-m-d" }}</td>
                                    <td>
                                        <span class="badge {{ record.absence_type_badge_class }}">
                                            {{ record.get_absence_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ record.duration_display }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ record.reason }}">
                                            {{ record.reason|truncatechars:30 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ record.deduction_amount }} {{ record.currency.symbol }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ record.status_badge_class }}">
                                            {{ record.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:absence_detail' record.pk %}" class="btn btn-sm btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:absence_edit' record.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if record.status == 'PENDING' %}
                                            <a href="{% url 'hr:absence_approve' record.pk %}" class="btn btn-sm btn-outline-success" title="اعتماد" onclick="return confirm('هل تريد اعتماد هذا السجل؟')">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="{% url 'hr:absence_reject' record.pk %}" class="btn btn-sm btn-outline-danger" title="رفض" onclick="return confirm('هل تريد رفض هذا السجل؟')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            {% endif %}
                                            <form method="post" action="{% url 'hr:absence_delete' record.pk %}" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-user-check fa-3x mb-3 d-block"></i>
                                        لا توجد مخالفات مسجلة
                                        <br>
                                        <a href="{% url 'hr:absence_create' %}" class="btn btn-primary mt-2">
                                            <i class="fas fa-plus me-2"></i>
                                            تسجيل أول مخالفة
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
});

// تأكيد الحذف
function confirmDelete(recordId) {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
        document.getElementById('delete-form-' + recordId).submit();
    }
}
</script>
{% endblock %}
