# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011-2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-25 09:47+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Basque (http://www.transifex.com/django/django/language/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Kudeaketa dokumentazioa"

msgid "Home"
msgstr "Hasiera"

msgid "Documentation"
msgstr "Dokumentazioa"

msgid "Bookmarklets"
msgstr "Markadoreak"

msgid "Documentation bookmarklets"
msgstr "Dokumentazio markadoreak"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Markadoreak instalatzeko eraman linka zure erreminta panelera (toolbar), edo "
"eskuineko botoiarekin klik egin eta gehitu erreminta panelera. Orain edozein "
"markadore aukera dezakezu webguneko edozein orrialdetatik."

msgid "Documentation for this page"
msgstr "Web orri honen dokumentazioa"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Edozein orritik, orria sortzen duen bistaren dokumentaziora eramango zaitu."

msgid "Tags"
msgstr "Etiketak"

msgid "List of all the template tags and their functions."
msgstr "Template tag guztien eta beraien funtzioen zerrnda."

msgid "Filters"
msgstr "Iragazkiak"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Txantiloi barneko aldagaien irteera aldatzeko aplikatzen diren ekintzak dira "
"Filter-ak."

msgid "Models"
msgstr "Ereduak"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Sisteman definitutako objektu eta beraien eremuen zehaztapenak dira "
"modeloak. Modelo bakoitzak eremu zerrenda bat dauka eta eremu hauetako "
"bakoitza txantiloietako aldagai gisa atzitu daiteke."

msgid "Views"
msgstr "Bistak"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Webgune publikoan agertzen den orri bakoitza bista batek sortzen du. Bistak "
"zehazten duzein txantiloi erabiliko den eta honek eskuragarri izango dituen "
"objektuak."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Adminera azkar sartzeko arakatzailearentzako tresnak"

msgid "Please install docutils"
msgstr "Instalatu docutils mesedez"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Admin dokumentazio sostemak Pythonen <a href=\"%(link)s\">docutils</a> "
"liburutegia behar du."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Eskatu zure administratzaileari <a href=\"%(link)s\">docutils</a> "
"instalatzeko."

#, python-format
msgid "Model: %(name)s"
msgstr "Modeloa: %(name)s"

msgid "Fields"
msgstr "Eremuak"

msgid "Field"
msgstr "Eremua"

msgid "Type"
msgstr "Mota"

msgid "Description"
msgstr "Deskribapena"

msgid "Methods with arguments"
msgstr "Argumentudun metodoak"

msgid "Method"
msgstr "Metodoa"

msgid "Arguments"
msgstr "Argumentuak"

msgid "Back to Model documentation"
msgstr "Bueltatu Modeloaren Dokumentaziora"

msgid "Model documentation"
msgstr "Modeloaren dokumentazioa"

msgid "Model groups"
msgstr "Modelo taldeak"

msgid "Templates"
msgstr "Txantiloiak"

#, python-format
msgid "Template: %(name)s"
msgstr "Txantiloia: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Txantiloia: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "\"%(name)s\" txantiloiaren bilaketa bidea:"

msgid "(does not exist)"
msgstr "(ez da existitzen)"

msgid "Back to Documentation"
msgstr "Bueltatu Dokumentaziora"

msgid "Template filters"
msgstr "Txantiloi iragazkiak"

msgid "Template filter documentation"
msgstr "Txantiloi iragazkien dokumentazioa"

msgid "Built-in filters"
msgstr "Iragazki integratuak"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Iragazki hauek erabiltzeko jarri <code>%(code)s</code> iragazkia erabili "
"aurretik zure txantiloian"

msgid "Template tags"
msgstr "Template tags"

msgid "Template tag documentation"
msgstr "Template tag documentazioa"

msgid "Built-in tags"
msgstr "Etiketa integratuak"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Tag hauek erabiltzeko, jarri <code>%(code)s</code> txantiloian taga erabili "
"aurretik."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Kontextua:"

msgid "Templates:"
msgstr "Txantiloia:"

msgid "Back to View documentation"
msgstr "Bueltatu Bisten Dokumentaziora"

msgid "View documentation"
msgstr "Bisten dokumentazioa"

msgid "Jump to namespace"
msgstr "Joan izen-espaziora"

msgid "Empty namespace"
msgstr "Izen-espazio hutsa"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Bistak %(name)s izen-espazio arabera"

msgid "Views by empty namespace"
msgstr "Bistak izen-espazio hutsen arabera"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Bista funtzioa: <code>%(full_name)s</code>. Izena: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiketa:"

msgid "filter:"
msgstr "filtroa:"

msgid "view:"
msgstr "bista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r aplikazioa ez da aurkitu"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(model_name)r modeloa ez da aurkitu %(app_label)r aplikazioan"

msgid "model:"
msgstr "modeloa:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "erlazionatutako `%(app_label)s.%(data_type)s` objektua"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "erlazionatutako `%(app_label)s.%(object_name)s` objektuak"

#, python-format
msgid "all %s"
msgstr "%s dena"

#, python-format
msgid "number of %s"
msgstr "%s zenbakia"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ez dirudi  url heredu objetua"
