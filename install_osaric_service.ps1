# PowerShell script لتثبيت خدمة أوساريك كخدمة Windows
# يجب تشغيل هذا الملف كمدير (Run as Administrator)

param(
    [switch]$Install,
    [switch]$Uninstall,
    [switch]$Start,
    [switch]$Stop,
    [switch]$Restart
)

$ServiceName = "OsaricService"
$ServiceDisplayName = "خدمة أوساريك - Osaric Django Service"
$ServiceDescription = "خدمة تشغيل تطبيق أوساريك Django بشكل دائم مع إعادة التشغيل التلقائي"
$ProjectPath = "D:\"
$ServiceScript = Join-Path $ProjectPath "osaric_service.py"
$PythonPath = (Get-Command python).Source
$ServiceCommand = "`"$PythonPath`" `"$ServiceScript`""

Write-Host "=== إدارة خدمة أوساريك ===" -ForegroundColor Green

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ يجب تشغيل هذا الملف كمدير (Run as Administrator)" -ForegroundColor Red
    exit 1
}

# دالة للتحقق من وجود الخدمة
function Test-ServiceExists {
    param($ServiceName)
    return (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) -ne $null
}

# دالة تثبيت الخدمة
function Install-OsaricService {
    Write-Host "🔧 تثبيت خدمة أوساريك..." -ForegroundColor Yellow
    
    # التحقق من وجود الملفات
    if (-not (Test-Path $ServiceScript)) {
        Write-Host "❌ ملف الخدمة غير موجود: $ServiceScript" -ForegroundColor Red
        return $false
    }
    
    if (-not (Test-Path (Join-Path $ProjectPath "manage.py"))) {
        Write-Host "❌ ملف manage.py غير موجود في: $ProjectPath" -ForegroundColor Red
        return $false
    }
    
    # حذف الخدمة إذا كانت موجودة
    if (Test-ServiceExists $ServiceName) {
        Write-Host "🗑️ حذف الخدمة الموجودة..." -ForegroundColor Yellow
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        sc.exe delete $ServiceName
        Start-Sleep -Seconds 2
    }
    
    # إنشاء الخدمة باستخدام NSSM (Non-Sucking Service Manager)
    # تحميل NSSM إذا لم يكن موجوداً
    $nssmPath = Join-Path $ProjectPath "nssm.exe"
    if (-not (Test-Path $nssmPath)) {
        Write-Host "📥 تحميل NSSM..." -ForegroundColor Yellow
        try {
            $nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
            $nssmZip = Join-Path $ProjectPath "nssm.zip"
            Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmZip
            Expand-Archive -Path $nssmZip -DestinationPath $ProjectPath -Force
            Copy-Item -Path (Join-Path $ProjectPath "nssm-2.24\win64\nssm.exe") -Destination $nssmPath
            Remove-Item -Path $nssmZip -Force
            Remove-Item -Path (Join-Path $ProjectPath "nssm-2.24") -Recurse -Force
            Write-Host "✅ تم تحميل NSSM بنجاح" -ForegroundColor Green
        } catch {
            Write-Host "❌ فشل في تحميل NSSM: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    
    # إنشاء الخدمة
    try {
        & $nssmPath install $ServiceName $PythonPath $ServiceScript
        & $nssmPath set $ServiceName DisplayName $ServiceDisplayName
        & $nssmPath set $ServiceName Description $ServiceDescription
        & $nssmPath set $ServiceName Start SERVICE_AUTO_START
        & $nssmPath set $ServiceName AppDirectory $ProjectPath
        & $nssmPath set $ServiceName AppStdout (Join-Path $ProjectPath "service_stdout.log")
        & $nssmPath set $ServiceName AppStderr (Join-Path $ProjectPath "service_stderr.log")
        & $nssmPath set $ServiceName AppRotateFiles 1
        & $nssmPath set $ServiceName AppRotateOnline 1
        & $nssmPath set $ServiceName AppRotateSeconds 86400
        & $nssmPath set $ServiceName AppRotateBytes 1048576
        
        Write-Host "✅ تم تثبيت الخدمة بنجاح!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ فشل في تثبيت الخدمة: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# دالة إلغاء تثبيت الخدمة
function Uninstall-OsaricService {
    Write-Host "🗑️ إلغاء تثبيت خدمة أوساريك..." -ForegroundColor Yellow
    
    if (Test-ServiceExists $ServiceName) {
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        sc.exe delete $ServiceName
        Write-Host "✅ تم إلغاء تثبيت الخدمة" -ForegroundColor Green
    } else {
        Write-Host "⚠️ الخدمة غير مثبتة" -ForegroundColor Yellow
    }
}

# دالة بدء الخدمة
function Start-OsaricService {
    Write-Host "🚀 بدء خدمة أوساريك..." -ForegroundColor Yellow
    
    if (Test-ServiceExists $ServiceName) {
        Start-Service -Name $ServiceName
        Write-Host "✅ تم بدء الخدمة" -ForegroundColor Green
    } else {
        Write-Host "❌ الخدمة غير مثبتة" -ForegroundColor Red
    }
}

# دالة إيقاف الخدمة
function Stop-OsaricService {
    Write-Host "⏹️ إيقاف خدمة أوساريك..." -ForegroundColor Yellow
    
    if (Test-ServiceExists $ServiceName) {
        Stop-Service -Name $ServiceName -Force
        Write-Host "✅ تم إيقاف الخدمة" -ForegroundColor Green
    } else {
        Write-Host "❌ الخدمة غير مثبتة" -ForegroundColor Red
    }
}

# دالة إعادة تشغيل الخدمة
function Restart-OsaricService {
    Write-Host "🔄 إعادة تشغيل خدمة أوساريك..." -ForegroundColor Yellow
    Stop-OsaricService
    Start-Sleep -Seconds 3
    Start-OsaricService
}

# تنفيذ الأوامر
if ($Install) {
    if (Install-OsaricService) {
        Start-OsaricService
    }
} elseif ($Uninstall) {
    Uninstall-OsaricService
} elseif ($Start) {
    Start-OsaricService
} elseif ($Stop) {
    Stop-OsaricService
} elseif ($Restart) {
    Restart-OsaricService
} else {
    Write-Host "الاستخدام:" -ForegroundColor Cyan
    Write-Host "  .\install_osaric_service.ps1 -Install    # تثبيت وبدء الخدمة" -ForegroundColor White
    Write-Host "  .\install_osaric_service.ps1 -Uninstall  # إلغاء تثبيت الخدمة" -ForegroundColor White
    Write-Host "  .\install_osaric_service.ps1 -Start      # بدء الخدمة" -ForegroundColor White
    Write-Host "  .\install_osaric_service.ps1 -Stop       # إيقاف الخدمة" -ForegroundColor White
    Write-Host "  .\install_osaric_service.ps1 -Restart    # إعادة تشغيل الخدمة" -ForegroundColor White
}

Write-Host "`nاضغط أي مفتاح للخروج..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
