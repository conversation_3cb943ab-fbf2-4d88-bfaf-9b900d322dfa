{% extends 'base/base.html' %}
{% load static %}

{% block title %}إنشاء أمر تصنيع{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.form-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.form-section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.form-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
}

.form-section-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.form-section-body {
    padding: 25px;
}

.add-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.delete-material, .delete-step, .delete-quality {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.material-input-row, .step-row, .quality-row {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.material-input-row:hover, .step-row:hover, .quality-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 15px 40px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
}

.materials-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.materials-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 12px;
    font-weight: 600;
    text-align: center;
}

.materials-table td {
    padding: 15px 12px;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #f8f9fa;
}

.materials-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
    transition: all 0.3s ease;
}

.material-form-row {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.availability-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    color: #856404;
    padding: 15px 20px;
    border-radius: 12px;
    margin-top: 15px;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.availability-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    color: #155724;
    padding: 15px 20px;
    border-radius: 12px;
    margin-top: 15px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.cost-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-top: 20px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.cost-summary h6 {
    margin-bottom: 15px;
    font-weight: 600;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.cost-item:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
    padding-top: 15px;
    border-top: 2px solid rgba(255,255,255,0.3);
}

.progress-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-fill {
    background: linear-gradient(90deg, #28a745, #20c997);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.floating-summary {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    padding: 20px;
    z-index: 1000;
    border: 1px solid #e9ecef;
    min-width: 300px;
}

.floating-summary h6 {
    color: #667eea;
    margin-bottom: 15px;
    font-weight: 600;
}

.floating-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.floating-total {
    border-top: 2px solid #667eea;
    padding-top: 10px;
    margin-top: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.required-field {
    position: relative;
}

.required-field::after {
    content: '*';
    color: #dc3545;
    position: absolute;
    top: 0;
    right: -10px;
    font-weight: bold;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.text-danger {
    color: #dc3545 !important;
    font-size: 0.85rem;
    margin-top: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.4);
}

/* تحسينات إضافية */
.quantity-input {
    font-weight: 500;
}

.quantity-input:focus {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.material-select {
    font-weight: 500;
}

.material-select option {
    padding: 8px;
}

/* تأثيرات بصرية */
.form-section:hover {
    box-shadow: 0 6px 25px rgba(0,0,0,0.12);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
}

/* تحسين الأزرار */
.btn {
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* تحسين الرسائل */
.alert-dismissible .btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: inherit;
}

/* تحسين الحقول */
.form-control[readonly] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    font-weight: 500;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-industry text-primary me-2"></i>
            إنشاء أمر تصنيع جديد
        </h1>
        <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i> العودة للقائمة
        </a>
    </div>
    
    <form method="post" id="manufacturing-form">
        {% csrf_token %}
        
        <!-- بيانات أساسية -->
        <div class="form-section">
            <div class="form-section-header mb-2">
                <h5 class="fw-bold text-primary"><i class="fas fa-box me-2"></i>بيانات المنتج النهائي</h5>
            </div>
            <div class="form-section-body">
                <div class="row g-4 align-items-end">
                    <div class="col-md-6">
                        <label class="form-label required-field"><i class="fas fa-cube me-1 text-secondary"></i>{{ form.finished_product.label }}</label>
                        {{ form.finished_product }}
                        {% if form.finished_product.errors %}
                            <div class="text-danger small">{{ form.finished_product.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required-field"><i class="fas fa-warehouse me-1 text-secondary"></i>{{ form.finished_goods_warehouse.label }}</label>
                        {{ form.finished_goods_warehouse }}
                        {% if form.finished_goods_warehouse.errors %}
                            <div class="text-danger small">{{ form.finished_goods_warehouse.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label required-field"><i class="fas fa-sort-numeric-up me-1 text-secondary"></i>{{ form.quantity_to_produce.label }}</label>
                        {{ form.quantity_to_produce }}
                        {% if form.quantity_to_produce.errors %}
                            <div class="text-danger small">{{ form.quantity_to_produce.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-calendar-alt me-1 text-secondary"></i>{{ form.expected_start_date.label }}</label>
                        {{ form.expected_start_date }}
                        {% if form.expected_start_date.errors %}
                            <div class="text-danger small">{{ form.expected_start_date.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-calendar-check me-1 text-secondary"></i>{{ form.expected_completion_date.label }}</label>
                        {{ form.expected_completion_date }}
                        {% if form.expected_completion_date.errors %}
                            <div class="text-danger small">{{ form.expected_completion_date.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-industry me-1 text-secondary"></i>{{ form.production_line.label }}</label>
                        {{ form.production_line }}
                        {% if form.production_line.errors %}
                            <div class="text-danger small">{{ form.production_line.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-clock me-1 text-secondary"></i>{{ form.shift.label }}</label>
                        {{ form.shift }}
                        {% if form.shift.errors %}
                            <div class="text-danger small">{{ form.shift.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-bolt me-1 text-secondary"></i>{{ form.priority.label }}</label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <div class="text-danger small">{{ form.priority.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- المواد الخام -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-box-seam me-2"></i>المواد الخام المطلوبة</h5>
            </div>
            <div class="form-section-body">
                <!-- نموذج إضافة مادة جديدة -->
                <div class="material-form-row">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label class="form-label">المادة الخام</label>
                            <select id="material-select" class="form-select material-select">
                                <option value="">اختر المادة الخام...</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الكمية المطلوبة</label>
                            <input type="number" id="quantity-input" class="form-control quantity-input" min="0.001" step="0.001" placeholder="0.000">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المتوفر</label>
                            <input type="text" id="available-display" class="form-control" readonly>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="text" id="unit-price-display" class="form-control" readonly>
                        </div>
                        <div class="col-md-2">
                            <button type="button" id="add-material-btn" class="btn btn-primary w-100" disabled>
                                <i class="fas fa-plus me-1"></i>إضافة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- جدول المواد المضافة -->
                <div class="materials-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المخزن</th>
                                <th>المادة الخام</th>
                                <th>النوع</th>
                                <th>المتوفر</th>
                                <th>المطلوب</th>
                                <th>نسبة الاستخدام</th>
                                <th>سعر الوحدة</th>
                                <th>إجمالي التكلفة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="materials-tbody">
                            <!-- سيتم ملء هذا الجدول ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <!-- رسائل التوفر -->
                <div id="materials-availability-warning" class="availability-warning" style="display: none;">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير: نقص في المواد</h6>
                    <div id="materials-shortage-details"></div>
                </div>
                <div id="materials-availability-success" class="availability-success" style="display: none;">
                    <h6><i class="fas fa-check-circle me-2"></i>جميع المواد متوفرة بكميات كافية</h6>
                </div>

                <!-- ملخص تكلفة المواد -->
                <div class="cost-summary" id="materials-cost-summary" style="display: none;">
                    <h6><i class="fas fa-calculator me-2"></i>ملخص تكلفة المواد</h6>
                    <div class="cost-item">
                        <span>عدد المواد:</span>
                        <span id="materials-count">0</span>
                    </div>
                    <div class="cost-item">
                        <span>إجمالي تكلفة المواد:</span>
                        <span id="total-materials-cost">0.00 ج.م</span>
                    </div>
                </div>

                <!-- الحقول المخفية للمواد -->
                <div id="hidden-fields"></div>
            </div>
        </div>

        <!-- خطوات التصنيع -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-tasks me-2"></i>خطوات التصنيع</h5>
            </div>
            <div class="form-section-body">
                <div class="step-form-row">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">اسم الخطوة</label>
                            <input type="text" id="step-name-input" class="form-control" placeholder="مثال: خلط المواد">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">وصف الخطوة</label>
                            <input type="text" id="step-desc-input" class="form-control" placeholder="شرح مختصر">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الوقت المتوقع (دقيقة)</label>
                            <input type="number" id="step-time-input" class="form-control" min="1" step="1" placeholder="0">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">العامل المسؤول</label>
                            <input type="text" id="step-worker-input" class="form-control" placeholder="اسم العامل">
                        </div>
                        <div class="col-md-1">
                            <button type="button" id="add-step-btn" class="btn btn-primary w-100" disabled>
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive mt-3">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الخطوة</th>
                                <th>الوصف</th>
                                <th>الوقت المتوقع</th>
                                <th>العامل المسؤول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="steps-tbody">
                            <!-- صفوف الخطوات الديناميكية -->
                        </tbody>
                    </table>
                </div>
                <div id="hidden-steps-fields"></div>
            </div>
        </div>

        <!-- فحوصات الجودة -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-vial me-2"></i>فحوصات الجودة</h5>
            </div>
            <div class="form-section-body">
                <div class="quality-form-row">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label class="form-label">اسم الفحص</label>
                            <input type="text" id="quality-name-input" class="form-control" placeholder="مثال: اختبار الصلابة">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المعيار</label>
                            <input type="text" id="quality-standard-input" class="form-control" placeholder="مثال: ASTM D638">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">النتيجة المتوقعة</label>
                            <input type="text" id="quality-expected-input" class="form-control" placeholder="مثال: ≥ 50 MPa">
                        </div>
                        <div class="col-md-1">
                            <button type="button" id="add-quality-btn" class="btn btn-primary w-100" disabled>
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive mt-3">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الفحص</th>
                                <th>المعيار</th>
                                <th>النتيجة المتوقعة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="qualities-tbody">
                            <!-- صفوف الفحوصات الديناميكية -->
                        </tbody>
                    </table>
                </div>
                <div id="hidden-qualities-fields"></div>
            </div>
        </div>

        <!-- التكاليف -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-calculator me-2"></i>التكاليف</h5>
            </div>
            <div class="form-section-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">{{ form.labor_cost.label }}</label>
                        {{ form.labor_cost }}
                        {% if form.labor_cost.errors %}
                            <div class="text-danger">{{ form.labor_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.overhead_cost.label }}</label>
                        {{ form.overhead_cost }}
                        {% if form.overhead_cost.errors %}
                            <div class="text-danger">{{ form.overhead_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.packaging_cost.label }}</label>
                        {{ form.packaging_cost }}
                        {% if form.packaging_cost.errors %}
                            <div class="text-danger">{{ form.packaging_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.quality_control_cost.label }}</label>
                        {{ form.quality_control_cost }}
                        {% if form.quality_control_cost.errors %}
                            <div class="text-danger">{{ form.quality_control_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.operating_expenses.label }}</label>
                        {{ form.operating_expenses }}
                        {% if form.operating_expenses.errors %}
                            <div class="text-danger">{{ form.operating_expenses.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.electricity_cost.label }}</label>
                        {{ form.electricity_cost }}
                        {% if form.electricity_cost.errors %}
                            <div class="text-danger">{{ form.electricity_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.transportation_cost.label }}</label>
                        {{ form.transportation_cost }}
                        {% if form.transportation_cost.errors %}
                            <div class="text-danger">{{ form.transportation_cost.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.other_costs.label }}</label>
                        {{ form.other_costs }}
                        {% if form.other_costs.errors %}
                            <div class="text-danger">{{ form.other_costs.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- الجودة -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-microscope me-2"></i>معايير الجودة</h5>
            </div>
            <div class="form-section-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">{{ form.quality_standard.label }}</label>
                        {{ form.quality_standard }}
                        {% if form.quality_standard.errors %}
                            <div class="text-danger">{{ form.quality_standard.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">{{ form.supervisor.label }}</label>
                        {{ form.supervisor }}
                        {% if form.supervisor.errors %}
                            <div class="text-danger">{{ form.supervisor.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-12">
                        <label class="form-label">{{ form.quality_notes.label }}</label>
                        {{ form.quality_notes }}
                        {% if form.quality_notes.errors %}
                            <div class="text-danger">{{ form.quality_notes.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        <div class="form-section">
            <div class="form-section-header">
                <h5><i class="fas fa-sticky-note me-2"></i>الملاحظات</h5>
            </div>
            <div class="form-section-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">{{ form.internal_notes.label }}</label>
                        {{ form.internal_notes }}
                        {% if form.internal_notes.errors %}
                            <div class="text-danger">{{ form.internal_notes.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-12">
                        <label class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center my-4">
            <button type="submit" class="btn btn-success btn-lg px-5">
                <i class="fas fa-save me-2"></i>حفظ أمر التصنيع
            </button>
        </div>
    </form>
</div>

<!-- الملخص العائم -->
<div class="floating-summary" id="floating-summary" style="display: none;">
    <h6><i class="fas fa-chart-pie me-2"></i>ملخص التكاليف</h6>
    <div class="floating-item">
        <span>تكلفة المواد:</span>
        <span id="floating-materials">0.00 ج.م</span>
    </div>
    <div class="floating-item">
        <span>تكلفة التشغيل:</span>
        <span id="floating-operating">0.00 ج.م</span>
    </div>
    <div class="floating-item floating-total">
        <span>الإجمالي:</span>
        <span id="floating-total">0.00 ج.م</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // تفعيل Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });
    
    // متغيرات عامة
    var materialsList = [];
    var materialCounter = 0;
    
    // دالة تحميل المواد
    function loadMaterials() {
        console.log('جاري تحميل المواد...');
        
        $.ajax({
            url: '/inventory/api/all-warehouse-materials/',
            method: 'GET',
            success: function(data) {
                console.log('استجابة API:', data);
                
                if (data.success && data.materials) {
                    console.log('تم تحميل', data.materials.length, 'مادة');
                    
                    // ملء قائمة المواد
                    var $select = $('#material-select');
                    $select.empty();
                    $select.append('<option value="">اختر المادة...</option>');
                    
                    data.materials.forEach(function(material) {
                        var option = new Option(
                            material.name + ' (' + material.code + ') - ' + material.warehouse_name,
                            material.id,
                            false,
                            false
                        );
                        option.material = material;
                        $select.append(option);
                    });
                    
                    console.log('تم ملء قائمة المواد');
                } else {
                    console.error('خطأ في تحميل المواد:', data);
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في الاتصال:', error);
            }
        });
    }
    
    // دالة اختيار مادة
    function selectMaterial(materialData) {
        console.log('تم اختيار المادة:', materialData);
        
        // ملء الحقول
        $('#available-display').val(materialData.available_quantity);
        $('#unit-price-display').val(materialData.unit_price);
        
        // تفعيل زر الإضافة
        validateAddButton();
    }
    
    // دالة التحقق من زر الإضافة
    function validateAddButton() {
        var materialId = $('#material-select').val();
        var quantity = parseFloat($('#quantity-input').val()) || 0;
        
        if (materialId && quantity > 0) {
            $('#add-material-btn').prop('disabled', false);
        } else {
            $('#add-material-btn').prop('disabled', true);
        }
    }
    
    // دالة إضافة مادة للجدول
    function addMaterialToTable() {
        var materialId = $('#material-select').val();
        var $selectedOption = $('#material-select option:selected');
        var materialData = $selectedOption[0].material;
        var quantity = parseFloat($('#quantity-input').val()) || 0;
        
        console.log('=== إضافة مادة للجدول ===');
        console.log('materialId:', materialId);
        console.log('materialData:', materialData);
        console.log('quantity:', quantity);
        
        if (!materialData || quantity <= 0) {
            alert('يرجى اختيار مادة وكمية صحيحة');
            return;
        }
        
        // التحقق من صحة البيانات
        if (!materialData.available_quantity) {
            console.error('خطأ: لا توجد كمية متاحة للمادة', materialData.name);
            alert('خطأ في بيانات المادة: لا توجد كمية متاحة');
            return;
        }
        
        // التحقق من عدم تكرار المادة
        var isDuplicate = materialsList.some(function(item) {
            return item.materialId === materialId;
        });
        
        if (isDuplicate) {
            alert('هذه المادة مضافة بالفعل');
            return;
        }
        
        // إضافة المادة للقائمة
        var newMaterial = {
            id: ++materialCounter,
            materialId: materialId,
            materialData: materialData,
            quantity: quantity,
            unitPrice: parseFloat(materialData.unit_price) || 0,
            totalCost: quantity * (parseFloat(materialData.unit_price) || 0)
        };
        
        console.log('المادة الجديدة:', newMaterial);
        
        materialsList.push(newMaterial);
        
        // إضافة صف للجدول
        var $tbody = $('#materials-tbody');
        var $row = $(`
            <tr data-material-id="${newMaterial.id}">
                <td>${newMaterial.id}</td>
                <td>${materialData.warehouse_name}</td>
                <td>${materialData.name} (${materialData.code})</td>
                <td>${materialData.item_type_ar || materialData.item_type}</td>
                <td>${materialData.available_quantity}</td>
                <td>${quantity}</td>
                <td>
                    <div>${((quantity / parseFloat(materialData.available_quantity)) * 100).toFixed(1)}%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${Math.min((quantity / parseFloat(materialData.available_quantity)) * 100, 100)}%"></div>
                    </div>
                </td>
                <td>${newMaterial.unitPrice.toFixed(2)}</td>
                <td>${newMaterial.totalCost.toFixed(2)}</td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm remove-material" data-id="${newMaterial.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `);
        
        $tbody.append($row);
        
        // إضافة حقول مخفية للنموذج
        addHiddenFields(newMaterial);
        
        // تحديث الإحصائيات
        updateMaterialsStats();
        
        // مسح الحقول
        clearMaterialFields();
        
        console.log('تم إضافة المادة للجدول:', newMaterial);
        console.log('قائمة المواد الحالية:', materialsList);
        
        // التحقق من التوفر بعد الإضافة
        setTimeout(function() {
            checkMaterialsAvailability();
        }, 100);
    }
    
    // دالة إضافة حقول مخفية
    function addHiddenFields(material) {
        var $hiddenFields = $('#hidden-fields');
        
        $hiddenFields.append(`
            <input type="hidden" name="materials[${material.id}][material_id]" value="${material.materialId}">
            <input type="hidden" name="materials[${material.id}][quantity]" value="${material.quantity}">
            <input type="hidden" name="materials[${material.id}][unit_price]" value="${material.unitPrice}">
            <input type="hidden" name="materials[${material.id}][total_cost]" value="${material.totalCost}">
        `);
    }
    
    // دالة تحديث إحصائيات المواد
    function updateMaterialsStats() {
        var totalCost = materialsList.reduce(function(sum, material) {
            return sum + material.totalCost;
        }, 0);
        
        $('#materials-count').text(materialsList.length);
        $('#total-materials-cost').text(totalCost.toFixed(2) + ' ج.م');
        
        // إظهار ملخص تكلفة المواد
        if (materialsList.length > 0) {
            $('#materials-cost-summary').show();
        } else {
            $('#materials-cost-summary').hide();
        }
        
        // تحديث الملخص العائم
        updateFloatingSummary();
        
        console.log('إجمالي تكلفة المواد:', totalCost);
    }
    
    // دالة تحديث الملخص العائم
    function updateFloatingSummary() {
        var materialsCost = materialsList.reduce(function(sum, material) {
            return sum + material.totalCost;
        }, 0);
        
        var operatingCost = parseFloat($('#id_labor_cost').val()) || 0 +
                           parseFloat($('#id_overhead_cost').val()) || 0 +
                           parseFloat($('#id_packaging_cost').val()) || 0 +
                           parseFloat($('#id_quality_control_cost').val()) || 0 +
                           parseFloat($('#id_operating_expenses').val()) || 0 +
                           parseFloat($('#id_electricity_cost').val()) || 0 +
                           parseFloat($('#id_transportation_cost').val()) || 0 +
                           parseFloat($('#id_other_costs').val()) || 0;
        
        var totalCost = materialsCost + operatingCost;
        
        $('#floating-materials').text(materialsCost.toFixed(2) + ' ج.م');
        $('#floating-operating').text(operatingCost.toFixed(2) + ' ج.م');
        $('#floating-total').text(totalCost.toFixed(2) + ' ج.م');
        
        // إظهار الملخص العائم إذا كان هناك تكاليف
        if (totalCost > 0) {
            $('#floating-summary').show();
        } else {
            $('#floating-summary').hide();
        }
    }
    
    // دالة مسح حقول المادة
    function clearMaterialFields() {
        $('#material-select').val('');
        $('#quantity-input').val('');
        $('#available-display').val('');
        $('#unit-price-display').val('');
        
        validateAddButton();
    }
    
    // دالة حذف مادة واحدة
    function removeMaterial(materialId) {
        materialsList = materialsList.filter(function(item) {
            return item.id !== materialId;
        });
        
        $(`tr[data-material-id="${materialId}"]`).remove();
        $(`input[name*="[${materialId}]"]`).remove();
        
        updateMaterialsStats();
        console.log('تم حذف المادة:', materialId);
    }
    
    // دالة التحقق من توفر المواد
    function checkMaterialsAvailability() {
        console.log('=== التحقق من توفر المواد ===');
        
        if (materialsList.length === 0) {
            hideAvailabilityMessages();
            return;
        }
        
        var quantityToProduce = parseFloat($('#id_quantity_to_produce').val()) || 0;
        var shortages = [];
        var allAvailable = true;
        
        console.log('الكمية المطلوبة للإنتاج:', quantityToProduce);
        console.log('قائمة المواد:', materialsList);
        
        materialsList.forEach(function(material, index) {
            console.log(`المادة ${index}:`, material);
            console.log(`محتوى materialData:`, material.materialData);
            
            // التحقق من وجود البيانات
            if (!material.materialData) {
                console.error('خطأ: لا توجد بيانات للمادة', material);
                return;
            }
            
            var requiredQuantity = parseFloat(material.quantity) || 0;
            var availableQuantity = parseFloat(material.materialData.available_quantity) || 0;
            var shortage = requiredQuantity - availableQuantity;
            
            console.log(`المادة: ${material.materialData.name}, المطلوب: ${requiredQuantity}, المتوفر: ${availableQuantity}, النقص: ${shortage}`);
            
            // إضافة تحقق إضافي للتأكد من صحة البيانات
            if (isNaN(requiredQuantity) || isNaN(availableQuantity)) {
                console.error('خطأ: قيم غير صحيحة للمادة', material.materialData.name);
                return;
            }
            
            if (shortage > 0) {
                shortages.push({
                    name: material.materialData.name,
                    required: requiredQuantity,
                    available: availableQuantity,
                    shortage: shortage,
                    warehouse: material.materialData.warehouse_name
                });
                allAvailable = false;
            }
        });
        
        if (shortages.length > 0) {
            showAvailabilityWarning(shortages);
        } else {
            showAvailabilitySuccess();
        }
        
        console.log('نتيجة التحقق:', { shortages: shortages, allAvailable: allAvailable });
    }
    
    // دالة إظهار تحذير نقص المواد
    function showAvailabilityWarning(shortages) {
        var detailsHtml = '<ul class="mb-0">';
        shortages.forEach(function(item) {
            detailsHtml += `<li><strong>${item.name}</strong> (${item.warehouse}): المطلوب ${item.required}، المتوفر ${item.available}، النقص ${item.shortage}</li>`;
        });
        detailsHtml += '</ul>';
        
        $('#materials-shortage-details').html(detailsHtml);
        $('#materials-availability-warning').show();
        $('#materials-availability-success').hide();
        
        console.log('تم إظهار تحذير نقص المواد:', shortages);
    }
    
    // دالة إظهار رسالة نجاح توفر المواد
    function showAvailabilitySuccess() {
        $('#materials-availability-warning').hide();
        $('#materials-availability-success').show();
        console.log('جميع المواد متوفرة بكميات كافية');
    }
    
    // دالة إخفاء رسائل التوفر
    function hideAvailabilityMessages() {
        $('#materials-availability-warning').hide();
        $('#materials-availability-success').hide();
    }
    
    // مستمعي الأحداث
    
    // عند تغيير المادة
    $('#material-select').on('change', function() {
        var $selectedOption = $(this).find('option:selected');
        if ($selectedOption.length > 0 && $selectedOption[0].material) {
            selectMaterial($selectedOption[0].material);
        } else {
            clearMaterialFields();
        }
    });
    
    // عند تغيير الكمية
    $('#quantity-input').on('input', function() {
        validateAddButton();
    });
    
    // عند الضغط على زر الإضافة
    $('#add-material-btn').on('click', function() {
        addMaterialToTable();
    });
    
    // عند الضغط على زر حذف مادة
    $(document).on('click', '.remove-material', function() {
        var materialId = parseInt($(this).data('id'));
        removeMaterial(materialId);
    });
    
    // عند تغيير كمية الإنتاج
    $('#id_quantity_to_produce').on('input', function() {
        setTimeout(function() {
            checkMaterialsAvailability();
        }, 500);
    });
    
    // عند تغيير أي من حقول التكاليف
    $('input[id^="id_"]').on('input', function() {
        updateFloatingSummary();
    });
    
    // تنسيق حقول العملة
    $('.currency-input').on('input', function() {
        var value = $(this).val();
        if (value && !isNaN(value)) {
            $(this).val(parseFloat(value).toFixed(2));
        }
    });
    
    // التحقق من صحة التواريخ
    $('#id_expected_start_date, #id_expected_completion_date').on('change', function() {
        var startDate = $('#id_expected_start_date').val();
        var completionDate = $('#id_expected_completion_date').val();
        
        if (startDate && completionDate && startDate > completionDate) {
            alert('تاريخ البدء لا يمكن أن يكون بعد تاريخ الانتهاء');
            $(this).val('');
        }
    });
    
    // التحقق من الكمية
    $('#id_quantity_to_produce').on('input', function() {
        var quantity = parseFloat($(this).val());
        if (quantity <= 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // تحميل المواد عند تحميل الصفحة
    loadMaterials();
    
    console.log('تم تهيئة نظام المواد والتحكم في التكاليف');

    // خطوات التصنيع
    var stepsList = [];
    var stepCounter = 0;

    function validateStepButton() {
        var name = $('#step-name-input').val().trim();
        var desc = $('#step-desc-input').val().trim();
        var time = $('#step-time-input').val();
        var worker = $('#step-worker-input').val().trim();
        if (name && desc && time > 0 && worker) {
            $('#add-step-btn').prop('disabled', false);
        } else {
            $('#add-step-btn').prop('disabled', true);
        }
    }
    $('#step-name-input, #step-desc-input, #step-time-input, #step-worker-input').on('input', validateStepButton);

    $('#add-step-btn').on('click', function() {
        var name = $('#step-name-input').val().trim();
        var desc = $('#step-desc-input').val().trim();
        var time = parseInt($('#step-time-input').val()) || 0;
        var worker = $('#step-worker-input').val().trim();
        if (!name || !desc || time <= 0 || !worker) return;
        var id = ++stepCounter;
        var newStep = {id, name, desc, time, worker};
        stepsList.push(newStep);
        var $row = $(`
            <tr data-step-id="${id}">
                <td>${id}</td>
                <td>${name}</td>
                <td>${desc}</td>
                <td>${time}</td>
                <td>${worker}</td>
                <td><button type="button" class="btn btn-danger btn-sm remove-step" data-id="${id}"><i class="fas fa-trash"></i></button></td>
            </tr>
        `);
        $('#steps-tbody').append($row);
        $('#hidden-steps-fields').append(`
            <input type="hidden" name="steps[${id}][name]" value="${name}">
            <input type="hidden" name="steps[${id}][desc]" value="${desc}">
            <input type="hidden" name="steps[${id}][time]" value="${time}">
            <input type="hidden" name="steps[${id}][worker]" value="${worker}">
        `);
        $('#step-name-input, #step-desc-input, #step-time-input, #step-worker-input').val('');
        validateStepButton();
    });
    $(document).on('click', '.remove-step', function() {
        var id = parseInt($(this).data('id'));
        stepsList = stepsList.filter(s => s.id !== id);
        $(`tr[data-step-id="${id}"]`).remove();
        $(`#hidden-steps-fields input[name^='steps[${id}]']`).remove();
    });

    // فحوصات الجودة
    var qualitiesList = [];
    var qualityCounter = 0;
    function validateQualityButton() {
        var name = $('#quality-name-input').val().trim();
        var standard = $('#quality-standard-input').val().trim();
        var expected = $('#quality-expected-input').val().trim();
        if (name && standard && expected) {
            $('#add-quality-btn').prop('disabled', false);
        } else {
            $('#add-quality-btn').prop('disabled', true);
        }
    }
    $('#quality-name-input, #quality-standard-input, #quality-expected-input').on('input', validateQualityButton);
    $('#add-quality-btn').on('click', function() {
        var name = $('#quality-name-input').val().trim();
        var standard = $('#quality-standard-input').val().trim();
        var expected = $('#quality-expected-input').val().trim();
        if (!name || !standard || !expected) return;
        var id = ++qualityCounter;
        var newQuality = {id, name, standard, expected};
        qualitiesList.push(newQuality);
        var $row = $(`
            <tr data-quality-id="${id}">
                <td>${id}</td>
                <td>${name}</td>
                <td>${standard}</td>
                <td>${expected}</td>
                <td><button type="button" class="btn btn-danger btn-sm remove-quality" data-id="${id}"><i class="fas fa-trash"></i></button></td>
            </tr>
        `);
        $('#qualities-tbody').append($row);
        $('#hidden-qualities-fields').append(`
            <input type="hidden" name="qualities[${id}][name]" value="${name}">
            <input type="hidden" name="qualities[${id}][standard]" value="${standard}">
            <input type="hidden" name="qualities[${id}][expected]" value="${expected}">
        `);
        $('#quality-name-input, #quality-standard-input, #quality-expected-input').val('');
        validateQualityButton();
    });
    $(document).on('click', '.remove-quality', function() {
        var id = parseInt($(this).data('id'));
        qualitiesList = qualitiesList.filter(q => q.id !== id);
        $(`tr[data-quality-id="${id}"]`).remove();
        $(`#hidden-qualities-fields input[name^='qualities[${id}]']`).remove();
    });
});
</script>
{% endblock %}
