#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.contrib.auth.models import User
from decimal import Decimal

def create_test_item():
    # Get required objects
    admin_user = User.objects.get(username='admin')
    category = ItemCategory.objects.get(code='GEN')
    unit = Unit.objects.get(code='PCS')
    
    # Create test item
    item = Item.objects.create(
        code='TEST001',
        name='Test Item for Delete',
        category=category,
        unit=unit,
        cost_price=Decimal('10.00'),
        selling_price=Decimal('15.00'),
        is_active=True,
        created_by=admin_user
    )
    
    print(f"Created test item: {item.code} - {item.name}")

if __name__ == "__main__":
    create_test_item()