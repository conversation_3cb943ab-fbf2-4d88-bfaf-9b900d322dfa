{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-chart-pie text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">ملخص شامل لكشف المرتبات والتكاليف الإجمالية</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-primary" onclick="generatePayrollSummary()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء ملخص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white payroll-card">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h3>{{ total_gross_salary }}</h3>
                    <p class="mb-0">إجمالي المرتبات الأساسية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white payroll-card">
                <div class="card-body text-center">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <h3>{{ total_allowances }}</h3>
                    <p class="mb-0">إجمالي البدلات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white payroll-card">
                <div class="card-body text-center">
                    <i class="fas fa-minus-circle fa-2x mb-2"></i>
                    <h3>{{ total_deductions }}</h3>
                    <p class="mb-0">إجمالي الخصومات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white payroll-card">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x mb-2"></i>
                    <h3>{{ total_net_salary }}</h3>
                    <p class="mb-0">صافي المرتبات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">الفترة:</label>
                            <select class="form-select" onchange="filterByPeriod()">
                                <option value="month" selected>هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">القسم:</label>
                            <select class="form-select" onchange="filterByDepartment()">
                                <option value="">جميع الأقسام</option>
                                <option value="تقنية المعلومات">تقنية المعلومات</option>
                                <option value="المالية">المالية</option>
                                <option value="المبيعات">المبيعات</option>
                                <option value="الإنتاج">الإنتاج</option>
                                <option value="الموارد البشرية">الموارد البشرية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التقرير:</label>
                            <select class="form-select" onchange="changeReportType()">
                                <option value="summary" selected>ملخص</option>
                                <option value="detailed">تفصيلي</option>
                                <option value="comparison">مقارنة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportPayroll()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    Excel
                                </button>
                                <button class="btn btn-info" onclick="printPayroll()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Breakdown -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        تفصيل كشف المرتبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder" style="height: 300px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">رسم بياني دائري لتفصيل المرتبات</h5>
                            <p class="text-muted">يظهر توزيع المرتبات والبدلات والخصومات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        التفاصيل المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="payroll-breakdown">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>المرتبات الأساسية:</span>
                            <strong class="text-primary">{{ basic_salary_percentage }}%</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>البدلات:</span>
                            <strong class="text-success">{{ allowances_percentage }}%</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>العمل الإضافي:</span>
                            <strong class="text-warning">{{ overtime_percentage }}%</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>الخصومات:</span>
                            <strong class="text-danger">{{ deductions_percentage }}%</strong>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>صافي المرتبات:</strong></span>
                            <strong class="text-info">{{ net_salary_percentage }}%</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Payroll Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        ملخص المرتبات حسب الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>القسم</th>
                                    <th>عدد الموظفين</th>
                                    <th>إجمالي المرتبات</th>
                                    <th>البدلات</th>
                                    <th>الخصومات</th>
                                    <th>صافي المرتبات</th>
                                    <th>متوسط الراتب</th>
                                    <th>النسبة من الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in department_payroll %}
                                <tr>
                                    <td>
                                        <i class="fas fa-building text-primary me-2"></i>
                                        <strong>{{ dept.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ dept.employee_count }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ dept.total_basic }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">{{ dept.total_allowances }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ dept.total_deductions }} ج.م</span>
                                    </td>
                                    <td>
                                        <strong class="text-info">{{ dept.total_net }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="text-warning">{{ dept.avg_salary }} ج.م</span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: {{ dept.percentage }}%">
                                                {{ dept.percentage }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        لا توجد بيانات مرتبات للأقسام
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Salary Distribution Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تحليل توزيع الرواتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-placeholder" style="height: 300px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">رسم بياني للتوزيع</h5>
                                    <p class="text-muted">توزيع الرواتب حسب الفئات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="salary-distribution">
                                <h6 class="mb-3">فئات الرواتب</h6>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="salary-indicator bg-success me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">أقل من 5,000 ج.م</h6>
                                            <small class="text-muted">الرواتب المبتدئة</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success">15 موظف</strong>
                                        <br>
                                        <small class="text-muted">25%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="salary-indicator bg-primary me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">5,000 - 10,000 ج.م</h6>
                                            <small class="text-muted">الرواتب المتوسطة</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-primary">25 موظف</strong>
                                        <br>
                                        <small class="text-muted">42%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="salary-indicator bg-warning me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">10,000 - 15,000 ج.م</h6>
                                            <small class="text-muted">الرواتب العالية</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-warning">15 موظف</strong>
                                        <br>
                                        <small class="text-muted">25%</small>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="salary-indicator bg-danger me-3" style="width: 20px; height: 20px; border-radius: 50%;"></div>
                                        <div>
                                            <h6 class="mb-0">أكثر من 15,000 ج.م</h6>
                                            <small class="text-muted">الرواتب الإدارية</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-danger">5 موظفين</strong>
                                        <br>
                                        <small class="text-muted">8%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Payroll Trend -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه المرتبات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder" style="height: 250px; background: linear-gradient(45deg, #f8f9fa, #e9ecef); border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">رسم بياني لاتجاه المرتبات</h5>
                            <p class="text-muted">يظهر تطور إجمالي المرتبات عبر الأشهر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات المرتبات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" role="alert">
                        <strong>معلومة:</strong>
                        <br>
                        زيادة في إجمالي المرتبات بنسبة 5% عن الشهر الماضي
                    </div>
                    <div class="alert alert-warning" role="alert">
                        <strong>تنبيه:</strong>
                        <br>
                        ارتفاع في بدل العمل الإضافي بنسبة 12%
                    </div>
                    <div class="alert alert-success" role="alert">
                        <strong>جيد:</strong>
                        <br>
                        انخفاض الخصومات بنسبة 8% هذا الشهر
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Comparison -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        مقارنة الأداء المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-primary">الشهر الحالي</h6>
                                <h4 class="text-success">{{ total_net_salary }}</h4>
                                <small class="text-muted">صافي المرتبات</small>
                                <div class="mt-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-arrow-up me-1"></i>+5%
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-warning">الشهر الماضي</h6>
                                <h4 class="text-secondary">556,048 ج.م</h4>
                                <small class="text-muted">صافي المرتبات</small>
                                <div class="mt-2">
                                    <span class="badge bg-warning">
                                        <i class="fas fa-minus me-1"></i>-2%
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-info">نفس الشهر العام الماضي</h6>
                                <h4 class="text-secondary">520,320 ج.م</h4>
                                <small class="text-muted">صافي المرتبات</small>
                                <div class="mt-2">
                                    <span class="badge bg-info">
                                        <i class="fas fa-arrow-up me-1"></i>+12%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Analysis -->
    <div class="row">
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تحليل التكاليف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>تكلفة المرتبات الشهرية:</span>
                            <strong class="text-primary">{{ monthly_cost }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>التكلفة السنوية المتوقعة:</span>
                            <strong class="text-info">{{ annual_cost }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>متوسط التكلفة/موظف:</span>
                            <strong class="text-warning">{{ avg_cost_per_employee }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>نسبة المرتبات من الإيرادات:</span>
                            <strong class="text-success">{{ salary_to_revenue_ratio }}%</strong>
                        </div>
                    </div>
                    <div class="progress mt-3">
                        <div class="progress-bar bg-success" style="width: {{ salary_to_revenue_ratio }}%">
                            {{ salary_to_revenue_ratio }}%
                        </div>
                    </div>
                    <small class="text-muted">نسبة صحية (أقل من 50%)</small>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        رؤى وتوصيات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success" role="alert">
                        <strong>مؤشر إيجابي:</strong>
                        <br>
                        <i class="fas fa-check-circle me-1"></i>
                        نمو صحي في المرتبات بنسبة 5% شهرياً
                    </div>
                    <div class="alert alert-info" role="alert">
                        <strong>ملاحظة:</strong>
                        <br>
                        <i class="fas fa-info-circle me-1"></i>
                        قسم تقنية المعلومات لديه أعلى متوسط راتب
                    </div>
                    <div class="alert alert-warning" role="alert">
                        <strong>توصية:</strong>
                        <br>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        مراجعة هيكل البدلات في قسم الإنتاج
                    </div>
                    <div class="alert alert-primary" role="alert">
                        <strong>اقتراح:</strong>
                        <br>
                        <i class="fas fa-star me-1"></i>
                        تطبيق نظام حوافز الأداء الجديد
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-download me-2"></i>
                        خيارات التصدير والطباعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-success w-100 mb-2" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>
                                تصدير Excel
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger w-100 mb-2" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>
                                تصدير PDF
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100 mb-2" onclick="printSummary()">
                                <i class="fas fa-print me-2"></i>
                                طباعة الملخص
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100 mb-2" onclick="emailSummary()">
                                <i class="fas fa-envelope me-2"></i>
                                إرسال بالبريد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterByPeriod() {
    const period = event.target.value;
    console.log('تم تطبيق فلتر الفترة:', period);

    const periodText = {
        'month': 'هذا الشهر',
        'quarter': 'هذا الربع',
        'year': 'هذا العام',
        'custom': 'فترة مخصصة'
    };

    showToast(`تم تطبيق فلتر الفترة: ${periodText[period] || period}`);

    // محاكاة تحديث البيانات
    setTimeout(() => {
        showToast('تم تحديث بيانات المرتبات بنجاح', 'success');
    }, 1000);
}

function filterByDepartment() {
    const department = event.target.value;
    console.log('تم تطبيق فلتر القسم:', department);

    // تطبيق الفلتر على جدول الأقسام
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (department === '' || row.textContent.includes(department)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    showToast(`تم تطبيق فلتر القسم: ${department || 'جميع الأقسام'}`);
}

function changeReportType() {
    const reportType = event.target.value;
    console.log('تم تغيير نوع التقرير:', reportType);

    const reportTypes = {
        'summary': 'ملخص',
        'detailed': 'تفصيلي',
        'comparison': 'مقارنة'
    };

    showToast(`تم تغيير نوع التقرير إلى: ${reportTypes[reportType] || reportType}`);
}

function exportPayroll() {
    // محاكاة تصدير البيانات
    const data = [
        ['القسم', 'عدد الموظفين', 'إجمالي المرتبات', 'البدلات', 'الخصومات', 'صافي المرتبات'],
        ['تقنية المعلومات', '15', '180000', '27000', '18000', '189000'],
        ['المالية', '8', '80000', '12000', '8000', '84000'],
        // يمكن إضافة المزيد من البيانات هنا
    ];

    console.log('بيانات التصدير:', data);
    showToast('تم تصدير ملخص المرتبات بنجاح!', 'success');
}

function printPayroll() {
    // جمع بيانات المرتبات من الجدول
    const payrollData = [];
    const rows = document.querySelectorAll('#payrollTable tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            payrollData.push({
                employee_name: cells[1]?.textContent?.trim() || 'غير محدد',
                basic_salary: cells[2]?.textContent?.trim() || '0',
                allowances: cells[3]?.textContent?.trim() || '0',
                overtime: cells[4]?.textContent?.trim() || '0',
                gross_salary: cells[5]?.textContent?.trim() || '0',
                insurance: cells[6]?.textContent?.trim() || '0',
                tax: cells[7]?.textContent?.trim() || '0',
                deductions: cells[8]?.textContent?.trim() || '0',
                net_salary: cells[9]?.textContent?.trim() || '0'
            });
        }
    });

    // استخدام القالب المخصص للطباعة
    if (typeof printPayrollReport !== 'undefined') {
        printPayrollReport(payrollData, 'كشف المرتبات الشهري');
    } else {
        // استخدام الطباعة العادية كبديل
        printInvoice('كشف المرتبات الشهري');
    }
}

function generatePayrollSummary() {
    // محاكاة إنشاء تقرير مخصص
    showToast('جاري إنشاء ملخص كشف المرتبات المخصص...', 'info');

    setTimeout(() => {
        showToast('تم إنشاء ملخص كشف المرتبات المخصص بنجاح!', 'success');
    }, 2000);
}

function exportToExcel() {
    showToast('جاري تصدير الملخص إلى Excel...', 'info');
    setTimeout(() => {
        showToast('تم تصدير الملخص إلى Excel بنجاح!', 'success');
    }, 1500);
}

function exportToPDF() {
    showToast('جاري تصدير الملخص إلى PDF...', 'info');
    setTimeout(() => {
        showToast('تم تصدير الملخص إلى PDF بنجاح!', 'success');
    }, 1500);
}

function printSummary() {
    // استخدام دالة الطباعة المحسنة
    printDashboard('ملخص كشف المرتبات');
}

function emailSummary() {
    showToast('جاري إرسال الملخص بالبريد الإلكتروني...', 'info');
    setTimeout(() => {
        showToast('تم إرسال الملخص بالبريد الإلكتروني بنجاح!', 'success');
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    toast.innerHTML = `
        <strong>${type === 'success' ? 'نجح!' : type === 'warning' ? 'تحذير!' : 'معلومة:'}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 4 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

// إضافة وظائف تفاعلية إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات للبطاقات
    const payrollCards = document.querySelectorAll('.payroll-card');
    payrollCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 16px rgba(0,0,0,0.15)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // إضافة تأثيرات لمؤشرات التوزيع
    const salaryIndicators = document.querySelectorAll('.salary-distribution .border');
    salaryIndicators.forEach(indicator => {
        indicator.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transform = 'translateX(5px)';
        });
        indicator.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'translateX(0)';
        });
    });

    // إضافة تأثيرات hover للجدول
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // تحديث الرسوم البيانية كل 30 ثانية (محاكاة)
    setInterval(() => {
        console.log('تحديث البيانات المالية...');
    }, 30000);
});
</script>

<style>
.payroll-card {
    transition: all 0.3s ease-in-out;
}

.salary-distribution .border {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.salary-indicator {
    transition: all 0.2s ease-in-out;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

.progress-bar {
    transition: width 0.3s ease-in-out;
}

.alert {
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-success {
    border-left-color: #198754;
}

.alert-primary {
    border-left-color: #0d6efd;
}

.card-header {
    font-weight: 600;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.chart-placeholder {
    transition: all 0.3s ease-in-out;
}

.chart-placeholder:hover {
    background: linear-gradient(45deg, #e9ecef, #f8f9fa);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .salary-distribution .d-flex {
        flex-direction: column;
        text-align: center;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
