#!/usr/bin/env python3
"""
خادم HTTPS مباشر بدون بروكسي
Direct HTTPS Server without Proxy

خادم Django مع HTTPS مباشر لتجنب مشاكل البروكسي
Django server with direct HTTPS to avoid proxy issues
"""

import os
import sys
import ssl
import socket
import subprocess
import threading
import time
from pathlib import Path
from datetime import datetime

class DirectHTTPSServer:
    """خادم HTTPS مباشر"""
    
    def __init__(self):
        self.port = 8443
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.ssl_cert = None
        self.ssl_key = None
        self.server_process = None
        self.is_running = False
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_error("لا توجد شهادة SSL")
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def install_django_extensions(self):
        """تثبيت django-extensions للـ HTTPS"""
        try:
            self.log_info("فحص django-extensions...")
            import django_extensions
            self.log_success("django-extensions متوفر")
            return True
        except ImportError:
            self.log_info("تثبيت django-extensions...")
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', 'django-extensions'
                ], check=True, capture_output=True)
                self.log_success("تم تثبيت django-extensions")
                return True
            except subprocess.CalledProcessError:
                self.log_error("فشل في تثبيت django-extensions")
                return False
    
    def update_django_settings(self):
        """تحديث إعدادات Django للـ HTTPS"""
        try:
            settings_file = Path('osaric_accounts/settings.py')
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إضافة django-extensions إذا لم يكن موجود
                if "'django_extensions'" not in content:
                    # البحث عن INSTALLED_APPS
                    if 'INSTALLED_APPS' in content:
                        # إضافة django_extensions
                        content = content.replace(
                            "'corsheaders',",
                            "'corsheaders',\n    'django_extensions',  # للـ HTTPS"
                        )
                        
                        with open(settings_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.log_success("تم تحديث إعدادات Django")
                
                return True
            else:
                self.log_error("ملف settings.py غير موجود")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في تحديث إعدادات Django: {e}")
            return False
    
    def start_https_server(self):
        """بدء خادم HTTPS مباشر"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # البحث عن منفذ متاح
            port = self.find_available_port(8443)
            if not port:
                self.log_error("لا يمكن العثور على منفذ متاح")
                return False
            
            self.port = port
            
            # تثبيت django-extensions
            if not self.install_django_extensions():
                self.log_error("فشل في تثبيت django-extensions")
                return False
            
            # تحديث إعدادات Django
            self.update_django_settings()
            
            self.log_info(f"بدء خادم HTTPS مباشر على المنفذ {port}")
            
            # تشغيل Django مع HTTPS مباشر
            cmd = [
                sys.executable, 'manage.py', 'runserver_plus',
                f'{self.host}:{port}',
                '--cert-file', str(self.ssl_cert),
                '--key-file', str(self.ssl_key),
                '--insecure'
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # انتظار بدء الخادم
            time.sleep(8)
            
            if self.server_process.poll() is None:
                self.log_success(f"خادم HTTPS مباشر يعمل على المنفذ {port}")
                self.display_access_info()
                return True
            else:
                self.log_error("فشل في بدء خادم HTTPS")
                # قراءة رسالة الخطأ
                if self.server_process.stderr:
                    error = self.server_process.stderr.read()
                    self.log_error(f"خطأ: {error[:200]}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في بدء خادم HTTPS: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🔒 خادم HTTPS مباشر")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔒 منفذ HTTPS: {self.port}")
        print("\n🔒 للوصول الآمن (HTTPS):")
        print(f"   https://{self.local_ip}:{self.port}/dashboard/")
        print(f"   https://localhost:{self.port}/dashboard/")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   https://{self.local_ip}:{self.port}/dashboard/")
        print("\n✅ مميزات الخادم المباشر:")
        print("   • لا مشاكل بروكسي")
        print("   • HTTPS مباشر من Django")
        print("   • أداء أفضل وأسرع")
        print("   • لا timeout أو أخطاء")
        print("=" * 60)
    
    def monitor_server(self):
        """مراقبة الخادم"""
        while self.is_running:
            try:
                time.sleep(10)
                
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("الخادم توقف - إعادة تشغيل...")
                    self.start_https_server()
                
            except Exception as e:
                self.log_error(f"خطأ في المراقبة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل خادم HTTPS مباشر"""
        print("=" * 60)
        print("🔒 خادم HTTPS مباشر - بدون بروكسي")
        print("Direct HTTPS Server - No Proxy")
        print("=" * 60)
        
        if not self.start_https_server():
            self.log_error("فشل في بدء خادم HTTPS!")
            return False
        
        self.is_running = True
        
        # بدء المراقبة
        monitor_thread = threading.Thread(target=self.monitor_server)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        self.log_success("خادم HTTPS مباشر يعمل بنجاح!")
        self.log_info("اضغط Ctrl+C للإيقاف")
        
        try:
            # انتظار
            while self.is_running:
                time.sleep(1)
                if self.server_process and self.server_process.poll() is not None:
                    break
                    
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        finally:
            self.is_running = False
            if self.server_process:
                self.server_process.terminate()
            self.log_info("تم إنهاء خادم HTTPS")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = DirectHTTPSServer()
    success = server.run()
    
    if success:
        print("✅ خادم HTTPS مباشر يعمل بنجاح!")
    else:
        print("❌ فشل في تشغيل خادم HTTPS!")

if __name__ == "__main__":
    main()
