/* Modern Components CSS */

/* Card Components */
.modern-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.02];
}

.modern-card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50;
}

.modern-card-body {
    @apply p-6;
}

.modern-card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50;
}

/* Button Components */
.btn-modern {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-modern-primary {
    @apply btn-modern bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800;
}

.btn-modern-secondary {
    @apply btn-modern bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 focus:ring-gray-500;
}

.btn-modern-success {
    @apply btn-modern bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-modern-danger {
    @apply btn-modern bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-modern-warning {
    @apply btn-modern bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-modern-info {
    @apply btn-modern bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

/* Input Components */
.input-modern {
    @apply w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors;
}

.input-modern-error {
    @apply input-modern border-red-300 dark:border-red-600 focus:ring-red-500;
}

.input-modern-success {
    @apply input-modern border-green-300 dark:border-green-600 focus:ring-green-500;
}

/* Table Components */
.table-modern {
    @apply w-full border-collapse bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm;
}

.table-modern th {
    @apply px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600;
}

.table-modern td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-600;
}

.table-modern tbody tr:hover {
    @apply bg-gray-50 dark:bg-gray-700/50;
}

/* Badge Components */
.badge-modern {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-modern-primary {
    @apply badge-modern bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400;
}

.badge-modern-success {
    @apply badge-modern bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
}

.badge-modern-danger {
    @apply badge-modern bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
}

.badge-modern-warning {
    @apply badge-modern bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
}

.badge-modern-info {
    @apply badge-modern bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

/* Alert Components */
.alert-modern {
    @apply p-4 rounded-lg border-l-4;
}

.alert-modern-success {
    @apply alert-modern bg-green-50 dark:bg-green-900/20 border-green-400 text-green-800 dark:text-green-400;
}

.alert-modern-danger {
    @apply alert-modern bg-red-50 dark:bg-red-900/20 border-red-400 text-red-800 dark:text-red-400;
}

.alert-modern-warning {
    @apply alert-modern bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 text-yellow-800 dark:text-yellow-400;
}

.alert-modern-info {
    @apply alert-modern bg-blue-50 dark:bg-blue-900/20 border-blue-400 text-blue-800 dark:text-blue-400;
}

/* Modal Components */
.modal-modern {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4;
}

.modal-modern-content {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md mx-auto;
}

.modal-modern-header {
    @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-modern-body {
    @apply p-6;
}

.modal-modern-footer {
    @apply flex items-center justify-end space-x-3 space-x-reverse p-6 border-t border-gray-200 dark:border-gray-700;
}

/* Dropdown Components */
.dropdown-modern {
    @apply relative inline-block;
}

.dropdown-modern-menu {
    @apply absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50;
}

.dropdown-modern-item {
    @apply block w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

/* Navigation Components */
.nav-modern {
    @apply flex space-x-8 space-x-reverse;
}

.nav-modern-item {
    @apply text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-modern-item-active {
    @apply nav-modern-item bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400;
}

/* Sidebar Components */
.sidebar-modern {
    @apply fixed inset-y-0 right-0 z-40 w-64 bg-white dark:bg-gray-800 shadow-lg border-l border-gray-200 dark:border-gray-700;
}

.sidebar-modern-nav {
    @apply flex flex-col space-y-1 p-4;
}

.sidebar-modern-item {
    @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors;
}

.sidebar-modern-item-active {
    @apply sidebar-modern-item bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400;
}

/* Form Components */
.form-modern-group {
    @apply mb-6;
}

.form-modern-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.form-modern-input {
    @apply input-modern;
}

.form-modern-textarea {
    @apply input-modern resize-none;
}

.form-modern-select {
    @apply input-modern;
}

.form-modern-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded;
}

.form-modern-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300;
}

/* Loading Components */
.loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin;
}

.loading-dots {
    @apply inline-flex space-x-1 space-x-reverse;
}

.loading-dots span {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
}

.loading-dots span:nth-child(1) {
    animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Progress Components */
.progress-modern {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-modern-bar {
    @apply h-2 bg-primary-600 rounded-full transition-all duration-300;
}

/* Tooltip Components */
.tooltip-modern {
    @apply absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200;
}

.tooltip-modern::after {
    content: '';
    @apply absolute w-0 h-0 border-4 border-transparent;
}

.tooltip-modern-top::after {
    @apply border-t-gray-900 top-full left-1/2 transform -translate-x-1/2;
}

.tooltip-modern-bottom::after {
    @apply border-b-gray-900 bottom-full left-1/2 transform -translate-x-1/2;
}

.tooltip-modern-right::after {
    @apply border-r-gray-900 right-full top-1/2 transform -translate-y-1/2;
}

.tooltip-modern-left::after {
    @apply border-l-gray-900 left-full top-1/2 transform -translate-y-1/2;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Responsive Utilities */
@media (max-width: 640px) {
    .sidebar-modern {
        @apply w-full;
    }
    
    .modal-modern-content {
        @apply max-w-sm;
    }
    
    .table-modern {
        @apply text-xs;
    }
    
    .table-modern th,
    .table-modern td {
        @apply px-2 py-2;
    }
}

/* Print Styles */
@media print {
    .sidebar-modern,
    .modal-modern,
    .tooltip-modern {
        display: none !important;
    }
    
    .modern-card {
        @apply shadow-none border border-gray-300;
    }
    
    .btn-modern {
        @apply border border-gray-300 bg-white text-gray-900;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .modern-card {
        @apply border-2;
    }
    
    .btn-modern {
        @apply border-2;
    }
    
    .input-modern {
        @apply border-2;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .fade-in,
    .slide-in-right,
    .slide-in-left,
    .bounce-in,
    .scale-in {
        animation: none;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    .loading-dots span {
        animation: none;
    }
}

/* Focus Visible */
.btn-modern:focus-visible,
.input-modern:focus-visible,
.dropdown-modern-item:focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
}

/* Selection */
::selection {
    @apply bg-primary-200 dark:bg-primary-800 text-primary-900 dark:text-primary-100;
}

/* Focus Ring */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Disabled State */
.disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* Loading State */
.loading {
    @apply relative;
}

.loading::after {
    content: '';
    @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center;
}

.loading::before {
    content: '';
    @apply absolute top-1/2 left-1/2 w-6 h-6 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin transform -translate-x-1/2 -translate-y-1/2;
}

/* Success State */
.success {
    @apply border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20;
}

/* Error State */
.error {
    @apply border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20;
}

/* Warning State */
.warning {
    @apply border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/20;
}

/* Info State */
.info {
    @apply border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20;
} 