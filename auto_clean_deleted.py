#!/usr/bin/env python
"""
Auto clean up deleted items from database
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.db import transaction

def auto_clean_all():
    """Automatically clean all inactive data"""
    
    print("=" * 60)
    print("Auto Database Cleanup - Delete Inactive Data")
    print("=" * 60)
    
    total_deleted = 0
    
    # Clean inactive items
    print("\n1. Cleaning inactive items...")
    inactive_items = Item.objects.filter(is_active=False)
    items_count = inactive_items.count()
    
    if items_count > 0:
        print(f"Found {items_count} inactive items:")
        for item in inactive_items:
            print(f"  - {item.name} ({item.code})")
        
        try:
            with transaction.atomic():
                for item in inactive_items:
                    item.delete()
                print(f"✓ Deleted {items_count} items")
                total_deleted += items_count
        except Exception as e:
            print(f"✗ Error deleting items: {str(e)}")
    else:
        print("✓ No inactive items found")
    
    # Clean inactive categories
    print("\n2. Cleaning inactive categories...")
    inactive_categories = ItemCategory.objects.filter(is_active=False)
    categories_count = inactive_categories.count()
    
    if categories_count > 0:
        print(f"Found {categories_count} inactive categories:")
        for category in inactive_categories:
            print(f"  - {category.name}")
        
        try:
            with transaction.atomic():
                for category in inactive_categories:
                    category.delete()
                print(f"✓ Deleted {categories_count} categories")
                total_deleted += categories_count
        except Exception as e:
            print(f"✗ Error deleting categories: {str(e)}")
    else:
        print("✓ No inactive categories found")
    
    # Clean inactive units
    print("\n3. Cleaning inactive units...")
    inactive_units = Unit.objects.filter(is_active=False)
    units_count = inactive_units.count()
    
    if units_count > 0:
        print(f"Found {units_count} inactive units:")
        for unit in inactive_units:
            print(f"  - {unit.name}")
        
        try:
            with transaction.atomic():
                for unit in inactive_units:
                    unit.delete()
                print(f"✓ Deleted {units_count} units")
                total_deleted += units_count
        except Exception as e:
            print(f"✗ Error deleting units: {str(e)}")
    else:
        print("✓ No inactive units found")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"CLEANUP COMPLETE - Total deleted: {total_deleted} records")
    print("=" * 60)
    
    if total_deleted == 0:
        print("✓ Database is clean - no inactive data found")
    else:
        print(f"✓ Successfully cleaned {total_deleted} inactive records from database")

if __name__ == '__main__':
    auto_clean_all()