#!/usr/bin/env python3
"""
إنشاء شهادة SSL محلية
Create Local SSL Certificate

إنشاء شهادة SSL للتطوير المحلي لتفعيل HTTPS
Create SSL certificate for local development to enable HTTPS
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_ssl_directory():
    """إنشاء مجلد SSL"""
    ssl_dir = Path('ssl')
    ssl_dir.mkdir(exist_ok=True)
    return ssl_dir

def create_openssl_config(ssl_dir):
    """إنشاء ملف إعدادات OpenSSL"""
    config_content = """[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=EG
ST=Cairo
L=Cairo
O=Osaric Accounting System
OU=IT Department
CN=localhost

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
DNS.3 = ***************
DNS.4 = DESKTOP-H8H1ID4
IP.1 = 127.0.0.1
IP.2 = ***************
"""
    
    config_file = ssl_dir / 'openssl.conf'
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    return config_file

def check_openssl():
    """فحص توفر OpenSSL"""
    try:
        result = subprocess.run(['openssl', 'version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ OpenSSL متوفر: {result.stdout.strip()}")
            return True
        else:
            print("❌ OpenSSL غير متوفر")
            return False
    except FileNotFoundError:
        print("❌ OpenSSL غير مثبت")
        return False

def install_openssl_windows():
    """تثبيت OpenSSL على Windows"""
    print("📦 محاولة تثبيت OpenSSL...")
    
    # محاولة تثبيت عبر chocolatey
    try:
        subprocess.run(['choco', 'install', 'openssl', '-y'], 
                      capture_output=True, text=True)
        if check_openssl():
            return True
    except FileNotFoundError:
        pass
    
    # محاولة تثبيت عبر winget
    try:
        subprocess.run(['winget', 'install', 'OpenSSL.Light'], 
                      capture_output=True, text=True)
        if check_openssl():
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️ لم يتم تثبيت OpenSSL تلقائياً")
    print("يرجى تحميل وتثبيت OpenSSL من:")
    print("https://slproweb.com/products/Win32OpenSSL.html")
    return False

def create_certificate_with_openssl(ssl_dir, config_file):
    """إنشاء الشهادة باستخدام OpenSSL"""
    try:
        # إنشاء المفتاح الخاص
        key_file = ssl_dir / 'server.key'
        subprocess.run([
            'openssl', 'genrsa', '-out', str(key_file), '2048'
        ], check=True, capture_output=True)
        
        # إنشاء طلب الشهادة
        csr_file = ssl_dir / 'server.csr'
        subprocess.run([
            'openssl', 'req', '-new', '-key', str(key_file),
            '-out', str(csr_file), '-config', str(config_file)
        ], check=True, capture_output=True)
        
        # إنشاء الشهادة
        cert_file = ssl_dir / 'server.crt'
        subprocess.run([
            'openssl', 'x509', '-req', '-days', '365',
            '-in', str(csr_file), '-signkey', str(key_file),
            '-out', str(cert_file), '-extensions', 'v3_req',
            '-extfile', str(config_file)
        ], check=True, capture_output=True)
        
        print("✅ تم إنشاء شهادة SSL بنجاح")
        return cert_file, key_file
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إنشاء الشهادة: {e}")
        return None, None

def create_certificate_with_python(ssl_dir):
    """إنشاء الشهادة باستخدام Python"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import ipaddress
        
        # إنشاء المفتاح الخاص
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # إنشاء الشهادة
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "EG"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Cairo"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Cairo"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Osaric Accounting System"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("127.0.0.1"),
                x509.DNSName("***************"),
                x509.DNSName("DESKTOP-H8H1ID4"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                x509.IPAddress(ipaddress.IPv4Address("***************")),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # حفظ المفتاح الخاص
        key_file = ssl_dir / 'server.key'
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # حفظ الشهادة
        cert_file = ssl_dir / 'server.crt'
        with open(cert_file, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        print("✅ تم إنشاء شهادة SSL بنجاح باستخدام Python")
        return cert_file, key_file
        
    except ImportError:
        print("❌ مكتبة cryptography غير مثبتة")
        print("جاري تثبيت cryptography...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'cryptography'], 
                          check=True)
            print("✅ تم تثبيت cryptography")
            return create_certificate_with_python(ssl_dir)
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت cryptography")
            return None, None
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشهادة: {e}")
        return None, None

def create_pem_file(ssl_dir, cert_file, key_file):
    """إنشاء ملف PEM مدمج"""
    try:
        pem_file = ssl_dir / 'server.pem'
        
        with open(pem_file, 'w') as pem:
            # إضافة الشهادة
            with open(cert_file, 'r') as cert:
                pem.write(cert.read())
            
            # إضافة المفتاح الخاص
            with open(key_file, 'r') as key:
                pem.write(key.read())
        
        print("✅ تم إنشاء ملف PEM مدمج")
        return pem_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف PEM: {e}")
        return None

def install_certificate_windows(cert_file):
    """تثبيت الشهادة في Windows"""
    try:
        # تثبيت في Trusted Root Certification Authorities
        subprocess.run([
            'certlm.msc', '/s', '/c', 'add', str(cert_file), 
            'Root', 'LocalMachine'
        ], capture_output=True)
        
        print("✅ تم تثبيت الشهادة في Windows")
        return True
        
    except Exception as e:
        print(f"⚠️ لم يتم تثبيت الشهادة تلقائياً: {e}")
        print("يمكنك تثبيت الشهادة يدوياً:")
        print(f"1. افتح {cert_file}")
        print("2. اضغط 'Install Certificate'")
        print("3. اختر 'Local Machine'")
        print("4. اختر 'Trusted Root Certification Authorities'")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔒 إنشاء شهادة SSL محلية")
    print("=" * 60)
    
    # إنشاء مجلد SSL
    ssl_dir = create_ssl_directory()
    print(f"📁 تم إنشاء مجلد SSL: {ssl_dir}")
    
    # فحص إذا كانت الشهادة موجودة بالفعل
    cert_file = ssl_dir / 'server.crt'
    key_file = ssl_dir / 'server.key'
    
    if cert_file.exists() and key_file.exists():
        print("✅ شهادة SSL موجودة بالفعل")
        choice = input("هل تريد إنشاء شهادة جديدة؟ (y/n): ")
        if choice.lower() != 'y':
            print("تم الاحتفاظ بالشهادة الحالية")
            return cert_file, key_file
    
    # محاولة إنشاء الشهادة بطرق مختلفة
    cert_file = None
    key_file = None
    
    # الطريقة 1: OpenSSL
    if check_openssl():
        config_file = create_openssl_config(ssl_dir)
        cert_file, key_file = create_certificate_with_openssl(ssl_dir, config_file)
    else:
        # محاولة تثبيت OpenSSL
        if install_openssl_windows():
            config_file = create_openssl_config(ssl_dir)
            cert_file, key_file = create_certificate_with_openssl(ssl_dir, config_file)
    
    # الطريقة 2: Python cryptography
    if not cert_file or not key_file:
        print("محاولة إنشاء الشهادة باستخدام Python...")
        cert_file, key_file = create_certificate_with_python(ssl_dir)
    
    if cert_file and key_file:
        # إنشاء ملف PEM مدمج
        pem_file = create_pem_file(ssl_dir, cert_file, key_file)
        
        # محاولة تثبيت الشهادة
        install_certificate_windows(cert_file)
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء شهادة SSL بنجاح!")
        print("=" * 60)
        print(f"📄 الشهادة: {cert_file}")
        print(f"🔑 المفتاح الخاص: {key_file}")
        if pem_file:
            print(f"📋 ملف PEM: {pem_file}")
        print("\n🔒 يمكنك الآن تشغيل الخادم مع HTTPS")
        print("=" * 60)
        
        return cert_file, key_file
    else:
        print("\n❌ فشل في إنشاء شهادة SSL")
        print("يرجى تثبيت OpenSSL أو cryptography يدوياً")
        return None, None

if __name__ == "__main__":
    main()
