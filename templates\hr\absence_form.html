{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-times text-secondary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if absence_record %}
                            تعديل سجل الغياب/التأخير للموظف {{ absence_record.employee.full_name }}
                        {% else %}
                            تسجيل غياب أو تأخير جديد
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'hr:absence_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {% if absence_record %}
                            تعديل سجل الغياب/التأخير
                        {% else %}
                            تسجيل غياب/تأخير جديد
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- الموظف ونوع المخالفة -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.employee.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-2"></i>
                                    الموظف *
                                </label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.employee.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.absence_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    نوع المخالفة *
                                </label>
                                {{ form.absence_type }}
                                {% if form.absence_type.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.absence_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- التاريخ -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>
                                    التاريخ *
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- وقت البداية -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.time_from.id_for_label }}" class="form-label">
                                    <i class="fas fa-clock me-2"></i>
                                    من وقت
                                </label>
                                {{ form.time_from }}
                                {% if form.time_from.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.time_from.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- وقت النهاية -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.time_to.id_for_label }}" class="form-label">
                                    <i class="fas fa-clock me-2"></i>
                                    إلى وقت
                                </label>
                                {{ form.time_to }}
                                {% if form.time_to.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.time_to.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- المدة -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.duration_hours.id_for_label }}" class="form-label">
                                    <i class="fas fa-hourglass-half me-2"></i>
                                    المدة (ساعات)
                                </label>
                                {{ form.duration_hours }}
                                {% if form.duration_hours.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.duration_hours.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                    <i class="fas fa-hourglass-half me-2"></i>
                                    المدة (دقائق)
                                </label>
                                {{ form.duration_minutes }}
                                {% if form.duration_minutes.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.duration_minutes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- مبلغ الخصم -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.deduction_amount.id_for_label }}" class="form-label">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    مبلغ الخصم
                                </label>
                                {{ form.deduction_amount }}
                                {% if form.deduction_amount.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.deduction_amount.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- العملة -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.currency.id_for_label }}" class="form-label">
                                    <i class="fas fa-coins me-2"></i>
                                    العملة
                                </label>
                                {{ form.currency }}
                                {% if form.currency.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.currency.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- السبب -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">
                                    <i class="fas fa-comment me-2"></i>
                                    السبب *
                                </label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.reason.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- التفاصيل -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.details.id_for_label }}" class="form-label">
                                    <i class="fas fa-info-circle me-2"></i>
                                    تفاصيل إضافية
                                </label>
                                {{ form.details }}
                                {% if form.details.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.details.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- المرفقات -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.attachment.id_for_label }}" class="form-label">
                                    <i class="fas fa-paperclip me-2"></i>
                                    مرفقات
                                </label>
                                {{ form.attachment }}
                                {% if form.attachment.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.attachment.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if absence_record.attachment %}
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            المرفق الحالي: 
                                            <a href="{{ absence_record.attachment.url }}" target="_blank">
                                                {{ absence_record.attachment.name|default_if_none:"" }}
                                            </a>
                                        </small>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- الملاحظات -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    <i class="fas fa-sticky-note me-2"></i>
                                    ملاحظات
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.notes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أخطاء النموذج العامة -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'hr:absence_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if absence_record %}
                                            تحديث
                                        {% else %}
                                            حفظ
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
});

// حساب المدة تلقائياً عند تغيير الأوقات
document.addEventListener('DOMContentLoaded', function() {
    const timeFrom = document.getElementById('{{ form.time_from.id_for_label }}');
    const timeTo = document.getElementById('{{ form.time_to.id_for_label }}');
    const durationHours = document.getElementById('{{ form.duration_hours.id_for_label }}');
    const durationMinutes = document.getElementById('{{ form.duration_minutes.id_for_label }}');
    
    function calculateDuration() {
        if (timeFrom.value && timeTo.value) {
            const from = new Date('2000-01-01T' + timeFrom.value);
            const to = new Date('2000-01-01T' + timeTo.value);
            
            if (to > from) {
                const diffMs = to - from;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                
                durationHours.value = diffHours;
                durationMinutes.value = diffMinutes;
            }
        }
    }
    
    if (timeFrom && timeTo) {
        timeFrom.addEventListener('change', calculateDuration);
        timeTo.addEventListener('change', calculateDuration);
    }
});
</script>
{% endblock %} 