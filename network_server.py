#!/usr/bin/env python3
"""
خادم الشبكة للوصول من أي جهاز
Network Server for Access from Any Device

يتيح الوصول للتطبيق من أي جهاز في الشبكة
Allows access to the application from any device on the network
"""

import os
import sys
import time
import signal
import socket
import subprocess
import threading
import logging
from datetime import datetime

# إعداد اللوجز
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_server.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class NetworkServer:
    """خادم الشبكة للوصول من أي جهاز"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 10000
        self.restart_delay = 3
        self.port = 8000
        self.host = '0.0.0.0'  # للوصول من أي جهاز
        self.health_check_interval = 20
        self.local_ip = self.get_local_ip()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            # إنشاء اتصال وهمي للحصول على IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def get_network_info(self):
        """الحصول على معلومات الشبكة"""
        try:
            hostname = socket.gethostname()
            return {
                'hostname': hostname,
                'local_ip': self.local_ip,
                'port': self.port
            }
        except Exception as e:
            self.log_error(f"خطأ في الحصول على معلومات الشبكة: {e}")
            return None
    
    def check_firewall_settings(self):
        """فحص إعدادات الجدار الناري"""
        self.log_info("فحص إعدادات الجدار الناري...")
        
        if os.name == 'nt':  # Windows
            try:
                # فحص قواعد الجدار الناري
                result = subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
                    f'name=Django-{self.port}'
                ], capture_output=True, text=True)
                
                if 'No rules match' in result.stdout:
                    self.log_info("إنشاء قاعدة جدار ناري للمنفذ...")
                    self.create_firewall_rule()
                else:
                    self.log_info("قاعدة الجدار الناري موجودة")
                    
            except Exception as e:
                self.log_error(f"خطأ في فحص الجدار الناري: {e}")
    
    def create_firewall_rule(self):
        """إنشاء قاعدة جدار ناري"""
        try:
            if os.name == 'nt':  # Windows
                # إنشاء قاعدة للاتصالات الواردة
                cmd_in = [
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    f'name=Django-{self.port}-In',
                    'dir=in',
                    'action=allow',
                    'protocol=TCP',
                    f'localport={self.port}'
                ]
                
                # إنشاء قاعدة للاتصالات الصادرة
                cmd_out = [
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    f'name=Django-{self.port}-Out',
                    'dir=out',
                    'action=allow',
                    'protocol=TCP',
                    f'localport={self.port}'
                ]
                
                subprocess.run(cmd_in, capture_output=True)
                subprocess.run(cmd_out, capture_output=True)
                
                self.log_info(f"تم إنشاء قواعد الجدار الناري للمنفذ {self.port}")
                
        except Exception as e:
            self.log_error(f"خطأ في إنشاء قاعدة الجدار الناري: {e}")
            self.log_info("يرجى إضافة قاعدة الجدار الناري يدوياً")
    
    def update_django_settings(self):
        """تحديث إعدادات Django للوصول من الشبكة"""
        try:
            settings_file = 'osaric_accounts/settings.py'
            
            # قراءة الملف
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحديث ALLOWED_HOSTS
            if 'ALLOWED_HOSTS = []' in content:
                content = content.replace(
                    'ALLOWED_HOSTS = []',
                    f"ALLOWED_HOSTS = ['*', '{self.local_ip}', 'localhost', '127.0.0.1']"
                )
                
                # كتابة الملف
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.log_info("تم تحديث ALLOWED_HOSTS في إعدادات Django")
            else:
                self.log_info("ALLOWED_HOSTS محدث بالفعل")
                
        except Exception as e:
            self.log_error(f"خطأ في تحديث إعدادات Django: {e}")
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة الإيقاف {signum}")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            self.log_info(f"بدء تشغيل خادم الشبكة على {self.host}:{self.port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}',
                '--insecure'  # لتقديم الملفات الثابتة
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.is_running = True
            self.log_info(f"تم بدء الخادم بنجاح! PID: {self.server_process.pid}")
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(5)
            
            if self.server_process.poll() is None:
                self.log_info("خادم الشبكة يعمل بنجاح")
                self.display_access_info()
                return True
            else:
                self.log_error(f"الخادم توقف فوراً بكود: {self.server_process.returncode}")
                return False
            
        except Exception as e:
            self.log_error(f"خطأ في بدء الخادم: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        network_info = self.get_network_info()
        if network_info:
            print("\n" + "=" * 60)
            print("🌐 معلومات الوصول للشبكة")
            print("=" * 60)
            print(f"🖥️  اسم الجهاز: {network_info['hostname']}")
            print(f"🌍 عنوان IP المحلي: {network_info['local_ip']}")
            print(f"🔌 المنفذ: {network_info['port']}")
            print("\n📱 للوصول من أجهزة أخرى:")
            print(f"   http://{network_info['local_ip']}:{network_info['port']}/")
            print("\n💻 للوصول من نفس الجهاز:")
            print(f"   http://localhost:{network_info['port']}/")
            print(f"   http://127.0.0.1:{network_info['port']}/")
            print("\n📋 تعليمات الوصول:")
            print("   1. تأكد من أن الأجهزة على نفس الشبكة")
            print("   2. تأكد من إعدادات الجدار الناري")
            print("   3. استخدم عنوان IP المحلي للوصول")
            print("=" * 60)
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            if not self.server_process or self.server_process.poll() is not None:
                return False
            
            import urllib.request
            import urllib.error
            
            # فحص الوصول المحلي
            url = f"http://127.0.0.1:{self.port}/"
            
            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'NetworkServer/1.0')
                response = urllib.request.urlopen(request, timeout=10)
                
                if response.getcode() == 200:
                    return True
                else:
                    self.log_error(f"الخادم يرد بكود خطأ: {response.getcode()}")
                    return False
                    
            except urllib.error.URLError as e:
                self.log_error(f"خطأ في الاتصال بالخادم: {e}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.log_info("بدء إعادة تشغيل خادم الشبكة...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        self.restart_count += 1
        
        if self.restart_count <= self.max_restarts:
            self.log_info(f"محاولة إعادة التشغيل رقم {self.restart_count}")
            return self.start_server()
        else:
            self.log_error(f"تم الوصول للحد الأقصى من إعادة التشغيل ({self.max_restarts})")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                self.log_info("إيقاف خادم الشبكة...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=10)
                    self.log_info("تم إيقاف الخادم بنجاح")
                except subprocess.TimeoutExpired:
                    self.log_info("إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                    self.log_info("تم إجبار إيقاف الخادم")
                
            except Exception as e:
                self.log_error(f"خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def health_monitor_loop(self):
        """حلقة مراقبة الصحة"""
        consecutive_failures = 0
        max_failures = 3
        
        while self.is_running:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("عملية خادم الشبكة توقفت!")
                    if not self.restart_server():
                        break
                    consecutive_failures = 0
                    continue
                
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_info("خادم الشبكة عاد للعمل بنجاح")
                    consecutive_failures = 0
                    self.log_info("خادم الشبكة يعمل بصحة جيدة")
                else:
                    consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.log_error("فشل متكرر في فحص الصحة - إعادة تشغيل الخادم")
                        if not self.restart_server():
                            break
                        consecutive_failures = 0
                
            except Exception as e:
                self.log_error(f"خطأ في حلقة مراقبة الصحة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل خادم الشبكة"""
        print("=" * 60)
        print("🌐 خادم الشبكة للوصول من أي جهاز")
        print("Network Server for Access from Any Device")
        print("=" * 60)
        
        self.log_info("بدء تشغيل خادم الشبكة...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # تحديث إعدادات Django
        self.update_django_settings()
        
        # فحص إعدادات الجدار الناري
        self.check_firewall_settings()
        
        # بدء الخادم
        if not self.start_server():
            self.log_error("فشل في بدء خادم الشبكة!")
            return False
        
        # بدء مراقبة الصحة
        health_thread = threading.Thread(target=self.health_monitor_loop)
        health_thread.daemon = True
        health_thread.start()
        
        self.log_info("خادم الشبكة يعمل بأقصى استقرار!")
        self.log_info("يمكن الوصول إليه من أي جهاز في الشبكة")
        self.log_info("اضغط Ctrl+C للإيقاف الآمن")
        
        try:
            while self.is_running:
                time.sleep(1)
                
                if self.server_process and self.server_process.poll() is not None:
                    return_code = self.server_process.returncode
                    if return_code != 0:
                        self.log_error(f"الخادم توقف بكود خطأ: {return_code}")
                        if not self.restart_server():
                            break
                    else:
                        self.log_info("الخادم توقف بشكل طبيعي")
                        break
                        
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
        finally:
            self.stop_server()
            self.log_info("تم إنهاء خادم الشبكة")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = NetworkServer()
    success = server.run()
    
    if success:
        print("تم تشغيل خادم الشبكة بنجاح!")
    else:
        print("فشل في تشغيل خادم الشبكة!")
        sys.exit(1)

if __name__ == "__main__":
    main()
