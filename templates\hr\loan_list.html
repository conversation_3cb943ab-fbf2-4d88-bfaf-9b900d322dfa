{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-hand-holding-usd text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة السلف والقروض للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addLoanModal">
                        <i class="fas fa-plus me-2"></i>
                        سلفة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>45,000 ج.م</h3>
                    <p class="mb-0">إجمالي السلف</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>28,500 ج.م</h3>
                    <p class="mb-0">المبلغ المسدد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>16,500 ج.م</h3>
                    <p class="mb-0">المتبقي</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>12</h3>
                    <p class="mb-0">عدد الموظفين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loans Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة السلف والقروض
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع السلفة</th>
                                    <th>المبلغ الأصلي</th>
                                    <th>المسدد</th>
                                    <th>المتبقي</th>
                                    <th>القسط الشهري</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for loan in loans %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ loan.employee }}
                                    </td>
                                    <td>
                                        {% if loan.type == 'سلفة شخصية' %}
                                            <span class="badge bg-primary">{{ loan.type }}</span>
                                        {% elif loan.type == 'قرض' %}
                                            <span class="badge bg-warning">{{ loan.type }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ loan.type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ loan.amount }} ج.م</strong>
                                    </td>
                                    <td>
                                        <span class="text-success">{{ loan.paid }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="text-danger">{{ loan.remaining }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ loan.monthly }} ج.م</span>
                                    </td>
                                    <td>
                                        {% if loan.status == 'نشط' %}
                                            <span class="badge bg-success">{{ loan.status }}</span>
                                        {% elif loan.status == 'مكتمل' %}
                                            <span class="badge bg-primary">{{ loan.status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ loan.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="دفعة">
                                                <i class="fas fa-money-bill"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="جدول السداد">
                                                <i class="fas fa-calendar-alt"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="fas fa-hand-holding-usd fa-3x mb-3 d-block"></i>
                                        لا توجد سلف أو قروض مسجلة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Schedule -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        جدول الأقساط المستحقة هذا الشهر
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع السلفة</th>
                                    <th>القسط المستحق</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>أحمد محمد</td>
                                    <td><span class="badge bg-primary">سلفة شخصية</span></td>
                                    <td><strong>500 ج.م</strong></td>
                                    <td>2024-01-31</td>
                                    <td><span class="badge bg-warning">مستحق</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-success">خصم من الراتب</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>فاطمة علي</td>
                                    <td><span class="badge bg-warning">قرض</span></td>
                                    <td><strong>750 ج.م</strong></td>
                                    <td>2024-01-31</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-info">عرض الإيصال</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>محمد أحمد</td>
                                    <td><span class="badge bg-info">سلفة طارئة</span></td>
                                    <td><strong>300 ج.م</strong></td>
                                    <td>2024-01-31</td>
                                    <td><span class="badge bg-warning">مستحق</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-success">خصم من الراتب</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Loan Modal -->
<div class="modal fade" id="addLoanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سلفة أو قرض جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">فاطمة علي</option>
                                    <option value="3">محمد أحمد</option>
                                    <option value="4">سارة علي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع السلفة</label>
                                <select class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="personal">سلفة شخصية</option>
                                    <option value="emergency">سلفة طارئة</option>
                                    <option value="loan">قرض</option>
                                    <option value="advance">سلفة على الراتب</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المبلغ (ج.م)</label>
                                <input type="number" class="form-control" min="100" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">عدد الأقساط</label>
                                <select class="form-select" required>
                                    <option value="">اختر عدد الأقساط</option>
                                    <option value="1">قسط واحد</option>
                                    <option value="3">3 أقساط</option>
                                    <option value="6">6 أقساط</option>
                                    <option value="12">12 قسط</option>
                                    <option value="24">24 قسط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معدل الفائدة (%)</label>
                                <input type="number" class="form-control" min="0" max="20" step="0.1" value="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب السلفة</label>
                        <textarea class="form-control" rows="3" placeholder="اذكر سبب طلب السلفة" required></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoDeduct">
                            <label class="form-check-label" for="autoDeduct">
                                خصم تلقائي من الراتب
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning">حفظ السلفة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
