#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append('/d/osama')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from services.models import SystemSettings
from django.contrib.auth.models import User

def create_settings():
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = User.objects.filter(is_staff=True).first()

    # ===== إعدادات الأمان =====
    password_settings = [
        # متطلبات كلمة المرور
        ('password_min_length', '12', 'الحد الأدنى لطول كلمة المرور (12 حرف)', 'إعدادات الأمان', 'INTEGER'),
        ('password_require_uppercase', 'true', 'يتطلب حرف كبير واحد على الأقل (A-Z)', 'إعدادات الأمان', 'BOOLEAN'),
        ('password_require_lowercase', 'true', 'يتطلب حرف صغير واحد على الأقل (a-z)', 'إعدادات الأمان', 'BOOLEAN'),
        ('password_require_numbers', 'true', 'يتطلب رقم واحد على الأقل (0-9)', 'إعدادات الأمان', 'BOOLEAN'),
        ('password_require_special', 'true', 'يتطلب رمز خاص واحد على الأقل (!@#$%^&*)', 'إعدادات الأمان', 'BOOLEAN'),
        ('password_expiry_days', '90', 'مدة انتهاء صلاحية كلمة المرور (أيام)', 'إعدادات الأمان', 'INTEGER'),
        ('password_history_count', '5', 'عدد كلمات المرور السابقة المحفوظة', 'إعدادات الأمان', 'INTEGER'),
        
        # إعدادات قفل الحساب
        ('max_failed_login_attempts', '5', 'عدد المحاولات الفاشلة قبل قفل الحساب', 'إعدادات الأمان', 'INTEGER'),
        ('account_lockout_minutes', '30', 'مدة قفل الحساب بعد تجاوز المحاولات (دقائق)', 'إعدادات الأمان', 'INTEGER'),
        ('enable_account_lockout', 'true', 'تفعيل قفل الحساب بعد محاولات فاشلة', 'إعدادات الأمان', 'BOOLEAN'),
    ]

    # ===== إعدادات واجهة المستخدم =====
    program_settings = [
        ('ui_records_per_page', '25', 'عدد السجلات في الصفحة', 'خيارات البرنامج', 'INTEGER'),
        ('ui_enable_animations', 'true', 'تفعيل الحركات والتأثيرات', 'خيارات البرنامج', 'BOOLEAN'),
        ('ui_compact_mode', 'false', 'الوضع المضغوط للواجهة', 'خيارات البرنامج', 'BOOLEAN'),
        ('ui_show_tooltips', 'true', 'إظهار التلميحات', 'خيارات البرنامج', 'BOOLEAN'),
        ('ui_auto_save_interval', '30', 'فترة الحفظ التلقائي بالثواني', 'خيارات البرنامج', 'INTEGER'),
        ('ui_notification_duration', '5', 'مدة عرض الإشعارات بالثواني', 'خيارات البرنامج', 'INTEGER'),
        ('ui_enable_sound', 'false', 'تفعيل الأصوات', 'خيارات البرنامج', 'BOOLEAN'),
        ('ui_enable_keyboard_shortcuts', 'true', 'تفعيل اختصارات لوحة المفاتيح', 'خيارات البرنامج', 'BOOLEAN'),
        ('ui_enable_dark_mode', 'false', 'تفعيل الوضع الداكن', 'خيارات البرنامج', 'BOOLEAN'),
    ]

    all_settings = password_settings + program_settings
    created_count = 0

    for key, value, desc, category, value_type in all_settings:
        setting, created = SystemSettings.objects.get_or_create(
            key=key,
            defaults={
                'value': value,
                'category': category,
                'description': desc,
                'value_type': value_type,
                'is_editable': True,
                'updated_by': admin_user
            }
        )
        if created:
            created_count += 1
            print(f'✅ تم إنشاء إعداد: {desc}')
        else:
            print(f'📝 الإعداد موجود: {desc}')

    print(f'\n🎉 تم إنشاء {created_count} إعداد جديد!')
    return created_count

if __name__ == '__main__':
    create_settings()
