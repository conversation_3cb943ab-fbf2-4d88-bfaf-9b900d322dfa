from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import authenticate, login, logout, update_session_auth_hash
from django.contrib.auth.forms import Password<PERSON>hangeForm, AuthenticationForm
from django.contrib import messages
from django.contrib.auth.models import User, Group, Permission
from django import forms
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.cache import never_cache
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .models import UserProfile, CustomPermissionGroup, UserPermission, UserActivity
from .forms import (CustomUserCreationForm, CustomUserChangeForm, UserPermissionForm,
                   BulkPermissionForm, CustomPermissionGroupForm, PasswordResetForm)


# دوال مساعدة
def is_admin_or_staff(user):
    """فحص إذا كان المستخدم مدير أو موظف إداري"""
    return user.is_superuser or user.is_staff

def log_user_activity(user, action, module, object_id=None, object_name=None, request=None):
    """تسجيل نشاط المستخدم"""
    try:
        ip_address = None
        user_agent = None

        if request:
            ip_address = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT')

        UserActivity.objects.create(
            user=user,
            action=action,
            module=module,
            object_id=str(object_id) if object_id else None,
            object_name=object_name,
            ip_address=ip_address,
            user_agent=user_agent
        )
    except Exception as e:
        print(f"Error logging user activity: {e}")


# Views إدارة المستخدمين
@login_required
@user_passes_test(is_admin_or_staff)
def users_list(request):
    """قائمة المستخدمين"""
    search_query = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')
    status_filter = request.GET.get('status', '')

    users = User.objects.select_related('profile').all()

    # البحث
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(profile__phone__icontains=search_query)
        )

    # فلترة حسب القسم
    if department_filter:
        users = users.filter(profile__department__icontains=department_filter)

    # فلترة حسب الحالة
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'staff':
        users = users.filter(is_staff=True)
    elif status_filter == 'superuser':
        users = users.filter(is_superuser=True)

    # ترقيم الصفحات
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على الأقسام للفلترة
    departments = UserProfile.objects.values_list('department', flat=True).distinct().exclude(department__isnull=True).exclude(department='')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'department_filter': department_filter,
        'status_filter': status_filter,
        'departments': departments,
        'total_users': users.count(),
    }

    log_user_activity(request.user, 'view', 'users_management', request=request)

    return render(request, 'accounts/users_list.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def user_create(request):
    """إنشاء مستخدم جديد"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'تم إنشاء المستخدم {user.username} بنجاح!')
            log_user_activity(request.user, 'create', 'users_management',
                             object_id=user.id, object_name=user.username, request=request)
            return redirect('accounts:users_list')
    else:
        form = CustomUserCreationForm()

    context = {
        'form': form,
        'title': 'إنشاء مستخدم جديد',
        'submit_text': 'إنشاء المستخدم'
    }

    return render(request, 'accounts/user_form.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def user_edit(request, user_id):
    """تعديل مستخدم"""
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = CustomUserChangeForm(request.POST, instance=user)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'تم تحديث بيانات المستخدم {user.username} بنجاح!')
            log_user_activity(request.user, 'update', 'users_management',
                             object_id=user.id, object_name=user.username, request=request)
            return redirect('accounts:users_list')
    else:
        form = CustomUserChangeForm(instance=user)

    context = {
        'form': form,
        'user_obj': user,
        'title': f'تعديل المستخدم: {user.username}',
        'submit_text': 'حفظ التغييرات'
    }

    return render(request, 'accounts/user_form.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def user_detail(request, user_id):
    """تفاصيل المستخدم"""
    user = get_object_or_404(User, id=user_id)

    # الحصول على صلاحيات المستخدم
    user_permissions = UserPermission.objects.filter(user=user)

    # الحصول على آخر النشاطات
    recent_activities = UserActivity.objects.filter(user=user)[:10]

    context = {
        'user_obj': user,
        'user_permissions': user_permissions,
        'recent_activities': recent_activities,
    }

    log_user_activity(request.user, 'view', 'users_management',
                     object_id=user.id, object_name=user.username, request=request)

    return render(request, 'accounts/user_detail.html', context)


class ProfileEditForm(forms.ModelForm):
    """نموذج تعديل الملف الشخصي"""
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'email': 'البريد الإلكتروني',
        }
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل الاسم الأول'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم العائلة'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'أدخل البريد الإلكتروني'}),
        }


@csrf_protect
@never_cache
def custom_login_view(request):
    """تسجيل الدخول المخصص مع تحسينات الأمان"""
    # التحقق من عدد محاولات تسجيل الدخول الفاشلة
    failed_attempts = request.session.get('failed_login_attempts', 0)
    if failed_attempts >= 5:
        messages.error(request, 'لقد تجاوزت الحد الأقصى لمحاولات تسجيل الدخول الفاشلة. يرجى المحاولة مرة أخرى لاحقاً.')
        return render(request, 'accounts/login.html', {'form': AuthenticationForm()})

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)

        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')

            user = authenticate(request, username=username, password=password)

            if user is not None:
                if user.is_active:
                    # تسجيل الدخول الناجح
                    login(request, user)
                    request.session['failed_login_attempts'] = 0  # إعادة تعيين العداد
                    
                    # تسجيل النشاط
                    log_user_activity(user, 'login', 'authentication', request=request)
                    
                    messages.success(request, f'مرحباً {user.username}! تم تسجيل الدخول بنجاح.')
                    next_url = request.POST.get('next') or request.GET.get('next') or '/dashboard/'
                    return redirect(next_url)
                else:
                    messages.error(request, 'حسابك غير مفعل. يرجى التواصل مع المسؤول.')
                    log_user_activity(None, 'login_failed', 'authentication',
                                    object_name=f"حساب غير مفعل: {username}", request=request)
            else:
                # زيادة عداد المحاولات الفاشلة
                request.session['failed_login_attempts'] = failed_attempts + 1
                remaining_attempts = 5 - (failed_attempts + 1)
                
                messages.error(request, f'بيانات الدخول غير صحيحة. لديك {remaining_attempts} محاولات متبقية.')
                log_user_activity(None, 'login_failed', 'authentication',
                                object_name=f"بيانات غير صحيحة: {username}", request=request)
        else:
            # زيادة عداد المحاولات الفاشلة
            request.session['failed_login_attempts'] = failed_attempts + 1
            remaining_attempts = 5 - (failed_attempts + 1)
            
            messages.error(request, f'يوجد خطأ في البيانات المدخلة. لديك {remaining_attempts} محاولات متبقية.')
    else:
        form = AuthenticationForm()

    return render(request, 'accounts/login.html', {
        'form': form,
        'next': request.GET.get('next', '')
    })


def logout_view(request):
    """تسجيل الخروج"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح!')
    return redirect('accounts:login')


@login_required
def profile(request):
    """صفحة الملف الشخصي"""
    return render(request, 'accounts/profile.html')


@login_required
def edit_profile(request):
    """تعديل الملف الشخصي"""
    if request.method == 'POST':
        form = ProfileEditForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الملف الشخصي بنجاح!')
            return redirect('accounts:profile')
    else:
        form = ProfileEditForm(instance=request.user)

    return render(request, 'accounts/edit_profile.html', {'form': form})


@login_required
def change_password(request):
    """تغيير كلمة المرور"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # للحفاظ على تسجيل الدخول
            messages.success(request, 'تم تغيير كلمة المرور بنجاح!')
            return redirect('accounts:profile')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه.')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'accounts/change_password.html', {'form': form})


@login_required
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def user_delete(request, user_id):
    """حذف مستخدم"""
    user = get_object_or_404(User, id=user_id)

    # منع حذف المستخدم الحالي
    if user == request.user:
        messages.error(request, 'لا يمكنك حذف حسابك الخاص!')
        return redirect('accounts:users_list')

    # منع حذف المدير الرئيسي
    if user.is_superuser and User.objects.filter(is_superuser=True).count() == 1:
        messages.error(request, 'لا يمكن حذف المدير الرئيسي الوحيد!')
        return redirect('accounts:users_list')

    username = user.username
    user.delete()
    messages.success(request, f'تم حذف المستخدم {username} بنجاح!')
    log_user_activity(request.user, 'delete', 'users_management',
                     object_name=username, request=request)

    return redirect('accounts:users_list')


@login_required
@user_passes_test(is_admin_or_staff)
def user_permissions(request, user_id):
    """إدارة صلاحيات المستخدم"""
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        # معالجة تحديث الصلاحيات
        for module, _ in UserPermission.MODULE_CHOICES:
            for permission_type, _ in UserPermission.PERMISSION_TYPES:
                field_name = f"{module}_{permission_type}"
                is_granted = request.POST.get(field_name) == 'on'

                permission, created = UserPermission.objects.get_or_create(
                    user=user,
                    module=module,
                    permission_type=permission_type,
                    defaults={
                        'is_granted': is_granted,
                        'granted_by': request.user
                    }
                )

                if not created and permission.is_granted != is_granted:
                    permission.is_granted = is_granted
                    permission.granted_by = request.user
                    permission.save()

        messages.success(request, f'تم تحديث صلاحيات المستخدم {user.username} بنجاح!')
        log_user_activity(request.user, 'update', 'user_permissions',
                         object_id=user.id, object_name=user.username, request=request)
        return redirect('accounts:user_permissions', user_id=user.id)

    # الحصول على الصلاحيات الحالية
    current_permissions = {}
    for perm in UserPermission.objects.filter(user=user):
        current_permissions[f"{perm.module}_{perm.permission_type}"] = perm.is_granted

    context = {
        'user_obj': user,
        'modules': UserPermission.MODULE_CHOICES,
        'permission_types': UserPermission.PERMISSION_TYPES,
        'current_permissions': current_permissions,
    }

    return render(request, 'accounts/user_permissions.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def bulk_permissions(request):
    """منح صلاحيات متعددة"""
    if request.method == 'POST':
        form = BulkPermissionForm(request.POST)
        if form.is_valid():
            users = form.cleaned_data['users']
            modules = form.cleaned_data['modules']
            permissions = form.cleaned_data['permissions']
            is_granted = form.cleaned_data['is_granted']

            count = 0
            for user in users:
                for module in modules:
                    for permission_type in permissions:
                        permission, created = UserPermission.objects.get_or_create(
                            user=user,
                            module=module,
                            permission_type=permission_type,
                            defaults={
                                'is_granted': is_granted,
                                'granted_by': request.user
                            }
                        )

                        if not created:
                            permission.is_granted = is_granted
                            permission.granted_by = request.user
                            permission.save()

                        count += 1

            action = 'منح' if is_granted else 'إلغاء'
            messages.success(request, f'تم {action} {count} صلاحية بنجاح!')
            log_user_activity(request.user, 'update', 'bulk_permissions', request=request)
            return redirect('accounts:users_list')
    else:
        form = BulkPermissionForm()

    context = {
        'form': form,
        'title': 'منح صلاحيات متعددة',
    }

    return render(request, 'accounts/bulk_permissions.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def reset_user_password(request, user_id):
    """إعادة تعيين كلمة مرور المستخدم"""
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = PasswordResetForm(request.POST)
        if form.is_valid():
            new_password = form.cleaned_data['new_password']
            user.set_password(new_password)
            user.save()

            messages.success(request, f'تم تغيير كلمة مرور المستخدم {user.username} بنجاح!')
            log_user_activity(request.user, 'update', 'password_reset',
                             object_id=user.id, object_name=user.username, request=request)
            return redirect('accounts:user_detail', user_id=user.id)
    else:
        form = PasswordResetForm(initial={'user': user})

    context = {
        'form': form,
        'user_obj': user,
        'title': f'إعادة تعيين كلمة مرور: {user.username}',
    }

    return render(request, 'accounts/password_reset.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def user_activities(request, user_id):
    """عرض نشاطات المستخدم"""
    user = get_object_or_404(User, id=user_id)

    activities = UserActivity.objects.filter(user=user).order_by('-timestamp')

    # فلترة حسب النشاط
    action_filter = request.GET.get('action', '')
    if action_filter:
        activities = activities.filter(action=action_filter)

    # فلترة حسب الوحدة
    module_filter = request.GET.get('module', '')
    if module_filter:
        activities = activities.filter(module=module_filter)

    # ترقيم الصفحات
    paginator = Paginator(activities, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'user_obj': user,
        'page_obj': page_obj,
        'action_filter': action_filter,
        'module_filter': module_filter,
        'action_choices': UserActivity.ACTION_TYPES,
    }

    return render(request, 'accounts/user_activities.html', context)
