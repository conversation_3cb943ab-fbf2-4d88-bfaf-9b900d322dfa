#!/usr/bin/env python
"""
سكريبت تشغيل الخادم المستقر
Stable Server Startup Script
"""

import os
import sys
import subprocess
import time
import signal
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class StableServer:
    def __init__(self):
        self.process = None
        self.running = False
        self.restart_count = 0
        self.max_restarts = 10
        
    def setup_environment(self):
        """إعداد البيئة"""
        logger.info("🔧 إعداد البيئة...")
        
        # إنشاء المجلدات المطلوبة
        os.makedirs('logs', exist_ok=True)
        os.makedirs('cache', exist_ok=True)
        os.makedirs('staticfiles', exist_ok=True)
        os.makedirs('media', exist_ok=True)
        
        # تعيين متغير البيئة
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.production_settings')
        
        logger.info("✅ تم إعداد البيئة بنجاح")
        
    def migrate_database(self):
        """تشغيل migrations"""
        logger.info("📊 تشغيل migrations...")
        try:
            subprocess.run([
                sys.executable, 'manage.py', 'migrate', '--settings=osaric_accounts.production_settings'
            ], check=True)
            logger.info("✅ تم تشغيل migrations بنجاح")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في migrations: {e}")
            
    def collect_static(self):
        """جمع الملفات الثابتة"""
        logger.info("📁 جمع الملفات الثابتة...")
        try:
            subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput', 
                '--settings=osaric_accounts.production_settings'
            ], check=True)
            logger.info("✅ تم جمع الملفات الثابتة بنجاح")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في جمع الملفات الثابتة: {e}")
            
    def create_superuser_if_needed(self):
        """إنشاء superuser إذا لم يكن موجوداً"""
        logger.info("👤 التحقق من وجود superuser...")
        try:
            # التحقق من وجود مستخدمين
            result = subprocess.run([
                sys.executable, 'manage.py', 'shell', '-c',
                'from django.contrib.auth.models import User; print(User.objects.filter(is_superuser=True).exists())',
                '--settings=osaric_accounts.production_settings'
            ], capture_output=True, text=True)
            
            if 'False' in result.stdout:
                logger.info("📝 إنشاء superuser افتراضي...")
                subprocess.run([
                    sys.executable, 'manage.py', 'shell', '-c',
                    """
from django.contrib.auth.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('تم إنشاء superuser: admin/admin123')
                    """,
                    '--settings=osaric_accounts.production_settings'
                ], check=True)
                logger.info("✅ تم إنشاء superuser بنجاح (admin/admin123)")
            else:
                logger.info("✅ superuser موجود بالفعل")
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في إنشاء superuser: {e}")
            
    def start_server(self):
        """تشغيل الخادم"""
        logger.info("🚀 تشغيل الخادم المستقر...")
        
        try:
            # تشغيل الخادم
            self.process = subprocess.Popen([
                sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000',
                '--settings=osaric_accounts.production_settings',
                '--noreload'  # تعطيل إعادة التحميل التلقائي للاستقرار
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.running = True
            logger.info("✅ تم تشغيل الخادم بنجاح على http://0.0.0.0:8000")
            logger.info("🌐 يمكن الوصول للخادم من:")
            logger.info("   - http://127.0.0.1:8000")
            logger.info("   - http://localhost:8000")
            logger.info("   - http://[IP-ADDRESS]:8000")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل الخادم: {e}")
            return False
            
    def monitor_server(self):
        """مراقبة الخادم وإعادة تشغيله عند الحاجة"""
        logger.info("👁️ بدء مراقبة الخادم...")
        
        while self.running and self.restart_count < self.max_restarts:
            try:
                # التحقق من حالة العملية
                if self.process.poll() is not None:
                    logger.warning("⚠️ الخادم توقف، محاولة إعادة التشغيل...")
                    self.restart_count += 1
                    
                    if self.restart_count < self.max_restarts:
                        time.sleep(5)  # انتظار 5 ثوان
                        if self.start_server():
                            logger.info(f"✅ تم إعادة تشغيل الخادم (المحاولة {self.restart_count})")
                        else:
                            logger.error("❌ فشل في إعادة تشغيل الخادم")
                    else:
                        logger.error(f"❌ تم الوصول للحد الأقصى من المحاولات ({self.max_restarts})")
                        break
                        
                time.sleep(10)  # فحص كل 10 ثوان
                
            except KeyboardInterrupt:
                logger.info("🛑 تم إيقاف المراقبة بواسطة المستخدم")
                self.stop_server()
                break
            except Exception as e:
                logger.error(f"❌ خطأ في المراقبة: {e}")
                time.sleep(5)
                
    def stop_server(self):
        """إيقاف الخادم"""
        logger.info("🛑 إيقاف الخادم...")
        self.running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                logger.info("✅ تم إيقاف الخادم بنجاح")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ إجبار إيقاف الخادم...")
                self.process.kill()
                self.process.wait()
                logger.info("✅ تم إجبار إيقاف الخادم")
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف الخادم: {e}")
                
    def run(self):
        """تشغيل الخادم المستقر"""
        logger.info("🎯 بدء تشغيل الخادم المستقر...")
        
        try:
            # إعداد البيئة
            self.setup_environment()
            
            # تشغيل migrations
            self.migrate_database()
            
            # جمع الملفات الثابتة
            self.collect_static()
            
            # إنشاء superuser
            self.create_superuser_if_needed()
            
            # تشغيل الخادم
            if self.start_server():
                # مراقبة الخادم
                self.monitor_server()
            else:
                logger.error("❌ فشل في تشغيل الخادم")
                
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف الخادم بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ عام: {e}")
        finally:
            self.stop_server()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 خادم أوساريك المحاسبي المستقر")
    print("🌟 Osaric Accounting Stable Server")
    print("=" * 60)
    
    server = StableServer()
    
    # إعداد معالج الإشارات
    def signal_handler(signum, frame):
        logger.info("🛑 تم استلام إشارة الإيقاف")
        server.stop_server()
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # تشغيل الخادم
    server.run()

if __name__ == '__main__':
    main()
