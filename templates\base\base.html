<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}حسابات أوساريك{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts - Enhanced Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-weight: 400;
            line-height: 1.6;
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 600;
            color: #2c3e50;
        }

        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 700;
        }

        .lead {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            font-weight: 400;
        }
        
        /* القائمة الجانبية الاحترافية الجديدة */
        .modern-sidebar {
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            width: 320px !important;
            height: 100vh !important;
            background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
            box-shadow: -10px 0 40px rgba(0,0,0,0.3), -5px 0 20px rgba(0,0,0,0.2) !important;
            z-index: 1000 !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
            border-left: 3px solid rgba(255,255,255,0.1) !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        /* Header الاحترافي */
        .modern-sidebar .sidebar-brand {
            padding: 2.5rem 2rem !important;
            text-align: center !important;
            background: rgba(0,0,0,0.3) !important;
            border-bottom: 3px solid rgba(255,255,255,0.15) !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 10 !important;
            backdrop-filter: blur(20px) !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            margin: 0 !important;
        }

        .modern-sidebar .sidebar-brand:hover {
            background: rgba(0,0,0,0.4) !important;
            transform: scale(1.02) !important;
        }

        .modern-sidebar .brand-logo {
            width: 70px !important;
            height: 70px !important;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb) !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 auto 1.5rem !important;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4) !important;
            transition: all 0.4s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .modern-sidebar .sidebar-brand:hover .brand-logo {
            transform: rotate(360deg) scale(1.15) !important;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6) !important;
        }

        .modern-sidebar .brand-logo i {
            font-size: 2rem !important;
            color: white !important;
            text-shadow: 0 3px 10px rgba(0,0,0,0.4) !important;
            z-index: 2 !important;
            position: relative !important;
        }

        .modern-sidebar .brand-text {
            color: white !important;
            font-size: 1.6rem !important;
            font-weight: 800 !important;
            margin: 0 !important;
            text-shadow: 0 3px 15px rgba(0,0,0,0.5) !important;
            letter-spacing: 1.2px !important;
        }
        
        /* CSS للقائمة الجانبية الجديدة */
        .modern-sidebar .sidebar-nav {
            padding: 2rem 0 !important;
            list-style: none !important;
            margin: 0 !important;
        }

        .modern-sidebar .nav-section {
            margin-bottom: 2.5rem !important;
        }

        .modern-sidebar .section-title {
            padding: 0.8rem 2rem !important;
            color: rgba(255,255,255,0.6) !important;
            font-size: 0.8rem !important;
            font-weight: 700 !important;
            text-transform: uppercase !important;
            letter-spacing: 2px !important;
            margin-bottom: 1.5rem !important;
            position: relative !important;
            background: rgba(255,255,255,0.05) !important;
            border-radius: 0 25px 25px 0 !important;
        }

        .modern-sidebar .nav-item {
            margin: 0.5rem 1.5rem !important;
        }

        .modern-sidebar .nav-link {
            display: flex !important;
            align-items: center !important;
            padding: 1.2rem 1.8rem !important;
            color: rgba(255,255,255,0.85) !important;
            text-decoration: none !important;
            border-radius: 20px !important;
            transition: all 0.4s ease !important;
            position: relative !important;
            font-weight: 500 !important;
            font-size: 1rem !important;
            margin-bottom: 0.3rem !important;
        }

        .modern-sidebar .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.12) !important;
            transform: translateX(10px) scale(1.02) !important;
        }

        .modern-sidebar .nav-link.active {
            background: rgba(255,255,255,0.25) !important;
            color: white !important;
            transform: translateX(8px) !important;
        }

        .modern-sidebar .nav-icon {
            width: 28px !important;
            height: 28px !important;
            margin-left: 1.2rem !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 1.2rem !important;
        }

        .modern-sidebar .nav-text {
            flex: 1 !important;
            font-weight: 500 !important;
        }
        
        .nav-submenu .nav-item {
            margin: 0.25rem 0;
        }
        
        .nav-submenu .nav-link {
            padding: 0.5rem 1rem 0.5rem 2rem;
            font-size: 0.9rem;
        }
        
        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 320px !important;
            padding: 2.5rem !important;
            min-height: 100vh !important;
            transition: all 0.3s ease !important;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .modern-sidebar {
                transform: translateX(100%) !important;
                width: 300px !important;
            }

            .modern-sidebar.show {
                transform: translateX(0) !important;
            }

            .main-content {
                margin-right: 0 !important;
                padding: 1.5rem !important;
            }

            .mobile-toggle {
                display: block !important;
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 1001 !important;
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                border: none !important;
                border-radius: 50% !important;
                width: 60px !important;
                height: 60px !important;
                color: white !important;
                font-size: 1.4rem !important;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
                transition: all 0.3s ease !important;
            }
        }

        .mobile-toggle {
            display: none !important;
        }
        
        /* Card styles */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand" onclick="window.location.href='/'">
            <div class="brand-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="brand-text">حسابات أوساريك</h3>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'home' %}">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </li>
            
            <!-- Definitions Section -->
            <li class="nav-item">
                <div class="nav-section-header">
                    <i class="fas fa-cogs"></i>
                    <span>التعريفات</span>
                </div>
                <ul class="nav-submenu">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:item_list' %}">
                            <i class="fas fa-box"></i>
                            <span>الأصناف</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:category_list' %}">
                            <i class="fas fa-tags"></i>
                            <span>الفئات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:item_location_list' %}">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>مواقع الأصناف</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:expense_category_list' %}">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>فئات المصروفات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:expense_item_list' %}">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>بنود المصروفات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:revenue_category_list' %}">
                            <i class="fas fa-chart-line"></i>
                            <span>فئات الإيرادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:revenue_item_list' %}">
                            <i class="fas fa-hand-holding-usd"></i>
                            <span>بنود الإيرادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'sales:customer_list' %}">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'purchases:supplier_list' %}">
                            <i class="fas fa-truck"></i>
                            <span>الموردين</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:warehouse_list' %}">
                    <i class="fas fa-warehouse"></i>
                    المخازن
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'sales:home' %}">
                    <i class="fas fa-shopping-cart"></i>
                    المبيعات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'purchases:home' %}">
                    <i class="fas fa-shopping-bag"></i>
                    المشتريات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'inventory:warehouse_dashboard' %}">
                    <i class="fas fa-chart-bar"></i>
                    المخزون
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:bank_list' %}">
                    <i class="fas fa-university"></i>
                    البنوك
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:treasury_list' %}">
                    <i class="fas fa-cash-register"></i>
                    الخزائن
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">مرحباً بك في حسابات أوساريك</h4>
                        </div>
                        <div class="card-body">
                            <p>نظام محاسبي متكامل وسهل الاستخدام</p>
                            <p>تم تحديث النظام ليصبح "حسابات أوساريك" مع قائمة جانبية احترافية</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-warehouse fa-2x text-primary mb-2"></i>
                                            <h5>المخازن</h5>
                                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-primary btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-box fa-2x text-success mb-2"></i>
                                            <h5>الأصناف</h5>
                                            <a href="{% url 'definitions:item_list' %}" class="btn btn-success btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                                            <h5>العملاء</h5>
                                            <a href="{% url 'definitions:person_list' %}" class="btn btn-info btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                            <h5>المخزون</h5>
                                            <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-warning btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endblock %}
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle sidebar on mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
        
        // Set active link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
