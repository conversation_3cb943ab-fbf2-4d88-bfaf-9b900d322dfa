<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}حسابات أوساريك{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts - Enhanced Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-weight: 400;
            line-height: 1.6;
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 600;
            color: #2c3e50;
        }

        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 700;
        }

        .lead {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            font-weight: 400;
        }
        
        /* القائمة الجانبية الاحترافية المتطورة */
        .modern-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(145deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            box-shadow: -10px 0 40px rgba(0,0,0,0.3), -5px 0 20px rgba(0,0,0,0.2);
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-left: 3px solid rgba(255,255,255,0.1);
        }

        .modern-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            pointer-events: none;
        }
        
        .sidebar-brand {
            padding: 2rem;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-brand:hover {
            background: rgba(255,255,255,0.1);
            transform: scale(1.02);
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .sidebar-brand:hover .brand-logo {
            transform: rotate(360deg) scale(1.1);
        }

        .brand-logo i {
            font-size: 1.5rem;
            color: white;
        }

        .brand-text {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        /* القائمة الرئيسية الاحترافية */
        .sidebar-nav {
            padding: 2rem 0;
            list-style: none;
            margin: 0;
        }

        .nav-section {
            margin-bottom: 2.5rem;
        }

        .section-title {
            padding: 0.8rem 2rem;
            color: rgba(255,255,255,0.6);
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1.5rem;
            position: relative;
            background: rgba(255,255,255,0.05);
            border-radius: 0 25px 25px 0;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 2rem;
            right: 2rem;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            border-radius: 1px;
        }

        .nav-item {
            margin: 0.5rem 1.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1.2rem 1.8rem;
            color: rgba(255,255,255,0.85);
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            font-weight: 500;
            font-size: 1rem;
            margin-bottom: 0.3rem;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 20px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 0;
            background: linear-gradient(135deg, #fff, #f093fb, #667eea);
            border-radius: 0 3px 3px 0;
            transition: height 0.3s ease;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255,255,255,0.12);
            transform: translateX(10px) scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3), 0 5px 15px rgba(255,255,255,0.1);
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link:hover::after {
            height: 70%;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
            color: white;
            transform: translateX(8px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.4);
        }

        .nav-link.active::after {
            height: 85%;
        }

        .nav-icon {
            width: 28px;
            height: 28px;
            margin-left: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
        }

        .nav-link:hover .nav-icon {
            transform: scale(1.3) rotate(10deg);
            filter: drop-shadow(0 5px 10px rgba(255,255,255,0.4));
        }

        .nav-text {
            flex: 1;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-submenu .nav-item {
            margin: 0.25rem 0;
        }
        
        .nav-submenu .nav-link {
            padding: 0.5rem 1rem 0.5rem 2rem;
            font-size: 0.9rem;
        }
        
        /* Main Content */
        .main-content {
            margin-right: 250px;
            padding: 20px;
            min-height: 100vh;
        }
        
        /* Scrollbar مخصص للقائمة الجانبية */
        .modern-sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .modern-sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .modern-sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(255,255,255,0.4), rgba(255,255,255,0.2));
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .modern-sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.6), rgba(255,255,255,0.4));
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .modern-sidebar {
                transform: translateX(100%);
                width: 300px;
            }

            .modern-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
                padding: 1.5rem;
            }

            .mobile-toggle {
                display: block;
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 50%;
                width: 60px;
                height: 60px;
                color: white;
                font-size: 1.4rem;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
            }

            .mobile-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }
        }

        .mobile-toggle {
            display: none;
        }
        
        /* Card styles */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand" onclick="window.location.href='/'">
            <div class="brand-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="brand-text">حسابات أوساريك</h3>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'home' %}">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </li>
            
            <!-- Definitions Section -->
            <li class="nav-item">
                <div class="nav-section-header">
                    <i class="fas fa-cogs"></i>
                    <span>التعريفات</span>
                </div>
                <ul class="nav-submenu">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:item_list' %}">
                            <i class="fas fa-box"></i>
                            <span>الأصناف</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:category_list' %}">
                            <i class="fas fa-tags"></i>
                            <span>الفئات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:item_location_list' %}">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>مواقع الأصناف</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:expense_category_list' %}">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>فئات المصروفات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:expense_item_list' %}">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>بنود المصروفات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:revenue_category_list' %}">
                            <i class="fas fa-chart-line"></i>
                            <span>فئات الإيرادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'definitions:revenue_item_list' %}">
                            <i class="fas fa-hand-holding-usd"></i>
                            <span>بنود الإيرادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'sales:customer_list' %}">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'purchases:supplier_list' %}">
                            <i class="fas fa-truck"></i>
                            <span>الموردين</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:warehouse_list' %}">
                    <i class="fas fa-warehouse"></i>
                    المخازن
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'sales:home' %}">
                    <i class="fas fa-shopping-cart"></i>
                    المبيعات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'purchases:home' %}">
                    <i class="fas fa-shopping-bag"></i>
                    المشتريات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'inventory:warehouse_dashboard' %}">
                    <i class="fas fa-chart-bar"></i>
                    المخزون
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:bank_list' %}">
                    <i class="fas fa-university"></i>
                    البنوك
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="{% url 'definitions:treasury_list' %}">
                    <i class="fas fa-cash-register"></i>
                    الخزائن
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">مرحباً بك في حسابات أوساريك</h4>
                        </div>
                        <div class="card-body">
                            <p>نظام محاسبي متكامل وسهل الاستخدام</p>
                            <p>تم تحديث النظام ليصبح "حسابات أوساريك" مع قائمة جانبية احترافية</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-warehouse fa-2x text-primary mb-2"></i>
                                            <h5>المخازن</h5>
                                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-primary btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-box fa-2x text-success mb-2"></i>
                                            <h5>الأصناف</h5>
                                            <a href="{% url 'definitions:item_list' %}" class="btn btn-success btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                                            <h5>العملاء</h5>
                                            <a href="{% url 'definitions:person_list' %}" class="btn btn-info btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                            <h5>المخزون</h5>
                                            <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-warning btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endblock %}
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle sidebar on mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Set active link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.modern-sidebar .nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add entrance animation to sidebar items
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(50px)';
                setTimeout(() => {
                    item.style.transition = 'all 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 100);
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
