<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}حسابات أوساريك{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts - Enhanced Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-weight: 400;
            line-height: 1.6;
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 600;
            color: #2c3e50;
        }

        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 700;
        }

        .lead {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            font-weight: 400;
        }
        
        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px 0;
        }
        
        .sidebar-brand {
            padding: 2rem;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-brand:hover {
            background: rgba(255,255,255,0.1);
            transform: scale(1.02);
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .sidebar-brand:hover .brand-logo {
            transform: rotate(360deg) scale(1.1);
        }

        .brand-logo i {
            font-size: 1.5rem;
            color: white;
        }

        .brand-text {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        
        .sidebar .nav-link i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        /* Navigation Sections */
        .nav-section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: rgba(255,255,255,0.9);
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 0.5rem;
        }
        
        .nav-section-header i {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        /* Submenu */
        .nav-submenu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-submenu .nav-item {
            margin: 0.25rem 0;
        }
        
        .nav-submenu .nav-link {
            padding: 0.5rem 1rem 0.5rem 2rem;
            font-size: 0.9rem;
        }
        
        /* Main Content */
        .main-content {
            margin-right: 250px;
            padding: 20px;
            min-height: 100vh;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .mobile-toggle {
                display: block;
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: #667eea;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
        
        /* Card styles */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand" onclick="window.location.href='/'">
            <div class="brand-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="brand-text">حسابات أوساريك</h3>
        </div>
        
        <!-- Navigation Menu -->
        <ul class="sidebar-nav">
            <!-- القسم الأساسي -->
            <li class="nav-section">
                <div class="section-title">الأقسام الأساسية</div>

                <div class="nav-item">
                    <a class="nav-link active" href="{% url 'home' %}">
                        <div class="nav-icon"><i class="fas fa-home"></i></div>
                        <span class="nav-text">الرئيسية</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'dashboard:home' %}">
                        <div class="nav-icon"><i class="fas fa-tachometer-alt"></i></div>
                        <span class="nav-text">لوحة التحكم</span>
                    </a>
                </div>
            </li>

            <!-- قسم التعريفات -->
            <li class="nav-section">
                <div class="section-title">التعريفات</div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'definitions:home' %}">
                        <div class="nav-icon"><i class="fas fa-cogs"></i></div>
                        <span class="nav-text">التعريفات العامة</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'definitions:item_list' %}">
                        <div class="nav-icon"><i class="fas fa-box"></i></div>
                        <span class="nav-text">الأصناف</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'definitions:warehouse_list' %}">
                        <div class="nav-icon"><i class="fas fa-warehouse"></i></div>
                        <span class="nav-text">المخازن</span>
                    </a>
                </div>
            </li>

            <!-- قسم العمليات التجارية -->
            <li class="nav-section">
                <div class="section-title">العمليات التجارية</div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'sales:home' %}">
                        <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
                        <span class="nav-text">المبيعات</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'purchases:home' %}">
                        <div class="nav-icon"><i class="fas fa-shopping-bag"></i></div>
                        <span class="nav-text">المشتريات</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'inventory:warehouse_dashboard' %}">
                        <div class="nav-icon"><i class="fas fa-chart-bar"></i></div>
                        <span class="nav-text">المخزون</span>
                    </a>
                </div>
            </li>

            <!-- قسم المالية -->
            <li class="nav-section">
                <div class="section-title">الإدارة المالية</div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'treasury:home' %}">
                        <div class="nav-icon"><i class="fas fa-cash-register"></i></div>
                        <span class="nav-text">الخزينة</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'definitions:bank_list' %}">
                        <div class="nav-icon"><i class="fas fa-university"></i></div>
                        <span class="nav-text">البنوك</span>
                    </a>
                </div>
            </li>

            <!-- قسم الموارد -->
            <li class="nav-section">
                <div class="section-title">إدارة الموارد</div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'assets:home' %}">
                        <div class="nav-icon"><i class="fas fa-building"></i></div>
                        <span class="nav-text">الأصول الثابتة</span>
                    </a>
                </div>
            </li>

            <!-- قسم التقارير والخدمات -->
            <li class="nav-section">
                <div class="section-title">التقارير والخدمات</div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'reports:dashboard' %}">
                        <div class="nav-icon"><i class="fas fa-chart-pie"></i></div>
                        <span class="nav-text">التقارير</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a class="nav-link" href="{% url 'services:services_dashboard' %}">
                        <div class="nav-icon"><i class="fas fa-tools"></i></div>
                        <span class="nav-text">الخدمات</span>
                    </a>
                </div>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">مرحباً بك في حسابات أوساريك</h4>
                        </div>
                        <div class="card-body">
                            <p>نظام محاسبي متكامل وسهل الاستخدام</p>
                            <p>تم تحديث النظام ليصبح "حسابات أوساريك" مع قائمة جانبية احترافية</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-warehouse fa-2x text-primary mb-2"></i>
                                            <h5>المخازن</h5>
                                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-primary btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-box fa-2x text-success mb-2"></i>
                                            <h5>الأصناف</h5>
                                            <a href="{% url 'definitions:item_list' %}" class="btn btn-success btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                                            <h5>العملاء</h5>
                                            <a href="{% url 'definitions:person_list' %}" class="btn btn-info btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                            <h5>المخزون</h5>
                                            <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-warning btn-sm">عرض</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endblock %}
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle sidebar on mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
        
        // Set active link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
