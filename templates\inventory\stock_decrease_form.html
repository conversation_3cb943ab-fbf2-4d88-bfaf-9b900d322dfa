{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<style>
/* جعل حقل سعر الوحدة readonly */
.price-input {
    background-color: #f8f9fa !important;
    cursor: not-allowed;
}

/* تنسيق حقل الإجمالي */
.total-cost {
    background-color: #e9ecef !important;
    font-weight: bold;
    text-align: center;
}

/* تنسيق الإجمالي العام */
#grand-total {
    font-size: 1.1em;
    color: #dc3545;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-minus-circle text-danger me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الإذن الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الإذن الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.decrease_number.id_for_label }}" class="form-label">{{ form.decrease_number.label }}</label>
                                {{ form.decrease_number }}
                                {% if form.decrease_number.errors %}
                                    <div class="text-danger small">{{ form.decrease_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }}</label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أصناف الإذن -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes me-2"></i>
                            أصناف الإذن
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="addItem()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة صنف
                        </button>
                    </div>
                    <div class="card-body">
                        {{ formset.management_form }}
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" id="items-table">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 25%">الصنف</th>
                                        <th style="width: 15%">الكمية</th>
                                        <th style="width: 15%">سعر الوحدة</th>
                                        <th style="width: 15%">الإجمالي</th>
                                        <th style="width: 15%">السبب</th>
                                        <th style="width: 10%">ملاحظات</th>
                                        <th style="width: 5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.item }}
                                                {% if form.item.errors %}
                                                    <div class="text-danger small">{{ form.item.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.quantity }}
                                                {% if form.quantity.errors %}
                                                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.unit_cost }}
                                                {% if form.unit_cost.errors %}
                                                    <div class="text-danger small">{{ form.unit_cost.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <input type="text" class="form-control total-cost" readonly>
                                            </td>
                                            <td>
                                                {{ form.reason }}
                                                {% if form.reason.errors %}
                                                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ form.notes }}
                                                {% if form.notes.errors %}
                                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if form.DELETE %}
                                                    {{ form.DELETE }}
                                                {% endif %}
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                            {% for hidden in form.hidden_fields %}
                                                {{ hidden }}
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <td colspan="3"><strong>الإجمالي العام:</strong></td>
                                        <td><strong id="grand-total">0.00 ج.م</strong></td>
                                        <td colspan="3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'inventory:stock_decrease_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-save me-1"></i>
                                {{ action }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let itemIndex = {{ formset.total_form_count }};

// وظيفة تحميل الأصناف عبر AJAX
async function loadItems(selectElement) {
    try {
        console.log('Loading items for select element:', selectElement);
        selectElement.innerHTML = '<option value="">جاري التحميل...</option>';
        
        const response = await fetch('/inventory/api/items-with-prices/');
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Loaded items:', data);
        
        if (data && data.success && data.items && Array.isArray(data.items)) {
            selectElement.innerHTML = '<option value="">اختر الصنف</option>';
            data.items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.name} (${item.code})`;
                option.setAttribute('data-price', item.cost_price || 0);
                option.setAttribute('data-unit', item.unit_name || '');
                selectElement.appendChild(option);
            });
        } else {
            console.error('Invalid data format:', data);
            selectElement.innerHTML = '<option value="">خطأ في تحميل البيانات</option>';
        }
    } catch (error) {
        console.error('Error loading items:', error);
        selectElement.innerHTML = '<option value="">خطأ في التحميل</option>';
    }
}

// وظيفة إضافة صف جديد
function addItem() {
    const tbody = document.querySelector('#items-table tbody');
    const newRow = document.createElement('tr');
    newRow.className = 'item-row';
    
    newRow.innerHTML = `
        <td>
            <select name="form-${itemIndex}-item" id="id_form-${itemIndex}-item" class="form-select item-select" required>
                <option value="">اختر الصنف</option>
            </select>
        </td>
        <td>
            <input type="number" name="form-${itemIndex}-quantity" id="id_form-${itemIndex}-quantity" class="form-control quantity-input" step="0.01" min="0" required>
        </td>
        <td>
            <input type="number" name="form-${itemIndex}-unit_cost" id="id_form-${itemIndex}-unit_cost" class="form-control price-input" step="0.01" min="0" readonly>
        </td>
        <td>
            <input type="text" class="form-control total-cost" readonly>
        </td>
        <td>
            <input type="text" name="form-${itemIndex}-reason" id="id_form-${itemIndex}-reason" class="form-control">
        </td>
        <td>
            <input type="text" name="form-${itemIndex}-notes" id="id_form-${itemIndex}-notes" class="form-control">
        </td>
        <td>
            <input type="checkbox" name="form-${itemIndex}-DELETE" id="id_form-${itemIndex}-DELETE" style="display: none;">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
        <input type="hidden" name="form-${itemIndex}-id" id="id_form-${itemIndex}-id">
    `;
    
    tbody.appendChild(newRow);
    
    // تحميل الأصناف للصف الجديد
    const itemSelect = newRow.querySelector('.item-select');
    loadItems(itemSelect);
    
    // إضافة مستمعي الأحداث
    addEventListeners(newRow);
    
    itemIndex++;
    updateManagementForm();
}

// وظيفة حذف صف
function removeItem(button) {
    const row = button.closest('tr');
    const deleteCheckbox = row.querySelector('input[type="checkbox"]');
    
    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        row.style.display = 'none';
    } else {
        row.remove();
    }
    
    updateManagementForm();
    calculateGrandTotal();
}

// وظيفة تحديث نموذج الإدارة
function updateManagementForm() {
    const visibleRows = document.querySelectorAll('#items-table tbody tr:not([style*="display: none"])');
    const totalFormsInput = document.querySelector('#id_form-TOTAL_FORMS');
    const initialFormsInput = document.querySelector('#id_form-INITIAL_FORMS');
    const minFormsInput = document.querySelector('#id_form-MIN_NUM_FORMS');
    const maxFormsInput = document.querySelector('#id_form-MAX_NUM_FORMS');
    
    if (totalFormsInput) totalFormsInput.value = visibleRows.length;
    if (initialFormsInput) initialFormsInput.value = visibleRows.length;
    if (minFormsInput) minFormsInput.value = 1;
    if (maxFormsInput) maxFormsInput.value = 1000;
}

// وظيفة إضافة مستمعي الأحداث
function addEventListeners(row) {
    const itemSelect = row.querySelector('select[name*="item"]');
    const quantityInput = row.querySelector('input[name*="quantity"]');
    const priceInput = row.querySelector('input[name*="unit_cost"]');
    
    if (itemSelect) {
        itemSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = parseFloat(selectedOption.getAttribute('data-price')) || 0;
            const unit = selectedOption.getAttribute('data-unit') || '';
            
            // تحديث سعر الوحدة تلقائياً
            if (priceInput) {
                priceInput.value = price.toFixed(2);
            }
            
            // إضافة معلومات إضافية للصف
            if (quantityInput && unit) {
                quantityInput.placeholder = `الكمية (${unit})`;
            }
            
            // حساب الإجمالي
            calculateRowTotal(row);
        });
    }
    
    if (quantityInput) {
        quantityInput.addEventListener('input', function() {
            calculateRowTotal(row);
        });
    }
}

// وظيفة حساب إجمالي الصف
function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('input[name*="quantity"]')?.value) || 0;
    const price = parseFloat(row.querySelector('input[name*="unit_cost"]')?.value) || 0;
    const total = quantity * price;
    
    const totalInput = row.querySelector('.total-cost');
    if (totalInput) {
        totalInput.value = total.toFixed(2);
    }
    
    calculateGrandTotal();
}

// وظيفة حساب الإجمالي العام
function calculateGrandTotal() {
    const totals = Array.from(document.querySelectorAll('.total-cost'))
        .map(input => parseFloat(input.value) || 0);
    
    const grandTotal = totals.reduce((sum, total) => sum + total, 0);
    const grandTotalElement = document.getElementById('grand-total');
    
    if (grandTotalElement) {
        grandTotalElement.textContent = grandTotal.toFixed(2) + ' ج.م';
    }
}

// وظيفة تحديث جميع الأسعار والإجماليات
function updateAllCalculations() {
    document.querySelectorAll('.item-row').forEach(row => {
        const itemSelect = row.querySelector('select[name*="item"]');
        if (itemSelect && itemSelect.value) {
            const selectedOption = itemSelect.options[itemSelect.selectedIndex];
            const price = parseFloat(selectedOption.getAttribute('data-price')) || 0;
            const priceInput = row.querySelector('input[name*="unit_cost"]');
            if (priceInput) {
                priceInput.value = price.toFixed(2);
            }
        }
        calculateRowTotal(row);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing stock decrease form...');
    
    // جعل حقل سعر الوحدة readonly في الصفوف الموجودة
    document.querySelectorAll('input[name*="unit_cost"]').forEach(input => {
        input.readOnly = true;
        input.classList.add('price-input');
    });
    
    // تحميل الأصناف للصفوف الموجودة
    document.querySelectorAll('select[name*="item"]').forEach(select => {
        loadItems(select);
    });
    
    // إضافة مستمعي الأحداث للصفوف الموجودة
    document.querySelectorAll('.item-row').forEach(row => {
        addEventListeners(row);
    });
    
    // تحديث جميع الحسابات
    setTimeout(() => {
        updateAllCalculations();
    }, 1000);
    
    // حساب الإجمالي الأولي
    calculateGrandTotal();
    
    console.log('Stock decrease form initialized successfully');
});

// إضافة مستمع لحدث تغيير المخزن (إذا كان موجوداً)
document.addEventListener('DOMContentLoaded', function() {
    const warehouseSelect = document.querySelector('select[name="warehouse"]');
    if (warehouseSelect) {
        warehouseSelect.addEventListener('change', function() {
            console.log('Warehouse changed, updating calculations...');
            setTimeout(() => {
                updateAllCalculations();
            }, 500);
        });
    }
});
</script>
{% endblock %} 