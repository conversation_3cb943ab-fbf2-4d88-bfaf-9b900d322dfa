"""
إعدادات الإنتاج المحسنة للخادم المستقر
Enhanced Production Settings for Stable Server
"""

from .settings import *
import os
import logging

# إعدادات الأمان المحسنة - Enhanced Security Settings
DEBUG = False
ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '*************',  # IP الشبكة المحلية
    'osaric-accounting.local',
    'accounting.local',
    '*',  # للوصول من أي مكان (يمكن تخصيصه حسب الحاجة)
]

# إعدادات قاعدة البيانات المحسنة - Enhanced Database Settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'production_db.sqlite3',
        'OPTIONS': {
            'timeout': 60,  # مهلة زمنية أطول للاستقرار
        }
    }
}

# إعدادات الجلسات المحسنة - Enhanced Session Settings
SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'
SESSION_COOKIE_AGE = 86400 * 7  # أسبوع واحد
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = False  # تحسين الأداء
SESSION_COOKIE_SECURE = False  # للشبكة المحلية
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'

# إعدادات التخزين المؤقت المحسنة - Enhanced Cache Settings
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'default-cache',
        'TIMEOUT': 3600,  # ساعة واحدة
    }
}

# إعدادات الأداء - Performance Settings
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
CONN_MAX_AGE = 3600  # ساعة واحدة

# إعدادات السجلات - Logging Settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

# إنشاء المجلدات المطلوبة
os.makedirs(BASE_DIR / 'logs', exist_ok=True)
os.makedirs(BASE_DIR / 'cache', exist_ok=True)
os.makedirs(BASE_DIR / 'staticfiles', exist_ok=True)

# إعدادات الأمان للإنتاج - Production Security Settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_REFERRER_POLICY = 'same-origin'
X_FRAME_OPTIONS = 'SAMEORIGIN'

# إعدادات CORS للوصول من أي مكان
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://*************:8000",
]

# إعدادات الملفات الثابتة للإنتاج
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# إعدادات إضافية للاستقرار
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# تحسينات الأداء
USE_TZ = True
USE_I18N = True
USE_L10N = False  # للتحكم في تنسيق التاريخ

# إعدادات البريد الإلكتروني (للإشعارات)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# إعدادات إضافية للاستقرار
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

print("✅ تم تحميل إعدادات الإنتاج المحسنة بنجاح")
print(f"🌐 الخادم سيعمل على: {ALLOWED_HOSTS}")
print(f"📁 قاعدة البيانات: {DATABASES['default']['NAME']}")
print(f"📝 ملفات السجلات: {BASE_DIR / 'logs'}")
