{% extends "base/base.html" %}
{% load static %}

{% block title %}أمر إنتاج حديث - واجهة تفاعلية{% endblock %}

{% block extra_css %}
<style>
    body, .manufacturing-container { background: #f4f6fb; }
    .manufacturing-container { min-height: 100vh; padding: 30px 0; }
    .modern-card { background: #fff; border-radius: 18px; box-shadow: 0 8px 32px rgba(80,80,160,0.10); overflow: hidden; max-width: 950px; margin: 40px auto; }
    .modern-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; padding: 32px 24px 18px; text-align: center; border-radius: 18px 18px 0 0; }
    .modern-title { font-size: 2.1rem; font-weight: bold; margin-bottom: 8px; }
    .modern-section { background: #fff; border-radius: 12px; border: 1px solid #e9ecef; box-shadow: 0 2px 8px rgba(0,0,0,0.04); margin-bottom: 22px; padding: 18px 20px; }
    .modern-label { font-weight: 600; color: #333; margin-bottom: 6px; }
    .modern-input, .modern-select { border-radius: 8px; border: 2px solid #e9ecef; padding: 10px 13px; font-size: 1.08rem; margin-bottom: 10px; width: 100%; }
    .modern-select:focus, .modern-input:focus { border-color: #667eea; box-shadow: 0 0 10px rgba(102,126,234,0.10); }
    .modern-table { width: 100%; border-radius: 8px; overflow: hidden; margin-bottom: 0; }
    .modern-table th, .modern-table td { padding: 13px 8px; vertical-align: middle; border: none; text-align: center; }
    .modern-table thead { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; }
    .modern-add-btn { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none; border-radius: 50px; padding: 10px 24px; color: #fff; font-weight: bold; font-size: 1.08rem; margin-bottom: 10px; transition: all 0.3s; box-shadow: 0 2px 8px rgba(79,172,254,0.18); }
    .modern-add-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 18px rgba(79,172,254,0.25); }
    .modern-remove-btn { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); border: none; border-radius: 20px; padding: 6px 12px; color: #fff; font-size: 1.1rem; transition: all 0.2s; }
    .modern-remove-btn:hover { transform: scale(1.08); box-shadow: 0 2px 8px rgba(255,107,107,0.18); }
    .modern-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; border-radius: 12px; padding: 18px 24px; margin: 20px 0; }
    .modern-summary-row { display: flex; justify-content: space-between; align-items: center; padding: 7px 0; border-bottom: 1px solid rgba(255,255,255,0.13); }
    .modern-summary-row:last-child { border-bottom: none; font-size: 1.1rem; font-weight: bold; background: rgba(255,255,255,0.08); margin: 8px -24px -18px; padding: 14px 24px; border-radius: 0 0 12px 12px; }
    .modern-btn-main { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border: none; border-radius: 50px; padding: 14px 38px; color: #fff; font-size: 1.1rem; font-weight: bold; transition: all 0.2s; box-shadow: 0 6px 18px rgba(17,153,142,0.18); }
    .modern-btn-main:hover { transform: translateY(-2px); box-shadow: 0 10px 28px rgba(17,153,142,0.25); }
    .modern-btn-outline { border: 2px solid #667eea; color: #667eea; background: #fff; border-radius: 50px; padding: 12px 32px; font-weight: bold; margin-right: 10px; }
    .modern-btn-outline:hover { background: #f0f4ff; }
    .modern-alert { border-radius: 12px; border: none; padding: 14px; margin-bottom: 16px; background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%); color: #d63031; display: none; }
    @media (max-width: 900px) { .modern-card { max-width: 100%; } }
    @media (max-width: 600px) { .modern-header, .modern-summary { padding: 12px 4px; } .modern-table th, .modern-table td { font-size: 0.98rem; padding: 8px 2px; } }
</style>
{% endblock %}

{% block content %}
<div class="manufacturing-container">
    <div class="modern-card">
        <div class="modern-header">
            <div class="modern-title">أمر إنتاج حديث</div>
            <div>رقم الأمر: <span id="orderNumber">{{ order_number|default:"2025-0001" }}</span> | تاريخ: <span id="orderDate">{{ current_date|default:"2025/06/27" }}</span></div>
        </div>
        <form method="post" id="modernManufacturingForm" autocomplete="off">
            {% csrf_token %}
            <div class="modern-section">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="modern-label">المنتج النهائي</label>
                        {{ form.finished_product }}
                    </div>
                    <div class="col-md-3">
                        <label class="modern-label">الكمية</label>
                        {{ form.quantity_to_produce }}
                    </div>
                    <div class="col-md-3">
                        <label class="modern-label">الوحدة</label>
                        <input type="text" class="modern-input" value="وحدة" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="modern-label">مخزن المواد الخام</label>
                        {{ form.raw_materials_warehouse }}
                    </div>
                    <div class="col-md-6">
                        <label class="modern-label">مخزن المنتج النهائي</label>
                        {{ form.finished_goods_warehouse }}
                    </div>
                </div>
            </div>
            <div class="modern-section">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="modern-label">المواد الخام المطلوبة</span>
                </div>
                <div class="table-responsive">
                    <table class="modern-table" id="modernMaterialsTable">
                        <thead>
                            <tr>
                                <th>المادة الخام</th>
                                <th>الكود</th>
                                <th>الوحدة</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>التكلفة الجزئية</th>
                                <th>المخزن</th>
                                <th>ملاحظات</th>
                                <th>متوفر بالمخزن</th>
                                <th>نسبة التغطية</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="modernMaterialsBody">
                            <tr class="modern-row" data-row="0" id="modern-default-row">
                                <td>
                                    <select class="modern-select material-select" name="form-0-material" required>
                                        <option value="">اختر المادة...</option>
                                        {% for item in items %}
                                            <option value="{{ item.id }}" data-cost="{{ item.unit_cost|default:item.cost_price|default:0|floatformat:2 }}" data-unit="{{ item.unit|default:'وحدة' }}" data-code="{{ item.code }}" data-stock="{{ item.stock|default:0 }}">{{ item.name }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td class="mat-code">-</td>
                                <td class="mat-unit">-</td>
                                <td><input type="number" class="modern-input quantity-input-field" name="form-0-quantity_required" min="0.01" step="0.01" required></td>
                                <td><input type="text" class="modern-input unit-cost text-center fw-bold" readonly value="0.00"></td>
                                <td><input type="text" class="modern-input partial-cost text-center fw-bold text-primary" readonly value="0.00"></td>
                                <td><input type="text" class="modern-input" name="form-0-warehouse" placeholder="اسم المخزن"></td>
                                <td><input type="text" class="modern-input" name="form-0-notes" placeholder="ملاحظات"></td>
                                <td class="mat-stock">-</td>
                                <td class="mat-coverage">-</td>
                                <td>
                                    <button type="button" class="modern-add-btn" style="padding:6px 12px;font-size:1.1rem;min-width:40px;" title="إضافة صف" onclick="addModernRow(this)"><i class="fas fa-plus"></i></button>
                                    <button type="button" class="modern-remove-btn" style="padding:6px 12px;font-size:1.1rem;min-width:40px;" title="حذف الصف" onclick="removeModernRow(0)"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div id="modernAlert" class="modern-alert"></div>
            </div>
            <div class="modern-summary">
                <div class="modern-summary-row"><span>إجمالي تكلفة المواد الخام:</span><span id="modernTotalMaterialCost">0.00 ج.م</span></div>
                <div class="modern-summary-row"><span>تكلفة الوحدة الواحدة:</span><span id="modernUnitCost">0.00 ج.م</span></div>
                <div class="modern-summary-row"><span>التكلفة الإجمالية للإنتاج:</span><span id="modernTotalProductionCost">0.00 ج.م</span></div>
            </div>
            <div class="modern-section">
                <label class="modern-label">ملاحظات إضافية</label>
                <textarea class="modern-input" name="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية هنا..."></textarea>
            </div>
            <div class="text-center mt-4">
                <button type="submit" class="modern-btn-main me-3"><i class="fas fa-cogs me-2"></i>تنفيذ أمر الإنتاج</button>
                <button type="button" class="modern-btn-outline me-3" onclick="window.print()"><i class="fas fa-print me-2"></i>طباعة</button>
                <a href="{% url 'inventory:manufacturing_order_list' %}" class="modern-btn-outline">العودة للقائمة</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    function updateModernRow(row) {
        const qty = parseFloat(row.find('.quantity-input-field').val()) || 0;
        const sel = row.find('.material-select option:selected');
        const unitCost = parseFloat(sel.data('cost')) || 0;
        const stock = parseFloat(sel.data('stock')) || 0;
        row.find('input.unit-cost').val(unitCost.toFixed(2));
        row.find('input.partial-cost').val((qty * unitCost).toFixed(2));
        row.find('.mat-code').text(sel.data('code') || '-');
        row.find('.mat-unit').text(sel.data('unit') || '-');
        row.find('.mat-stock').text(!isNaN(stock) ? stock.toLocaleString() : '-');
        // نسبة التغطية
        if (qty > 0 && stock >= 0) {
            const coverage = Math.min(100, (stock / qty) * 100);
            row.find('.mat-coverage').html(`<span style="color:${coverage>=100?'#27ae60':'#e67e22'};font-weight:bold">${coverage.toFixed(0)}%</span>`);
        } else {
            row.find('.mat-coverage').text('-');
        }
    }
    function updateModernTotal() {
        let total = 0;
        $('input.partial-cost').each(function() { total += parseFloat($(this).val()) || 0; });
        $('#modernTotalMaterialCost').text(total.toFixed(2) + ' ج.م');
        const qty = parseFloat($('input[name="quantity_to_produce"]').val()) || 0;
        $('#modernUnitCost').text(qty > 0 ? (total/qty).toFixed(2) + ' ج.م' : '0.00 ج.م');
        $('#modernTotalProductionCost').text(total.toFixed(2) + ' ج.م');
    }
    function showModernAlert(msg) {
        $('#modernAlert').show().text(msg);
    }
    function hideModernAlert() {
        $('#modernAlert').hide().text('');
    }
    $(document).on('change input', '.quantity-input-field, .material-select', function() {
        const row = $(this).closest('tr');
        updateModernRow(row);
        updateModernTotal();
        hideModernAlert();
    });
    $('input[name="quantity_to_produce"]').on('input', updateModernTotal);
    window.addModernRow = function(btn) {
        hideModernAlert();
        const idx = $('.modern-row').length;
        const row = $('#modern-default-row').clone();
        row.attr('data-row', idx).removeAttr('id');
        row.find('select, input').each(function() {
            const name = $(this).attr('name');
            if (name) $(this).attr('name', name.replace(/form-\d+-/g, `form-${idx}-`));
            const id = $(this).attr('id');
            if (id) $(this).attr('id', id.replace(/form-\d+-/g, `form-${idx}-`));
            if ($(this).is('input')) {
                if ($(this).hasClass('unit-cost') || $(this).hasClass('partial-cost')) {
                    $(this).val('0.00');
                } else {
                    $(this).val('');
                }
            }
        });
        row.find('.material-select').prop('selectedIndex', 0);
        row.find('input.unit-cost').val('0.00');
        row.find('input.partial-cost').val('0.00');
        row.find('input.quantity-input-field').val('');
        row.find('.mat-code').text('-');
        row.find('.mat-unit').text('-');
        row.find('.mat-stock').text('-');
        row.find('.mat-coverage').text('-');
        row.find('.modern-add-btn').attr('onclick', 'addModernRow(this)');
        row.find('.modern-remove-btn').attr('onclick', `removeModernRow(${idx})`);
        $(btn).closest('tr').after(row);
    };
    window.removeModernRow = function(rowId) {
        if ($('.modern-row').length > 1) {
            $(`.modern-row[data-row="${rowId}"]`).remove();
            updateModernTotal();
        } else {
            showModernAlert('يجب أن يحتوي أمر الإنتاج على مادة خام واحدة على الأقل.');
        }
    };
    // عند تحميل الصفحة: تحديث كل صف حسب القيم المدخلة
    $('.modern-row').each(function() {
        updateModernRow($(this));
    });
    updateModernTotal();
    // التحقق من صحة الإدخال عند الإرسال
    $('#modernManufacturingForm').on('submit', function() {
        let isValid = true;
        $('.modern-row').each(function() {
            const sel = $(this).find('.material-select');
            const qty = $(this).find('.quantity-input-field');
            if (!sel.val() || !qty.val() || parseFloat(qty.val()) <= 0) {
                isValid = false;
                showModernAlert('يرجى تحديد المادة الخام وإدخال كمية صحيحة لكل صف.');
                sel.addClass('is-invalid');
                qty.addClass('is-invalid');
            } else {
                sel.removeClass('is-invalid');
                qty.removeClass('is-invalid');
            }
        });
        return isValid;
    });
});
</script>
{% endblock %}
