from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # لوحة التحكم
    path('', views.dashboard, name='dashboard'),
    
    # أنظمة صرف المرتبات
    path('salary-systems/', views.salary_system_list, name='salary_system_list'),
    path('salary-systems/create/', views.salary_system_create, name='salary_system_create'),
    path('salary-systems/<int:pk>/', views.salary_system_detail, name='salary_system_detail'),
    path('salary-systems/<int:pk>/edit/', views.salary_system_edit, name='salary_system_edit'),
    path('salary-systems/<int:pk>/delete/', views.salary_system_delete, name='salary_system_delete'),
    
    # الأقسام
    path('departments/', views.department_list, name='department_list'),
    path('departments/create/', views.department_create, name='department_create'),
    path('departments/<int:pk>/', views.department_detail, name='department_detail'),
    path('departments/<int:pk>/edit/', views.department_edit, name='department_edit'),
    path('departments/<int:pk>/delete/', views.department_delete, name='department_delete'),
    
    # الموظفين
    path('employees/', views.employee_list, name='employee_list'),
    path('employees/create/', views.employee_create, name='employee_create'),
    path('employees/<int:pk>/', views.employee_detail, name='employee_detail'),
    path('employees/<int:pk>/edit/', views.employee_edit, name='employee_edit'),
    path('employees/<int:pk>/delete/', views.employee_delete, name='employee_delete'),
    
    # المناصب
    path('positions/', views.position_list, name='position_list'),
    path('positions/create/', views.position_create, name='position_create'),
    path('positions/<int:pk>/', views.position_detail, name='position_detail'),
    path('positions/<int:pk>/edit/', views.position_edit, name='position_edit'),
    path('positions/<int:pk>/delete/', views.position_delete, name='position_delete'),

    # البيانات الأساسية
    path('job-titles/', views.job_title_list, name='job_title_list'),
    path('job-titles/create/', views.job_title_create, name='job_title_create'),
    path('qualifications/', views.qualification_list, name='qualification_list'),
    path('qualifications/create/', views.qualification_create, name='qualification_create'),
    path('qualifications/<int:pk>/', views.qualification_detail, name='qualification_detail'),
    path('qualifications/<int:pk>/edit/', views.qualification_edit, name='qualification_edit'),
    path('qualifications/<int:pk>/delete/', views.qualification_delete, name='qualification_delete'),
    path('shifts/', views.shift_list, name='shift_list'),
    path('shifts/create/', views.shift_create, name='shift_create'),
    path('shifts/<int:pk>/', views.shift_detail, name='shift_detail'),
    path('shifts/<int:pk>/edit/', views.shift_edit, name='shift_edit'),
    path('shifts/<int:pk>/delete/', views.shift_delete, name='shift_delete'),
    path('allowances-deductions/', views.allowance_deduction_list, name='allowance_deduction_list'),
    path('allowances-deductions/allowance/create/', views.allowance_create, name='allowance_create'),
    path('allowances-deductions/deduction/create/', views.deduction_create, name='deduction_create'),
    path('allowance-deduction/<int:pk>/delete/', views.allowance_deduction_delete, name='allowance_deduction_delete'),
    path('allowance-deduction/<int:pk>/edit/', views.allowance_deduction_edit, name='allowance_deduction_edit'),
    path('allowance-deduction/<int:pk>/print/', views.allowance_deduction_print, name='allowance_deduction_print'),

    # أنواع الإضافات
    path('allowance-types/', views.allowance_type_list, name='allowance_type_list'),
    path('allowance-types/create/', views.allowance_type_create, name='allowance_type_create'),
    path('allowance-types/<int:pk>/edit/', views.allowance_type_edit, name='allowance_type_edit'),
    path('allowance-types/<int:pk>/delete/', views.allowance_type_delete, name='allowance_type_delete'),

    # أنواع الخصومات
    path('deduction-types/', views.deduction_type_list, name='deduction_type_list'),
    path('deduction-types/create/', views.deduction_type_create, name='deduction_type_create'),
    path('deduction-types/<int:pk>/edit/', views.deduction_type_edit, name='deduction_type_edit'),
    path('deduction-types/<int:pk>/delete/', views.deduction_type_delete, name='deduction_type_delete'),

    # الحضور والانصراف
    path('attendance/', views.attendance_list, name='attendance_list'),
    path('attendance/report/', views.attendance_report, name='attendance_report'),
    path('overtime/', views.overtime_list, name='overtime_list'),
    path('overtime/create/', views.overtime_create, name='overtime_create'),
    path('overtime/<int:pk>/edit/', views.overtime_edit, name='overtime_edit'),
    path('overtime/<int:pk>/delete/', views.overtime_delete, name='overtime_delete'),
    path('leave-requests/', views.leave_request_list, name='leave_request_list'),
    path('leave-requests/create/', views.leave_request_create, name='leave_request_create'),
    path('leave-requests/<int:pk>/', views.leave_request_detail, name='leave_request_detail'),
    path('leave-requests/<int:pk>/edit/', views.leave_request_edit, name='leave_request_edit'),
    path('leave-requests/<int:pk>/approve/', views.leave_request_approve, name='leave_request_approve'),
    path('leave-requests/<int:pk>/reject/', views.leave_request_reject, name='leave_request_reject'),
    path('leave-requests/<int:pk>/delete/', views.leave_request_delete, name='leave_request_delete'),
    path('absence/', views.absence_list, name='absence_list'),
    path('absence/create/', views.absence_create, name='absence_create'),
    path('absence/<int:pk>/', views.absence_detail, name='absence_detail'),
    path('absence/<int:pk>/edit/', views.absence_edit, name='absence_edit'),
    path('absence/<int:pk>/delete/', views.absence_delete, name='absence_delete'),
    path('absence/<int:pk>/approve/', views.absence_approve, name='absence_approve'),
    path('absence/<int:pk>/reject/', views.absence_reject, name='absence_reject'),
    path('attendance/<int:pk>/edit/', views.attendance_edit, name='attendance_edit'),
    path('attendance/<int:pk>/delete/', views.attendance_delete, name='attendance_delete'),

    # إدارة الإجازات
    path('leave-types/', views.leave_type_list, name='leave_type_list'),
    path('leave-types/create/', views.leave_type_create, name='leave_type_create'),
    path('leave-types/<int:pk>/edit/', views.leave_type_edit, name='leave_type_edit'),
    path('leave-types/<int:pk>/delete/', views.leave_type_delete, name='leave_type_delete'),
    path('leave-balances/', views.leave_balance_list, name='leave_balance_list'),
    path('leave-balances/create/', views.leave_balance_create, name='leave_balance_create'),
    path('leave-balances/<int:pk>/edit/', views.leave_balance_edit, name='leave_balance_edit'),
    path('leave-balances/<int:pk>/delete/', views.leave_balance_delete, name='leave_balance_delete'),
    path('leave-approvals/', views.leave_approval_list, name='leave_approval_list'),
    path('leave-calendar/', views.leave_calendar, name='leave_calendar'),

    # العقود والتعيينات
    path('contracts/', views.contract_list, name='contract_list'),
    path('contracts/create/', views.contract_create, name='contract_create'),
    path('contracts/<int:pk>/edit/', views.contract_edit, name='contract_edit'),
    path('contracts/<int:pk>/delete/', views.contract_delete, name='contract_delete'),
    path('contracts/<int:pk>/unarchive/', views.contract_unarchive, name='contract_unarchive'),
    path('contracts/<int:pk>/archive/', views.contract_archive, name='contract_archive'),
    path('contracts/<int:pk>/print/', views.contract_print, name='contract_print'),
    path('contracts/<int:pk>/print_summary/', views.contract_print_summary, name='contract_print_summary'),
    path('employment/', views.employment_list, name='employment_list'),
    path('promotions/', views.promotion_list, name='promotion_list'),
    path('promotions/create/', views.promotion_create, name='promotion_create'),
    path('promotions/<int:pk>/', views.promotion_detail, name='promotion_detail'),
    path('promotions/<int:pk>/edit/', views.promotion_edit, name='promotion_edit'),
    path('promotions/<int:pk>/delete/', views.promotion_delete, name='promotion_delete'),
    path('promotions/<int:pk>/approve/', views.promotion_approve, name='promotion_approve'),
    path('promotions/<int:pk>/reject/', views.promotion_reject, name='promotion_reject'),
    path('transfers/', views.transfer_list, name='transfer_list'),
    path('transfers/create/', views.transfer_create, name='transfer_create'),
    path('transfers/<int:pk>/', views.transfer_detail, name='transfer_detail'),
    path('transfers/<int:pk>/edit/', views.transfer_edit, name='transfer_edit'),
    path('transfers/<int:pk>/delete/', views.transfer_delete, name='transfer_delete'),
    path('transfers/<int:pk>/approve/', views.transfer_approve, name='transfer_approve'),
    path('transfers/<int:pk>/reject/', views.transfer_reject, name='transfer_reject'),
    path('terminations/', views.termination_list, name='termination_list'),
    path('employments/create/', views.employment_create, name='employment_create'),

    # المرتبات والحسابات
    path('salary-additions/', views.salary_addition_list, name='salary_addition_list'),
    path('salary-additions/create/', views.salary_addition_create, name='salary_addition_create'),
    path('salary-additions/<int:pk>/', views.salary_addition_detail, name='salary_addition_detail'),
    path('salary-additions/<int:pk>/edit/', views.salary_addition_edit, name='salary_addition_edit'),
    path('salary-additions/<int:pk>/delete/', views.salary_addition_delete, name='salary_addition_delete'),
    path('salary-deductions/', views.salary_deduction_list, name='salary_deduction_list'),
    path('salary-deductions/<int:pk>/print/', views.salary_deduction_print, name='salary_deduction_print'),
    path('salary-deductions/<int:pk>/edit/', views.salary_deduction_edit, name='salary_deduction_edit'),
    path('salary-deductions/<int:pk>/', views.salary_deduction_detail, name='salary_deduction_detail'),
    path('payroll/', views.payroll_list, name='payroll_list'),
    path('loans/', views.loan_list, name='loan_list'),
    path('salary-calculation/', views.salary_calculation, name='salary_calculation'),

    # التدريب والتطوير
    path('training-programs/', views.training_program_list, name='training_program_list'),
    path('training-enrollments/', views.training_enrollment_list, name='training_enrollment_list'),
    path('training-evaluations/', views.training_evaluation_list, name='training_evaluation_list'),
    path('training-certificates/', views.training_certificate_list, name='training_certificate_list'),

    # تقييم الأداء
    path('evaluation-criteria/', views.evaluation_criteria_list, name='evaluation_criteria_list'),
    path('performance-evaluations/', views.performance_evaluation_list, name='performance_evaluation_list'),
    path('performance-reports/', views.performance_report_list, name='performance_report_list'),
    path('development-plans/', views.development_plan_list, name='development_plan_list'),

    # التقارير المتخصصة
    path('employee-report/', views.employee_report, name='employee_report'),
    path('employee-diary/', views.employee_diary, name='employee_diary'),
    path('salary-report/', views.salary_report, name='salary_report'),
    path('attendance-summary/', views.attendance_summary, name='attendance_summary'),

    # تقييم الأداء
    path('performance-criteria/', views.performance_criteria_list, name='performance_criteria_list'),
    path('performance-evaluations/', views.performance_evaluation_list, name='performance_evaluation_list'),
    path('performance-reports/', views.performance_report_list, name='performance_report_list'),
    path('performance-improvement-plans/', views.performance_improvement_plan_list, name='performance_improvement_plan_list'),

    # التقارير المتخصصة
    path('reports/employees/', views.employee_report, name='employee_report'),
    path('reports/journal/', views.hr_journal, name='hr_journal'),
    path('reports/salary/', views.salary_report, name='salary_report'),

    # إنهاء الخدمة
    path('terminations/', views.termination_list, name='termination_list'),
    path('terminations/create/', views.termination_create, name='termination_create'),
    path('terminations/<int:pk>/', views.termination_detail, name='termination_detail'),
    path('terminations/<int:pk>/edit/', views.termination_edit, name='termination_edit'),
    path('terminations/<int:pk>/delete/', views.termination_delete, name='termination_delete'),
    path('terminations/<int:pk>/approve/', views.termination_approve, name='termination_approve'),
    path('terminations/<int:pk>/reject/', views.termination_reject, name='termination_reject'),
]
