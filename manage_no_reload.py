#!/usr/bin/env python
"""
Django management utility بدون autoreload.
"""
import os
import sys

if __name__ == '__main__':
    # تعطيل Django autoreload
    os.environ['DJANGO_AUTORELOAD'] = 'False'
    os.environ['RUN_MAIN'] = 'true'
    
    # إعداد Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
    
    try:
        from django.core.management import execute_from_command_line
        
        # تعطيل autoreload قبل تشغيل Django
        from django.utils import autoreload
        autoreload.USE_INOTIFY = False
        autoreload.RUN_RELOADER = False
        
        print("🔧 تم تعطيل Django autoreload")
        print("🚀 تشغيل الخادم بدون مراقبة الملفات...")
        
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    
    execute_from_command_line(sys.argv)
