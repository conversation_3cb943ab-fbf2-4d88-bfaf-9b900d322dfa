#!/usr/bin/env python3
"""
تثبيت خدمة Windows للخادم الأبدي
Install Windows Service for Eternal Server

تثبيت الخادم كخدمة Windows تعمل تلقائياً عند بدء التشغيل
Install the server as a Windows service that starts automatically at boot
"""

import os
import sys
import subprocess
from pathlib import Path

def create_service_script():
    """إنشاء سكريبت الخدمة"""
    service_script = """
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import subprocess
import time
from pathlib import Path

class EternalServerService(win32serviceutil.ServiceFramework):
    _svc_name_ = "EternalAccountingServer"
    _svc_display_name_ = "Eternal Accounting Server"
    _svc_description_ = "خادم المحاسبة الأبدي - لا يتوقف أبداً"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_alive = True
        self.server_process = None

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        if self.server_process:
            self.server_process.terminate()

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        # تغيير المجلد إلى مجلد المشروع
        project_dir = Path(__file__).parent
        os.chdir(project_dir)
        
        while self.is_alive:
            try:
                # تشغيل الخادم الأبدي
                self.server_process = subprocess.Popen([
                    sys.executable, 'eternal_server.py'
                ], cwd=project_dir)
                
                # انتظار حتى توقف الخادم أو إيقاف الخدمة
                while self.is_alive and self.server_process.poll() is None:
                    time.sleep(10)
                
                if self.is_alive and self.server_process.poll() is not None:
                    # الخادم توقف، أعد تشغيله
                    servicemanager.LogMsg(servicemanager.EVENTLOG_WARNING_TYPE,
                                          servicemanager.PYS_SERVICE_STOPPED,
                                          (self._svc_name_, 'Server stopped, restarting...'))
                    time.sleep(5)
                    
            except Exception as e:
                servicemanager.LogMsg(servicemanager.EVENTLOG_ERROR_TYPE,
                                      servicemanager.PYS_SERVICE_STOPPED,
                                      (self._svc_name_, str(e)))
                time.sleep(10)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(EternalServerService)
"""
    
    with open('eternal_service.py', 'w', encoding='utf-8') as f:
        f.write(service_script)
    
    print("✅ تم إنشاء سكريبت الخدمة")

def install_service_requirements():
    """تثبيت متطلبات الخدمة"""
    print("📦 تثبيت متطلبات خدمة Windows...")
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'pywin32'
        ], check=True)
        print("✅ تم تثبيت pywin32")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت pywin32")
        return False
    
    return True

def install_service():
    """تثبيت الخدمة"""
    print("🔧 تثبيت خدمة Windows...")
    
    try:
        subprocess.run([
            sys.executable, 'eternal_service.py', 'install'
        ], check=True)
        print("✅ تم تثبيت الخدمة")
        
        # تعيين الخدمة للبدء التلقائي
        subprocess.run([
            'sc', 'config', 'EternalAccountingServer', 'start=', 'auto'
        ], check=True)
        print("✅ تم تعيين البدء التلقائي")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت الخدمة: {e}")
        return False

def start_service():
    """بدء الخدمة"""
    print("🚀 بدء خدمة الخادم الأبدي...")
    
    try:
        subprocess.run([
            'sc', 'start', 'EternalAccountingServer'
        ], check=True)
        print("✅ تم بدء الخدمة")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بدء الخدمة: {e}")
        return False

def create_task_scheduler():
    """إنشاء مهمة في Task Scheduler كبديل للخدمة"""
    print("📅 إنشاء مهمة في Task Scheduler...")
    
    task_xml = f"""<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>2024-01-01T00:00:00</Date>
    <Author>Eternal Server</Author>
    <Description>خادم المحاسبة الأبدي</Description>
  </RegistrationInfo>
  <Triggers>
    <BootTrigger>
      <Enabled>true</Enabled>
    </BootTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <UserId>S-1-5-18</UserId>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>false</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
    <RestartOnFailure>
      <Interval>PT1M</Interval>
      <Count>999</Count>
    </RestartOnFailure>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{sys.executable}</Command>
      <Arguments>eternal_server.py</Arguments>
      <WorkingDirectory>{os.getcwd()}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>"""
    
    # حفظ ملف XML
    with open('eternal_task.xml', 'w', encoding='utf-16') as f:
        f.write(task_xml)
    
    try:
        # إنشاء المهمة
        subprocess.run([
            'schtasks', '/create', '/tn', 'EternalAccountingServer',
            '/xml', 'eternal_task.xml', '/f'
        ], check=True)
        print("✅ تم إنشاء مهمة Task Scheduler")
        
        # تشغيل المهمة
        subprocess.run([
            'schtasks', '/run', '/tn', 'EternalAccountingServer'
        ], check=True)
        print("✅ تم تشغيل المهمة")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء المهمة: {e}")
        return False

def create_startup_script():
    """إنشاء سكريبت بدء التشغيل"""
    print("📝 إنشاء سكريپت بدء التشغيل...")
    
    startup_script = f"""@echo off
cd /d "{os.getcwd()}"
python eternal_server.py
"""
    
    # حفظ السكريپت
    with open('eternal_startup.bat', 'w') as f:
        f.write(startup_script)
    
    # إضافة إلى مجلد Startup
    import winreg
    try:
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                            r"Software\Microsoft\Windows\CurrentVersion\Run", 
                            0, winreg.KEY_SET_VALUE)
        winreg.SetValueEx(key, "EternalAccountingServer", 0, winreg.REG_SZ, 
                         os.path.join(os.getcwd(), "eternal_startup.bat"))
        winreg.CloseKey(key)
        print("✅ تم إضافة السكريپت لبدء التشغيل")
        return True
    except Exception as e:
        print(f"❌ فشل في إضافة السكريپت لبدء التشغيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 تثبيت خدمة الخادم الأبدي")
    print("=" * 60)
    
    print("\nاختر طريقة التثبيت:")
    print("1. خدمة Windows (موصى به)")
    print("2. مهمة Task Scheduler")
    print("3. سكريپت بدء التشغيل")
    print("4. تثبيت الكل")
    print("0. خروج")
    
    choice = input("\nاختر رقم الخيار: ")
    
    if choice == "1":
        if install_service_requirements():
            create_service_script()
            if install_service():
                start_service()
                print("\n✅ تم تثبيت وتشغيل خدمة Windows بنجاح!")
            
    elif choice == "2":
        if create_task_scheduler():
            print("\n✅ تم إنشاء مهمة Task Scheduler بنجاح!")
            
    elif choice == "3":
        if create_startup_script():
            print("\n✅ تم إنشاء سكريپت بدء التشغيل بنجاح!")
            
    elif choice == "4":
        print("\n🔧 تثبيت جميع الطرق...")
        
        # خدمة Windows
        if install_service_requirements():
            create_service_script()
            install_service()
            start_service()
        
        # Task Scheduler
        create_task_scheduler()
        
        # Startup Script
        create_startup_script()
        
        print("\n✅ تم تثبيت جميع الطرق!")
        
    elif choice == "0":
        print("👋 تم الإلغاء")
        return
    else:
        print("❌ خيار غير صحيح")
        return
    
    print("\n" + "=" * 60)
    print("🎉 اكتمل التثبيت!")
    print("الخادم الأبدي سيعمل الآن تلقائياً عند بدء التشغيل")
    print("=" * 60)

if __name__ == "__main__":
    main()
