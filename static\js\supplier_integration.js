/**
 * تكامل الموردين مع فواتير المشتريات
 * Supplier Integration with Purchase Invoices
 */

$(document).ready(function() {
    // تحديث بيانات المورد عند تغيير الاختيار
    $('#id_supplier').on('change', function() {
        const supplierId = $(this).val();
        if (supplierId) {
            loadSupplierData(supplierId);
        } else {
            clearSupplierData();
        }
    });

    // تحميل بيانات المورد
    function loadSupplierData(supplierId) {
        $.ajax({
            url: `/api/suppliers/${supplierId}/`,
            method: 'GET',
            success: function(data) {
                updateSupplierFields(data);
                showSupplierInfo(data);
            },
            error: function(xhr, status, error) {
                console.error('خطأ في تحميل بيانات المورد:', error);
                showAlert('خطأ في تحميل بيانات المورد', 'error');
            }
        });
    }

    // تحديث حقول المورد
    function updateSupplierFields(supplier) {
        // تحديث معلومات المورد في النموذج
        if ($('#supplier_name').length) {
            $('#supplier_name').text(supplier.name);
        }
        
        if ($('#supplier_phone').length) {
            $('#supplier_phone').text(supplier.phone || 'غير محدد');
        }
        
        if ($('#supplier_email').length) {
            $('#supplier_email').text(supplier.email || 'غير محدد');
        }
        
        if ($('#supplier_address').length) {
            $('#supplier_address').text(supplier.address || 'غير محدد');
        }

        // تحديث شروط الدفع إذا كانت متوفرة
        if (supplier.payment_terms && $('#id_payment_terms').length) {
            $('#id_payment_terms').val(supplier.payment_terms);
        }

        // تحديث العملة المفضلة إذا كانت متوفرة
        if (supplier.preferred_currency && $('#id_currency').length) {
            $('#id_currency').val(supplier.preferred_currency);
        }

        // تحديث طريقة الدفع المفضلة
        if (supplier.preferred_payment_method && $('#id_payment_method').length) {
            $('#id_payment_method').val(supplier.preferred_payment_method);
        }
    }

    // عرض معلومات المورد
    function showSupplierInfo(supplier) {
        const supplierInfoHtml = `
            <div class="card mt-3" id="supplier-info-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        معلومات المورد
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> ${supplier.name}</p>
                            <p><strong>الهاتف:</strong> ${supplier.phone || 'غير محدد'}</p>
                            <p><strong>البريد الإلكتروني:</strong> ${supplier.email || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>العنوان:</strong> ${supplier.address || 'غير محدد'}</p>
                            <p><strong>الرصيد الحالي:</strong> ${supplier.current_balance || '0.00'} ج.م</p>
                            <p><strong>حد الائتمان:</strong> ${supplier.credit_limit || 'غير محدد'}</p>
                        </div>
                    </div>
                    ${supplier.notes ? `<div class="mt-2"><strong>ملاحظات:</strong> ${supplier.notes}</div>` : ''}
                </div>
            </div>
        `;

        // إزالة البطاقة السابقة إن وجدت
        $('#supplier-info-card').remove();
        
        // إضافة البطاقة الجديدة
        $('#id_supplier').closest('.form-group, .mb-3').after(supplierInfoHtml);
    }

    // مسح بيانات المورد
    function clearSupplierData() {
        $('#supplier-info-card').remove();
        
        // مسح الحقول المحدثة تلقائياً
        if ($('#supplier_name').length) {
            $('#supplier_name').text('');
        }
        
        if ($('#supplier_phone').length) {
            $('#supplier_phone').text('');
        }
        
        if ($('#supplier_email').length) {
            $('#supplier_email').text('');
        }
        
        if ($('#supplier_address').length) {
            $('#supplier_address').text('');
        }
    }

    // تحسين البحث في قائمة الموردين
    if ($('#id_supplier').length && $('#id_supplier').hasClass('select2')) {
        $('#id_supplier').select2({
            placeholder: 'اختر المورد...',
            allowClear: true,
            language: {
                noResults: function() {
                    return 'لا توجد نتائج';
                },
                searching: function() {
                    return 'جاري البحث...';
                }
            },
            templateResult: function(supplier) {
                if (supplier.loading) {
                    return supplier.text;
                }

                // تنسيق عرض المورد في القائمة
                const $result = $(
                    `<div class="supplier-option">
                        <div class="supplier-name">${supplier.text}</div>
                        <div class="supplier-details text-muted small">
                            ${supplier.phone ? `هاتف: ${supplier.phone}` : ''}
                            ${supplier.email ? ` | بريد: ${supplier.email}` : ''}
                        </div>
                    </div>`
                );

                return $result;
            }
        });
    }

    // إضافة مورد جديد من النموذج
    $('#add-new-supplier-btn').on('click', function() {
        openSupplierModal();
    });

    // فتح نافذة إضافة مورد جديد
    function openSupplierModal() {
        const modalHtml = `
            <div class="modal fade" id="addSupplierModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مورد جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addSupplierForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المورد *</label>
                                            <input type="text" class="form-control" name="name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" name="phone">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" name="email">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">العنوان</label>
                                            <textarea class="form-control" name="address" rows="3"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" name="credit_limit" step="0.01">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveSupplierBtn">حفظ المورد</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        $('#addSupplierModal').remove();
        
        // إضافة النافذة الجديدة
        $('body').append(modalHtml);
        
        // عرض النافذة
        $('#addSupplierModal').modal('show');

        // معالج حفظ المورد الجديد
        $('#saveSupplierBtn').on('click', function() {
            saveNewSupplier();
        });
    }

    // حفظ مورد جديد
    function saveNewSupplier() {
        const formData = new FormData($('#addSupplierForm')[0]);
        
        // إضافة نوع الشخص كمورد
        formData.append('person_type', 'SUPPLIER');
        formData.append('is_active_supplier', 'true');

        $.ajax({
            url: '/api/suppliers/create/',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                // إضافة المورد الجديد للقائمة
                const newOption = new Option(data.name, data.id, true, true);
                $('#id_supplier').append(newOption).trigger('change');
                
                // إغلاق النافذة
                $('#addSupplierModal').modal('hide');
                
                // عرض رسالة نجاح
                showAlert('تم إضافة المورد بنجاح', 'success');
            },
            error: function(xhr, status, error) {
                console.error('خطأ في حفظ المورد:', error);
                showAlert('خطأ في حفظ المورد', 'error');
            }
        });
    }

    // عرض رسائل التنبيه
    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى النموذج
        $('.container, .card-body').first().prepend(alertHtml);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});

// دوال مساعدة للتكامل مع الموردين
window.SupplierIntegration = {
    // تحديث قائمة الموردين
    refreshSupplierList: function() {
        $.ajax({
            url: '/api/suppliers/',
            method: 'GET',
            success: function(data) {
                const $supplierSelect = $('#id_supplier');
                $supplierSelect.empty();
                $supplierSelect.append('<option value="">اختر المورد...</option>');
                
                data.forEach(function(supplier) {
                    $supplierSelect.append(
                        `<option value="${supplier.id}">${supplier.name}</option>`
                    );
                });
                
                // تحديث Select2 إذا كان مفعلاً
                if ($supplierSelect.hasClass('select2-hidden-accessible')) {
                    $supplierSelect.trigger('change');
                }
            }
        });
    },

    // الحصول على بيانات مورد محدد
    getSupplierData: function(supplierId, callback) {
        $.ajax({
            url: `/api/suppliers/${supplierId}/`,
            method: 'GET',
            success: callback,
            error: function(xhr, status, error) {
                console.error('خطأ في تحميل بيانات المورد:', error);
            }
        });
    }
};
