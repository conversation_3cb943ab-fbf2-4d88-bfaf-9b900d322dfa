{% load i18n %}

{% if app_list %}
  {% for app in app_list %}
    <div class="app-{{ app.app_label }} module{% if app.app_url in request.path|urlencode %} current-app{% endif %}">
      <table>
        <caption>
          <a href="{{ app.app_url }}" class="section" title="{% blocktranslate with name=app.name %}Models in the {{ name }} application{% endblocktranslate %}">{{ app.name }}</a>
        </caption>
        <thead class="visually-hidden">
          <tr>
            <th scope="col">{% translate 'Model name' %}</th>
            <th scope="col">{% translate 'Add link' %}</th>
            <th scope="col">{% translate 'Change or view list link' %}</th>
          </tr>
        </thead>
        {% for model in app.models %}
          {% with model_name=model.object_name|lower %}
            <tr class="model-{{ model_name }}{% if model.admin_url in request.path|urlencode %} current-model{% endif %}">
              <th scope="row" id="{{ app.app_label }}-{{ model_name }}">
                {% if model.admin_url %}
                  <a href="{{ model.admin_url }}"{% if model.admin_url in request.path|urlencode %} aria-current="page"{% endif %}>{{ model.name }}</a>
                {% else %}
                  {{ model.name }}
                {% endif %}
              </th>

              {% if model.add_url %}
                <td><a href="{{ model.add_url }}" class="addlink" aria-describedby="{{ app.app_label }}-{{ model_name }}">{% translate 'Add' %}</a></td>
              {% else %}
                <td></td>
              {% endif %}

              {% if model.admin_url and show_changelinks %}
                {% if model.view_only %}
                  <td><a href="{{ model.admin_url }}" class="viewlink" aria-describedby="{{ app.app_label }}-{{ model_name }}">{% translate 'View' %}</a></td>
                {% else %}
                  <td><a href="{{ model.admin_url }}" class="changelink" aria-describedby="{{ app.app_label }}-{{ model_name }}">{% translate 'Change' %}</a></td>
                {% endif %}
              {% elif show_changelinks %}
                <td></td>
              {% endif %}
            </tr>
          {% endwith %}
        {% endfor %}
      </table>
    </div>
  {% endfor %}
{% else %}
  <p>{% translate 'You don’t have permission to view or edit anything.' %}</p>
{% endif %}
