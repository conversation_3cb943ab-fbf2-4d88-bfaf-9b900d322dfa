#!/usr/bin/env python3
"""
تحميل ngrok للحصول على HTTPS معتمد مجاني
Download ngrok for free certified HTTPS
"""

import os
import sys
import urllib.request
import zipfile
from pathlib import Path

def download_ngrok():
    """تحميل ngrok"""
    print("📥 تحميل ngrok...")
    
    # إنشاء مجلد ngrok
    ngrok_dir = Path('ngrok')
    ngrok_dir.mkdir(exist_ok=True)
    
    # رابط تحميل ngrok
    url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
    zip_path = ngrok_dir / "ngrok.zip"
    
    try:
        print("🔄 جاري التحميل من ngrok.com...")
        urllib.request.urlretrieve(url, zip_path)
        print("✅ تم تحميل ngrok")
        
        # فك الضغط
        print("📂 فك ضغط الملف...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ngrok_dir)
        
        # حذف ملف الzip
        zip_path.unlink()
        
        # التحقق من وجود ngrok.exe
        ngrok_exe = ngrok_dir / "ngrok.exe"
        if ngrok_exe.exists():
            print("✅ تم تثبيت ngrok بنجاح!")
            return str(ngrok_exe)
        else:
            print("❌ لم يتم العثور على ngrok.exe")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في التحميل: {e}")
        return None

def create_ngrok_scripts(ngrok_exe):
    """إنشاء سكريپتات ngrok"""
    
    # سكريپت بدء ngrok
    start_script = f"""@echo off
title ngrok - HTTPS معتمد مجاني
color 0A
echo ============================================================
echo 🔒 ngrok - شهادة HTTPS معتمدة مجانية
echo Free Certified HTTPS Certificate
echo ============================================================
echo.
echo 🚀 بدء tunnel HTTPS...
echo Starting HTTPS tunnel...
echo.
echo ⏳ انتظر قليلاً حتى يبدأ ngrok...
echo Wait a moment for ngrok to start...
echo.

"{ngrok_exe}" http 8000 --region us

pause
"""
    
    with open('start_ngrok.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # سكريپت الحصول على الرابط
    get_url_script = f"""@echo off
title الحصول على رابط HTTPS
echo ============================================================
echo 🔗 الحصول على رابط HTTPS المعتمد
echo Getting Certified HTTPS URL
echo ============================================================
echo.

echo 🌐 افتح المتصفح واذهب إلى:
echo Open browser and go to:
echo.
echo    http://127.0.0.1:4040
echo.
echo 📋 انسخ الرابط الذي يبدأ بـ https://
echo Copy the URL that starts with https://
echo.
echo 💡 هذا الرابط معتمد ولن تظهر تحذيرات أمان
echo This URL is certified and no security warnings will appear
echo.

start http://127.0.0.1:4040

pause
"""
    
    with open('get_ngrok_url.bat', 'w', encoding='utf-8') as f:
        f.write(get_url_script)
    
    print("✅ تم إنشاء سكريپتات التشغيل")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔒 تحميل ngrok للحصول على HTTPS معتمد مجاني")
    print("=" * 60)
    
    # تحميل ngrok
    ngrok_exe = download_ngrok()
    
    if ngrok_exe:
        # إنشاء السكريپتات
        create_ngrok_scripts(ngrok_exe)
        
        print("\n" + "=" * 60)
        print("🎉 تم تحميل ngrok بنجاح!")
        print("=" * 60)
        
        print("\n📋 الخطوات التالية:")
        print("1. تأكد أن الخادم الأبدي يعمل:")
        print("   python eternal_server.py")
        print()
        print("2. شغل ngrok:")
        print("   start_ngrok.bat")
        print()
        print("3. احصل على الرابط:")
        print("   get_ngrok_url.bat")
        print()
        
        print("🔒 المميزات:")
        print("✅ HTTPS معتمد مجاني")
        print("✅ لا تحذيرات أمان")
        print("✅ يعمل من أي مكان")
        print("✅ شهادة صحيحة 100%")
        
        return True
    else:
        print("\n❌ فشل في تحميل ngrok")
        print("\n🔧 حل بديل:")
        print("1. اذهب إلى https://ngrok.com/download")
        print("2. حمل ngrok لـ Windows")
        print("3. فك الضغط في مجلد ngrok")
        print("4. شغل: ngrok http 8000")
        
        return False

if __name__ == "__main__":
    main()
