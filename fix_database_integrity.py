#!/usr/bin/env python
"""
Fix database integrity issues after deleting items
"""

import os
import sys
import django
import sqlite3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def fix_database_integrity():
    """Fix foreign key integrity issues"""
    
    print("Fixing database integrity issues...")
    
    # Get database path
    db_path = settings.DATABASES['default']['NAME']
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Disable foreign key checks temporarily
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        print("1. Checking purchase invoice items...")
        # Find and fix purchase invoice items with invalid item references
        cursor.execute("""
            DELETE FROM purchases_purchaseinvoiceitem 
            WHERE item_id NOT IN (SELECT id FROM definitions_item)
        """)
        deleted_purchase_items = cursor.rowcount
        print(f"   Deleted {deleted_purchase_items} invalid purchase invoice items")
        
        print("2. Checking sales invoice items...")
        # Find and fix sales invoice items with invalid item references
        cursor.execute("""
            DELETE FROM sales_salesinvoiceitem 
            WHERE item_id NOT IN (SELECT id FROM definitions_item)
        """)
        deleted_sales_items = cursor.rowcount
        print(f"   Deleted {deleted_sales_items} invalid sales invoice items")
        
        print("3. Checking stock movements...")
        # Find and fix stock movement items with invalid item references
        cursor.execute("""
            DELETE FROM inventory_stockmovementitem 
            WHERE item_id NOT IN (SELECT id FROM definitions_item)
        """)
        deleted_stock_items = cursor.rowcount
        print(f"   Deleted {deleted_stock_items} invalid stock movement items")
        
        print("4. Checking stock records...")
        # Find and fix stock records with invalid item references
        cursor.execute("""
            DELETE FROM inventory_stock 
            WHERE item_id NOT IN (SELECT id FROM definitions_item)
        """)
        deleted_stock_records = cursor.rowcount
        print(f"   Deleted {deleted_stock_records} invalid stock records")
        
        print("5. Checking manufacturing order materials...")
        # Find and fix manufacturing order materials with invalid item references
        cursor.execute("""
            DELETE FROM inventory_manufacturingordermaterial 
            WHERE material_id NOT IN (SELECT id FROM definitions_item)
        """)
        deleted_manufacturing_materials = cursor.rowcount
        print(f"   Deleted {deleted_manufacturing_materials} invalid manufacturing materials")
        
        # Re-enable foreign key checks
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Commit changes
        conn.commit()
        
        total_deleted = (deleted_purchase_items + deleted_sales_items + 
                        deleted_stock_items + deleted_stock_records + 
                        deleted_manufacturing_materials)
        
        print(f"\nDatabase integrity fixed!")
        print(f"Total invalid records removed: {total_deleted}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == '__main__':
    fix_database_integrity()