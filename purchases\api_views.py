"""
API Views للموردين والمشتريات
API Views for Suppliers and Purchases
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.core.serializers import serialize
from django.forms.models import model_to_dict
from django.db import models
import json

from definitions.models import Person
from .models import PurchaseInvoice, PurchaseOrder, PurchaseReturn


@login_required
@require_http_methods(["GET"])
def suppliers_list_api(request):
    """API لجلب قائمة الموردين"""
    try:
        # جلب الموردين من تعريفات الأشخاص والجهات
        suppliers = Person.objects.filter(
            person_type__in=['SUPPLIER', 'BOTH'],
            is_active_supplier=True,
            is_active=True
        ).order_by('name')

        suppliers_data = []
        for supplier in suppliers:
            supplier_data = {
                'id': supplier.id,
                'name': supplier.name,
                'phone': supplier.phone,
                'email': supplier.email,
                'address': supplier.address,
                'current_balance': float(supplier.current_balance) if supplier.current_balance else 0.0,
                'credit_limit': float(supplier.credit_limit) if supplier.credit_limit else None,
                'payment_terms': supplier.payment_terms,
                'preferred_currency': supplier.preferred_currency_id if supplier.preferred_currency else None,
                'preferred_payment_method': supplier.preferred_payment_method,
                'notes': supplier.notes,
                'is_active': supplier.is_active,
            }
            suppliers_data.append(supplier_data)

        return JsonResponse({
            'success': True,
            'data': suppliers_data,
            'count': len(suppliers_data)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def supplier_detail_api(request, supplier_id):
    """API لجلب تفاصيل مورد محدد"""
    try:
        supplier = get_object_or_404(
            Person,
            id=supplier_id,
            person_type__in=['SUPPLIER', 'BOTH'],
            is_active_supplier=True
        )

        # حساب إحصائيات المورد
        total_purchases = PurchaseInvoice.objects.filter(supplier=supplier).count()
        total_amount = sum(
            invoice.total_amount for invoice in 
            PurchaseInvoice.objects.filter(supplier=supplier, status='COMPLETED')
        )
        
        pending_orders = PurchaseOrder.objects.filter(
            supplier=supplier, 
            status__in=['PENDING', 'CONFIRMED']
        ).count()

        supplier_data = {
            'id': supplier.id,
            'name': supplier.name,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'current_balance': float(supplier.current_balance) if supplier.current_balance else 0.0,
            'credit_limit': float(supplier.credit_limit) if supplier.credit_limit else None,
            'payment_terms': supplier.payment_terms,
            'preferred_currency': supplier.preferred_currency_id if supplier.preferred_currency else None,
            'preferred_payment_method': supplier.preferred_payment_method,
            'notes': supplier.notes,
            'is_active': supplier.is_active,
            'created_at': supplier.created_at.isoformat() if supplier.created_at else None,
            
            # إحصائيات
            'statistics': {
                'total_purchases': total_purchases,
                'total_amount': float(total_amount),
                'pending_orders': pending_orders,
            }
        }

        return JsonResponse({
            'success': True,
            'data': supplier_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def supplier_create_api(request):
    """API لإنشاء مورد جديد"""
    try:
        data = json.loads(request.body) if request.content_type == 'application/json' else request.POST

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return JsonResponse({
                'success': False,
                'error': 'اسم المورد مطلوب'
            }, status=400)

        # التحقق من عدم تكرار الاسم
        if Person.objects.filter(name=data['name']).exists():
            return JsonResponse({
                'success': False,
                'error': 'يوجد مورد بنفس الاسم'
            }, status=400)

        # إنشاء المورد الجديد
        supplier = Person.objects.create(
            name=data['name'],
            phone=data.get('phone', ''),
            email=data.get('email', ''),
            address=data.get('address', ''),
            credit_limit=data.get('credit_limit') if data.get('credit_limit') else None,
            payment_terms=data.get('payment_terms', ''),
            notes=data.get('notes', ''),
            person_type='SUPPLIER',
            is_active_supplier=True,
            is_active=True,
            created_by=request.user
        )

        supplier_data = {
            'id': supplier.id,
            'name': supplier.name,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'current_balance': float(supplier.current_balance) if supplier.current_balance else 0.0,
            'credit_limit': float(supplier.credit_limit) if supplier.credit_limit else None,
            'payment_terms': supplier.payment_terms,
            'notes': supplier.notes,
        }

        return JsonResponse({
            'success': True,
            'message': 'تم إنشاء المورد بنجاح',
            'data': supplier_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def supplier_purchases_api(request, supplier_id):
    """API لجلب مشتريات مورد محدد"""
    try:
        supplier = get_object_or_404(
            Person,
            id=supplier_id,
            person_type__in=['SUPPLIER', 'BOTH'],
            is_active_supplier=True
        )

        # جلب فواتير المشتريات
        invoices = PurchaseInvoice.objects.filter(supplier=supplier).order_by('-date')
        
        invoices_data = []
        for invoice in invoices:
            invoice_data = {
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'date': invoice.date.isoformat(),
                'total_amount': float(invoice.total_amount),
                'status': invoice.status,
                'status_display': invoice.get_status_display(),
                'warehouse': invoice.warehouse.name if invoice.warehouse else None,
                'notes': invoice.notes,
            }
            invoices_data.append(invoice_data)

        # جلب أوامر الشراء
        orders = PurchaseOrder.objects.filter(supplier=supplier).order_by('-date')
        
        orders_data = []
        for order in orders:
            order_data = {
                'id': order.id,
                'order_number': order.order_number,
                'date': order.date.isoformat(),
                'total_amount': float(order.total_amount),
                'status': order.status,
                'status_display': order.get_status_display(),
                'expected_delivery_date': order.expected_delivery_date.isoformat() if order.expected_delivery_date else None,
                'notes': order.notes,
            }
            orders_data.append(order_data)

        return JsonResponse({
            'success': True,
            'data': {
                'supplier': {
                    'id': supplier.id,
                    'name': supplier.name,
                },
                'invoices': invoices_data,
                'orders': orders_data,
                'summary': {
                    'total_invoices': len(invoices_data),
                    'total_orders': len(orders_data),
                    'total_amount': sum(float(inv['total_amount']) for inv in invoices_data),
                }
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def supplier_search_api(request):
    """API للبحث في الموردين"""
    try:
        query = request.GET.get('q', '').strip()
        
        if not query:
            return JsonResponse({
                'success': False,
                'error': 'يرجى إدخال نص للبحث'
            }, status=400)

        # البحث في الموردين
        suppliers = Person.objects.filter(
            person_type__in=['SUPPLIER', 'BOTH'],
            is_active_supplier=True,
            is_active=True
        ).filter(
            models.Q(name__icontains=query) |
            models.Q(phone__icontains=query) |
            models.Q(email__icontains=query)
        ).order_by('name')[:20]  # حد أقصى 20 نتيجة

        suppliers_data = []
        for supplier in suppliers:
            supplier_data = {
                'id': supplier.id,
                'name': supplier.name,
                'phone': supplier.phone,
                'email': supplier.email,
                'current_balance': float(supplier.current_balance) if supplier.current_balance else 0.0,
            }
            suppliers_data.append(supplier_data)

        return JsonResponse({
            'success': True,
            'data': suppliers_data,
            'count': len(suppliers_data),
            'query': query
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def supplier_balance_api(request, supplier_id):
    """API لجلب رصيد مورد محدد"""
    try:
        supplier = get_object_or_404(
            Person,
            id=supplier_id,
            person_type__in=['SUPPLIER', 'BOTH'],
            is_active_supplier=True
        )

        # حساب الرصيد من الفواتير
        total_invoices = sum(
            invoice.total_amount for invoice in 
            PurchaseInvoice.objects.filter(supplier=supplier, status='COMPLETED')
        )
        
        # حساب المدفوعات (يمكن إضافة نموذج المدفوعات لاحقاً)
        total_payments = 0  # سيتم تحديثه عند إضافة نموذج المدفوعات

        current_balance = total_invoices - total_payments

        balance_data = {
            'supplier_id': supplier.id,
            'supplier_name': supplier.name,
            'total_invoices': float(total_invoices),
            'total_payments': float(total_payments),
            'current_balance': float(current_balance),
            'credit_limit': float(supplier.credit_limit) if supplier.credit_limit else None,
            'available_credit': float(supplier.credit_limit - current_balance) if supplier.credit_limit else None,
        }

        return JsonResponse({
            'success': True,
            'data': balance_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
