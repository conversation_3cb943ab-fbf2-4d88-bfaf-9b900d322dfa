# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> Guerra <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-09-25 17:13+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (http://www.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipos de contenido"

msgid "python model class name"
msgstr "nombre de la clase modelo de python"

msgid "content type"
msgstr "tipo de contenido"

msgid "content types"
msgstr "tipos de contenido"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""
"El objeto de tipo de contenido %(ct_id)s  no tiene ningún modelo asociado."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "El tipo de contenido %(ct_id)s del objeto %(obj_id)s no existe"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s objetos no tienen un método get_absolute_url()"
