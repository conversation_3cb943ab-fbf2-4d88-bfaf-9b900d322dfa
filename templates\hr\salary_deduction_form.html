{% extends 'base/base.html' %}
{% block title %}تعديل الخصم{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل بيانات الخصم</h4>
                </div>
                <form method="post" novalidate>
                    {% csrf_token %}
                    <div class="card-body">
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">{{ form.non_field_errors }}</div>
                        {% endif %}
                        <div class="mb-3">
                            {{ form.employee.label_tag }}
                            {{ form.employee }}
                            {% if form.employee.errors %}<div class="text-danger small">{{ form.employee.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.name.label_tag }}
                            {{ form.name }}
                            {% if form.name.errors %}<div class="text-danger small">{{ form.name.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.amount.label_tag }}
                            {{ form.amount }}
                            {% if form.amount.errors %}<div class="text-danger small">{{ form.amount.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.date.label_tag }}
                            {{ form.date }}
                            {% if form.date.errors %}<div class="text-danger small">{{ form.date.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.is_active }} {{ form.is_active.label_tag }}
                            {% if form.is_active.errors %}<div class="text-danger small">{{ form.is_active.errors }}</div>{% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.notes.label_tag }}
                            {{ form.notes }}
                            {% if form.notes.errors %}<div class="text-danger small">{{ form.notes.errors }}</div>{% endif %}
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <a href="{% url 'hr:salary_deduction_list' %}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>رجوع</a>
                        <button type="submit" class="btn btn-warning text-dark"><i class="fas fa-save me-2"></i>حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 