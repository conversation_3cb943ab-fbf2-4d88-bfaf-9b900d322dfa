{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">مراجعة واعتماد طلبات الإجازات</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-success" onclick="approveAll()">
                        <i class="fas fa-check-double me-2"></i>
                        اعتماد الكل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ pending_requests|length }}</h3>
                    <p class="mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ approved_count }}</h3>
                    <p class="mb-0">معتمد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ rejected_count }}</h3>
                    <p class="mb-0">مرفوض</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ all_requests|length }}</h3>
                    <p class="mb-0">إجمالي الطلبات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الطلبات قيد المراجعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_requests %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ request.employee.full_name }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ request.leave_type.name }}</span>
                                    </td>
                                    <td>{{ request.from_date|date:"Y-m-d" }}</td>
                                    <td>{{ request.to_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ request.duration_display }}</span>
                                    </td>
                                    <td>{{ request.reason|default_if_none:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <form method="post" action="{% url 'hr:leave_request_approve' request.id %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-success" title="اعتماد">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <form method="post" action="{% url 'hr:leave_request_reject' request.id %}" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-danger" title="رفض">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                            <a href="{% url 'hr:leave_request_detail' request.id %}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-check-circle fa-3x mb-3 d-block text-success"></i>
                                        لا توجد طلبات قيد المراجعة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- All Requests History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        تاريخ جميع الطلبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>التاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الاعتماد</th>
                                    <th>المعتمد من</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in all_requests %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ request.employee.full_name }}</td>
                                    <td><span class="badge bg-info">{{ request.leave_type.name }}</span></td>
                                    <td>{{ request.from_date|date:"Y-m-d" }} إلى {{ request.to_date|date:"Y-m-d" }}</td>
                                    <td><span class="badge bg-primary">{{ request.duration_display }}</span></td>
                                    <td><span class="badge {{ request.status_badge_class }}">{{ request.get_status_display }}</span></td>
                                    <td>{% if request.approved_at %}{{ request.approved_at|date:"Y-m-d" }}{% else %}-{% endif %}</td>
                                    <td>{% if request.approved_by %}{{ request.approved_by.get_full_name|default:request.approved_by.username }}{% else %}-{% endif %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-history fa-3x mb-3 d-block text-info"></i>
                                        لا يوجد تاريخ طلبات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function approveRequest(id) {
    if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
        alert('تم اعتماد الطلب بنجاح');
        // Add approval logic here
    }
}

function rejectRequest(id) {
    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
        alert('تم رفض الطلب');
        // Add rejection logic here
    }
}

function approveAll() {
    if (confirm('هل أنت متأكد من اعتماد جميع الطلبات قيد المراجعة؟')) {
        alert('تم اعتماد جميع الطلبات بنجاح');
        // Add bulk approval logic here
    }
}
</script>
{% endblock %}
