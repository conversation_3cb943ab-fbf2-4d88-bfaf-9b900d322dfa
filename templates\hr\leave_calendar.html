{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-calendar text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">عرض الإجازات في تقويم شهري</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقويم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a class="btn btn-outline-primary" href="?month={{ prev_month }}&year={{ prev_year }}">
                            <i class="fas fa-chevron-left me-2"></i>
                            الشهر السابق
                        </a>
                        <h4 class="mb-0">{{ month_name }} {{ current_year }}</h4>
                        <div>
                            <a class="btn btn-outline-primary me-2" href="?month={{ next_month }}&year={{ next_year }}">
                                الشهر التالي
                                <i class="fas fa-chevron-right ms-2"></i>
                            </a>
                            <a class="btn btn-outline-success" href="?month={{ today_month }}&year={{ today_year }}">
                                الشهر الحالي
                                <i class="fas fa-calendar-day ms-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Legend -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <span class="badge bg-success me-2">●</span>
                            <small>إجازة سنوية</small>
                        </div>
                        <div class="col-md-3">
                            <span class="badge bg-info me-2">●</span>
                            <small>إجازة مرضية</small>
                        </div>
                        <div class="col-md-3">
                            <span class="badge bg-warning me-2">●</span>
                            <small>إجازة طارئة</small>
                        </div>
                        <div class="col-md-3">
                            <span class="badge bg-danger me-2">●</span>
                            <small>إجازة أمومة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered calendar-table">
                            <thead class="table-dark">
                                <tr>
                                    <th class="text-center">الأحد</th>
                                    <th class="text-center">الاثنين</th>
                                    <th class="text-center">الثلاثاء</th>
                                    <th class="text-center">الأربعاء</th>
                                    <th class="text-center">الخميس</th>
                                    <th class="text-center">الجمعة</th>
                                    <th class="text-center">السبت</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for week in calendar_weeks %}
                                <tr>
                                    {% for day in week %}
                                    <td class="calendar-day {% if day.is_weekend %}weekend{% endif %}">
                                        {% if day.in_month %}
                                            <div class="day-number">{{ day.day }}</div>
                                            <div class="day-events">
                                                {% for leave in day.leaves %}
                                                    <span class="badge {{ leave.badge_class }} mb-1">{{ leave.employee_name }}</span>
                                                {% empty %}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    font-size: 0.9rem;
}

.calendar-day {
    height: 120px;
    vertical-align: top;
    padding: 8px;
    position: relative;
}

.calendar-day.weekend {
    background-color: #f8f9fa;
}

.day-number {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.day-events {
    font-size: 0.75rem;
}

.day-events .badge {
    display: block;
    font-size: 0.7rem;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>

<script>
function previousMonth() {
    alert('سيتم عرض الشهر السابق');
    // Add previous month logic here
}

function nextMonth() {
    alert('سيتم عرض الشهر التالي');
    // Add next month logic here
}
</script>
{% endblock %}
