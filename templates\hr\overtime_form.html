{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-clock text-warning me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">{% if overtime %}تعديل سجل العمل الإضافي{% else %}إضافة سجل عمل إضافي جديد{% endif %}</p>
                </div>
                <div>
                    <a href="{% url 'hr:overtime_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-{% if overtime %}edit{% else %}plus{% endif %} me-2"></i>
                        بيانات العمل الإضافي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.employee.label }}</label>
                                    {{ form.employee }}
                                    {% if form.employee.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.employee.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.date.label }}</label>
                                    {{ form.date }}
                                    {% if form.date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.hours.label }}</label>
                                    {{ form.hours }}
                                    {% if form.hours.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.hours.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.hourly_rate.label }}</label>
                                    {{ form.hourly_rate }}
                                    {% if form.hourly_rate.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.hourly_rate.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ الإجمالي</label>
                                    <div class="form-control-plaintext" id="totalAmount">
                                        {% if overtime %}
                                            {{ overtime.total_amount|floatformat:2 }} ج.م
                                        {% else %}
                                            0.00 ج.م
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">{{ form.notes.label }}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'hr:overtime_list' %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if overtime %}تحديث{% else %}حفظ{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حساب المبلغ الإجمالي تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const hoursInput = document.querySelector('input[name="hours"]');
    const rateInput = document.querySelector('input[name="hourly_rate"]');
    const totalAmountDiv = document.getElementById('totalAmount');
    
    function calculateTotal() {
        const hours = parseFloat(hoursInput.value) || 0;
        const rate = parseFloat(rateInput.value) || 0;
        const total = hours * rate;
        
        // عرض المبلغ الإجمالي
        totalAmountDiv.textContent = total.toFixed(2) + ' ج.م';
    }
    
    hoursInput.addEventListener('input', calculateTotal);
    rateInput.addEventListener('input', calculateTotal);
    
    // حساب المبلغ عند تحميل الصفحة
    calculateTotal();
});
</script>
{% endblock %} 