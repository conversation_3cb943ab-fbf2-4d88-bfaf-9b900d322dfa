"""
أمر Django لتحديث العملة الافتراضية إلى الجنيه المصري في جميع أنحاء التطبيق
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from definitions.models import Currency


class Command(BaseCommand):
    help = 'تحديث العملة الافتراضية إلى الجنيه المصري في جميع أنحاء التطبيق'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='فرض التحديث حتى لو كان الجنيه المصري موجود بالفعل',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🇪🇬 بدء تحديث العملة إلى الجنيه المصري...')
        )

        try:
            with transaction.atomic():
                # التأكد من وجود الجنيه المصري
                egp_currency = self.ensure_egp_currency()
                
                # تحديث العملة الافتراضية
                self.update_default_currency(egp_currency)
                
                # تحديث أسعار الصرف
                self.update_exchange_rates(egp_currency)
                
                # عرض النتائج
                self.display_results(egp_currency)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ حدث خطأ أثناء التحديث: {str(e)}')
            )
            raise

    def ensure_egp_currency(self):
        """التأكد من وجود الجنيه المصري في النظام"""
        self.stdout.write('🔍 فحص وجود الجنيه المصري...')
        
        egp_currency, created = Currency.objects.get_or_create(
            code='EGP',
            defaults={
                'name': 'الجنيه المصري',
                'name_en': 'Egyptian Pound',
                'symbol': 'ج.م',
                'symbol_en': 'EGP',
                'exchange_rate': 1.0000,
                'is_base_currency': True,
                'is_active': True,
                'decimal_places': 2,
                'rounding_method': 'ROUND_HALF_UP',
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('✅ تم إنشاء الجنيه المصري بنجاح')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠️ الجنيه المصري موجود بالفعل')
            )
            
        return egp_currency

    def update_default_currency(self, egp_currency):
        """تحديث العملة الافتراضية"""
        self.stdout.write('🔄 تحديث العملة الافتراضية...')
        
        # إلغاء تعيين العملات الأخرى كعملة أساسية
        other_currencies = Currency.objects.exclude(id=egp_currency.id).filter(
            is_base_currency=True
        )
        
        updated_count = other_currencies.update(is_base_currency=False)
        
        if updated_count > 0:
            self.stdout.write(
                f'  ✓ تم إلغاء تعيين {updated_count} عملة كعملة أساسية'
            )
        
        # تعيين الجنيه المصري كعملة أساسية
        egp_currency.is_base_currency = True
        egp_currency.exchange_rate = 1.0000
        egp_currency.is_active = True
        egp_currency.save()
        
        self.stdout.write(
            self.style.SUCCESS('✅ تم تعيين الجنيه المصري كعملة أساسية')
        )

    def update_exchange_rates(self, egp_currency):
        """تحديث أسعار الصرف للعملات الأخرى"""
        self.stdout.write('💱 تحديث أسعار الصرف...')
        
        # أسعار الصرف الحالية (يونيو 2025)
        exchange_rates = {
            'USD': 49.77,   # الدولار الأمريكي
            'EUR': 53.45,   # اليورو
            'GBP': 63.20,   # الجنيه الإسترليني
            'SAR': 13.27,   # الريال السعودي
            'AED': 13.55,   # الدرهم الإماراتي
            'KWD': 162.49,  # الدينار الكويتي
            'QAR': 13.67,   # الريال القطري
            'OMR': 129.35,  # الريال العماني
            'BHD': 132.15,  # الدينار البحريني
            'JOD': 70.25,   # الدينار الأردني
        }
        
        updated_currencies = 0
        
        for currency_code, rate in exchange_rates.items():
            try:
                currency = Currency.objects.get(code=currency_code)
                currency.exchange_rate = rate
                currency.save()
                updated_currencies += 1
                
                self.stdout.write(
                    f'  ✓ {currency.name}: {rate} ج.م'
                )
                
            except Currency.DoesNotExist:
                # إنشاء العملة إذا لم تكن موجودة
                currency_names = {
                    'USD': ('الدولار الأمريكي', 'US Dollar', '$'),
                    'EUR': ('اليورو', 'Euro', '€'),
                    'GBP': ('الجنيه الإسترليني', 'British Pound', '£'),
                    'SAR': ('الريال السعودي', 'Saudi Riyal', 'ر.س'),
                    'AED': ('الدرهم الإماراتي', 'UAE Dirham', 'د.إ'),
                    'KWD': ('الدينار الكويتي', 'Kuwaiti Dinar', 'د.ك'),
                    'QAR': ('الريال القطري', 'Qatari Riyal', 'ر.ق'),
                    'OMR': ('الريال العماني', 'Omani Rial', 'ر.ع'),
                    'BHD': ('الدينار البحريني', 'Bahraini Dinar', 'د.ب'),
                    'JOD': ('الدينار الأردني', 'Jordanian Dinar', 'د.أ'),
                }
                
                if currency_code in currency_names:
                    name, name_en, symbol = currency_names[currency_code]
                    
                    Currency.objects.create(
                        code=currency_code,
                        name=name,
                        name_en=name_en,
                        symbol=symbol,
                        symbol_en=currency_code,
                        exchange_rate=rate,
                        is_base_currency=False,
                        is_active=True,
                        decimal_places=2,
                    )
                    
                    updated_currencies += 1
                    self.stdout.write(
                        f'  ✓ تم إنشاء {name}: {rate} ج.م'
                    )
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ تم تحديث {updated_currencies} عملة')
        )

    def display_results(self, egp_currency):
        """عرض نتائج التحديث"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🎉 تم تحديث العملة بنجاح!')
        )
        self.stdout.write('='*60)
        
        self.stdout.write(f'العملة الأساسية: {egp_currency.name} ({egp_currency.code})')
        self.stdout.write(f'الرمز: {egp_currency.symbol}')
        self.stdout.write(f'سعر الصرف: {egp_currency.exchange_rate}')
        
        # عرض العملات الأخرى
        other_currencies = Currency.objects.exclude(
            id=egp_currency.id
        ).filter(is_active=True).order_by('name')
        
        if other_currencies.exists():
            self.stdout.write('\nالعملات الأخرى:')
            for currency in other_currencies:
                self.stdout.write(
                    f'  • {currency.name}: {currency.exchange_rate} ج.م'
                )
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('✅ جميع المبالغ في التطبيق ستظهر الآن بالجنيه المصري!')
        )
        self.stdout.write('='*60)
