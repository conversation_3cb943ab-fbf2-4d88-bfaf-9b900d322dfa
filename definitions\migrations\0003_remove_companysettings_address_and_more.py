# Generated by Django 5.2.4 on 2025-07-12 04:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0002_person_is_active_employee'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='companysettings',
            name='address',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='app_name',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='app_version',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='auto_backup_enabled',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='backup_frequency_days',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='city',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='commercial_register',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='company_name_english',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='country',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='date_format',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='default_currency',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='default_report_format',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='email',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='email_notifications_enabled',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='items_per_page',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='logo',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='mobile',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='password_min_length',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='phone',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='postal_code',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='print_company_info',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='print_logo_on_reports',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='session_timeout_minutes',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='sms_notifications_enabled',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='state',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='tax_number',
        ),
        migrations.RemoveField(
            model_name='companysettings',
            name='website',
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_address',
            field=models.TextField(blank=True, null=True, verbose_name='عنوان الشركة'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد الشركة الإلكتروني'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_logo',
            field=models.ImageField(blank=True, help_text='يفضل أن يكون الشعار مربع الشكل (مثل 200x200 بكسل)', null=True, upload_to='company_logos/', verbose_name='شعار الشركة'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_phone',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='هاتف الشركة'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_subtitle',
            field=models.CharField(default='نظام إدارة الحسابات المتكامل والاحترافي', max_length=300, verbose_name='العنوان الفرعي'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='company_website',
            field=models.URLField(blank=True, null=True, verbose_name='موقع الشركة الإلكتروني'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='header_background_color',
            field=models.CharField(default='#667eea', help_text='كود اللون بصيغة HEX (مثل #667eea)', max_length=7, verbose_name='لون خلفية الشريط العلوي'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='show_company_name_in_header',
            field=models.BooleanField(default=True, verbose_name='إظهار اسم الشركة في الشريط العلوي'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='show_logo_in_header',
            field=models.BooleanField(default=True, verbose_name='إظهار الشعار في الشريط العلوي'),
        ),
        migrations.AddField(
            model_name='companysettings',
            name='show_subtitle_in_header',
            field=models.BooleanField(default=True, verbose_name='إظهار العنوان الفرعي في الشريط العلوي'),
        ),
        migrations.AlterField(
            model_name='companysettings',
            name='company_name',
            field=models.CharField(default='حسابات أوساريك', max_length=200, verbose_name='اسم الشركة'),
        ),
    ]
