#!/usr/bin/env python3
"""
حماية القائمة الجانبية الجديدة مع الشريط العلوي الثابت
"""

import os
import hashlib
import shutil
from datetime import datetime

def get_file_hash(file_path):
    """حساب hash للملف"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def protect_new_sidebar():
    """حماية القائمة الجانبية الجديدة"""
    base_file = "templates/base/base.html"
    
    if os.path.exists(base_file):
        print("🛡️ حماية القائمة الجانبية الجديدة...")
        
        # حساب hash الحالي
        current_hash = get_file_hash(base_file)
        print(f"📊 Hash الحالي: {current_hash}")
        
        # حفظ hash في ملف
        with open("sidebar_hash.txt", "w", encoding='utf-8') as f:
            f.write(f"{current_hash}\n")
            f.write(f"Protection Date: {datetime.now()}\n")
            f.write("New sidebar with fixed header protected\n")
        
        print("✅ تم حفظ hash القائمة الجانبية الجديدة")
        print("🔒 القائمة الجانبية الجديدة محمية الآن")
        
        return current_hash
    else:
        print("❌ ملف القائمة الجانبية غير موجود")
        return None

if __name__ == "__main__":
    print("🛡️ نظام حماية القائمة الجانبية الجديدة")
    print("="*50)
    
    # حماية القائمة الجديدة
    protect_new_sidebar()
    
    print("\n" + "="*50)
    print("✅ تم تطبيق الحماية على القائمة الجديدة بنجاح!")
