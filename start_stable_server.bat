@echo off
chcp 65001 >nul
REM ===================================================
REM Django Stable Server Launcher
REM ===================================================

title Django Stable Server - Osaric Accounts System

echo.
echo ========================================
echo    Django Stable Server
echo    Osaric Accounts System
echo ========================================
echo.

REM Change to project directory
cd /d "%~dp0"

REM Create required directories
if not exist "logs" mkdir logs
if not exist "static" mkdir static
if not exist "media" mkdir media
if not exist "backup" mkdir backup

echo ✅ Required directories created

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not installed or not in PATH
    echo Please install Python first
    pause
    exit /b 1
)

echo ✅ Python available

REM Install basic requirements
echo.
echo 📦 Installing requirements...
pip install django >nul 2>&1

REM Check Django
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django not installed properly
    echo Installing Django...
    pip install django
)

echo ✅ Django ready

echo.
echo 🎯 Server Options:
echo ==================
echo 1. Start Stable Server (Recommended)
echo 2. Start Normal Server
echo 3. Check System Status
echo 4. Run Database Migrations
echo 5. Create Superuser
echo 6. Collect Static Files
echo 0. Exit
echo.

set /p choice="Choose option (1-6): "

if "%choice%"=="1" goto stable_server
if "%choice%"=="2" goto normal_server
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto migrate
if "%choice%"=="5" goto create_superuser
if "%choice%"=="6" goto collect_static
if "%choice%"=="0" goto exit
goto invalid_choice

:stable_server
echo.
echo 🚀 Starting Stable Server...
echo =============================
echo.
echo 🔥 Stable Server Features:
echo • Never stops - automatic restart
echo • Continuous monitoring every 30 seconds
echo • Detailed logging of all events
echo • Protection from all types of errors
echo • Quick response to problems
echo.
echo 🌐 Website will be available at: http://127.0.0.1:8000/
echo ⌨️ Press Ctrl+C to stop server safely
echo.
pause
python stable_server_fixed.py
goto end

:normal_server
echo.
echo 🚀 Starting Normal Server...
python manage.py runserver 127.0.0.1:8000
goto end

:check_status
echo.
echo 🔍 Checking System Status...
python manage.py check
echo.
pause
goto start

:migrate
echo.
echo 🔄 Running Database Migrations...
python manage.py migrate
echo.
pause
goto start

:create_superuser
echo.
echo 👤 Creating Superuser...
python manage.py createsuperuser
echo.
pause
goto start

:collect_static
echo.
echo 📁 Collecting Static Files...
python manage.py collectstatic --noinput
echo.
pause
goto start

:invalid_choice
echo ❌ Invalid choice
pause
goto start

:exit
echo 👋 Thank you for using the system!
exit /b 0

:end
echo.
echo 🎉 Operation completed
pause

:start
cls
goto start