@echo off
REM ===================================================
REM سكريپت تشغيل الخادم الأبدي
REM Eternal Server Startup Script
REM ===================================================

title الخادم الأبدي - لا يتوقف أبداً - Eternal Server

echo.
echo ========================================
echo    🌟 الخادم الأبدي - لا يتوقف أبداً
echo    Eternal Server - Never Stops
echo ========================================
echo.

REM تحديد مجلد المشروع
cd /d "%~dp0"

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات
echo.
echo 📦 تثبيت المتطلبات...
pip install django psutil >nul 2>&1

echo ✅ المتطلبات جاهزة

REM فحص إذا كان الخادم الأبدي يعمل بالفعل
if exist "eternal_server.pid" (
    echo.
    echo ⚠️ يبدو أن الخادم الأبدي يعمل بالفعل
    echo فحص حالة الخادم...
    
    for /f %%i in (eternal_server.pid) do set PID=%%i
    tasklist /FI "PID eq %PID%" 2>nul | find /i "python" >nul
    if not errorlevel 1 (
        echo ✅ الخادم الأبدي يعمل بالفعل! PID: %PID%
        echo.
        echo 🌐 للوصول للموقع:
        echo    http://localhost:8000/
        echo    http://localhost:8001/
        echo    http://localhost:8002/
        echo.
        echo اضغط أي مفتاح للخروج...
        pause >nul
        exit /b 0
    ) else (
        echo ⚠️ ملف PID موجود لكن العملية غير نشطة
        del eternal_server.pid >nul 2>&1
        echo تم حذف ملف PID القديم
    )
)

echo.
echo 🎯 خيارات الخادم الأبدي:
echo ===========================
echo 1. تشغيل الخادم الأبدي (موصى به)
echo 2. تشغيل الخادم الأبدي في الخلفية
echo 3. إيقاف الخادم الأبدي
echo 4. عرض حالة الخادم
echo 5. عرض سجل الخادم الأبدي
echo 6. إعادة تشغيل الخادم الأبدي
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-6): "

if "%choice%"=="1" goto eternal_server
if "%choice%"=="2" goto eternal_background
if "%choice%"=="3" goto stop_eternal
if "%choice%"=="4" goto show_status
if "%choice%"=="5" goto show_log
if "%choice%"=="6" goto restart_eternal
if "%choice%"=="0" goto exit
goto invalid_choice

:eternal_server
echo.
echo 🌟 تشغيل الخادم الأبدي...
echo ===========================
echo.
echo 🔥 مميزات الخادم الأبدي:
echo • لا يتوقف أبداً تحت أي ظرف
echo • إعادة تشغيل تلقائية فورية
echo • مقاوم لجميع أنواع الأخطاء
echo • فحص صحة كل 10 ثوانٍ
echo • يجد منفذ متاح تلقائياً
echo • حماية من إشارات الإيقاف
echo • مراقبة مستمرة 24/7
echo.
echo 🌐 سيتم عرض معلومات الوصول بعد التشغيل
echo ⚠️ هذا الخادم لا يتوقف! استخدم الخيار 3 لإيقافه
echo.
pause
python eternal_server.py
goto end

:eternal_background
echo.
echo 🌟 تشغيل الخادم الأبدي في الخلفية...
echo =====================================
echo.
echo تشغيل الخادم في الخلفية...
start /B python eternal_server.py
echo.
echo ✅ تم تشغيل الخادم الأبدي في الخلفية
echo.
echo 🌐 للوصول للموقع:
echo    http://localhost:8000/
echo    http://localhost:8001/
echo    http://localhost:8002/
echo.
echo 📋 لإيقاف الخادم، استخدم الخيار 3 من القائمة
echo.
pause
goto start

:stop_eternal
echo.
echo 🛑 إيقاف الخادم الأبدي...
echo =========================
echo.
if exist "eternal_server.pid" (
    for /f %%i in (eternal_server.pid) do set PID=%%i
    echo محاولة إيقاف العملية PID: %PID%
    taskkill /F /PID %PID% >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ لم يتم العثور على العملية أو تم إيقافها بالفعل
    ) else (
        echo ✅ تم إيقاف الخادم الأبدي
    )
    del eternal_server.pid >nul 2>&1
) else (
    echo ⚠️ لا يوجد ملف PID - الخادم غير نشط
)

REM البحث عن عمليات Python أخرى قد تكون خوادم
echo.
echo البحث عن خوادم Django أخرى...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| find "python.exe"') do (
    set PID=%%i
    set PID=!PID:"=!
    echo محاولة إيقاف العملية PID: !PID!
    taskkill /F /PID !PID! >nul 2>&1
)

echo.
echo ✅ تم إيقاف جميع خوادم Django
pause
goto start

:show_status
echo.
echo 📊 حالة الخادم الأبدي:
echo ====================
if exist "eternal_server.pid" (
    for /f %%i in (eternal_server.pid) do set PID=%%i
    echo PID: %PID%
    
    tasklist /FI "PID eq %PID%" 2>nul | find /i "python" >nul
    if not errorlevel 1 (
        echo ✅ الحالة: يعمل
        echo 📅 وقت التشغيل: جاري الحساب...
        
        REM فحص المنافذ
        echo.
        echo 🔌 المنافذ النشطة:
        netstat -ano | findstr :8000 | findstr %PID%
        netstat -ano | findstr :8001 | findstr %PID%
        netstat -ano | findstr :8002 | findstr %PID%
        
    ) else (
        echo ❌ الحالة: متوقف (ملف PID موجود لكن العملية غير نشطة)
    )
) else (
    echo ❌ الحالة: متوقف (لا يوجد ملف PID)
)

echo.
echo 💾 استخدام الذاكرة:
python -c "
try:
    import psutil
    for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
        if 'python' in proc.info['name'].lower():
            memory_mb = proc.info['memory_info'].rss / 1024 / 1024
            print(f'PID {proc.info[\"pid\"]}: {memory_mb:.1f} MB')
except ImportError:
    print('psutil غير مثبت')
except Exception as e:
    print(f'خطأ: {e}')
"

echo.
pause
goto start

:show_log
echo.
echo 📋 سجل الخادم الأبدي:
echo ===================
if exist "logs\eternal_server.log" (
    echo آخر 30 سطر من سجل الخادم الأبدي:
    echo.
    powershell "Get-Content logs\eternal_server.log | Select-Object -Last 30"
) else (
    echo لا يوجد سجل للخادم الأبدي حتى الآن
    echo قم بتشغيل الخادم الأبدي أولاً
)
echo.
pause
goto start

:restart_eternal
echo.
echo 🔄 إعادة تشغيل الخادم الأبدي...
echo ==============================
echo.
echo إيقاف الخادم الحالي...
if exist "eternal_server.pid" (
    for /f %%i in (eternal_server.pid) do set PID=%%i
    taskkill /F /PID %PID% >nul 2>&1
    del eternal_server.pid >nul 2>&1
)

echo انتظار 3 ثوانٍ...
timeout /t 3 /nobreak >nul

echo تشغيل الخادم الأبدي مرة أخرى...
start /B python eternal_server.py

echo.
echo ✅ تم إعادة تشغيل الخادم الأبدي
echo.
pause
goto start

:invalid_choice
echo ❌ خيار غير صحيح
pause
goto start

:exit
echo 👋 شكراً لاستخدام الخادم الأبدي!
echo.
echo ⚠️ ملاحظة: إذا كان الخادم الأبدي يعمل، فهو سيستمر في العمل
echo استخدم الخيار 3 لإيقافه إذا لزم الأمر
echo.
exit /b 0

:end
echo.
echo 🎉 تم إنهاء العملية
pause

:start
cls
goto start
