# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016-2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON>ie<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && "
"(n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && "
"n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "PostgreSQL extensions"
msgstr "Rozszerzenia PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Element %(nth)s w tablicy nie przeszedł walidacji:"

msgid "Nested arrays must have the same length."
msgstr "Zagnieżdżone tablice muszą mieć tę samą długość."

msgid "Map of strings to strings/nulls"
msgstr "Mapowanie ciągów znaków na ciągi znaków/nulle"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Wartość „%(key)s” nie jest ciągiem znaków ani nullem."

msgid "Could not load JSON data."
msgstr "Nie można załadować danych JSON."

msgid "Input must be a JSON dictionary."
msgstr "Wejście musi być słownikiem JSON."

msgid "Enter two valid values."
msgstr "Podaj dwie poprawne wartości."

msgid "The start of the range must not exceed the end of the range."
msgstr "Początek zakresu nie może przekroczyć jego końca."

msgid "Enter two whole numbers."
msgstr "Podaj dwie liczby całkowite."

msgid "Enter two numbers."
msgstr "Podaj dwie liczby."

msgid "Enter two valid date/times."
msgstr "Podaj dwie poprawne daty/godziny."

msgid "Enter two valid dates."
msgstr "Podaj dwie poprawne daty."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Lista zawiera %(show_value)d element, a nie powinna zawierać więcej niż "
"%(limit_value)d."
msgstr[1] ""
"Lista zawiera %(show_value)d elementów, a nie powinna zawierać więcej niż "
"%(limit_value)d."
msgstr[2] ""
"Lista zawiera %(show_value)d elementów, a nie powinna zawierać więcej niż "
"%(limit_value)d."
msgstr[3] ""
"Lista zawiera %(show_value)d elementów, a nie powinna zawierać więcej niż "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Lista zawiera %(show_value)d element, a powinna zawierać nie mniej niż "
"%(limit_value)d."
msgstr[1] ""
"Lista zawiera %(show_value)d elementów, a powinna zawierać nie mniej niż "
"%(limit_value)d."
msgstr[2] ""
"Lista zawiera %(show_value)d elementów, a powinna zawierać nie mniej niż "
"%(limit_value)d."
msgstr[3] ""
"Lista zawiera %(show_value)d elementów, a powinna zawierać nie mniej niż "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Brak części kluczy: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Podano nieznane klucze: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Upewnij się, że górna granica zakresu nie jest większa niż %(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Upewnij się, że dolna granica zakresu nie jest mniejsza niż %(limit_value)s."
