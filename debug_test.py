#!/usr/bin/env python
"""
اختبار مفصل للموقع مع عرض الأخطاء
"""

import os
import sys
import django
import traceback

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.test import Client
from django.template.loader import get_template
from django.template import TemplateDoesNotExist

def debug_test():
    """اختبار مفصل للموقع"""
    
    print("🔍 اختبار مفصل للموقع...")
    
    # 1. اختبار وجود template
    print("\n📄 اختبار وجود template...")
    try:
        template = get_template('base/base.html')
        print("✅ template base/base.html موجود")
    except TemplateDoesNotExist as e:
        print(f"❌ template غير موجود: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في template: {e}")
        traceback.print_exc()
        return False
    
    # 2. اختبار الصفحة
    print("\n📊 اختبار صفحة dashboard...")
    client = Client()
    
    try:
        response = client.get('/dashboard/', HTTP_HOST='127.0.0.1:8000')
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 500:
            print('❌ خطأ 500!')
            # محاولة عرض محتوى الخطأ
            content = response.content.decode('utf-8', errors='ignore')
            print("محتوى الخطأ:")
            print(content[:1000])
            return False
        elif response.status_code == 200:
            print('✅ الصفحة تعمل بنجاح!')
            return True
        else:
            print(f'Status غير متوقع: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_test()
    if success:
        print("\n🎉 الموقع يعمل بشكل مثالي!")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
