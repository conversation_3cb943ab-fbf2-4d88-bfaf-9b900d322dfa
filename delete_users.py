import os
import django
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import transaction

def delete_users_except_admin():
    try:
        with transaction.atomic():
            # Get all users except admin
            users = User.objects.exclude(username='admin')
            
            # Delete users one by one to handle relations
            for user in users:
                user.delete()
                
            print(f"Successfully deleted {len(users)} users")
            
    except Exception as e:
        print(f"Error deleting users: {e}")

if __name__ == "__main__":
    delete_users_except_admin()