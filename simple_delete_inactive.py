#!/usr/bin/env python
"""
Simple deletion of inactive items
"""

import os
import sys
import django
import sqlite3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.conf import settings

def simple_delete():
    """Delete inactive items directly"""
    
    # Get database path
    db_path = settings.DATABASES['default']['NAME']
    
    print("Simple Database Cleanup")
    print("=" * 30)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Count inactive records
        cursor.execute("SELECT COUNT(*) FROM definitions_item WHERE is_active = 0")
        items_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM definitions_itemcategory WHERE is_active = 0")
        categories_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM definitions_unit WHERE is_active = 0")
        units_count = cursor.fetchone()[0]
        
        total_count = items_count + categories_count + units_count
        
        print(f"Inactive items: {items_count}")
        print(f"Inactive categories: {categories_count}")
        print(f"Inactive units: {units_count}")
        print(f"Total inactive records: {total_count}")
        
        if total_count == 0:
            print("No inactive data found")
            return
        
        # Auto delete without confirmation for automation
        print("\nDeleting inactive data...")
        
        deleted_total = 0
        
        # Delete items
        if items_count > 0:
            cursor.execute("DELETE FROM definitions_item WHERE is_active = 0")
            deleted_items = cursor.rowcount
            print(f"Deleted {deleted_items} items")
            deleted_total += deleted_items
        
        # Delete categories
        if categories_count > 0:
            cursor.execute("DELETE FROM definitions_itemcategory WHERE is_active = 0")
            deleted_categories = cursor.rowcount
            print(f"Deleted {deleted_categories} categories")
            deleted_total += deleted_categories
        
        # Delete units
        if units_count > 0:
            cursor.execute("DELETE FROM definitions_unit WHERE is_active = 0")
            deleted_units = cursor.rowcount
            print(f"Deleted {deleted_units} units")
            deleted_total += deleted_units
        
        # Commit changes
        conn.commit()
        
        print(f"\nSUCCESS: Deleted {deleted_total} inactive records")
        print("Database cleanup completed!")
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    simple_delete()