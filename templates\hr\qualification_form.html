{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-graduation-cap text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'hr:qualification_list' %}">المؤهلات</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'hr:qualification_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        {% if qualification %}تعديل المؤهل{% else %}إضافة مؤهل جديد{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        اسم المؤهل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{% if qualification %}{{ qualification.name }}{% endif %}" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المؤهل
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        <i class="fas fa-code me-2"></i>
                                        كود المؤهل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           value="{% if qualification %}{{ qualification.code }}{% endif %}" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كود المؤهل
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-2"></i>
                                وصف المؤهل
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="أدخل وصفاً مختصراً للمؤهل...">{% if qualification %}{{ qualification.description }}{% endif %}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="level" class="form-label">
                                        <i class="fas fa-layer-group me-2"></i>
                                        مستوى المؤهل
                                    </label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="">اختر المستوى</option>
                                        <option value="PRIMARY" {% if qualification.level == 'PRIMARY' %}selected{% endif %}>ابتدائي</option>
                                        <option value="PREP" {% if qualification.level == 'PREP' %}selected{% endif %}>إعدادي</option>
                                        <option value="SECONDARY" {% if qualification.level == 'SECONDARY' %}selected{% endif %}>ثانوي</option>
                                        <option value="DIPLOMA" {% if qualification.level == 'DIPLOMA' %}selected{% endif %}>دبلوم</option>
                                        <option value="BACHELOR" {% if qualification.level == 'BACHELOR' %}selected{% endif %}>بكالوريوس</option>
                                        <option value="MASTER" {% if qualification.level == 'MASTER' %}selected{% endif %}>ماجستير</option>
                                        <option value="PHD" {% if qualification.level == 'PHD' %}selected{% endif %}>دكتوراه</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock me-2"></i>
                                        مدة الدراسة
                                    </label>
                                    <input type="text" class="form-control" id="duration" name="duration" 
                                           value="{% if qualification %}{{ qualification.duration }}{% endif %}" 
                                           placeholder="مثال: 4 سنوات">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if not qualification or qualification.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    <i class="fas fa-check-circle me-2"></i>
                                    مؤهل نشط
                                </label>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if qualification %}تحديث المؤهل{% else %}حفظ المؤهل{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-generate code from name (only if not editing)
{% if not qualification %}
document.getElementById('name').addEventListener('input', function() {
    var name = this.value;
    var code = name.replace(/[^A-Za-z0-9]/g, '').toUpperCase().substring(0, 3);
    document.getElementById('code').value = code;
});
{% endif %}
</script>
{% endblock %} 