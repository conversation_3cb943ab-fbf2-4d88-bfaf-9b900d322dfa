#!/usr/bin/env python
"""
إعادة تعيين خادم Django بالكامل
"""

import os
import sys
import subprocess
import time

def reset_django_server():
    """إعادة تعيين خادم Django"""
    
    print("🔄 إعادة تعيين خادم Django...")
    
    # 1. إيقاف جميع عمليات Python
    try:
        print("⏹️ إيقاف جميع عمليات Python...")
        subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                      capture_output=True, text=True)
        time.sleep(2)
    except:
        pass
    
    # 2. حذف ملفات cache
    cache_dirs = [
        '__pycache__',
        '.pytest_cache',
        'staticfiles',
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                print(f"🗑️ حذف {cache_dir}...")
                subprocess.run(['rmdir', '/s', '/q', cache_dir], 
                              shell=True, capture_output=True)
            except:
                pass
    
    # 3. حذف ملفات .pyc
    try:
        print("🗑️ حذف ملفات .pyc...")
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    try:
                        os.remove(os.path.join(root, file))
                    except:
                        pass
    except:
        pass
    
    # 4. تنظيف متغيرات البيئة
    env_vars_to_clear = [
        'DJANGO_AUTORELOAD',
        'RUN_MAIN',
        'DJANGO_SETTINGS_MODULE'
    ]
    
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
    
    print("✅ تم إعادة تعيين البيئة بنجاح!")
    
    # 5. تشغيل الخادم الجديد
    print("🚀 تشغيل خادم Django جديد...")
    
    # تعيين متغيرات البيئة الجديدة
    os.environ['DJANGO_SETTINGS_MODULE'] = 'osaric_accounts.settings'
    os.environ['PYTHONPATH'] = os.getcwd()
    
    # تشغيل الخادم
    cmd = [
        sys.executable, 'manage.py', 'runserver', 
        '127.0.0.1:8000', '--noreload', '--insecure'
    ]
    
    print(f"📝 تشغيل الأمر: {' '.join(cmd)}")
    
    try:
        # تشغيل الخادم في background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"🎯 تم تشغيل الخادم - PID: {process.pid}")
        print("⏳ انتظار تشغيل الخادم...")
        time.sleep(5)
        
        # التحقق من حالة الخادم
        if process.poll() is None:
            print("✅ الخادم يعمل بنجاح!")
            print("🌐 الرابط: http://127.0.0.1:8000/")
            print("🌐 Dashboard: http://127.0.0.1:8000/dashboard/")
            return True
        else:
            print("❌ فشل في تشغيل الخادم")
            stdout, stderr = process.communicate()
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return False

if __name__ == "__main__":
    success = reset_django_server()
    if success:
        print("\n🎉 تم إعادة تعيين وتشغيل الخادم بنجاح!")
        print("💡 يمكنك الآن فتح المتصفح والذهاب إلى:")
        print("   http://127.0.0.1:8000/dashboard/")
    else:
        print("\n❌ فشل في إعادة تعيين الخادم")
        print("💡 جرب تشغيل الأمر يدوياً:")
        print("   python manage.py runserver 127.0.0.1:8000 --noreload")
