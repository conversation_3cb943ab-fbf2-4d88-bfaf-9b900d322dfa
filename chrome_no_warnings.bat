@echo off
REM تشغيل Chrome بدون تحذيرات SSL
REM Run Chrome without SSL warnings

echo بدء Chrome بدون تحذيرات SSL...
echo Starting Chrome without SSL warnings...

REM البحث عن Chrome
set CHROME_PATH=""
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else (
    echo Chrome غير موجود
    pause
    exit
)

REM تشغيل Chrome مع إعدادات خاصة
%CHROME_PATH% --ignore-certificate-errors --ignore-ssl-errors --ignore-certificate-errors-spki-list --ignore-certificate-errors-skip-list --disable-ssl-warnings --allow-running-insecure-content --disable-web-security --user-data-dir="%TEMP%\chrome_dev" https://***************:8443/

pause
