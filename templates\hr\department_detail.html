{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل القسم - {{ department.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-sitemap text-primary me-2"></i>
                            تفاصيل القسم
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">شؤون العاملين</a></li>
                                <li class="breadcrumb-item"><a href="{% url 'hr:department_list' %}">الأقسام</a></li>
                                <li class="breadcrumb-item active">{{ department.name }}</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div>
                    <a href="{% url 'hr:department_edit' department.pk %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="{% url 'hr:department_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Info -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات القسم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted">كود القسم:</td>
                                    <td><span class="badge bg-primary fs-6">{{ department.code }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">اسم القسم:</td>
                                    <td class="fs-5">{{ department.name }}</td>
                                </tr>
                                {% if department.name_english %}
                                <tr>
                                    <td class="fw-bold text-muted">الاسم بالإنجليزية:</td>
                                    <td>{{ department.name_english }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold text-muted">القسم الرئيسي:</td>
                                    <td>
                                        {% if department.parent_department %}
                                            <a href="{% url 'hr:department_detail' department.parent_department.pk %}" class="text-decoration-none">
                                                {{ department.parent_department.name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted">مدير القسم:</td>
                                    <td>
                                        {% if department.manager %}
                                            <a href="{% url 'hr:employee_detail' department.manager.pk %}" class="text-decoration-none">
                                                {{ department.manager.full_name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">الحالة:</td>
                                    <td>
                                        {% if department.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                                    <td>{{ department.created_at|date:"Y/m/d" }}</td>
                                </tr>
                                {% if department.updated_at %}
                                <tr>
                                    <td class="fw-bold text-muted">آخر تحديث:</td>
                                    <td>{{ department.updated_at|date:"Y/m/d" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    {% if department.description %}
                    <div class="mt-3">
                        <h6 class="text-muted">الوصف:</h6>
                        <p class="mb-0">{{ department.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-md-4">
            <div class="row g-3">
                <div class="col-12">
                    <div class="card bg-primary text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ employees.count }}</h3>
                            <small>موظف</small>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card bg-success text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-user-tie fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ positions.count }}</h3>
                            <small>منصب</small>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card bg-info text-white shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-sitemap fa-2x mb-2"></i>
                            <h3 class="mb-0">{{ sub_departments.count }}</h3>
                            <small>قسم فرعي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees -->
    {% if employees %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        موظفي القسم ({{ employees.count }})
                    </h5>
                    <a href="{% url 'hr:employee_create' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i> إضافة موظف
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم</th>
                                    <th>المنصب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التعيين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td><strong>{{ employee.employee_number }}</strong></td>
                                    <td>
                                        <a href="{% url 'hr:employee_detail' employee.pk %}" class="text-decoration-none">
                                            {{ employee.full_name }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ employee.position.name }}</span>
                                    </td>
                                    <td>
                                        {% if employee.status == 'ACTIVE' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif employee.status == 'INACTIVE' %}
                                            <span class="badge bg-warning text-dark">غير نشط</span>
                                        {% elif employee.status == 'TERMINATED' %}
                                            <span class="badge bg-danger">منتهي</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ employee.hire_date|date:"Y/m/d" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:employee_detail' employee.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:employee_edit' employee.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Positions -->
    {% if positions %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        مناصب القسم ({{ positions.count }})
                    </h5>
                    <a href="{% url 'hr:position_create' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i> إضافة منصب
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الكود</th>
                                    <th>المنصب</th>
                                    <th>عدد الموظفين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for position in positions %}
                                <tr>
                                    <td><span class="badge bg-secondary">{{ position.code }}</span></td>
                                    <td>
                                        <a href="{% url 'hr:position_detail' position.pk %}" class="text-decoration-none">
                                            {{ position.name }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ position.employees.count }}</span>
                                    </td>
                                    <td>
                                        {% if position.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:position_detail' position.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:position_edit' position.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Sub Departments -->
    {% if sub_departments %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>
                        الأقسام الفرعية ({{ sub_departments.count }})
                    </h5>
                    <a href="{% url 'hr:department_create' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i> إضافة قسم
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم القسم</th>
                                    <th>مدير القسم</th>
                                    <th>عدد الموظفين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sub_dept in sub_departments %}
                                <tr>
                                    <td><span class="badge bg-secondary">{{ sub_dept.code }}</span></td>
                                    <td>
                                        <a href="{% url 'hr:department_detail' sub_dept.pk %}" class="text-decoration-none">
                                            {{ sub_dept.name }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if sub_dept.manager %}
                                            <a href="{% url 'hr:employee_detail' sub_dept.manager.pk %}" class="text-decoration-none">
                                                {{ sub_dept.manager.full_name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ sub_dept.employees.count }}</span>
                                    </td>
                                    <td>
                                        {% if sub_dept.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:department_detail' sub_dept.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'hr:department_edit' sub_dept.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Empty State -->
    {% if not employees and not positions and not sub_departments %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات للعرض</h5>
                    <p class="text-muted mb-3">هذا القسم لا يحتوي على موظفين أو مناصب أو أقسام فرعية</p>
                    <div class="d-flex justify-content-center gap-2">
                        <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إضافة موظف
                        </a>
                        <a href="{% url 'hr:position_create' %}" class="btn btn-success">
                            <i class="fas fa-user-tie me-1"></i> إضافة منصب
                        </a>
                        <a href="{% url 'hr:department_create' %}" class="btn btn-info">
                            <i class="fas fa-sitemap me-1"></i> إضافة قسم فرعي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 