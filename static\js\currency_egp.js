/**
 * JavaScript لتحديث العملة إلى الجنيه المصري
 * Currency Update JavaScript for Egyptian Pound
 */

$(document).ready(function() {
    // تحديث جميع النصوص التي تحتوي على الريال السعودي
    updateCurrencyTexts();
    
    // تحديث حقول الإدخال
    updateCurrencyInputs();
    
    // تحديث الجداول
    updateCurrencyTables();
    
    // إضافة event listeners للحقول الجديدة
    addCurrencyEventListeners();
});

/**
 * تحديث جميع النصوص التي تحتوي على الريال السعودي
 */
function updateCurrencyTexts() {
    // البحث عن جميع العناصر التي تحتوي على "ر.س"
    $('*:contains("ر.س")').each(function() {
        if ($(this).children().length === 0) { // فقط العناصر التي لا تحتوي على عناصر فرعية
            var text = $(this).text();
            var updatedText = text.replace(/ر\.س/g, 'ج.م');
            $(this).text(updatedText);
        }
    });
    
    // البحث عن جميع العناصر التي تحتوي على "ريال"
    $('*:contains("ريال")').each(function() {
        if ($(this).children().length === 0) {
            var text = $(this).text();
            var updatedText = text.replace(/ريال/g, 'جنيه');
            $(this).text(updatedText);
        }
    });
    
    // البحث عن جميع العناصر التي تحتوي على "SAR"
    $('*:contains("SAR")').each(function() {
        if ($(this).children().length === 0) {
            var text = $(this).text();
            var updatedText = text.replace(/SAR/g, 'EGP');
            $(this).text(updatedText);
        }
    });
}

/**
 * تحديث حقول الإدخال
 */
function updateCurrencyInputs() {
    // تحديث placeholders
    $('input[placeholder*="ر.س"]').each(function() {
        var placeholder = $(this).attr('placeholder');
        var updatedPlaceholder = placeholder.replace(/ر\.س/g, 'ج.م');
        $(this).attr('placeholder', updatedPlaceholder);
    });
    
    // تحديث labels
    $('label:contains("ر.س")').each(function() {
        var text = $(this).text();
        var updatedText = text.replace(/ر\.س/g, 'ج.م');
        $(this).text(updatedText);
    });
    
    // إضافة class للحقول المالية النصية فقط (وليس الرقمية)
    $('input').each(function() {
        var type = $(this).attr('type') || '';
        var name = $(this).attr('name') || '';
        var id = $(this).attr('id') || '';
        if ((type === '' || type === 'text') && (
            name.includes('amount') || name.includes('price') || name.includes('cost') || 
            name.includes('total') || name.includes('balance') || name.includes('salary') ||
            id.includes('amount') || id.includes('price') || id.includes('cost') || 
            id.includes('total') || id.includes('balance') || id.includes('salary'))
        ) {
            $(this).addClass('currency-egp-input');
        }
    });
}

/**
 * تحديث الجداول
 */
function updateCurrencyTables() {
    // تحديث headers الجداول
    $('th:contains("ر.س")').each(function() {
        var text = $(this).text();
        var updatedText = text.replace(/ر\.س/g, 'ج.م');
        $(this).text(updatedText);
    });
    
    // تحديث خلايا الجداول
    $('td').each(function() {
        var text = $(this).text();
        if (text.includes('ر.س')) {
            var updatedText = text.replace(/ر\.س/g, 'ج.م');
            $(this).html(updatedText);
            $(this).addClass('currency-cell');
        }
    });
}

/**
 * إضافة event listeners للحقول الجديدة
 */
function addCurrencyEventListeners() {
    // تنسيق الأرقام عند الإدخال
    $(document).on('input', '.currency-egp-input', function() {
        formatCurrencyInput($(this));
    });
    
    // تنسيق الأرقام عند فقدان التركيز
    $(document).on('blur', '.currency-egp-input', function() {
        formatCurrencyValue($(this));
    });
    
    // منع إدخال أحرف غير صالحة
    $(document).on('keypress', '.currency-egp-input', function(e) {
        return validateCurrencyInput(e);
    });
}

/**
 * تنسيق حقل الإدخال المالي
 */
function formatCurrencyInput(input) {
    var value = input.val();
    
    // إزالة أي أحرف غير رقمية عدا النقطة
    value = value.replace(/[^0-9.]/g, '');
    
    // التأكد من وجود نقطة واحدة فقط
    var parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // تحديد عدد الخانات العشرية إلى 2
    if (parts.length === 2 && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    input.val(value);
}

/**
 * تنسيق القيمة النهائية
 */
function formatCurrencyValue(input) {
    var value = parseFloat(input.val()) || 0;
    input.val(value.toFixed(2));
}

/**
 * التحقق من صحة الإدخال
 */
function validateCurrencyInput(e) {
    var char = String.fromCharCode(e.which);
    var value = e.target.value;
    
    // السماح بالأرقام والنقطة والمفاتيح الخاصة
    if (e.which === 8 || e.which === 9 || e.which === 46 || e.which === 37 || e.which === 39) {
        return true; // Backspace, Tab, Delete, Left, Right
    }
    
    if (char === '.') {
        // السماح بنقطة واحدة فقط
        return value.indexOf('.') === -1;
    }
    
    // السماح بالأرقام فقط
    return /[0-9]/.test(char);
}

/**
 * تحويل مبلغ إلى تنسيق الجنيه المصري
 */
function formatEGP(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return '0.00 ج.م';
    }
    
    var formatted = parseFloat(amount).toFixed(2);
    
    // إضافة فواصل الآلاف
    formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    return formatted + ' ج.م';
}

/**
 * تحويل مبلغ إلى تنسيق HTML للجنيه المصري
 */
function formatEGPHTML(amount, cssClass = '') {
    var formatted = formatEGP(amount);
    return `<span class="currency-amount ${cssClass}">${formatted}</span>`;
}

/**
 * تحديث مبلغ في عنصر HTML
 */
function updateAmountDisplay(element, amount) {
    var $element = $(element);
    var formatted = formatEGP(amount);
    
    $element.html(formatted);
    $element.addClass('currency-amount');
    
    // إضافة لون حسب القيمة
    $element.removeClass('amount-positive amount-negative amount-zero');
    
    if (amount > 0) {
        $element.addClass('amount-positive');
    } else if (amount < 0) {
        $element.addClass('amount-negative');
    } else {
        $element.addClass('amount-zero');
    }
}

/**
 * حساب إجمالي مجموعة من المبالغ
 */
function calculateTotal(amounts) {
    var total = 0;
    
    amounts.forEach(function(amount) {
        var value = parseFloat(amount) || 0;
        total += value;
    });
    
    return total;
}

/**
 * تحديث إجماليات الفاتورة
 */
function updateInvoiceTotals() {
    var subtotal = 0;
    var totalDiscount = 0;
    var totalTax = 0;
    
    // حساب المجموع الفرعي
    $('.item-total').each(function() {
        var value = parseFloat($(this).val()) || 0;
        subtotal += value;
    });
    
    // حساب إجمالي الخصم
    $('.item-discount-amount').each(function() {
        var value = parseFloat($(this).val()) || 0;
        totalDiscount += value;
    });
    
    // حساب إجمالي الضريبة
    $('.item-tax-amount').each(function() {
        var value = parseFloat($(this).val()) || 0;
        totalTax += value;
    });
    
    var grandTotal = subtotal - totalDiscount + totalTax;
    
    // تحديث العرض
    updateAmountDisplay('#subtotal', subtotal);
    updateAmountDisplay('#total-discount', totalDiscount);
    updateAmountDisplay('#total-tax', totalTax);
    updateAmountDisplay('#grand-total', grandTotal);
}

/**
 * إضافة رمز العملة المصرية لعنصر
 */
function addEGPSymbol(element) {
    var $element = $(element);
    
    if (!$element.hasClass('currency-egp')) {
        $element.addClass('currency-egp');
    }
}

/**
 * تحديث جميع العملات في الصفحة
 */
function updateAllCurrencies() {
    updateCurrencyTexts();
    updateCurrencyInputs();
    updateCurrencyTables();
    
    // تحديث أي عناصر إضافية
    $('.currency-display').each(function() {
        addEGPSymbol(this);
    });
    
    $('.amount').each(function() {
        addEGPSymbol(this);
    });
}

// تصدير الدوال للاستخدام العام
window.CurrencyEGP = {
    format: formatEGP,
    formatHTML: formatEGPHTML,
    updateDisplay: updateAmountDisplay,
    calculateTotal: calculateTotal,
    updateInvoiceTotals: updateInvoiceTotals,
    updateAll: updateAllCurrencies
};

// تحديث العملات عند تحميل محتوى جديد عبر AJAX
$(document).ajaxComplete(function() {
    setTimeout(updateAllCurrencies, 100);
});
