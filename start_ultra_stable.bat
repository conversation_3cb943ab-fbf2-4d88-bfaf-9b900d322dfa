@echo off
REM ===================================================
REM سكريبت تشغيل الخادم فائق الاستقرار
REM Ultra Stable Server Startup Script
REM ===================================================

title نظام المحاسبة فائق الاستقرار - Ultra Stable Accounting System

echo.
echo ========================================
echo    🚀 نظام المحاسبة فائق الاستقرار
echo    Ultra Stable Accounting System
echo ========================================
echo.

REM تحديد مجلد المشروع
cd /d "%~dp0"

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "static" mkdir static
if not exist "media" mkdir media
if not exist "backup" mkdir backup

echo ✅ تم إنشاء المجلدات المطلوبة

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات الأساسية
echo.
echo 📦 تثبيت المتطلبات...
pip install django psutil >nul 2>&1

REM فحص Django
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django غير مثبت بشكل صحيح
    echo جاري تثبيت Django...
    pip install django
)

echo ✅ Django جاهز

REM فحص psutil
python -c "import psutil" 2>nul
if errorlevel 1 (
    echo جاري تثبيت psutil...
    pip install psutil
)

echo ✅ psutil جاهز

echo.
echo 🎯 خيارات تشغيل الخادم فائق الاستقرار:
echo ==========================================
echo 1. تشغيل الخادم فائق الاستقرار (موصى به)
echo 2. تشخيص مشاكل الخادم
echo 3. تشغيل الخادم العادي
echo 4. تشغيل مراقب النظام
echo 5. إنشاء نسخة احتياطية
echo 6. عرض معلومات النظام
echo 7. عرض سجل الاستقرار
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار (1-7): "

if "%choice%"=="1" goto ultra_stable
if "%choice%"=="2" goto diagnose
if "%choice%"=="3" goto normal_server
if "%choice%"=="4" goto monitor
if "%choice%"=="5" goto backup
if "%choice%"=="6" goto system_info
if "%choice%"=="7" goto show_log
if "%choice%"=="0" goto exit
goto invalid_choice

:ultra_stable
echo.
echo 🚀 تشغيل الخادم فائق الاستقرار...
echo =====================================
echo.
echo 🔥 مميزات الخادم فائق الاستقرار:
echo • لا يتوقف أبداً - إعادة تشغيل تلقائية
echo • مراقبة مستمرة كل 15 ثانية
echo • تسجيل مفصل لجميع الأحداث
echo • حماية من جميع أنواع الأخطاء
echo • استجابة سريعة للمشاكل
echo.
echo 🌐 الموقع سيكون متاح على: http://127.0.0.1:8000/
echo ⌨️ اضغط Ctrl+C لإيقاف الخادم بأمان
echo.
pause
python ultra_stable_server.py
goto end

:diagnose
echo.
echo 🔍 تشخيص مشاكل الخادم...
python diagnose_server.py
echo.
pause
goto start

:normal_server
echo.
echo 🚀 تشغيل الخادم العادي...
python manage.py runserver 127.0.0.1:8000
goto end

:monitor
echo.
echo 🔍 تشغيل مراقب النظام...
python system_monitor.py
goto end

:backup
echo.
echo 💾 إنشاء نسخة احتياطية...
set backup_name=backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_name=%backup_name: =0%
mkdir "backup\%backup_name%" 2>nul
copy "db.sqlite3" "backup\%backup_name%\db.sqlite3" >nul 2>&1
xcopy "media" "backup\%backup_name%\media" /E /I /Q >nul 2>&1
echo ✅ تم إنشاء النسخة الاحتياطية: backup\%backup_name%
pause
goto start

:system_info
echo.
echo 💻 معلومات النظام:
echo ==================
python -c "
import platform
import sys
try:
    import psutil
    print(f'نظام التشغيل: {platform.system()} {platform.release()}')
    print(f'Python: {platform.python_version()}')
    try:
        import django
        print(f'Django: {django.get_version()}')
    except:
        print('Django: غير مثبت')
    print(f'المعالج: {psutil.cpu_count()} cores')
    print(f'الذاكرة: {psutil.virtual_memory().total // (1024**3)} GB')
    print(f'مساحة القرص: {psutil.disk_usage('.').total // (1024**3)} GB')
    print(f'استخدام المعالج: {psutil.cpu_percent()}%%')
    print(f'استخدام الذاكرة: {psutil.virtual_memory().percent}%%')
except ImportError:
    print('نظام التشغيل:', platform.system(), platform.release())
    print('Python:', platform.python_version())
    try:
        import django
        print('Django:', django.get_version())
    except:
        print('Django: غير مثبت')
    print('psutil غير مثبت - لا يمكن عرض معلومات النظام الكاملة')
"
echo.
pause
goto start

:show_log
echo.
echo 📊 سجل الاستقرار:
echo ================
if exist "ultra_stable.log" (
    echo آخر 20 سطر من سجل الاستقرار:
    echo.
    powershell "Get-Content ultra_stable.log | Select-Object -Last 20"
) else (
    echo لا يوجد سجل استقرار حتى الآن
    echo قم بتشغيل الخادم فائق الاستقرار أولاً
)
echo.
pause
goto start

:invalid_choice
echo ❌ خيار غير صحيح
pause
goto start

:exit
echo 👋 شكراً لاستخدام النظام!
exit /b 0

:end
echo.
echo 🎉 تم إنهاء العملية
pause

:start
cls
goto start
