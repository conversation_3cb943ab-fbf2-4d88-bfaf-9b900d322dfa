<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - حسابات أوساريك</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            left: 80%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 5%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .input-focus:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="w-full max-w-md">
        <!-- Login Card -->
        <div class="glass-effect rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="text-center p-8">
                <div class="text-6xl text-white mb-4 opacity-90">
                    <i class="fas fa-calculator"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">حسابات أوساريك</h1>
                <p class="text-white opacity-90">نظام إدارة الحسابات المالية والمخزون</p>
            </div>
            
            <!-- Login Form -->
            <div class="bg-white p-8">
                {% if form.errors %}
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                            <span class="text-red-700 font-medium">خطأ في تسجيل الدخول</span>
                        </div>
                        <div class="mt-2 text-red-600 text-sm">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    {{ error }}<br>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
                
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Username Field -->
                    <div>
                        <label for="id_username" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المستخدم
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                            <input 
                                type="text" 
                                name="username" 
                                id="id_username" 
                                required
                                class="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg input-focus focus:outline-none transition-colors"
                                placeholder="أدخل اسم المستخدم"
                                value="{{ form.username.value|default:'' }}"
                            >
                        </div>
                    </div>
                    
                    <!-- Password Field -->
                    <div>
                        <label for="id_password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input 
                                type="password" 
                                name="password" 
                                id="id_password" 
                                required
                                class="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg input-focus focus:outline-none transition-colors"
                                placeholder="أدخل كلمة المرور"
                            >
                        </div>
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" name="remember" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                        </label>
                    </div>
                    
                    <!-- Login Button -->
                    <button type="submit" class="w-full btn-gradient text-white font-semibold py-3 px-6 rounded-lg text-lg">
                        <i class="fas fa-sign-in-alt ml-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <!-- Demo Credentials -->
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                        <span class="text-blue-700 font-medium">بيانات تجريبية</span>
                    </div>
                    <div class="text-sm text-blue-600">
                        <div>اسم المستخدم: <code class="bg-blue-100 px-1 rounded">admin</code></div>
                        <div>كلمة المرور: <code class="bg-blue-100 px-1 rounded">admin</code></div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center p-6 bg-gray-50">
                <p class="text-gray-600 text-sm">
                    © 2024 حسابات أوساريك. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
        
        <!-- Additional Info -->
        <div class="mt-6 text-center">
            <div class="glass-effect rounded-lg p-4">
                <div class="flex items-center justify-center space-x-4 text-white text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt ml-1"></i>
                        <span>آمن ومشفر</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock ml-1"></i>
                        <span>متاح 24/7</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-headset ml-1"></i>
                        <span>دعم فني</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
