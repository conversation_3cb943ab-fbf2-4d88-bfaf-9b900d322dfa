@echo off
REM تثبيت شهادة SSL في Windows
REM Install SSL Certificate in Windows

echo ============================================================
echo 🔒 تثبيت شهادة SSL
echo SSL Certificate Installation
echo ============================================================
echo.

REM فحص صلاحيات Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ يعمل بصلاحيات Administrator
) else (
    echo ❌ يجب تشغيل السكريپت كـ Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo 🔧 تثبيت الشهادة في النظام...
certutil -addstore -f "Root" "ssl\server.crt"

if %errorLevel% == 0 (
    echo ✅ تم تثبيت الشهادة بنجاح!
    echo.
    echo 🎉 الآن يمكنك الوصول للموقع بأمان:
    echo    https://***************:8443/
    echo    https://localhost:8443/
    echo.
    echo 💡 لن تظهر تحذيرات الأمان بعد الآن
) else (
    echo ❌ فشل في تثبيت الشهادة
    echo.
    echo 🔧 تثبيت يدوي:
    echo 1. افتح ssl\server.crt
    echo 2. اضغط Install Certificate
    echo 3. اختر Local Machine
    echo 4. اختر Trusted Root Certification Authorities
)

echo.
echo ============================================================
pause
