# Generated by Django 5.2.2 on 2025-07-04 14:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AllowanceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود نوع الإضافة')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='اسم نوع الإضافة')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_taxable', models.BooleanField(default=False, verbose_name='خاضعة للضريبة؟')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نوع إضافة',
                'verbose_name_plural': 'أنواع الإضافات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DeductionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود نوع الخصم')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الخصم')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_statutory', models.BooleanField(default=False, verbose_name='خصم إلزامي (قانوني)؟')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'نوع خصم',
                'verbose_name_plural': 'أنواع الخصومات',
                'ordering': ['name'],
            },
        ),
    ]
