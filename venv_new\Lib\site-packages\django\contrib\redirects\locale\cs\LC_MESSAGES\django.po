# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-18 23:14+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "Redirects"
msgstr "Přesměrování"

msgid "site"
msgstr "web"

msgid "redirect from"
msgstr "přesměrovat z"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Toto by měla být absolutní cesta bez domény. Příklad: \"/udalosti/hledat/\"."

msgid "redirect to"
msgstr "přesměrovat na"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Toto by měla být buď absolutní cesta (jako výše) nebo plná adresa URL "
"začínající schématem, tedy například \"https://\"."

msgid "redirect"
msgstr "přesměrování"

msgid "redirects"
msgstr "přesměrování"
