{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'hr:shift_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> عودة للقائمة
                        </a>
                    </div>
                </div>
                <form method="POST">
                    {% csrf_token %}
                    <div class="card-body">
                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>
                                
                                <div class="form-group">
                                    <label for="name">اسم الوردية *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ shift.name|default:'' }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="code">كود الوردية *</label>
                                    <input type="text" class="form-control" id="code" name="code" 
                                           value="{{ shift.code|default:'' }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="description">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ shift.description|default:'' }}</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="shift_type">نوع الوردية</label>
                                    <select class="form-control" id="shift_type" name="shift_type">
                                        <option value="MORNING" {% if shift.shift_type == 'MORNING' %}selected{% endif %}>صباحية</option>
                                        <option value="EVENING" {% if shift.shift_type == 'EVENING' %}selected{% endif %}>مسائية</option>
                                        <option value="NIGHT" {% if shift.shift_type == 'NIGHT' %}selected{% endif %}>ليلية</option>
                                        <option value="FLEXIBLE" {% if shift.shift_type == 'FLEXIBLE' %}selected{% endif %}>مرنة</option>
                                        <option value="PART_TIME" {% if shift.shift_type == 'PART_TIME' %}selected{% endif %}>دوام جزئي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- أوقات الوردية -->
                            <div class="col-md-6">
                                <h5 class="mb-3">أوقات الوردية</h5>
                                
                                <div class="form-group">
                                    <label for="start_time">وقت البداية *</label>
                                    <input type="time" class="form-control" id="start_time" name="start_time" 
                                           value="{{ shift.start_time|time:'H:i'|default:'' }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="end_time">وقت النهاية *</label>
                                    <input type="time" class="form-control" id="end_time" name="end_time" 
                                           value="{{ shift.end_time|time:'H:i'|default:'' }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="duration_hours">مدة الوردية (ساعات)</label>
                                    <input type="number" class="form-control" id="duration_hours" name="duration_hours" 
                                           value="{{ shift.duration_hours|default:8.0 }}" step="0.5" min="0">
                                </div>
                                
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="include_break" name="include_break" 
                                               {% if shift.include_break or not shift.pk %}checked{% endif %}>
                                        <label class="custom-control-label" for="include_break">يشمل استراحة</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="break_duration">مدة الاستراحة (دقائق)</label>
                                    <input type="number" class="form-control" id="break_duration" name="break_duration" 
                                           value="{{ shift.break_duration|default:60 }}" min="0">
                                </div>
                                
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="overtime_allowed" name="overtime_allowed" 
                                               {% if shift.overtime_allowed or not shift.pk %}checked{% endif %}>
                                        <label class="custom-control-label" for="overtime_allowed">يسمح بالعمل الإضافي</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {% if shift.is_active or not shift.pk %}checked{% endif %}>
                                        <label class="custom-control-label" for="is_active">نشط</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'hr:shift_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الوردية
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة الأوقات
document.getElementById('end_time').addEventListener('change', function() {
    const startTime = document.getElementById('start_time').value;
    const endTime = this.value;
    
    if (startTime && endTime) {
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        
        if (end <= start) {
            alert('وقت النهاية يجب أن يكون بعد وقت البداية');
            this.value = '';
        }
    }
});

// حساب مدة الوردية تلقائياً
function calculateDuration() {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    
    if (startTime && endTime) {
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        
        if (end > start) {
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);
            document.getElementById('duration_hours').value = diffHours.toFixed(2);
        }
    }
}

document.getElementById('start_time').addEventListener('change', calculateDuration);
document.getElementById('end_time').addEventListener('change', calculateDuration);
</script>
{% endblock %} 