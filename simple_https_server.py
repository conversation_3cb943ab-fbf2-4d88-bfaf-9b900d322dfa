#!/usr/bin/env python3
"""
خادم HTTPS مبسط
Simple HTTPS Server

خادم Django مع HTTPS باستخدام طريقة مبسطة
Django server with HTTPS using simplified approach
"""

import os
import sys
import time
import signal
import socket
import subprocess
import threading
import logging
import ssl
import http.server
import socketserver
from pathlib import Path
from datetime import datetime

# إعداد اللوجز
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'simple_https.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class SimpleHTTPSServer:
    """خادم HTTPS مبسط"""
    
    def __init__(self):
        self.django_process = None
        self.proxy_server = None
        self.is_running = False
        self.django_port = 8000
        self.https_port = 8443
        self.host = '0.0.0.0'
        self.local_ip = self.get_local_ip()
        self.ssl_cert = None
        self.ssl_key = None
        self.setup_ssl()
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ✅ {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def setup_ssl(self):
        """إعداد SSL"""
        ssl_dir = Path('ssl')
        cert_file = ssl_dir / 'server.crt'
        key_file = ssl_dir / 'server.key'
        
        if cert_file.exists() and key_file.exists():
            self.ssl_cert = cert_file
            self.ssl_key = key_file
            self.log_success("تم العثور على شهادة SSL")
        else:
            self.log_info("إنشاء شهادة SSL...")
            self.create_ssl_certificate()
    
    def create_ssl_certificate(self):
        """إنشاء شهادة SSL"""
        try:
            from simple_ssl import create_ssl_certificate
            cert_file, key_file, pem_file = create_ssl_certificate()
            
            if cert_file and key_file:
                self.ssl_cert = cert_file
                self.ssl_key = key_file
                self.log_success("تم إنشاء شهادة SSL")
            else:
                self.log_error("فشل في إنشاء شهادة SSL")
                
        except Exception as e:
            self.log_error(f"خطأ في إنشاء شهادة SSL: {e}")
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except Exception:
            return True
    
    def find_available_port(self, start_port):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return None
    
    def start_django_server(self):
        """بدء تشغيل خادم Django"""
        try:
            # البحث عن منفذ متاح لـ Django
            django_port = self.find_available_port(8000)
            if not django_port:
                self.log_error("لا يمكن العثور على منفذ متاح لـ Django")
                return False
            
            self.django_port = django_port
            self.log_info(f"بدء تشغيل خادم Django على المنفذ {django_port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'127.0.0.1:{django_port}',
                '--insecure'
            ]
            
            self.django_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # انتظار بدء Django
            time.sleep(5)
            
            if self.django_process.poll() is None:
                self.log_success(f"خادم Django يعمل على المنفذ {django_port}")
                return True
            else:
                self.log_error("فشل في بدء خادم Django")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في بدء خادم Django: {e}")
            return False
    
    def create_https_proxy(self):
        """إنشاء بروكسي HTTPS"""
        try:
            if not self.ssl_cert or not self.ssl_key:
                self.log_error("لا توجد شهادة SSL")
                return False
            
            # البحث عن منفذ متاح لـ HTTPS
            https_port = self.find_available_port(8443)
            if not https_port:
                self.log_error("لا يمكن العثور على منفذ متاح لـ HTTPS")
                return False
            
            self.https_port = https_port
            
            class HTTPSProxyHandler(http.server.BaseHTTPRequestHandler):
                def __init__(self, django_port, *args, **kwargs):
                    self.django_port = django_port
                    super().__init__(*args, **kwargs)
                
                def do_GET(self):
                    self.proxy_request()
                
                def do_POST(self):
                    self.proxy_request()
                
                def do_PUT(self):
                    self.proxy_request()
                
                def do_DELETE(self):
                    self.proxy_request()
                
                def proxy_request(self):
                    try:
                        import urllib.request
                        import urllib.parse
                        
                        # إنشاء URL للخادم Django
                        django_url = f"http://127.0.0.1:{self.django_port}{self.path}"
                        
                        # إنشاء الطلب
                        req = urllib.request.Request(django_url, method=self.command)
                        
                        # نسخ الهيدرز
                        for header, value in self.headers.items():
                            if header.lower() not in ['host', 'connection']:
                                req.add_header(header, value)
                        
                        # إضافة البيانات للطلبات POST/PUT
                        if self.command in ['POST', 'PUT']:
                            content_length = int(self.headers.get('Content-Length', 0))
                            if content_length > 0:
                                req.data = self.rfile.read(content_length)
                        
                        # إرسال الطلب
                        response = urllib.request.urlopen(req, timeout=30)
                        
                        # إرسال الاستجابة
                        self.send_response(response.getcode())
                        
                        # نسخ هيدرز الاستجابة
                        for header, value in response.headers.items():
                            if header.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(header, value)
                        
                        self.end_headers()
                        
                        # نسخ محتوى الاستجابة
                        self.wfile.write(response.read())
                        
                    except Exception as e:
                        self.send_error(500, f"Proxy Error: {e}")
                
                def log_message(self, format, *args):
                    # تجاهل رسائل اللوج العادية
                    pass
            
            # إنشاء معالج مع django_port
            def handler_factory(*args, **kwargs):
                return HTTPSProxyHandler(self.django_port, *args, **kwargs)
            
            # إنشاء الخادم
            self.proxy_server = socketserver.TCPServer(('0.0.0.0', https_port), handler_factory)
            
            # إعداد SSL
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain(str(self.ssl_cert), str(self.ssl_key))
            
            self.proxy_server.socket = context.wrap_socket(
                self.proxy_server.socket,
                server_side=True
            )
            
            self.log_success(f"تم إنشاء بروكسي HTTPS على المنفذ {https_port}")
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في إنشاء بروكسي HTTPS: {e}")
            return False
    
    def start_https_proxy(self):
        """بدء تشغيل بروكسي HTTPS"""
        try:
            self.log_info("بدء تشغيل بروكسي HTTPS...")
            self.proxy_server.serve_forever()
        except Exception as e:
            self.log_error(f"خطأ في تشغيل بروكسي HTTPS: {e}")
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🔒 خادم HTTPS المبسط")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 منفذ Django: {self.django_port}")
        print(f"🔒 منفذ HTTPS: {self.https_port}")
        print("\n🔒 للوصول الآمن (HTTPS):")
        print(f"   https://{self.local_ip}:{self.https_port}/")
        print(f"   https://localhost:{self.https_port}/")
        print("\n🌐 للوصول العادي (HTTP):")
        print(f"   http://{self.local_ip}:{self.django_port}/")
        print(f"   http://localhost:{self.django_port}/")
        print("\n⚠️ ملاحظات:")
        print("   • قد تظهر تحذيرات الأمان في المتصفح")
        print("   • اضغط 'Advanced' ثم 'Proceed to localhost'")
        print("   • الاتصال مشفر ومحمي")
        print("=" * 60)
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة الإيقاف {signum}")
        self.stop_servers()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def stop_servers(self):
        """إيقاف الخوادم"""
        self.is_running = False
        
        if self.proxy_server:
            try:
                self.log_info("إيقاف بروكسي HTTPS...")
                self.proxy_server.shutdown()
                self.proxy_server.server_close()
                self.log_success("تم إيقاف بروكسي HTTPS")
            except Exception as e:
                self.log_error(f"خطأ في إيقاف بروكسي HTTPS: {e}")
        
        if self.django_process:
            try:
                self.log_info("إيقاف خادم Django...")
                self.django_process.terminate()
                try:
                    self.django_process.wait(timeout=10)
                    self.log_success("تم إيقاف خادم Django")
                except subprocess.TimeoutExpired:
                    self.django_process.kill()
                    self.django_process.wait()
                    self.log_success("تم إجبار إيقاف خادم Django")
            except Exception as e:
                self.log_error(f"خطأ في إيقاف خادم Django: {e}")
    
    def run(self):
        """تشغيل الخادم المبسط"""
        print("=" * 60)
        print("🔒 خادم HTTPS المبسط")
        print("Simple HTTPS Server")
        print("=" * 60)
        
        self.log_info("بدء تشغيل خادم HTTPS المبسط...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # بدء خادم Django
        if not self.start_django_server():
            self.log_error("فشل في بدء خادم Django!")
            return False
        
        # إنشاء بروكسي HTTPS
        if not self.create_https_proxy():
            self.log_error("فشل في إنشاء بروكسي HTTPS!")
            self.stop_servers()
            return False
        
        # عرض معلومات الوصول
        self.display_access_info()
        
        self.is_running = True
        self.log_success("خادم HTTPS المبسط يعمل بنجاح!")
        self.log_info("اضغط Ctrl+C للإيقاف")
        
        try:
            # بدء بروكسي HTTPS في thread منفصل
            https_thread = threading.Thread(target=self.start_https_proxy)
            https_thread.daemon = True
            https_thread.start()
            
            # انتظار
            while self.is_running:
                time.sleep(1)
                
                # فحص إذا كان Django ما زال يعمل
                if self.django_process and self.django_process.poll() is not None:
                    self.log_error("خادم Django توقف!")
                    break
                    
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
        finally:
            self.stop_servers()
            self.log_info("تم إنهاء خادم HTTPS المبسط")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = SimpleHTTPSServer()
    success = server.run()
    
    if success:
        print("✅ تم تشغيل خادم HTTPS المبسط بنجاح!")
    else:
        print("❌ فشل في تشغيل خادم HTTPS المبسط!")
        sys.exit(1)

if __name__ == "__main__":
    main()
