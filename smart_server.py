#!/usr/bin/env python3
"""
خادم ذكي يحل جميع المشاكل تلقائياً
Smart Server that Fixes All Issues Automatically

خادم متقدم يتعامل مع جميع المشاكل ويجد منفذ متاح تلقائياً
Advanced server that handles all issues and finds available port automatically
"""

import os
import sys
import time
import signal
import socket
import subprocess
import threading
import logging
from datetime import datetime

# إعداد اللوجز
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_server.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class SmartServer:
    """خادم ذكي يحل جميع المشاكل"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 10000
        self.restart_delay = 3
        self.preferred_ports = [8000, 8001, 8002, 8003, 8080, 8888, 9000]
        self.current_port = None
        self.host = '0.0.0.0'  # للوصول من الشبكة
        self.local_ip = self.get_local_ip()
        self.health_check_interval = 20
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ERROR: {message}"
        logger.error(clean_message)
        print(clean_message)
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        clean_message = f"[{timestamp}] ✅ {message}"
        logger.info(clean_message)
        print(clean_message)
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def is_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0  # True إذا كان المنفذ متاح
        except Exception:
            return True
    
    def find_available_port(self):
        """البحث عن منفذ متاح"""
        self.log_info("البحث عن منفذ متاح...")
        
        for port in self.preferred_ports:
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        # البحث في نطاق أوسع
        for port in range(8000, 9000):
            if self.is_port_available(port):
                self.log_success(f"تم العثور على منفذ متاح: {port}")
                return port
        
        self.log_error("لم يتم العثور على منفذ متاح")
        return None
    
    def kill_process_on_port(self, port):
        """محاولة قتل العملية المستخدمة للمنفذ"""
        try:
            if os.name == 'nt':  # Windows
                # استخدام netstat للعثور على PID
                result = subprocess.run(
                    f'netstat -ano | findstr :{port}',
                    shell=True, capture_output=True, text=True
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    pids = set()
                    
                    for line in lines:
                        parts = line.split()
                        if len(parts) >= 5 and 'LISTENING' in line:
                            try:
                                pid = int(parts[-1])
                                pids.add(pid)
                            except ValueError:
                                continue
                    
                    # قتل العمليات
                    for pid in pids:
                        try:
                            subprocess.run(f'taskkill /F /PID {pid}', 
                                         shell=True, capture_output=True)
                            self.log_success(f"تم إنهاء العملية PID: {pid}")
                        except Exception:
                            pass
                    
                    if pids:
                        time.sleep(2)  # انتظار قصير
                        return True
            
            return False
            
        except Exception as e:
            self.log_error(f"خطأ في قتل العملية: {e}")
            return False
    
    def prepare_environment(self):
        """تحضير البيئة"""
        try:
            # إنشاء المجلدات المطلوبة
            dirs = ['logs', 'static', 'staticfiles', 'media', 'backup']
            for dir_name in dirs:
                if not os.path.exists(dir_name):
                    os.makedirs(dir_name)
            
            # تحديث إعدادات Django
            self.update_django_settings()
            
            # تشغيل الترحيلات
            self.run_migrations()
            
            # جمع الملفات الثابتة
            self.collect_static()
            
            return True
            
        except Exception as e:
            self.log_error(f"خطأ في تحضير البيئة: {e}")
            return False
    
    def update_django_settings(self):
        """تحديث إعدادات Django"""
        try:
            settings_file = 'osaric_accounts/settings.py'
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التأكد من ALLOWED_HOSTS
                if "ALLOWED_HOSTS = ['*'" not in content:
                    if 'ALLOWED_HOSTS = []' in content:
                        content = content.replace(
                            'ALLOWED_HOSTS = []',
                            f"ALLOWED_HOSTS = ['*', '{self.local_ip}', 'localhost', '127.0.0.1']"
                        )
                        
                        with open(settings_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.log_success("تم تحديث ALLOWED_HOSTS")
                
        except Exception as e:
            self.log_error(f"خطأ في تحديث الإعدادات: {e}")
    
    def run_migrations(self):
        """تشغيل الترحيلات"""
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'migrate', '--noinput'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_success("تم تشغيل الترحيلات بنجاح")
            else:
                self.log_info("تحذير في الترحيلات (سنستمر)")
                
        except Exception as e:
            self.log_error(f"خطأ في الترحيلات: {e}")
    
    def collect_static(self):
        """جمع الملفات الثابتة"""
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_success("تم جمع الملفات الثابتة")
            else:
                self.log_info("تحذير في جمع الملفات الثابتة (سنستمر)")
                
        except Exception as e:
            self.log_info(f"تحذير في جمع الملفات الثابتة: {e}")
    
    def signal_handler(self, signum, frame):
        """معالج إشارات النظام"""
        self.log_info(f"تم استلام إشارة الإيقاف {signum}")
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            # البحث عن منفذ متاح
            port = self.find_available_port()
            if not port:
                # محاولة تحرير المنفذ المفضل
                if self.kill_process_on_port(8000):
                    port = 8000 if self.is_port_available(8000) else self.find_available_port()
                
                if not port:
                    self.log_error("لا يمكن العثور على منفذ متاح")
                    return False
            
            self.current_port = port
            self.log_info(f"بدء تشغيل الخادم الذكي على {self.host}:{port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{port}',
                '--insecure'
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.is_running = True
            self.log_success(f"تم بدء الخادم بنجاح! PID: {self.server_process.pid}")
            
            # انتظار قصير للتأكد من بدء الخادم
            time.sleep(5)
            
            if self.server_process.poll() is None:
                self.log_success("الخادم الذكي يعمل بنجاح")
                self.display_access_info()
                return True
            else:
                self.log_error(f"الخادم توقف فوراً بكود: {self.server_process.returncode}")
                return False
            
        except Exception as e:
            self.log_error(f"خطأ في بدء الخادم: {e}")
            return False
    
    def display_access_info(self):
        """عرض معلومات الوصول"""
        print("\n" + "=" * 60)
        print("🌐 معلومات الوصول للخادم الذكي")
        print("=" * 60)
        print(f"🖥️  اسم الجهاز: {socket.gethostname()}")
        print(f"🌍 عنوان IP المحلي: {self.local_ip}")
        print(f"🔌 المنفذ: {self.current_port}")
        print("\n📱 للوصول من أجهزة أخرى:")
        print(f"   http://{self.local_ip}:{self.current_port}/")
        print("\n💻 للوصول من نفس الجهاز:")
        print(f"   http://localhost:{self.current_port}/")
        print(f"   http://127.0.0.1:{self.current_port}/")
        print("\n📋 تعليمات الوصول:")
        print("   1. تأكد من أن الأجهزة على نفس الشبكة")
        print("   2. استخدم عنوان IP المحلي للوصول")
        print("   3. الخادم يعمل بأقصى استقرار")
        print("=" * 60)
    
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            if not self.server_process or self.server_process.poll() is not None:
                return False
            
            import urllib.request
            import urllib.error
            
            url = f"http://127.0.0.1:{self.current_port}/"
            
            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'SmartServer/1.0')
                response = urllib.request.urlopen(request, timeout=10)
                
                if response.getcode() == 200:
                    return True
                else:
                    self.log_error(f"الخادم يرد بكود خطأ: {response.getcode()}")
                    return False
                    
            except urllib.error.URLError as e:
                self.log_error(f"خطأ في الاتصال بالخادم: {e}")
                return False
                
        except Exception as e:
            self.log_error(f"خطأ في فحص الصحة: {e}")
            return False
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.log_info("بدء إعادة تشغيل الخادم الذكي...")
        
        self.stop_server()
        time.sleep(self.restart_delay)
        
        self.restart_count += 1
        
        if self.restart_count <= self.max_restarts:
            self.log_info(f"محاولة إعادة التشغيل رقم {self.restart_count}")
            return self.start_server()
        else:
            self.log_error(f"تم الوصول للحد الأقصى من إعادة التشغيل ({self.max_restarts})")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                self.log_info("إيقاف الخادم الذكي...")
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=10)
                    self.log_success("تم إيقاف الخادم بنجاح")
                except subprocess.TimeoutExpired:
                    self.log_info("إجبار إيقاف الخادم...")
                    self.server_process.kill()
                    self.server_process.wait()
                    self.log_success("تم إجبار إيقاف الخادم")
                
            except Exception as e:
                self.log_error(f"خطأ في إيقاف الخادم: {e}")
            finally:
                self.server_process = None
                self.is_running = False
    
    def health_monitor_loop(self):
        """حلقة مراقبة الصحة"""
        consecutive_failures = 0
        max_failures = 3
        
        while self.is_running:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_error("عملية الخادم الذكي توقفت!")
                    if not self.restart_server():
                        break
                    consecutive_failures = 0
                    continue
                
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_success("الخادم الذكي عاد للعمل بنجاح")
                    consecutive_failures = 0
                    self.log_info("الخادم الذكي يعمل بصحة جيدة")
                else:
                    consecutive_failures += 1
                    self.log_error(f"فشل فحص الصحة ({consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        self.log_error("فشل متكرر في فحص الصحة - إعادة تشغيل الخادم")
                        if not self.restart_server():
                            break
                        consecutive_failures = 0
                
            except Exception as e:
                self.log_error(f"خطأ في حلقة مراقبة الصحة: {e}")
                time.sleep(5)
    
    def run(self):
        """تشغيل الخادم الذكي"""
        print("=" * 60)
        print("🧠 الخادم الذكي - يحل جميع المشاكل تلقائياً")
        print("Smart Server - Fixes All Issues Automatically")
        print("=" * 60)
        
        self.log_info("بدء تشغيل الخادم الذكي...")
        
        # إعداد معالجات الإشارات
        self.setup_signal_handlers()
        
        # تحضير البيئة
        if not self.prepare_environment():
            self.log_error("فشل في تحضير البيئة")
            return False
        
        # بدء الخادم
        if not self.start_server():
            self.log_error("فشل في بدء الخادم الذكي!")
            return False
        
        # بدء مراقبة الصحة
        health_thread = threading.Thread(target=self.health_monitor_loop)
        health_thread.daemon = True
        health_thread.start()
        
        self.log_success("الخادم الذكي يعمل بأقصى استقرار!")
        self.log_info("يمكن الوصول إليه من أي جهاز في الشبكة")
        self.log_info("اضغط Ctrl+C للإيقاف الآمن")
        
        try:
            while self.is_running:
                time.sleep(1)
                
                if self.server_process and self.server_process.poll() is not None:
                    return_code = self.server_process.returncode
                    if return_code != 0:
                        self.log_error(f"الخادم توقف بكود خطأ: {return_code}")
                        if not self.restart_server():
                            break
                    else:
                        self.log_info("الخادم توقف بشكل طبيعي")
                        break
                        
        except KeyboardInterrupt:
            self.log_info("تم الضغط على Ctrl+C")
        except Exception as e:
            self.log_error(f"خطأ في الحلقة الرئيسية: {e}")
        finally:
            self.stop_server()
            self.log_success("تم إنهاء الخادم الذكي")
        
        return True

def main():
    """الدالة الرئيسية"""
    server = SmartServer()
    success = server.run()
    
    if success:
        print("✅ تم تشغيل الخادم الذكي بنجاح!")
    else:
        print("❌ فشل في تشغيل الخادم الذكي!")
        sys.exit(1)

if __name__ == "__main__":
    main()
