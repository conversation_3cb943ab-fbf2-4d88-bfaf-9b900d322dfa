{% load rest_framework %}

<div class="row intro">
<div class="col-md-6 intro-title">
    <h1>{{ document.title }}</h1>
    {% if document.description %}
    <p>{% render_markdown document.description %}</p>
    {% endif %}
</div>
<div class="col-md-6 intro-code">
    {% for html in lang_intro_htmls %}
        {% include html %}
    {% endfor %}
</div>
</div>
{% if document|data %}
{% for section_key, section in document|data|items %}
{% if section_key %}
    <h2 id="{{ section_key }}" class="coredocs-section-title">{{ section_key }} <a href="#{{ section_key }}"><i class="fa fa-link" aria-hidden="true"></i>
</a></h2>
{% endif %}

    {% for link_key, link in section|schema_links|items %}
        {% include "rest_framework/docs/link.html" %}
    {% endfor %}
{% endfor %}

{% for link_key, link in document.links|items %}
    {% include "rest_framework/docs/link.html" %}
{% endfor %}
{% endif %}
