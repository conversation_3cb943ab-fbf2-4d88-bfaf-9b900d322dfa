/**
 * تحكم متقدم في الطباعة
 * Advanced Print Controls
 */

// إعدادات الطباعة المتقدمة
const PrintSettings = {
    // إعدادات افتراضية
    defaults: {
        orientation: 'portrait',
        paperSize: 'A4',
        margins: '1.5cm',
        fontSize: '11pt',
        showHeader: true,
        showFooter: true,
        showPageNumbers: true,
        companyName: 'شركة [اسم الشركة]',
        systemName: 'نظام إدارة الموارد البشرية'
    },
    
    // إعدادات مخصصة لكل نوع تقرير
    reportTypes: {
        employee: {
            title: 'تقرير الموظفين',
            headerColor: '#3498db',
            orientation: 'portrait'
        },
        attendance: {
            title: 'تقرير الحضور والانصراف',
            headerColor: '#e74c3c',
            orientation: 'landscape'
        },
        payroll: {
            title: 'كشف المرتبات',
            headerColor: '#27ae60',
            orientation: 'landscape'
        },
        overtime: {
            title: 'تقرير العمل الإضافي',
            headerColor: '#f39c12',
            orientation: 'portrait'
        }
    }
};

// دالة إنشاء نافذة طباعة مخصصة
function createCustomPrintWindow(content, options = {}) {
    const settings = { ...PrintSettings.defaults, ...options };
    const reportType = settings.reportType || 'general';
    const reportSettings = PrintSettings.reportTypes[reportType] || {};
    
    const printWindow = window.open('', '_blank', 'width=1200,height=800');
    
    const htmlContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${settings.title || reportSettings.title || 'طباعة'}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                @media screen {
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                        background: #f8f9fa;
                    }
                    
                    .print-preview {
                        background: white;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        max-width: 210mm;
                        margin: 0 auto;
                    }
                    
                    .print-controls {
                        position: fixed;
                        top: 20px;
                        left: 20px;
                        background: white;
                        padding: 15px;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        z-index: 1000;
                    }
                    
                    .print-controls button {
                        margin: 5px;
                        padding: 8px 15px;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        font-family: 'Cairo', Arial, sans-serif;
                    }
                    
                    .btn-print {
                        background: #27ae60;
                        color: white;
                    }
                    
                    .btn-close {
                        background: #e74c3c;
                        color: white;
                    }
                    
                    .btn-settings {
                        background: #3498db;
                        color: white;
                    }
                }
                
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                    
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        font-size: ${settings.fontSize};
                        line-height: 1.5;
                        color: #2c3e50;
                        margin: 0;
                        padding: 0;
                        direction: rtl;
                        background: white;
                    }
                    
                    .print-controls {
                        display: none !important;
                    }
                    
                    .print-preview {
                        background: white;
                        padding: 0;
                        border-radius: 0;
                        box-shadow: none;
                        max-width: none;
                        margin: 0;
                    }
                    
                    .print-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border: 3px solid ${reportSettings.headerColor || '#2c3e50'};
                        border-radius: 15px;
                        padding: 30px;
                        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                        page-break-inside: avoid;
                    }
                    
                    .company-info {
                        background: linear-gradient(135deg, ${reportSettings.headerColor || '#2c3e50'}, #34495e);
                        color: white;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 20px;
                    }
                    
                    .report-title {
                        font-size: 24pt;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 15px;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    }
                    
                    .report-date {
                        font-size: 12pt;
                        color: #7f8c8d;
                        font-weight: 400;
                    }
                    
                    .print-footer {
                        position: fixed;
                        bottom: 20px;
                        left: 0;
                        right: 0;
                        text-align: center;
                        font-size: 10pt;
                        color: #7f8c8d;
                        border-top: 1px solid #bdc3c7;
                        padding-top: 10px;
                    }
                    
                    @page {
                        margin: ${settings.margins};
                        size: ${settings.paperSize} ${settings.orientation};
                    }
                    
                    .page-break {
                        page-break-before: always;
                    }
                    
                    .no-break {
                        page-break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-controls">
                <button class="btn-print" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn-settings" onclick="showPrintSettings()">
                    <i class="fas fa-cog"></i> إعدادات
                </button>
                <button class="btn-close" onclick="window.close()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
            
            <div class="print-preview">
                ${settings.showHeader ? `
                    <div class="print-header">
                        <div class="company-info">
                            <div style="font-size: 20pt; font-weight: 700; margin-bottom: 10px;">
                                ${settings.systemName}
                            </div>
                            <div style="font-size: 14pt; margin-bottom: 10px;">
                                ${settings.companyName}
                            </div>
                        </div>
                        <div class="report-title">${settings.title || reportSettings.title || 'تقرير'}</div>
                        <div class="report-date">
                            تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}
                        </div>
                    </div>
                ` : ''}
                
                ${content}
                
                ${settings.showFooter ? `
                    <div class="print-footer">
                        <div>${settings.systemName} - جميع الحقوق محفوظة © ${new Date().getFullYear()}</div>
                        ${settings.showPageNumbers ? '<div class="page-number"></div>' : ''}
                    </div>
                ` : ''}
            </div>
            
            <script>
                function showPrintSettings() {
                    alert('إعدادات الطباعة - قريباً');
                }
                
                // طباعة تلقائية بعد التحميل (اختياري)
                ${settings.autoPrint ? 'window.onload = () => setTimeout(() => window.print(), 1000);' : ''}
            </script>
        </body>
        </html>
    `;
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    return printWindow;
}

// دالة طباعة محسنة للجداول
function printEnhancedTable(tableSelector, options = {}) {
    const table = document.querySelector(tableSelector);
    if (!table) {
        console.error('الجدول غير موجود:', tableSelector);
        return;
    }
    
    const tableHTML = table.outerHTML;
    const settings = {
        title: options.title || 'تقرير جدولي',
        reportType: options.reportType || 'general',
        showHeader: true,
        showFooter: true,
        orientation: 'landscape',
        ...options
    };
    
    const enhancedContent = `
        <div class="table-container">
            ${tableHTML}
        </div>
        
        <style>
            .table-container {
                overflow-x: auto;
            }
            
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                font-size: 9pt !important;
            }
            
            table th {
                background: linear-gradient(135deg, #34495e, #2c3e50) !important;
                color: white !important;
                border: 1px solid #2c3e50 !important;
                padding: 8px 6px !important;
                text-align: center !important;
                font-weight: 600 !important;
            }
            
            table td {
                border: 1px solid #bdc3c7 !important;
                padding: 6px 4px !important;
                text-align: center !important;
            }
            
            table tbody tr:nth-child(even) {
                background-color: #f8f9fa !important;
            }
        </style>
    `;
    
    createCustomPrintWindow(enhancedContent, settings);
}

// دالة طباعة محسنة للنماذج
function printEnhancedForm(formSelector, options = {}) {
    const form = document.querySelector(formSelector);
    if (!form) {
        console.error('النموذج غير موجود:', formSelector);
        return;
    }
    
    const formData = new FormData(form);
    let formHTML = '<div class="form-print">';
    
    for (let [key, value] of formData.entries()) {
        const label = form.querySelector(`label[for="${key}"]`)?.textContent || key;
        formHTML += `
            <div class="form-field">
                <label class="field-label">${label}:</label>
                <span class="field-value">${value}</span>
            </div>
        `;
    }
    
    formHTML += '</div>';
    
    const settings = {
        title: options.title || 'طباعة النموذج',
        reportType: options.reportType || 'general',
        ...options
    };
    
    const enhancedContent = `
        ${formHTML}
        
        <style>
            .form-print {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin: 20px 0;
            }
            
            .form-field {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                background: #f8f9fa;
            }
            
            .field-label {
                font-weight: 600;
                color: #34495e;
                display: block;
                margin-bottom: 8px;
            }
            
            .field-value {
                color: #2c3e50;
                font-size: 12pt;
                border-bottom: 1px solid #bdc3c7;
                padding-bottom: 5px;
                display: block;
            }
        </style>
    `;
    
    createCustomPrintWindow(enhancedContent, settings);
}

// دالة طباعة سريعة مع معاينة
function quickPrintPreview(content, title = 'معاينة الطباعة') {
    const settings = {
        title: title,
        showHeader: true,
        showFooter: true,
        autoPrint: false
    };
    
    createCustomPrintWindow(content, settings);
}

// تصدير الدوال للاستخدام العام
window.PrintSettings = PrintSettings;
window.createCustomPrintWindow = createCustomPrintWindow;
window.printEnhancedTable = printEnhancedTable;
window.printEnhancedForm = printEnhancedForm;
window.quickPrintPreview = quickPrintPreview;
