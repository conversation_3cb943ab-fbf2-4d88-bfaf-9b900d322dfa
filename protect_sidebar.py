#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حماية القائمة الجانبية من التغيير
"""

import os
import hashlib
import shutil
from datetime import datetime

def get_file_hash(file_path):
    """حساب hash للملف"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def backup_file(file_path):
    """إنشاء نسخة احتياطية من الملف"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def protect_sidebar():
    """حماية القائمة الجانبية"""
    base_file = "templates/base/base.html"
    
    if os.path.exists(base_file):
        print("🛡️ حماية القائمة الجانبية...")
        
        # إنشاء نسخة احتياطية
        backup_path = backup_file(base_file)
        
        # حساب hash الحالي
        current_hash = get_file_hash(base_file)
        print(f"📊 Hash الحالي: {current_hash}")
        
        # حفظ hash في ملف
        with open("sidebar_hash.txt", "w", encoding='utf-8') as f:
            f.write(f"{current_hash}\n")
            f.write(f"Protection Date: {datetime.now()}\n")
            f.write("Sidebar protected against changes\n")
        
        print("✅ تم حفظ hash القائمة الجانبية")
        print("🔒 القائمة الجانبية محمية الآن")
        
        return current_hash
    else:
        print("❌ ملف القائمة الجانبية غير موجود")
        return None

def check_sidebar_integrity():
    """فحص سلامة القائمة الجانبية"""
    base_file = "templates/base/base.html"
    hash_file = "sidebar_hash.txt"
    
    if not os.path.exists(hash_file):
        print("⚠️ ملف hash غير موجود - تشغيل الحماية أولاً")
        return False
    
    # قراءة hash المحفوظ
    with open(hash_file, "r", encoding='utf-8') as f:
        saved_hash = f.readline().strip()
    
    # حساب hash الحالي
    current_hash = get_file_hash(base_file)
    
    if saved_hash == current_hash:
        print("✅ القائمة الجانبية سليمة ولم تتغير")
        return True
    else:
        print("🚨 تحذير: القائمة الجانبية تم تغييرها!")
        print(f"Hash المحفوظ: {saved_hash}")
        print(f"Hash الحالي: {current_hash}")
        return False

if __name__ == "__main__":
    print("🛡️ نظام حماية القائمة الجانبية")
    print("="*50)
    
    # حماية القائمة
    protect_sidebar()
    
    print("\n" + "="*50)
    print("✅ تم تطبيق الحماية بنجاح!")
    print("💡 استخدم check_sidebar_integrity() للفحص الدوري")
