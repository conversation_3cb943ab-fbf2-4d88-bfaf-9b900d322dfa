from django import forms
from django.forms import inlineformset_factory
from django.contrib.auth.models import User
from .models import (StockIncrease, StockIncreaseItem, StockDecrease, StockDecreaseItem,
GoodsReceivedOnLoan, GoodsReceivedOnLoanItem,
GoodsIssuedOnLoan, GoodsIssuedOnLoanItem,
WarehouseTransfer, WarehouseTransferItem,
ItemTransformation, ItemTransformationInput, ItemTransformationOutput,
ManufacturingOrder, ManufacturingMaterial, ManufacturingStep, QualityControl)
from definitions.models import Warehouse, Item, ItemCategory, ItemType
from django.utils import timezone


class GroupedItemSelect(forms.Select):
    """Widget مخصص لعرض الأصناف مجمعة حسب الفئات"""
    
    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex, attrs)
        if value:
            try:
                item = Item.objects.get(pk=value)
                if item.category:
                    option['attrs']['data-category'] = item.category.name
            except Item.DoesNotExist:
                pass
        return option
    
    def optgroups(self, name, value, attrs=None):
        """تجميع الخيارات حسب الفئات"""
        groups = []
        
        # إضافة خيار فارغ
        if self.allow_multiple_selected:
            default = (None, [], 0)
        else:
            default = (None, [self.create_option(name, '', self.choices.field.empty_label or '', False, 0)], 0)
        groups.append(default)
        
        # تجميع الأصناف حسب الفئات
        categories = ItemCategory.objects.filter(is_active=True).order_by('name')
        
        for category in categories:
            category_items = []
            items = Item.objects.filter(category=category, is_active=True).order_by('name')
            
            if items.exists():
                subindex = 0
                for item in items:
                    selected = str(item.pk) in value if value else False
                    option = self.create_option(
                        name, item.pk, str(item), selected, 
                        len(groups), subindex
                    )
                    category_items.append(option)
                    subindex += 1
                
                if category_items:
                    groups.append((category.name, category_items, len(groups)))
        
        # إضافة الأصناف بدون فئة
        uncategorized_items = []
        items_without_category = Item.objects.filter(category__isnull=True, is_active=True).order_by('name')
        
        if items_without_category.exists():
            subindex = 0
            for item in items_without_category:
                selected = str(item.pk) in value if value else False
                option = self.create_option(
                    name, item.pk, str(item), selected, 
                    len(groups), subindex
                )
                uncategorized_items.append(option)
                subindex += 1
            
            if uncategorized_items:
                groups.append(("أصناف أخرى", uncategorized_items, len(groups)))
        
        return groups


# ========== نماذج التصنيع ==========

class ManufacturingOrderForm(forms.ModelForm):
    """نموذج إنشاء أمر تصنيع متطور"""
    
    # حقول إضافية للتحكم
    auto_calculate_costs = forms.BooleanField(
        required=False,
        initial=True,
        label="حساب التكاليف تلقائياً",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'id': 'auto_calculate_costs'
        })
    )
    
    estimated_total_cost = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        label="التكلفة الإجمالية المقدرة",
        widget=forms.NumberInput(attrs={
            'class': 'form-control total-cost-display',
            'readonly': 'readonly',
            'placeholder': 'سيتم الحساب تلقائياً'
        })
    )
    
    class Meta:
        model = ManufacturingOrder
        fields = [
            'finished_product', 'quantity_to_produce', 'finished_goods_warehouse',
            'expected_start_date', 'expected_completion_date', 'production_line', 'shift', 'priority',
            'labor_cost', 'overhead_cost', 'packaging_cost', 'quality_control_cost', 'operating_expenses',
            'electricity_cost', 'transportation_cost', 'other_costs', 'quality_standard', 'quality_notes',
            'supervisor', 'internal_notes', 'notes'
        ]
        widgets = {
            'finished_product': forms.Select(attrs={
                'class': 'form-select select2 product-select',
                'data-placeholder': 'اختر المنتج النهائي',
                'data-search': 'true',
                'title': 'اختر المنتج النهائي الذي سيتم إنتاجه في هذا الأمر'
            }),
            'quantity_to_produce': forms.NumberInput(attrs={
                'class': 'form-control quantity-input',
                'min': '0.001',
                'step': '0.001',
                'placeholder': 'أدخل الكمية المطلوبة (مثال: 1000)',
                'title': 'الكمية النهائية التي سيتم إنتاجها'
            }),
            'finished_goods_warehouse': forms.Select(attrs={
                'class': 'form-select select2 warehouse-select',
                'data-placeholder': 'اختر مخزن المنتجات النهائية',
                'title': 'حدد المخزن الذي سيتم تخزين المنتج النهائي فيه'
            }),
            'expected_start_date': forms.DateInput(attrs={
                'class': 'form-control date-input',
                'type': 'date',
                'min': 'today',
                'title': 'تاريخ بداية الإنتاج المتوقع'
            }),
            'expected_completion_date': forms.DateInput(attrs={
                'class': 'form-control date-input',
                'type': 'date',
                'min': 'today',
                'title': 'تاريخ انتهاء الإنتاج المتوقع'
            }),
            'production_line': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم خط الإنتاج (مثال: خط إنتاج 1)',
                'title': 'حدد خط الإنتاج المسؤول عن الأمر'
            }),
            'shift': forms.Select(attrs={
                'class': 'form-select shift-select',
                'title': 'حدد الوردية (صباحية/مسائية/ليلية)'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select priority-select',
                'title': 'حدد أولوية تنفيذ الأمر'
            }),
            'labor_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكلفة العمالة (مثال: 5000 ج.م)',
                'data-cost-type': 'labor',
                'title': 'أدخل تكلفة العمالة المتوقعة لهذا الأمر'
            }),
            'overhead_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'التكاليف الإدارية (مثال: 2000 ج.م)',
                'data-cost-type': 'overhead',
                'title': 'أدخل التكاليف الإدارية المرتبطة بالأمر'
            }),
            'packaging_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكلفة التغليف (مثال: 1000 ج.م)',
                'data-cost-type': 'packaging',
                'title': 'أدخل تكلفة التغليف للمنتج النهائي'
            }),
            'quality_control_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكلفة مراقبة الجودة (مثال: 500 ج.م)',
                'data-cost-type': 'quality',
                'title': 'أدخل تكلفة مراقبة الجودة لهذا الأمر'
            }),
            'operating_expenses': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'المصروفات التشغيلية (مثال: 1500 ج.م)',
                'data-cost-type': 'operating',
                'title': 'أدخل المصروفات التشغيلية المرتبطة بالأمر'
            }),
            'electricity_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكلفة الكهرباء (مثال: 800 ج.م)',
                'data-cost-type': 'electricity',
                'title': 'أدخل تكلفة الكهرباء المتوقعة'
            }),
            'transportation_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكلفة النقل (مثال: 600 ج.م)',
                'data-cost-type': 'transportation',
                'title': 'أدخل تكلفة النقل للمنتج النهائي'
            }),
            'other_costs': forms.NumberInput(attrs={
                'class': 'form-control cost-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'تكاليف أخرى (مثال: 300 ج.م)',
                'data-cost-type': 'other',
                'title': 'أدخل أي تكاليف إضافية أخرى'
            }),
            'quality_standard': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'معيار الجودة المطلوب (مثال: ISO 9001)',
                'title': 'حدد معيار الجودة المطلوب للمنتج النهائي'
            }),
            'quality_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات خاصة بالجودة والمتطلبات',
                'title': 'أضف أي ملاحظات أو متطلبات جودة إضافية'
            }),
            'supervisor': forms.Select(attrs={
                'class': 'form-select select2 supervisor-select',
                'data-placeholder': 'اختر المشرف المسؤول',
                'title': 'حدد المشرف المسؤول عن متابعة الأمر'
            }),
            'internal_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات داخلية (للاستخدام الإداري فقط)',
                'title': 'أضف أي ملاحظات داخلية تخص الإدارة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات عامة (أي تفاصيل إضافية)',
                'title': 'أضف أي ملاحظات عامة تخص الأمر'
            }),
        }
        help_texts = {
            'finished_product': 'اختر المنتج النهائي الذي سيتم إنتاجه في هذا الأمر.',
            'quantity_to_produce': 'أدخل الكمية النهائية التي سيتم إنتاجها (مثال: 1000).',
            'finished_goods_warehouse': 'حدد المخزن الذي سيتم تخزين المنتج النهائي فيه.',
            'expected_start_date': 'حدد تاريخ بداية الإنتاج المتوقع.',
            'expected_completion_date': 'حدد تاريخ انتهاء الإنتاج المتوقع.',
            'production_line': 'حدد خط الإنتاج المسؤول عن تنفيذ الأمر.',
            'shift': 'حدد الوردية المناسبة (صباحية/مسائية/ليلية/يوم كامل).',
            'priority': 'حدد أولوية تنفيذ الأمر (منخفض/متوسط/عالي/عاجل).',
            'labor_cost': 'أدخل تكلفة العمالة المتوقعة لهذا الأمر.',
            'overhead_cost': 'أدخل التكاليف الإدارية المرتبطة بالأمر.',
            'packaging_cost': 'أدخل تكلفة التغليف للمنتج النهائي.',
            'quality_control_cost': 'أدخل تكلفة مراقبة الجودة لهذا الأمر.',
            'operating_expenses': 'أدخل المصروفات التشغيلية المرتبطة بالأمر.',
            'electricity_cost': 'أدخل تكلفة الكهرباء المتوقعة.',
            'transportation_cost': 'أدخل تكلفة النقل للمنتج النهائي.',
            'other_costs': 'أدخل أي تكاليف إضافية أخرى.',
            'quality_standard': 'حدد معيار الجودة المطلوب للمنتج النهائي (مثال: ISO 9001).',
            'quality_notes': 'أضف أي ملاحظات أو متطلبات جودة إضافية.',
            'supervisor': 'حدد المشرف المسؤول عن متابعة الأمر.',
            'internal_notes': 'أضف أي ملاحظات داخلية تخص الإدارة.',
            'notes': 'أضف أي ملاحظات عامة تخص الأمر.'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد المنتجات النهائية ونصف المصنعة فقط
        self.fields['finished_product'].queryset = Item.objects.filter(
            item_type__in=['FINISHED_PRODUCT', 'SEMI_FINISHED'],
            is_active=True
        ).order_by('name')
        
        # تحديد المخازن النشطة
        self.fields['finished_goods_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        
        # تحديد المستخدمين النشطين
        self.fields['supervisor'].queryset = User.objects.filter(is_active=True).order_by('username')
        
        # تحديد خيارات الوردية
        self.fields['shift'].choices = [
            ('', 'اختر الوردية'),
            ('MORNING', 'صباحية'),
            ('AFTERNOON', 'مسائية'),
            ('NIGHT', 'ليلية'),
            ('FULL_DAY', 'يوم كامل')
        ]
        
        # تحديد خيارات الأولوية
        self.fields['priority'].choices = [
            ('', 'اختر الأولوية'),
            ('LOW', 'منخفض'),
            ('MEDIUM', 'متوسط'),
            ('HIGH', 'عالي'),
            ('URGENT', 'عاجل')
        ]
        
        # إضافة labels مخصصة
        self.fields['finished_product'].label = "المنتج النهائي"
        self.fields['quantity_to_produce'].label = "الكمية المطلوبة"
        self.fields['finished_goods_warehouse'].label = "مخزن المنتجات النهائية"
        self.fields['expected_start_date'].label = "تاريخ بداية الإنتاج المتوقع"
        self.fields['expected_completion_date'].label = "تاريخ انتهاء الإنتاج المتوقع"
        self.fields['production_line'].label = "خط الإنتاج"
        self.fields['shift'].label = "الوردية"
        self.fields['priority'].label = "الأولوية"
        self.fields['labor_cost'].label = "تكلفة العمالة"
        self.fields['overhead_cost'].label = "التكاليف الإدارية"
        self.fields['packaging_cost'].label = "تكلفة التغليف"
        self.fields['quality_control_cost'].label = "تكلفة مراقبة الجودة"
        self.fields['operating_expenses'].label = "المصروفات التشغيلية"
        self.fields['electricity_cost'].label = "تكلفة الكهرباء"
        self.fields['transportation_cost'].label = "تكلفة النقل"
        self.fields['other_costs'].label = "تكاليف أخرى"
        self.fields['quality_standard'].label = "معيار الجودة"
        self.fields['quality_notes'].label = "ملاحظات الجودة"
        self.fields['supervisor'].label = "المشرف المسؤول"
        self.fields['internal_notes'].label = "ملاحظات داخلية"
        self.fields['notes'].label = "ملاحظات عامة"
        
        # تعيين القيم الافتراضية للتواريخ
        if not self.instance.pk:  # إذا كان إنشاء جديد
            from datetime import date, timedelta
            today = date.today()
            self.fields['expected_start_date'].initial = today
            self.fields['expected_completion_date'].initial = today + timedelta(days=7)
    
    def clean(self):
        cleaned_data = super().clean()
        
        # التحقق من التواريخ
        start_date = cleaned_data.get('expected_start_date')
        completion_date = cleaned_data.get('expected_completion_date')
        
        if start_date and completion_date:
            if start_date > completion_date:
                raise forms.ValidationError("تاريخ بداية الإنتاج يجب أن يكون قبل تاريخ الانتهاء")
            
            if start_date < timezone.now().date():
                raise forms.ValidationError("تاريخ بداية الإنتاج لا يمكن أن يكون في الماضي")
        
        # التحقق من الكمية
        quantity = cleaned_data.get('quantity_to_produce')
        if quantity and quantity <= 0:
            raise forms.ValidationError("الكمية المطلوبة يجب أن تكون أكبر من صفر")
        
        # حساب التكلفة الإجمالية
        total_cost = 0
        cost_fields = [
            'labor_cost', 'overhead_cost', 'packaging_cost', 'quality_control_cost',
            'operating_expenses', 'electricity_cost', 'transportation_cost', 'other_costs'
        ]
        
        for field in cost_fields:
            value = cleaned_data.get(field)
            if value:
                total_cost += value
        
        cleaned_data['estimated_total_cost'] = total_cost
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # حساب التكلفة الإجمالية
        total_cost = 0
        cost_fields = [
            'labor_cost', 'overhead_cost', 'packaging_cost', 'quality_control_cost',
            'operating_expenses', 'electricity_cost', 'transportation_cost', 'other_costs'
        ]
        
        for field in cost_fields:
            value = getattr(instance, field, 0) or 0
            total_cost += value
        
        # يمكن إضافة حقل total_cost للموديل إذا كان موجوداً
        if hasattr(instance, 'total_cost'):
            instance.total_cost = total_cost
        
        if commit:
            instance.save()
        
        return instance


class ManufacturingMaterialForm(forms.ModelForm):
    """نموذج إضافة مادة خام لأمر التصنيع متطور"""
    
    # حقول إضافية للتحكم
    check_availability = forms.BooleanField(
        required=False,
        initial=True,
        label="التحقق من توفر المادة",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'id': 'check_availability'
        })
    )
    
    material_info = forms.CharField(
        required=False,
        label="معلومات المادة",
        widget=forms.TextInput(attrs={
            'class': 'form-control material-info-display',
            'readonly': 'readonly',
            'placeholder': 'سيتم عرض معلومات المادة تلقائياً'
        })
    )
    
    class Meta:
        model = ManufacturingMaterial
        fields = ['material', 'available_quantity', 'quantity_required', 'unit_cost', 'total_cost', 'notes']
        widgets = {
            'material': forms.Select(attrs={
                'class': 'form-select material-select',
                'data-placeholder': 'اختر المادة الخام',
                'data-search': 'true'
            }),
            'available_quantity': forms.NumberInput(attrs={
                'class': 'form-control available-quantity-input',
                'readonly': 'readonly',
                'min': '0',
                'step': '0.001',
                'placeholder': 'سيتم تحديثها تلقائياً'
            }),
            'quantity_required': forms.NumberInput(attrs={
                'class': 'form-control quantity-input',
                'min': '0.001',
                'step': '0.001',
                'placeholder': 'أدخل الكمية المطلوبة'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control cost-input currency-input',
                'min': '0',
                'step': '0.01',
                'placeholder': '0.00 ج.م',
                'data-cost-type': 'unit'
            }),
            'total_cost': forms.NumberInput(attrs={
                'class': 'form-control total-cost-input currency-input',
                'readonly': 'readonly',
                'min': '0',
                'step': '0.01',
                'placeholder': 'سيتم الحساب تلقائياً'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات خاصة بالمادة'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد المواد الخام فقط
        self.fields['material'].queryset = Item.objects.filter(
            item_type='RAW_MATERIAL',
            is_active=True
        ).order_by('name')
        
        # إضافة labels مخصصة
        self.fields['material'].label = "المادة الخام"
        self.fields['available_quantity'].label = "الكمية المتوفرة"
        self.fields['quantity_required'].label = "الكمية المطلوبة"
        self.fields['unit_cost'].label = "سعر الوحدة"
        self.fields['total_cost'].label = "إجمالي التكلفة"
        self.fields['notes'].label = "ملاحظات"
        
        # إضافة help text
        self.fields['material'].help_text = "اختر المادة الخام المطلوبة للإنتاج"
        self.fields['quantity_required'].help_text = "أدخل الكمية المطلوبة من هذه المادة"
        self.fields['unit_cost'].help_text = "سعر الوحدة الواحدة من المادة"
        self.fields['notes'].help_text = "ملاحظات إضافية حول استخدام هذه المادة"
    
    def clean(self):
        cleaned_data = super().clean()
        
        material = cleaned_data.get('material')
        quantity_required = cleaned_data.get('quantity_required')
        available_quantity = cleaned_data.get('available_quantity')
        unit_cost = cleaned_data.get('unit_cost')
        
        # التحقق من اختيار المادة
        if not material:
            raise forms.ValidationError("يجب اختيار المادة الخام")
        
        # التحقق من الكمية المطلوبة
        if quantity_required and quantity_required <= 0:
            raise forms.ValidationError("الكمية المطلوبة يجب أن تكون أكبر من صفر")
        
        # التحقق من توفر الكمية
        if quantity_required and available_quantity:
            if quantity_required > available_quantity:
                raise forms.ValidationError(
                    f"الكمية المطلوبة ({quantity_required}) أكبر من الكمية المتوفرة ({available_quantity})"
                )
        
        # التحقق من سعر الوحدة
        if unit_cost and unit_cost < 0:
            raise forms.ValidationError("سعر الوحدة لا يمكن أن يكون سالباً")
        
        # حساب التكلفة الإجمالية
        if quantity_required and unit_cost:
            total_cost = quantity_required * unit_cost
            cleaned_data['total_cost'] = total_cost
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # حساب التكلفة الإجمالية
        if instance.quantity_required and instance.unit_cost:
            instance.total_cost = instance.quantity_required * instance.unit_cost
        
        if commit:
            instance.save()
        
        return instance


# إنشاء Formset للمواد الخام
ManufacturingMaterialFormSet = inlineformset_factory(
    ManufacturingOrder, ManufacturingMaterial, form=ManufacturingMaterialForm,
    extra=1, min_num=0, validate_min=False, can_delete=True
)


class ManufacturingStepForm(forms.ModelForm):
    """نموذج خطوة التصنيع متطور"""
    
    # حقول إضافية للتحكم
    estimated_hours = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        label="المدة بالساعات",
        widget=forms.NumberInput(attrs={
            'class': 'form-control hours-input',
            'min': '0',
            'step': '0.5',
            'placeholder': '0.0'
        })
    )
    
    step_dependencies = forms.CharField(
        required=False,
        label="الخطوات المطلوبة مسبقاً",
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أرقام الخطوات المطلوبة مسبقاً (مثال: 1,2)'
        })
    )
    
    class Meta:
        model = ManufacturingStep
        fields = ['step_number', 'step_name', 'description', 'estimated_duration', 'assigned_to', 'notes']
        widgets = {
            'step_number': forms.NumberInput(attrs={
                'class': 'form-control step-number-input',
                'min': '1',
                'step': '1',
                'placeholder': 'رقم الخطوة'
            }),
            'step_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الخطوة (مثال: تحضير المواد)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف تفصيلي للخطوة والمتطلبات'
            }),
            'estimated_duration': forms.NumberInput(attrs={
                'class': 'form-control duration-input',
                'min': '0',
                'step': '0.5',
                'placeholder': 'المدة بالدقائق'
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-select select2 assigned-select',
                'data-placeholder': 'اختر الموظف المسؤول'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات خاصة بالخطوة'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد المستخدمين النشطين
        self.fields['assigned_to'].queryset = User.objects.filter(is_active=True).order_by('username')
        
        # إضافة labels مخصصة
        self.fields['step_number'].label = "رقم الخطوة"
        self.fields['step_name'].label = "اسم الخطوة"
        self.fields['description'].label = "وصف الخطوة"
        self.fields['estimated_duration'].label = "المدة المتوقعة (دقائق)"
        self.fields['assigned_to'].label = "الموظف المسؤول"
        self.fields['notes'].label = "ملاحظات"
        
        # إضافة help text
        self.fields['step_number'].help_text = "ترتيب الخطوة في عملية التصنيع"
        self.fields['step_name'].help_text = "اسم واضح ومختصر للخطوة"
        self.fields['description'].help_text = "وصف تفصيلي لما سيتم عمله في هذه الخطوة"
        self.fields['estimated_duration'].help_text = "الوقت المتوقع لإنجاز هذه الخطوة بالدقائق"
        self.fields['assigned_to'].help_text = "الموظف المسؤول عن تنفيذ هذه الخطوة"
        self.fields['notes'].help_text = "ملاحظات إضافية أو تعليمات خاصة"
    
    def clean(self):
        cleaned_data = super().clean()
        
        step_number = cleaned_data.get('step_number')
        step_name = cleaned_data.get('step_name')
        estimated_duration = cleaned_data.get('estimated_duration')
        estimated_hours = cleaned_data.get('estimated_hours')
        
        # التحقق من رقم الخطوة
        if step_number and step_number <= 0:
            raise forms.ValidationError("رقم الخطوة يجب أن يكون أكبر من صفر")
        
        # التحقق من اسم الخطوة
        if not step_name or len(step_name.strip()) < 3:
            raise forms.ValidationError("اسم الخطوة يجب أن يكون 3 أحرف على الأقل")
        
        # التحقق من المدة
        if estimated_duration and estimated_duration < 0:
            raise forms.ValidationError("المدة المتوقعة لا يمكن أن تكون سالبة")
        
        # تحويل الساعات إلى دقائق إذا تم إدخالها
        if estimated_hours and not estimated_duration:
            cleaned_data['estimated_duration'] = estimated_hours * 60
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # تحويل الساعات إلى دقائق إذا لزم الأمر
        estimated_hours = self.cleaned_data.get('estimated_hours')
        if estimated_hours and not instance.estimated_duration:
            instance.estimated_duration = estimated_hours * 60
        
        if commit:
            instance.save()
        
        return instance


# إنشاء Formset لخطوات التصنيع
ManufacturingStepFormSet = inlineformset_factory(
    ManufacturingOrder, ManufacturingStep, form=ManufacturingStepForm,
    extra=1, min_num=0, validate_min=False, can_delete=True
)


class QualityControlForm(forms.ModelForm):
    """نموذج مراقبة الجودة متطور"""
    
    # حقول إضافية للتحكم
    tolerance_percentage = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        label="نسبة التسامح (%)",
        widget=forms.NumberInput(attrs={
            'class': 'form-control tolerance-percentage-input',
            'min': '0',
            'max': '100',
            'step': '0.1',
            'placeholder': '0.0'
        })
    )
    
    min_acceptable = forms.DecimalField(
        max_digits=15,
        decimal_places=3,
        required=False,
        label="الحد الأدنى المقبول",
        widget=forms.NumberInput(attrs={
            'class': 'form-control min-acceptable-input',
            'readonly': 'readonly',
            'placeholder': 'سيتم الحساب تلقائياً'
        })
    )
    
    max_acceptable = forms.DecimalField(
        max_digits=15,
        decimal_places=3,
        required=False,
        label="الحد الأقصى المقبول",
        widget=forms.NumberInput(attrs={
            'class': 'form-control max-acceptable-input',
            'readonly': 'readonly',
            'placeholder': 'سيتم الحساب تلقائياً'
        })
    )
    
    class Meta:
        model = QualityControl
        fields = ['check_number', 'check_name', 'standard_value', 'actual_value', 'tolerance', 'checked_by', 'notes']
        widgets = {
            'check_number': forms.NumberInput(attrs={
                'class': 'form-control check-number-input',
                'min': '1',
                'step': '1',
                'placeholder': 'رقم الفحص'
            }),
            'check_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم فحص الجودة (مثال: قياس الأبعاد)'
            }),
            'standard_value': forms.NumberInput(attrs={
                'class': 'form-control standard-value-input',
                'step': '0.001',
                'placeholder': 'القيمة القياسية المطلوبة'
            }),
            'actual_value': forms.NumberInput(attrs={
                'class': 'form-control actual-value-input',
                'step': '0.001',
                'placeholder': 'القيمة الفعلية المقاسة'
            }),
            'tolerance': forms.NumberInput(attrs={
                'class': 'form-control tolerance-input',
                'min': '0',
                'step': '0.001',
                'placeholder': 'قيمة التسامح المسموح'
            }),
            'checked_by': forms.Select(attrs={
                'class': 'form-select select2 checker-select',
                'data-placeholder': 'اختر الفاحص'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول نتائج الفحص'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد المستخدمين النشطين
        self.fields['checked_by'].queryset = User.objects.filter(is_active=True).order_by('username')
        
        # إضافة labels مخصصة
        self.fields['check_number'].label = "رقم الفحص"
        self.fields['check_name'].label = "اسم فحص الجودة"
        self.fields['standard_value'].label = "القيمة القياسية"
        self.fields['actual_value'].label = "القيمة الفعلية"
        self.fields['tolerance'].label = "قيمة التسامح"
        self.fields['checked_by'].label = "الموظف الفاحص"
        self.fields['notes'].label = "ملاحظات"
        
        # إضافة help text
        self.fields['check_number'].help_text = "ترتيب فحص الجودة في العملية"
        self.fields['check_name'].help_text = "اسم واضح لفحص الجودة المطلوب"
        self.fields['standard_value'].help_text = "القيمة المثالية أو القياسية المطلوبة"
        self.fields['actual_value'].help_text = "القيمة الفعلية التي تم قياسها"
        self.fields['tolerance'].help_text = "الانحراف المسموح عن القيمة القياسية"
        self.fields['checked_by'].help_text = "الموظف المسؤول عن إجراء الفحص"
        self.fields['notes'].help_text = "ملاحظات حول نتائج الفحص أو المشاكل المكتشفة"
    
    def clean(self):
        cleaned_data = super().clean()
        
        check_number = cleaned_data.get('check_number')
        check_name = cleaned_data.get('check_name')
        standard_value = cleaned_data.get('standard_value')
        actual_value = cleaned_data.get('actual_value')
        tolerance = cleaned_data.get('tolerance')
        tolerance_percentage = cleaned_data.get('tolerance_percentage')
        
        # التحقق من رقم الفحص
        if check_number and check_number <= 0:
            raise forms.ValidationError("رقم الفحص يجب أن يكون أكبر من صفر")
        
        # التحقق من اسم الفحص
        if not check_name or len(check_name.strip()) < 3:
            raise forms.ValidationError("اسم فحص الجودة يجب أن يكون 3 أحرف على الأقل")
        
        # التحقق من القيم
        if standard_value is not None and standard_value < 0:
            raise forms.ValidationError("القيمة القياسية لا يمكن أن تكون سالبة")
        
        if actual_value is not None and actual_value < 0:
            raise forms.ValidationError("القيمة الفعلية لا يمكن أن تكون سالبة")
        
        if tolerance is not None and tolerance < 0:
            raise forms.ValidationError("قيمة التسامح لا يمكن أن تكون سالبة")
        
        # حساب التسامح من النسبة المئوية
        if tolerance_percentage and standard_value and not tolerance:
            cleaned_data['tolerance'] = (standard_value * tolerance_percentage) / 100
        
        # حساب الحدود المقبولة
        if standard_value and tolerance:
            min_acceptable = standard_value - tolerance
            max_acceptable = standard_value + tolerance
            cleaned_data['min_acceptable'] = min_acceptable
            cleaned_data['max_acceptable'] = max_acceptable
            
            # التحقق من القيمة الفعلية
            if actual_value is not None:
                if actual_value < min_acceptable or actual_value > max_acceptable:
                    raise forms.ValidationError(
                        f"القيمة الفعلية ({actual_value}) خارج نطاق القبول "
                        f"({min_acceptable} - {max_acceptable})"
                    )
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # حساب الحدود المقبولة
        if instance.standard_value and instance.tolerance:
            instance.min_acceptable = instance.standard_value - instance.tolerance
            instance.max_acceptable = instance.standard_value + instance.tolerance
        
        if commit:
            instance.save()
        
        return instance


# إنشاء Formset لفحوصات الجودة
QualityControlFormSet = inlineformset_factory(
    ManufacturingOrder, QualityControl, form=QualityControlForm,
    extra=1, min_num=0, validate_min=False, can_delete=True
)


class ManufacturingOrderFilterForm(forms.Form):
    """نموذج تصفية أوامر التصنيع المتقدم"""
    
    # البحث النصي
    search = forms.CharField(
        required=False,
        label="بحث",
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في رقم الأمر، اسم المنتج، الوصف...'
        })
    )
    
    # تصفية حسب الحالة
    status = forms.ChoiceField(
        choices=[
            ('', 'جميع الحالات'),
            ('PENDING', 'في الانتظار'),
            ('IN_PROGRESS', 'قيد التنفيذ'),
            ('COMPLETED', 'مكتمل'),
            ('CANCELLED', 'ملغي'),
            ('ON_HOLD', 'معلق')
        ],
        required=False,
        label="الحالة",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # تصفية حسب الأولوية
    priority = forms.ChoiceField(
        choices=[
            ('', 'جميع الأولويات'),
            ('LOW', 'منخفضة'),
            ('MEDIUM', 'متوسطة'),
            ('HIGH', 'عالية'),
            ('URGENT', 'عاجلة')
        ],
        required=False,
        label="الأولوية",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # تصفية حسب المنتج
    product = forms.ModelChoiceField(
        queryset=Item.objects.filter(is_active=True),
        required=False,
        label="المنتج",
        widget=forms.Select(attrs={
            'class': 'form-select',
            'placeholder': 'اختر المنتج'
        })
    )
    
    # تصفية حسب الوردية
    shift = forms.ChoiceField(
        choices=[
            ('', 'جميع الورديات'),
            ('MORNING', 'صباحية'),
            ('EVENING', 'مسائية'),
            ('NIGHT', 'ليلية')
        ],
        required=False,
        label="الوردية",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # تصفية حسب التاريخ
    date_from = forms.DateField(
        required=False,
        label="من تاريخ",
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        label="إلى تاريخ",
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    # تصفية حسب التكلفة
    min_cost = forms.DecimalField(
        required=False,
        label="الحد الأدنى للتكلفة",
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'min': '0',
            'step': '0.01'
        })
    )
    
    max_cost = forms.DecimalField(
        required=False,
        label="الحد الأقصى للتكلفة",
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'min': '0',
            'step': '0.01'
        })
    )
    
    # تصفية حسب الكمية
    min_quantity = forms.IntegerField(
        required=False,
        label="الحد الأدنى للكمية",
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0',
            'min': '0'
        })
    )
    
    max_quantity = forms.IntegerField(
        required=False,
        label="الحد الأقصى للكمية",
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0',
            'min': '0'
        })
    )
    
    # خيارات إضافية
    show_completed = forms.BooleanField(
        required=False,
        initial=True,
        label="إظهار المكتملة",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    show_cancelled = forms.BooleanField(
        required=False,
        initial=False,
        label="إظهار الملغية",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    # ترتيب النتائج
    order_by = forms.ChoiceField(
        choices=[
            ('-created_at', 'الأحدث أولاً'),
            ('created_at', 'الأقدم أولاً'),
            ('-priority', 'الأولوية العالية أولاً'),
            ('priority', 'الأولوية المنخفضة أولاً'),
            ('-estimated_total_cost', 'الأعلى تكلفة أولاً'),
            ('estimated_total_cost', 'الأقل تكلفة أولاً'),
            ('-quantity_to_produce', 'الأعلى كمية أولاً'),
            ('quantity_to_produce', 'الأقل كمية أولاً')
        ],
        required=False,
        initial='-created_at',
        label="ترتيب النتائج",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        
        # التحقق من التواريخ
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise forms.ValidationError("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        
        # التحقق من التكاليف
        min_cost = cleaned_data.get('min_cost')
        max_cost = cleaned_data.get('max_cost')
        
        if min_cost and max_cost and min_cost > max_cost:
            raise forms.ValidationError("الحد الأدنى للتكلفة يجب أن يكون أقل من الحد الأقصى")
        
        # التحقق من الكميات
        min_quantity = cleaned_data.get('min_quantity')
        max_quantity = cleaned_data.get('max_quantity')
        
        if min_quantity and max_quantity and min_quantity > max_quantity:
            raise forms.ValidationError("الحد الأدنى للكمية يجب أن يكون أقل من الحد الأقصى")
        
        return cleaned_data


class ManufacturingReportForm(forms.Form):
    """نموذج تقرير أوامر التصنيع المتقدم"""
    
    # نوع التقرير
    report_type = forms.ChoiceField(
        choices=[
            ('summary', 'تقرير ملخص'),
            ('detailed', 'تقرير مفصل'),
            ('cost_analysis', 'تحليل التكاليف'),
            ('production_efficiency', 'كفاءة الإنتاج'),
            ('material_consumption', 'استهلاك المواد'),
            ('quality_control', 'مراقبة الجودة'),
            ('timeline', 'الجدول الزمني')
        ],
        required=True,
        initial='summary',
        label="نوع التقرير",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # الفترة الزمنية
    period_type = forms.ChoiceField(
        choices=[
            ('today', 'اليوم'),
            ('yesterday', 'أمس'),
            ('this_week', 'هذا الأسبوع'),
            ('last_week', 'الأسبوع الماضي'),
            ('this_month', 'هذا الشهر'),
            ('last_month', 'الشهر الماضي'),
            ('this_quarter', 'هذا الربع'),
            ('last_quarter', 'الربع الماضي'),
            ('this_year', 'هذا العام'),
            ('last_year', 'العام الماضي'),
            ('custom', 'فترة مخصصة')
        ],
        required=True,
        initial='this_month',
        label="الفترة الزمنية",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # الفترة المخصصة
    custom_date_from = forms.DateField(
        required=False,
        label="من تاريخ",
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    custom_date_to = forms.DateField(
        required=False,
        label="إلى تاريخ",
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    # تصفية حسب الحالة
    status_filter = forms.MultipleChoiceField(
        choices=[
            ('PENDING', 'في الانتظار'),
            ('IN_PROGRESS', 'قيد التنفيذ'),
            ('COMPLETED', 'مكتمل'),
            ('CANCELLED', 'ملغي'),
            ('ON_HOLD', 'معلق')
        ],
        required=False,
        initial=['PENDING', 'IN_PROGRESS', 'COMPLETED'],
        label="حالة الأوامر",
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )
    
    # تصفية حسب المنتج
    product_filter = forms.ModelMultipleChoiceField(
        queryset=Item.objects.filter(is_active=True),
        required=False,
        label="المنتجات",
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select'
        })
    )
    
    # تصفية حسب الوردية
    shift_filter = forms.MultipleChoiceField(
        choices=[
            ('MORNING', 'صباحية'),
            ('EVENING', 'مسائية'),
            ('NIGHT', 'ليلية')
        ],
        required=False,
        label="الورديات",
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )
    
    # خيارات التقرير
    include_costs = forms.BooleanField(
        required=False,
        initial=True,
        label="تضمين التكاليف",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    include_materials = forms.BooleanField(
        required=False,
        initial=True,
        label="تضمين المواد الخام",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    include_steps = forms.BooleanField(
        required=False,
        initial=False,
        label="تضمين خطوات التصنيع",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    include_quality = forms.BooleanField(
        required=False,
        initial=False,
        label="تضمين فحوصات الجودة",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    # تجميع البيانات
    group_by = forms.ChoiceField(
        choices=[
            ('none', 'بدون تجميع'),
            ('product', 'حسب المنتج'),
            ('status', 'حسب الحالة'),
            ('shift', 'حسب الوردية'),
            ('priority', 'حسب الأولوية'),
            ('date', 'حسب التاريخ'),
            ('week', 'حسب الأسبوع'),
            ('month', 'حسب الشهر')
        ],
        required=False,
        initial='none',
        label="تجميع البيانات",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # تنسيق التصدير
    export_format = forms.ChoiceField(
        choices=[
            ('html', 'HTML'),
            ('pdf', 'PDF'),
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('json', 'JSON')
        ],
        required=False,
        initial='html',
        label="تنسيق التصدير",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    # خيارات إضافية
    show_charts = forms.BooleanField(
        required=False,
        initial=True,
        label="إظهار الرسوم البيانية",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    show_totals = forms.BooleanField(
        required=False,
        initial=True,
        label="إظهار الإجماليات",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    show_percentages = forms.BooleanField(
        required=False,
        initial=True,
        label="إظهار النسب المئوية",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        
        # التحقق من الفترة المخصصة
        period_type = cleaned_data.get('period_type')
        custom_date_from = cleaned_data.get('custom_date_from')
        custom_date_to = cleaned_data.get('custom_date_to')
        
        if period_type == 'custom':
            if not custom_date_from:
                raise forms.ValidationError("يجب تحديد تاريخ البداية للفترة المخصصة")
            if not custom_date_to:
                raise forms.ValidationError("يجب تحديد تاريخ النهاية للفترة المخصصة")
            if custom_date_from > custom_date_to:
                raise forms.ValidationError("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        
        return cleaned_data


# ========== النماذج الأخرى ==========

class StockIncreaseForm(forms.ModelForm):
    """نموذج إذن إضافة الزيادات"""
    class Meta:
        model = StockIncrease
        fields = ['increase_number', 'date', 'warehouse', 'reason', 'notes']
        widgets = {
            'increase_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم إذن الزيادة'
            }),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سبب الزيادة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['increase_number'].required = False
        self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        self.fields['warehouse'].label_from_instance = lambda obj: f"{obj.name} ({obj.code})"


class StockIncreaseItemForm(forms.ModelForm):
    """نموذج صنف إذن إضافة الزيادات"""
    class Meta:
        model = StockIncreaseItem
        fields = ['item', 'quantity', 'unit_cost', 'expiry_date', 'batch_number', 'notes']
        widgets = {
            'item': forms.Select(attrs={'class': 'form-select select2'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control quantity', 'min': '0.001', 'step': '0.001'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control unit-cost', 'min': '0', 'step': '0.01'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'batch_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الدفعة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['item'].queryset = Item.objects.filter(is_active=True).order_by('name')


StockIncreaseItemFormSet = inlineformset_factory(
    StockIncrease, StockIncreaseItem, form=StockIncreaseItemForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class StockDecreaseForm(forms.ModelForm):
    """نموذج إذن صرف النواقص"""
    class Meta:
        model = StockDecrease
        fields = ['decrease_number', 'date', 'warehouse', 'reason', 'notes']
        widgets = {
            'decrease_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم إذن النقص'
            }),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سبب النقص'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        # جعل حقل decrease_number اختياري لأنه سيتم إنشاؤه تلقائياً
        self.fields['decrease_number'].required = False


class StockDecreaseItemForm(forms.ModelForm):
    """نموذج صنف إذن صرف النواقص"""
    class Meta:
        model = StockDecreaseItem
        fields = ['item', 'quantity', 'unit_cost', 'batch_number', 'notes']
        widgets = {
            'item': forms.Select(attrs={'class': 'form-select select2'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control quantity', 'min': '0.001', 'step': '0.001'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control unit-cost', 'min': '0', 'step': '0.01'}),
            'batch_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الدفعة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['item'].queryset = Item.objects.filter(is_active=True).order_by('name')


StockDecreaseItemFormSet = inlineformset_factory(
    StockDecrease, StockDecreaseItem, form=StockDecreaseItemForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class GoodsReceivedOnLoanForm(forms.ModelForm):
    """نموذج بضاعة مضافة سلفة من الغير"""
    class Meta:
        model = GoodsReceivedOnLoan
        fields = ['date', 'warehouse', 'lender_name', 'lender_phone', 'lender_address', 
                 'loan_reason', 'expected_return_date', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'lender_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المُقرض'}),
            'lender_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'هاتف المُقرض'}),
            'lender_address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'عنوان المُقرض'}),
            'loan_reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سبب السلفة'}),
            'expected_return_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)


class GoodsReceivedOnLoanItemForm(forms.ModelForm):
    """نموذج صنف بضاعة مضافة سلفة من الغير"""
    class Meta:
        model = GoodsReceivedOnLoanItem
        fields = ['item', 'quantity_received', 'estimated_unit_value', 'condition_received', 
                 'expiry_date', 'batch_number', 'notes']
        widgets = {
            'item': forms.Select(attrs={'class': 'form-select select2'}),
            'quantity_received': forms.NumberInput(attrs={'class': 'form-control', 'min': '0.001', 'step': '0.001'}),
            'estimated_unit_value': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'step': '0.01'}),
            'condition_received': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'حالة البضاعة عند الاستلام'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'batch_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الدفعة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['item'].queryset = Item.objects.filter(is_active=True).order_by('name')


GoodsReceivedOnLoanItemFormSet = inlineformset_factory(
    GoodsReceivedOnLoan, GoodsReceivedOnLoanItem, form=GoodsReceivedOnLoanItemForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class GoodsIssuedOnLoanForm(forms.ModelForm):
    """نموذج بضاعة منصرفة سلفة لدى الغير"""
    class Meta:
        model = GoodsIssuedOnLoan
        fields = ['date', 'warehouse', 'borrower_name', 'borrower_phone', 'borrower_address', 
                 'loan_reason', 'expected_return_date', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'borrower_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المُستعير'}),
            'borrower_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'هاتف المُستعير'}),
            'borrower_address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'عنوان المُستعير'}),
            'loan_reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سبب السلفة'}),
            'expected_return_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)


class GoodsIssuedOnLoanItemForm(forms.ModelForm):
    """نموذج صنف بضاعة منصرفة سلفة لدى الغير"""
    class Meta:
        model = GoodsIssuedOnLoanItem
        fields = ['item', 'quantity_issued', 'estimated_unit_value', 'condition_issued', 
                 'expiry_date', 'batch_number', 'notes']
        widgets = {
            'item': forms.Select(attrs={'class': 'form-select select2'}),
            'quantity_issued': forms.NumberInput(attrs={'class': 'form-control', 'min': '0.001', 'step': '0.001'}),
            'estimated_unit_value': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'step': '0.01'}),
            'condition_issued': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'حالة البضاعة عند الصرف'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'batch_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الدفعة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['item'].queryset = Item.objects.filter(is_active=True).order_by('name')


GoodsIssuedOnLoanItemFormSet = inlineformset_factory(
    GoodsIssuedOnLoan, GoodsIssuedOnLoanItem, form=GoodsIssuedOnLoanItemForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class WarehouseTransferForm(forms.ModelForm):
    """نموذج تحويل بين المخازن"""
    class Meta:
        model = WarehouseTransfer
        fields = ['date', 'from_warehouse', 'to_warehouse', 'transfer_reason', 'notes']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'from_warehouse': forms.Select(attrs={'class': 'form-select'}),
            'to_warehouse': forms.Select(attrs={'class': 'form-select'}),
            'transfer_reason': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'سبب التحويل'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['from_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        self.fields['to_warehouse'].queryset = Warehouse.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        from_warehouse = cleaned_data.get('from_warehouse')
        to_warehouse = cleaned_data.get('to_warehouse')
        
        if from_warehouse and to_warehouse and from_warehouse == to_warehouse:
            raise forms.ValidationError("لا يمكن التحويل إلى نفس المخزن")
        
        return cleaned_data


class WarehouseTransferItemForm(forms.ModelForm):
    """نموذج صنف تحويل بين المخازن"""
    class Meta:
        model = WarehouseTransferItem
        fields = ['item', 'quantity_requested', 'unit_cost', 'expiry_date', 'batch_number', 'notes']
        widgets = {
            'item': forms.Select(attrs={'class': 'form-select select2'}),
            'quantity_requested': forms.NumberInput(attrs={'class': 'form-control', 'min': '0.001', 'step': '0.001'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'min': '0', 'step': '0.01'}),
            'expiry_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'batch_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الدفعة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['item'].queryset = Item.objects.filter(is_active=True).order_by('name')


WarehouseTransferItemFormSet = inlineformset_factory(
    WarehouseTransfer, WarehouseTransferItem, form=WarehouseTransferItemForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class ItemTransformationForm(forms.ModelForm):
    class Meta:
        model = ItemTransformation
        fields = ['transformation_number', 'date', 'warehouse', 'notes']


class ItemTransformationInputForm(forms.ModelForm):
    class Meta:
        model = ItemTransformationInput
        fields = ['item', 'quantity', 'unit_cost', 'batch_number', 'notes']


class ItemTransformationOutputForm(forms.ModelForm):
    class Meta:
        model = ItemTransformationOutput
        fields = ['item', 'quantity', 'unit_cost', 'batch_number', 'notes']


ItemTransformationInputFormSet = inlineformset_factory(
    ItemTransformation, ItemTransformationInput, form=ItemTransformationInputForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)

ItemTransformationOutputFormSet = inlineformset_factory(
    ItemTransformation, ItemTransformationOutput, form=ItemTransformationOutputForm,
    extra=1, min_num=1, validate_min=True, can_delete=True
)


class ItemTransformationFilterForm(forms.Form):
    search = forms.CharField(required=False)
    warehouse = forms.ModelChoiceField(queryset=Warehouse.objects.all(), required=False)
    date_from = forms.DateField(required=False)
    date_to = forms.DateField(required=False)


class CountItemForm(forms.Form):
    item = forms.ModelChoiceField(queryset=Item.objects.all())
    actual_quantity = forms.DecimalField(max_digits=15, decimal_places=3)
    notes = forms.CharField(required=False)