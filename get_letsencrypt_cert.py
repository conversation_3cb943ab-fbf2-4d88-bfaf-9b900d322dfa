#!/usr/bin/env python3
"""
الحصول على شهادة Let's Encrypt معتمدة
Get Let's Encrypt Certified Certificate

شهادة SSL مجانية ومعتمدة من Let's Encrypt
Free and certified SSL certificate from Let's Encrypt
"""

import os
import sys
import subprocess
import socket
from pathlib import Path
from datetime import datetime

class LetsEncryptCertificate:
    """إدارة شهادات Let's Encrypt"""
    
    def __init__(self):
        self.domain = None
        self.email = None
        self.certbot_path = None
        self.cert_dir = Path('letsencrypt_certs')
        self.cert_dir.mkdir(exist_ok=True)
        
    def log_info(self, message):
        """تسجيل معلومات"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def log_error(self, message):
        """تسجيل أخطاء"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ❌ {message}")
    
    def log_success(self, message):
        """تسجيل نجاح"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] ✅ {message}")
    
    def check_requirements(self):
        """فحص المتطلبات"""
        print("🔍 فحص المتطلبات...")
        
        # فحص الاتصال بالإنترنت
        try:
            socket.create_connection(("*******", 53), timeout=5)
            self.log_success("الاتصال بالإنترنت متوفر")
        except OSError:
            self.log_error("لا يوجد اتصال بالإنترنت")
            return False
        
        # فحص إذا كان لديك دومين
        print("\n⚠️ متطلبات Let's Encrypt:")
        print("1. دومين حقيقي (مثل: mycompany.com)")
        print("2. الدومين يشير إلى عنوان IP العام")
        print("3. المنفذ 80 و 443 مفتوحان للعامة")
        print("4. خادم ويب يعمل على المنفذ 80")
        
        return True
    
    def install_certbot(self):
        """تثبيت Certbot"""
        print("\n📦 تثبيت Certbot...")
        
        try:
            # محاولة تثبيت عبر pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'certbot'], 
                          check=True, capture_output=True)
            self.log_success("تم تثبيت Certbot")
            return True
        except subprocess.CalledProcessError:
            self.log_error("فشل في تثبيت Certbot")
            
            print("\n🔧 طرق تثبيت بديلة:")
            print("Windows:")
            print("  choco install certbot")
            print("  أو تحميل من: https://certbot.eff.org/")
            
            return False
    
    def get_domain_info(self):
        """الحصول على معلومات الدومين"""
        print("\n📝 معلومات الدومين:")
        
        self.domain = input("أدخل اسم الدومين (مثل: mycompany.com): ").strip()
        if not self.domain:
            self.log_error("يجب إدخال اسم الدومين")
            return False
        
        self.email = input("أدخل البريد الإلكتروني: ").strip()
        if not self.email:
            self.log_error("يجب إدخال البريد الإلكتروني")
            return False
        
        return True
    
    def create_webroot_challenge(self):
        """إنشاء تحدي webroot"""
        webroot_dir = Path('webroot')
        webroot_dir.mkdir(exist_ok=True)
        
        # إنشاء ملف index.html بسيط
        index_file = webroot_dir / 'index.html'
        with open(index_file, 'w') as f:
            f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <title>Domain Verification</title>
</head>
<body>
    <h1>Domain Verification for {self.domain}</h1>
    <p>This page is used for SSL certificate verification.</p>
</body>
</html>
""")
        
        self.log_success(f"تم إنشاء webroot في: {webroot_dir}")
        return webroot_dir
    
    def get_certificate_standalone(self):
        """الحصول على الشهادة - طريقة standalone"""
        print(f"\n🔒 الحصول على شهادة لـ {self.domain}...")
        
        cmd = [
            'certbot', 'certonly',
            '--standalone',
            '--non-interactive',
            '--agree-tos',
            '--email', self.email,
            '-d', self.domain,
            '--cert-path', str(self.cert_dir / 'cert.pem'),
            '--key-path', str(self.cert_dir / 'privkey.pem'),
            '--fullchain-path', str(self.cert_dir / 'fullchain.pem')
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_success("تم الحصول على الشهادة بنجاح!")
                return True
            else:
                self.log_error(f"فشل في الحصول على الشهادة: {result.stderr}")
                return False
                
        except FileNotFoundError:
            self.log_error("Certbot غير مثبت")
            return False
    
    def get_certificate_webroot(self, webroot_path):
        """الحصول على الشهادة - طريقة webroot"""
        print(f"\n🔒 الحصول على شهادة لـ {self.domain} (webroot)...")
        
        cmd = [
            'certbot', 'certonly',
            '--webroot',
            '--webroot-path', str(webroot_path),
            '--non-interactive',
            '--agree-tos',
            '--email', self.email,
            '-d', self.domain
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_success("تم الحصول على الشهادة بنجاح!")
                self.copy_certificates()
                return True
            else:
                self.log_error(f"فشل في الحصول على الشهادة: {result.stderr}")
                return False
                
        except FileNotFoundError:
            self.log_error("Certbot غير مثبت")
            return False
    
    def copy_certificates(self):
        """نسخ الشهادات إلى مجلد ssl"""
        try:
            # مسار شهادات Let's Encrypt
            le_cert_dir = Path(f'/etc/letsencrypt/live/{self.domain}')
            if not le_cert_dir.exists():
                # Windows path
                le_cert_dir = Path(f'C:/Certbot/live/{self.domain}')
            
            if le_cert_dir.exists():
                ssl_dir = Path('ssl')
                ssl_dir.mkdir(exist_ok=True)
                
                # نسخ الملفات
                import shutil
                shutil.copy2(le_cert_dir / 'fullchain.pem', ssl_dir / 'server.crt')
                shutil.copy2(le_cert_dir / 'privkey.pem', ssl_dir / 'server.key')
                
                self.log_success("تم نسخ الشهادات إلى مجلد ssl")
            else:
                self.log_error("لم يتم العثور على مجلد الشهادات")
                
        except Exception as e:
            self.log_error(f"خطأ في نسخ الشهادات: {e}")
    
    def display_instructions(self):
        """عرض التعليمات"""
        print("\n" + "=" * 60)
        print("📋 تعليمات الحصول على شهادة Let's Encrypt")
        print("=" * 60)
        
        print("\n🔧 المتطلبات:")
        print("1. دومين حقيقي مسجل")
        print("2. DNS يشير إلى عنوان IP العام")
        print("3. المنافذ 80 و 443 مفتوحة")
        print("4. لا يوجد firewall يحجب الاتصالات")
        
        print("\n📝 الخطوات:")
        print("1. سجل دومين (من GoDaddy, Namecheap, etc.)")
        print("2. اجعل DNS يشير إلى عنوان IP العام")
        print("3. افتح المنافذ 80 و 443 في الراوتر")
        print("4. شغل هذا السكريپت")
        
        print("\n⚠️ ملاحظات:")
        print("• Let's Encrypt مجاني لكن يحتاج دومين حقيقي")
        print("• الشهادة تنتهي كل 90 يوم (تجديد تلقائي)")
        print("• يجب أن يكون الخادم متاح من الإنترنت")
        
        print("\n🌐 بدائل للتطوير المحلي:")
        print("• استخدم الشهادة المحلية الحالية")
        print("• تجاهل تحذيرات المتصفح")
        print("• استخدم ngrok للوصول العام")
    
    def run(self):
        """تشغيل عملية الحصول على الشهادة"""
        print("=" * 60)
        print("🔒 الحصول على شهادة Let's Encrypt")
        print("=" * 60)
        
        if not self.check_requirements():
            return False
        
        choice = input("\nهل لديك دومين حقيقي؟ (y/n): ").lower()
        
        if choice != 'y':
            self.display_instructions()
            return False
        
        if not self.get_domain_info():
            return False
        
        print("\n📋 طرق الحصول على الشهادة:")
        print("1. Standalone (يحتاج إيقاف الخادم مؤقتاً)")
        print("2. Webroot (يعمل مع الخادم)")
        
        method = input("اختر الطريقة (1 أو 2): ").strip()
        
        if method == "1":
            print("\n⚠️ سيتم إيقاف الخادم مؤقتاً...")
            input("اضغط Enter للمتابعة...")
            return self.get_certificate_standalone()
        
        elif method == "2":
            webroot_path = self.create_webroot_challenge()
            print(f"\n🌐 تأكد أن {self.domain} يشير إلى هذا الخادم")
            print(f"وأن المجلد {webroot_path} متاح على المنفذ 80")
            input("اضغط Enter للمتابعة...")
            return self.get_certificate_webroot(webroot_path)
        
        else:
            self.log_error("خيار غير صحيح")
            return False

def main():
    """الدالة الرئيسية"""
    cert_manager = LetsEncryptCertificate()
    success = cert_manager.run()
    
    if success:
        print("\n🎉 تم الحصول على شهادة معتمدة!")
        print("أعد تشغيل الخادم لاستخدام الشهادة الجديدة")
    else:
        print("\n💡 للتطوير المحلي، يمكنك:")
        print("• استخدام الشهادة المحلية الحالية")
        print("• تجاهل تحذيرات المتصفح بأمان")

if __name__ == "__main__":
    main()
