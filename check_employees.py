#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from hr.models import Employee

def check_employees():
    print("=== Employee Data Check ===")
    for emp in Employee.objects.all():
        print(f"ID: {emp.id}")
        print(f"  Name: {emp.full_name}")
        print(f"  Active: {emp.is_active}")
        print(f"  Status: {emp.status}")
        print(f"  Department: {emp.department} (Active: {emp.department.is_active if emp.department else 'N/A'})")
        print(f"  Position: {emp.position} (Active: {emp.position.is_active if emp.position else 'N/A'})")
        print(f"  Person: {emp.person} (Active: {emp.person.is_active if emp.person else 'N/A'})")
        print("---")

if __name__ == "__main__":
    check_employees() 