/**
 * Live Statistics System
 * نظام الإحصائيات المباشرة
 */

class LiveStatistics {
    constructor() {
        this.stats = {
            totalMaterials: 0,
            totalCost: 0,
            averageCostPerUnit: 0,
            estimatedTime: 0,
            efficiency: 0
        };
        this.init();
    }

    init() {
        this.addStatisticsPanel();
        this.setupUpdateListeners();
        this.updateStatistics();
    }

    addStatisticsPanel() {
        const statsPanel = `
            <div class="statistics-panel" id="statisticsPanel">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات مباشرة
                            <button type="button" class="btn btn-sm btn-outline-light float-end" 
                                    onclick="liveStats.togglePanel()">
                                <i class="fas fa-minus" id="toggleIcon"></i>
                            </button>
                        </h6>
                    </div>
                    <div class="card-body" id="statsBody">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-icon bg-primary">
                                        <i class="fas fa-cubes"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="statMaterials">0</div>
                                        <div class="stat-label">مواد خام</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-icon bg-success">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="statTotalCost">0.00</div>
                                        <div class="stat-label">إجمالي التكلفة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-icon bg-info">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="statUnitCost">0.00</div>
                                        <div class="stat-label">تكلفة الوحدة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-icon bg-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="statTime">0</div>
                                        <div class="stat-label">وقت متوقع (ساعة)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-3">
                        
                        <div class="efficiency-meter">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small fw-bold">كفاءة التكلفة</span>
                                <span class="small" id="efficiencyPercent">0%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-gradient-success" 
                                     id="efficiencyBar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="small text-muted">متوفر</div>
                                    <div class="fw-bold text-success" id="availableMaterials">0</div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">نقص</div>
                                    <div class="fw-bold text-warning" id="lowStockMaterials">0</div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">غير متوفر</div>
                                    <div class="fw-bold text-danger" id="outOfStockMaterials">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة اللوحة إلى الصفحة
        $('.manufacturing-container').append(statsPanel);
        
        // تحديد موقع اللوحة
        $('#statisticsPanel').css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            width: '300px',
            zIndex: 1000,
            maxHeight: '80vh',
            overflowY: 'auto'
        });

        this.addStatisticsStyles();
    }

    addStatisticsStyles() {
        const styles = `
            <style id="statisticsStyles">
                .statistics-panel {
                    backdrop-filter: blur(20px);
                    border-radius: 15px;
                    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                }

                .stat-item {
                    display: flex;
                    align-items: center;
                    padding: 0.75rem;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 10px;
                    transition: all 0.3s ease;
                }

                .stat-item:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }

                .stat-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    margin-right: 0.75rem;
                    font-size: 1.1rem;
                }

                .stat-content {
                    flex: 1;
                }

                .stat-value {
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: #2c3e50;
                    line-height: 1;
                }

                .stat-label {
                    font-size: 0.8rem;
                    color: #6c757d;
                    margin-top: 0.25rem;
                }

                .efficiency-meter {
                    background: rgba(255, 255, 255, 0.5);
                    padding: 1rem;
                    border-radius: 10px;
                    border: 1px solid rgba(0,0,0,0.05);
                }

                .bg-gradient-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                }

                .bg-gradient-success {
                    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
                }

                @media (max-width: 768px) {
                    .statistics-panel {
                        position: relative !important;
                        width: 100% !important;
                        top: auto !important;
                        right: auto !important;
                        margin-top: 2rem;
                    }
                }
            </style>
        `;
        
        $('#statisticsStyles').remove();
        $('head').append(styles);
    }

    setupUpdateListeners() {
        // تحديث الإحصائيات عند تغيير البيانات
        $(document).on('input change', '.material-select, .quantity-input, .cost-input, #id_quantity_to_produce', () => {
            this.updateStatistics();
        });

        // تحديث عند إضافة أو حذف المواد
        $(document).on('DOMNodeInserted DOMNodeRemoved', '#materialsContainer', () => {
            setTimeout(() => this.updateStatistics(), 100);
        });
    }

    updateStatistics() {
        this.calculateStats();
        this.updateDisplay();
    }

    calculateStats() {
        // حساب عدد المواد
        let validMaterials = 0;
        let totalCost = 0;
        let availableCount = 0;
        let lowStockCount = 0;
        let outOfStockCount = 0;

        $('.material-row').each((index, row) => {
            const $row = $(row);
            const material = $row.find('.material-select').val();
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            const partialCost = parseFloat($row.find('.partial-cost-display').val()) || 0;
            const stockText = $row.find('.stock-status').text();

            if (material && quantity > 0) {
                validMaterials++;
                totalCost += partialCost;

                // تحليل حالة المخزون
                if (stockText.includes('متوفر')) {
                    availableCount++;
                } else if (stockText.includes('نقص')) {
                    lowStockCount++;
                } else {
                    outOfStockCount++;
                }
            }
        });

        // حساب التكاليف الإضافية
        const additionalCosts = [
            '#laborCost', '#operatingExpenses', '#electricityCost',
            '#qualityCost', '#packagingCost', '#otherCosts'
        ].reduce((sum, selector) => {
            return sum + (parseFloat($(selector).val()) || 0);
        }, 0);

        totalCost += additionalCosts;

        // حساب تكلفة الوحدة
        const quantity = parseFloat($('#id_quantity_to_produce').val()) || 0;
        const unitCost = quantity > 0 ? totalCost / quantity : 0;

        // تقدير الوقت (بناءً على عدد المواد والكمية)
        const estimatedTime = Math.max(1, validMaterials * 0.5 + quantity * 0.1);

        // حساب الكفاءة (مبسط)
        const efficiency = Math.min(100, Math.max(0, 
            100 - (outOfStockCount * 30) - (lowStockCount * 15)
        ));

        this.stats = {
            totalMaterials: validMaterials,
            totalCost: totalCost,
            unitCost: unitCost,
            estimatedTime: estimatedTime,
            efficiency: efficiency,
            availableCount: availableCount,
            lowStockCount: lowStockCount,
            outOfStockCount: outOfStockCount
        };
    }

    updateDisplay() {
        // تحديث القيم مع تأثيرات بصرية
        this.animateValue('#statMaterials', this.stats.totalMaterials);
        this.animateValue('#statTotalCost', this.stats.totalCost.toFixed(2) + ' ج.م');
        this.animateValue('#statUnitCost', this.stats.unitCost.toFixed(2) + ' ج.م');
        this.animateValue('#statTime', this.stats.estimatedTime.toFixed(1));

        // تحديث شريط الكفاءة
        $('#efficiencyBar').css('width', this.stats.efficiency + '%');
        $('#efficiencyPercent').text(this.stats.efficiency.toFixed(0) + '%');

        // تحديث إحصائيات المخزون
        $('#availableMaterials').text(this.stats.availableCount);
        $('#lowStockMaterials').text(this.stats.lowStockCount);
        $('#outOfStockMaterials').text(this.stats.outOfStockCount);

        // تغيير لون شريط الكفاءة حسب القيمة
        const $bar = $('#efficiencyBar');
        $bar.removeClass('bg-gradient-success bg-gradient-warning bg-gradient-danger');
        
        if (this.stats.efficiency >= 80) {
            $bar.addClass('bg-gradient-success');
        } else if (this.stats.efficiency >= 50) {
            $bar.addClass('bg-gradient-warning');
        } else {
            $bar.addClass('bg-gradient-danger');
        }
    }

    animateValue(selector, newValue) {
        const $element = $(selector);
        const currentValue = $element.text();
        
        if (currentValue !== newValue.toString()) {
            $element.addClass('animate__animated animate__pulse');
            $element.text(newValue);
            
            setTimeout(() => {
                $element.removeClass('animate__animated animate__pulse');
            }, 600);
        }
    }

    togglePanel() {
        const $body = $('#statsBody');
        const $icon = $('#toggleIcon');
        
        if ($body.is(':visible')) {
            $body.slideUp(300);
            $icon.removeClass('fa-minus').addClass('fa-plus');
        } else {
            $body.slideDown(300);
            $icon.removeClass('fa-plus').addClass('fa-minus');
        }
    }

    hide() {
        $('#statisticsPanel').fadeOut(300);
    }

    show() {
        $('#statisticsPanel').fadeIn(300);
    }

    exportStats() {
        return {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            formData: {
                orderNumber: $('#orderNumber').text(),
                finishedProduct: $('#id_finished_product option:selected').text(),
                quantity: $('#id_quantity_to_produce').val()
            }
        };
    }
}

// إنشاء نسخة عامة من النظام
let liveStats;

$(document).ready(function() {
    liveStats = new LiveStatistics();
    
    // إضافة زر إظهار/إخفاء الإحصائيات في القائمة الرئيسية
    $('.section-title').first().append(`
        <button type="button" class="btn btn-outline-info btn-sm ms-2" 
                onclick="liveStats.show()" title="إظهار الإحصائيات المباشرة">
            <i class="fas fa-chart-bar me-1"></i>إحصائيات
        </button>
    `);
});
