{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-undo text-danger me-2"></i>
                    {{ title }}
                </h2>
                <a href="{% url 'purchases:return_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    العودة للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات مرتجع المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.return_number.id_for_label }}" class="form-label">{{ form.return_number.label }}</label>
                                {{ form.return_number }}
                                {% if form.return_number.errors %}
                                    <div class="text-danger small">{{ form.return_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger small">{{ form.date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">{{ form.supplier.label }}</label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger small">{{ form.supplier.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">{{ form.warehouse.label }}</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الفاتورة الأصلية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger border-bottom pb-2 mb-3">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    الفاتورة الأصلية
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.original_invoice.id_for_label }}" class="form-label">{{ form.original_invoice.label }}</label>
                                {{ form.original_invoice }}
                                {% if form.original_invoice.errors %}
                                    <div class="text-danger small">{{ form.original_invoice.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.return_reason.id_for_label }}" class="form-label">{{ form.return_reason.label }}</label>
                                {{ form.return_reason }}
                                {% if form.return_reason.errors %}
                                    <div class="text-danger small">{{ form.return_reason.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الأصناف المرتجعة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger border-bottom pb-2 mb-3">
                                    <i class="fas fa-boxes me-1"></i>
                                    الأصناف المرتجعة
                                </h6>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.selected_items.id_for_label }}" class="form-label">{{ form.selected_items.label }}</label>
                                {{ form.selected_items }}
                                {% if form.selected_items.errors %}
                                    <div class="text-danger small">{{ form.selected_items.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">اختر الأصناف التي تريد إرجاعها من الفاتورة الأصلية</div>
                            </div>
                        </div>

                        <!-- تفاصيل الفاتورة الأصلية -->
                        <div id="invoice-details" class="row mb-4" style="display: none;">
                            <div class="col-12">
                                <h6 class="text-info border-bottom pb-2 mb-3">
                                    <i class="fas fa-list me-1"></i>
                                    تفاصيل الفاتورة الأصلية
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered" id="invoice-items-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="5%">
                                                    <input type="checkbox" id="select-all-items" class="form-check-input">
                                                </th>
                                                <th width="15%">الصنف</th>
                                                <th width="10%">الكمية</th>
                                                <th width="10%">سعر الوحدة</th>
                                                <th width="10%">الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody id="invoice-items-body">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger border-bottom pb-2 mb-3">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    الملاحظات
                                </h6>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'purchases:return_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>
                                        {{ action }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
    <div id="successToast" class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                تم حفظ المرتجع بنجاح
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const originalInvoiceSelect = document.getElementById('{{ form.original_invoice.id_for_label }}');
    const selectedItemsSelect = document.getElementById('{{ form.selected_items.id_for_label }}');
    const invoiceDetailsDiv = document.getElementById('invoice-details');
    const invoiceItemsTable = document.getElementById('invoice-items-table');
    const invoiceItemsBody = document.getElementById('invoice-items-body');
    const selectAllCheckbox = document.getElementById('select-all-items');
    
    // Toast
    const successToast = new bootstrap.Toast(document.getElementById('successToast'));

    // تحميل تفاصيل الفاتورة عند اختيارها
    originalInvoiceSelect.addEventListener('change', function() {
        const invoiceId = this.value;
        if (invoiceId) {
            loadInvoiceDetails(invoiceId);
        } else {
            hideInvoiceDetails();
        }
    });

    // تحميل تفاصيل الفاتورة من الخادم
    function loadInvoiceDetails(invoiceId) {
        fetch(`/purchases/api/invoices/${invoiceId}/items/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayInvoiceItems(data.items);
                    showInvoiceDetails();
                } else {
                    console.error('Error loading invoice details:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    // عرض تفاصيل الفاتورة
    function displayInvoiceItems(items) {
        invoiceItemsBody.innerHTML = '';
        
        items.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="form-check-input item-checkbox" 
                           data-item-id="${item.id}" data-item-name="${item.name}">
                </td>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.unit_cost}</td>
                <td>${item.total_amount}</td>
            `;
            invoiceItemsBody.appendChild(row);
        });

        // إضافة مستمعي الأحداث للـ checkboxes
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedItems);
        });
    }

    // إظهار تفاصيل الفاتورة
    function showInvoiceDetails() {
        invoiceDetailsDiv.style.display = 'block';
    }

    // إخفاء تفاصيل الفاتورة
    function hideInvoiceDetails() {
        invoiceDetailsDiv.style.display = 'none';
        invoiceItemsBody.innerHTML = '';
        selectedItemsSelect.innerHTML = '';
    }

    // تحديث الأصناف المختارة
    function updateSelectedItems() {
        const selectedItems = [];
        document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
            selectedItems.push(checkbox.dataset.itemId);
        });
        
        // تحديث الـ select
        selectedItemsSelect.innerHTML = '';
        selectedItems.forEach(itemId => {
            const option = document.createElement('option');
            option.value = itemId;
            option.selected = true;
            selectedItemsSelect.appendChild(option);
        });
    }

    // تحديد/إلغاء تحديد جميع الأصناف
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateSelectedItems();
    });

    // تحديث حالة "تحديد الكل" عند تغيير الأصناف الفردية
    function updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
        
        selectAllCheckbox.checked = checkboxes.length > 0 && checkboxes.length === checkedCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkboxes.length !== checkedCheckboxes.length;
    }

    // إضافة مستمع لـ change event على الأصناف الفردية
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('item-checkbox')) {
            updateSelectAllState();
        }
    });
});
</script>
{% endblock %}
