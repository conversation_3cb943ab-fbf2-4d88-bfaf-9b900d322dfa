{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-book text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">سجل يومي لجميع أنشطة وأحداث شؤون العاملين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEntryModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة حدث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ today_entries }}</h3>
                    <p class="mb-0">أحداث اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ week_entries }}</h3>
                    <p class="mb-0">أحداث الأسبوع</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ pending_actions }}</h3>
                    <p class="mb-0">إجراءات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ total_entries }}</h3>
                    <p class="mb-0">إجمالي الأحداث</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">نوع الحدث:</label>
                            <select class="form-select" onchange="filterByType()">
                                <option value="">جميع الأنواع</option>
                                <option value="hiring">توظيف</option>
                                <option value="termination">إنهاء خدمة</option>
                                <option value="promotion">ترقية</option>
                                <option value="transfer">نقل</option>
                                <option value="vacation">إجازة</option>
                                <option value="training">تدريب</option>
                                <option value="disciplinary">إجراء تأديبي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">التاريخ من:</label>
                            <input type="date" class="form-control" onchange="filterByDate()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">التاريخ إلى:</label>
                            <input type="date" class="form-control" onchange="filterByDate()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إجراءات:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="exportDiary()">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                                <button class="btn btn-info" onclick="printDiary()">
                                    <i class="fas fa-print me-1"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diary Entries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        يوميات شؤون العاملين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for entry in diary_entries %}
                        <div class="timeline-item">
                            <div class="timeline-marker 
                                {% if entry.type == 'توظيف' %}bg-success
                                {% elif entry.type == 'إنهاء خدمة' %}bg-danger
                                {% elif entry.type == 'ترقية' %}bg-warning
                                {% elif entry.type == 'نقل' %}bg-info
                                {% elif entry.type == 'إجازة' %}bg-secondary
                                {% elif entry.type == 'تدريب' %}bg-primary
                                {% else %}bg-dark{% endif %}">
                                {% if entry.type == 'توظيف' %}
                                    <i class="fas fa-user-plus"></i>
                                {% elif entry.type == 'إنهاء خدمة' %}
                                    <i class="fas fa-user-times"></i>
                                {% elif entry.type == 'ترقية' %}
                                    <i class="fas fa-arrow-up"></i>
                                {% elif entry.type == 'نقل' %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% elif entry.type == 'إجازة' %}
                                    <i class="fas fa-calendar-times"></i>
                                {% elif entry.type == 'تدريب' %}
                                    <i class="fas fa-graduation-cap"></i>
                                {% else %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% endif %}
                            </div>
                            <div class="timeline-content">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                {% if entry.type == 'توظيف' %}
                                                    <span class="badge bg-success me-2">{{ entry.type }}</span>
                                                {% elif entry.type == 'إنهاء خدمة' %}
                                                    <span class="badge bg-danger me-2">{{ entry.type }}</span>
                                                {% elif entry.type == 'ترقية' %}
                                                    <span class="badge bg-warning me-2">{{ entry.type }}</span>
                                                {% elif entry.type == 'نقل' %}
                                                    <span class="badge bg-info me-2">{{ entry.type }}</span>
                                                {% elif entry.type == 'إجازة' %}
                                                    <span class="badge bg-secondary me-2">{{ entry.type }}</span>
                                                {% elif entry.type == 'تدريب' %}
                                                    <span class="badge bg-primary me-2">{{ entry.type }}</span>
                                                {% else %}
                                                    <span class="badge bg-dark me-2">{{ entry.type }}</span>
                                                {% endif %}
                                                {{ entry.title }}
                                            </h6>
                                            <small class="text-muted">{{ entry.date }} - {{ entry.time }}</small>
                                        </div>
                                        <p class="card-text">
                                            <strong>الموظف:</strong> {{ entry.employee }}
                                            <br>
                                            <strong>التفاصيل:</strong> {{ entry.description }}
                                        </p>
                                        {% if entry.department %}
                                        <p class="card-text">
                                            <strong>القسم:</strong> <span class="badge bg-secondary">{{ entry.department }}</span>
                                        </p>
                                        {% endif %}
                                        {% if entry.responsible %}
                                        <p class="card-text">
                                            <strong>المسؤول:</strong> {{ entry.responsible }}
                                        </p>
                                        {% endif %}
                                        {% if entry.notes %}
                                        <div class="alert alert-light mt-2">
                                            <small><strong>ملاحظات:</strong> {{ entry.notes }}</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-book fa-3x mb-3 d-block"></i>
                            <h5>لا توجد أحداث مسجلة</h5>
                            <p>ابدأ بإضافة الأحداث اليومية لشؤون العاملين</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        ملخص الأنشطة الأخيرة (آخر 30 يوم)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-success text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>5</strong>
                                </div>
                                <h6 class="text-success">توظيف</h6>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-danger text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>2</strong>
                                </div>
                                <h6 class="text-danger">إنهاء خدمة</h6>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-warning text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>3</strong>
                                </div>
                                <h6 class="text-warning">ترقية</h6>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-info text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>4</strong>
                                </div>
                                <h6 class="text-info">نقل</h6>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-secondary text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>12</strong>
                                </div>
                                <h6 class="text-secondary">إجازات</h6>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="activity-circle bg-primary text-white mb-2" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <strong>8</strong>
                                </div>
                                <h6 class="text-primary">تدريب</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Entry Modal -->
<div class="modal fade" id="addEntryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة حدث جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الحدث</label>
                                <select class="form-select" required>
                                    <option value="">اختر نوع الحدث</option>
                                    <option value="hiring">توظيف</option>
                                    <option value="termination">إنهاء خدمة</option>
                                    <option value="promotion">ترقية</option>
                                    <option value="transfer">نقل</option>
                                    <option value="vacation">إجازة</option>
                                    <option value="training">تدريب</option>
                                    <option value="disciplinary">إجراء تأديبي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">فاطمة علي</option>
                                    <option value="3">محمد أحمد</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوقت</label>
                                <input type="time" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">عنوان الحدث</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وصف الحدث</label>
                        <textarea class="form-control" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المسؤول عن الحدث</label>
                        <input type="text" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">حفظ الحدث</button>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    margin-left: 20px;
}
</style>

<script>
function filterByType() {
    alert('تم تطبيق فلتر نوع الحدث');
}

function filterByDate() {
    alert('تم تطبيق فلتر التاريخ');
}

function exportDiary() {
    alert('سيتم تصدير اليوميات');
}

function printDiary() {
    window.print();
}
</script>
{% endblock %}
