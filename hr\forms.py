from django import forms
from .models import Department, Position, SalarySystem, Employee, EmployeeSalary, Attendance, Overtime, AbsenceRecord, Contract, Transfer, Termination, SalaryAddition, AllowanceDeduction, AllowanceType, DeductionType
from definitions.models import Person, Currency


class DepartmentForm(forms.ModelForm):
    class Meta:
        model = Department
        fields = ['code', 'name', 'name_english', 'description', 'parent_department', 'manager']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود القسم'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم القسم'}),
            'name_english': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم بالإنجليزية'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف القسم'}),
            'parent_department': forms.Select(attrs={'class': 'form-select'}),
            'manager': forms.Select(attrs={'class': 'form-select'}),
        }


class PositionForm(forms.ModelForm):
    class Meta:
        model = Position
        fields = ['code', 'name', 'name_english', 'description', 'department']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود المنصب'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنصب'}),
            'name_english': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم بالإنجليزية'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المنصب'}),
            'department': forms.Select(attrs={'class': 'form-select'}),
        }


class SalarySystemForm(forms.ModelForm):
    class Meta:
        model = SalarySystem
        fields = [
            'code', 'name', 'description', 'system_type', 'currency',
            'basic_salary', 'include_overtime', 'overtime_rate',
            'social_insurance_rate', 'tax_exemption'
        ]
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود النظام'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم النظام'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف النظام'}),
            'system_type': forms.Select(attrs={'class': 'form-select'}),
            'currency': forms.Select(attrs={'class': 'form-select'}),
            'basic_salary': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'include_overtime': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'overtime_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'social_insurance_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_exemption': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }


class EmployeeSalaryForm(forms.ModelForm):
    class Meta:
        model = EmployeeSalary
        fields = [
            'basic_salary', 'housing_allowance', 'transportation_allowance', 'food_allowance', 'other_allowances',
            'social_insurance', 'tax_deduction', 'other_deductions',
            'overtime_hours', 'overtime_rate', 'effective_date', 'notes'
        ]
        widgets = {
            'basic_salary': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'housing_allowance': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'transportation_allowance': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'food_allowance': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'other_allowances': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'social_insurance': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'tax_deduction': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'other_deductions': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.01', 'min': '0'}),
            'overtime_hours': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.5', 'min': '0'}),
            'overtime_rate': forms.NumberInput(attrs={'class': 'form-control salary-field', 'step': '0.1', 'min': '0'}),
            'effective_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات حول المرتب...'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعيين تاريخ اليوم كتاريخ سريان افتراضي
        if not self.instance.pk:
            from datetime import date
            self.fields['effective_date'].initial = date.today()


class EmployeeForm(forms.ModelForm):
    # البيانات الشخصية الأساسية (من Person)
    name = forms.CharField(
        label='الاسم الكامل',
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الكامل للموظف'
        })
    )
    
    national_id = forms.CharField(
        label='الرقم القومي',
        max_length=14,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '14 رقم (اختياري)',
            'pattern': '[0-9]{14}',
            'title': 'يجب أن يكون 14 رقم'
        }),
        help_text='اختياري - سيتم التحقق من عدم تكراره في النظام'
    )
    
    birth_date = forms.DateField(
        label='تاريخ الميلاد',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    phone = forms.CharField(
        label='رقم الهاتف',
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الهاتف'
        })
    )
    
    mobile = forms.CharField(
        label='رقم الجوال',
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الجوال'
        })
    )
    
    email = forms.EmailField(
        label='البريد الإلكتروني',
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'البريد الإلكتروني (اختياري)'
        }),
        help_text='اختياري - سيتم التحقق من عدم تكراره في النظام'
    )
    
    address = forms.CharField(
        label='العنوان',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'العنوان بالتفصيل'
        })
    )
    
    city = forms.CharField(
        label='المدينة',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'المدينة'
        })
    )
    
    # البيانات الوظيفية
    employee_number = forms.CharField(
        label='الرقم الوظيفي',
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'سيتم توليده تلقائياً إذا تركته فارغاً'
        }),
        help_text='اختياري - سيتم توليد رقم فريد تلقائياً إذا تركته فارغاً'
    )
    
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    position = forms.ModelChoiceField(
        label='المنصب',
        queryset=Position.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    salary_system = forms.ModelChoiceField(
        label='نظام المرتب',
        queryset=SalarySystem.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    hire_date = forms.DateField(
        label='تاريخ التعيين',
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    contract_start_date = forms.DateField(
        label='تاريخ بداية العقد',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    contract_end_date = forms.DateField(
        label='تاريخ انتهاء العقد',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    current_salary = forms.DecimalField(
        label='المرتب الحالي',
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'المرتب الحالي'
        })
    )
    
    status = forms.ChoiceField(
        label='الحالة',
        choices=Employee.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    class Meta:
        model = Employee
        fields = [
            'employee_number', 'department', 'position', 'salary_system',
            'hire_date', 'contract_start_date', 'contract_end_date',
            'current_salary', 'status'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعيين تاريخ اليوم كتاريخ تعيين افتراضي
        from datetime import date
        if not self.instance.pk:
            self.fields['hire_date'].initial = date.today()
            self.fields['contract_start_date'].initial = date.today()
        
        # ترتيب الحقول
        self.fields['department'].queryset = Department.objects.filter(is_active=True).order_by('name')
        self.fields['position'].queryset = Position.objects.filter(is_active=True).order_by('name')
        self.fields['salary_system'].queryset = SalarySystem.objects.filter(is_active=True).order_by('name')
        
        # تعيين القيم الأولية من Person إذا كان موجوداً
        if self.instance.pk and hasattr(self.instance, 'person'):
            person = self.instance.person
            self.fields['name'].initial = person.name
            self.fields['national_id'].initial = person.national_id
            # birth_date يتم حفظه في notes، لذا نستخرجه من هناك
            if person.notes and 'تاريخ الميلاد:' in person.notes:
                try:
                    birth_date_str = person.notes.split('تاريخ الميلاد:')[1].strip().split('\n')[0]
                    from datetime import datetime
                    birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
                    self.fields['birth_date'].initial = birth_date
                except:
                    pass
            self.fields['phone'].initial = person.phone
            self.fields['mobile'].initial = person.mobile
            self.fields['email'].initial = person.email
            self.fields['address'].initial = person.address
            self.fields['city'].initial = person.city
        
    def clean_birth_date(self):
        birth_date = self.cleaned_data.get('birth_date')
        if birth_date:
            from datetime import date
            today = date.today()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            if age < 18:
                raise forms.ValidationError('عمر الموظف يجب أن يكون 18 سنة على الأقل')
            if age > 65:
                raise forms.ValidationError('عمر الموظف يجب أن يكون أقل من 65 سنة')
        return birth_date
    
    def clean_national_id(self):
        national_id = self.cleaned_data.get('national_id')
        if national_id:
            if not national_id.isdigit() or len(national_id) != 14:
                raise forms.ValidationError('الرقم القومي يجب أن يكون 14 رقم')
            existing_person = Person.objects.filter(national_id=national_id).first()
            if existing_person and (not self.instance.pk or not hasattr(self.instance, 'person') or existing_person.pk != self.instance.person.pk):
                raise forms.ValidationError('الرقم القومي مسجل بالفعل في النظام')
        return national_id

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            existing_person = Person.objects.filter(email=email).first()
            if existing_person and (not self.instance.pk or not hasattr(self.instance, 'person') or existing_person.pk != self.instance.person.pk):
                raise forms.ValidationError('البريد الإلكتروني مسجل بالفعل في النظام')
        return email
        
    def clean_employee_number(self):
        employee_number = self.cleaned_data.get('employee_number')
        if employee_number:
            existing_employee = Employee.objects.filter(employee_number=employee_number).first()
            if existing_employee and (not self.instance.pk or existing_employee.pk != self.instance.pk):
                raise forms.ValidationError('الرقم الوظيفي مسجل بالفعل في النظام')
        return employee_number
    
    def clean(self):
        """تنظيف شامل للنموذج"""
        cleaned_data = super().clean()
        
        # التحقق من أن تاريخ انتهاء العقد بعد تاريخ بدايته
        contract_start_date = cleaned_data.get('contract_start_date')
        contract_end_date = cleaned_data.get('contract_end_date')
        
        if contract_start_date and contract_end_date and contract_end_date <= contract_start_date:
            raise forms.ValidationError('تاريخ انتهاء العقد يجب أن يكون بعد تاريخ بداية العقد')
        
        # التحقق من أن تاريخ التعيين منطقي
        hire_date = cleaned_data.get('hire_date')
        birth_date = cleaned_data.get('birth_date')
        
        if hire_date and birth_date:
            from datetime import date
            today = date.today()
            age_at_hire = hire_date.year - birth_date.year - ((hire_date.month, hire_date.day) < (birth_date.month, birth_date.day))
            
            if age_at_hire < 18:
                raise forms.ValidationError('عمر الموظف عند التعيين يجب أن يكون 18 سنة على الأقل')
        
        return cleaned_data


class AttendanceForm(forms.ModelForm):
    class Meta:
        model = Attendance
        fields = ['employee', 'date', 'check_in', 'check_out', 'notes', 'attendance_state']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-select'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'check_in': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'check_out': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات اختيارية'}),
            'attendance_state': forms.Select(attrs={'class': 'form-select'}, choices=Attendance.ATTENDANCE_STATES),
        }
        labels = {
            'employee': 'الموظف',
            'date': 'التاريخ',
            'check_in': 'وقت الحضور',
            'check_out': 'وقت الانصراف',
            'notes': 'ملاحظات',
            'attendance_state': 'الحالة (اختياري)',
        }


class OvertimeForm(forms.ModelForm):
    class Meta:
        model = Overtime
        fields = ['employee', 'date', 'hours', 'hourly_rate', 'notes', 'status']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-select'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'hours': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5', 'min': '0.5', 'max': '12'}),
            'hourly_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات حول العمل الإضافي...'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'employee': 'الموظف',
            'date': 'التاريخ',
            'hours': 'عدد الساعات',
            'hourly_rate': 'سعر الساعة (ج.م)',
            'notes': 'ملاحظات',
            'status': 'الحالة',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # عرض الموظفين النشطين فقط
        self.fields['employee'].queryset = Employee.objects.filter(status='ACTIVE').order_by('person__name')
        # تعيين تاريخ اليوم كتاريخ افتراضي
        if not self.instance.pk:
            from datetime import date
            self.fields['date'].initial = date.today()


class AbsenceRecordForm(forms.ModelForm):
    """نموذج تسجيل الغياب والتأخير"""
    
    class Meta:
        model = AbsenceRecord
        fields = [
            'employee', 'absence_type', 'date', 'time_from', 'time_to',
            'duration_hours', 'duration_minutes', 'reason', 'details',
            'deduction_amount', 'currency', 'notes', 'attachment'
        ]
        widgets = {
            'employee': forms.Select(attrs={
                'class': 'form-select',
                'placeholder': 'اختر الموظف'
            }),
            'absence_type': forms.Select(attrs={
                'class': 'form-select',
                'placeholder': 'اختر نوع المخالفة'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'time_from': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'time_to': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'duration_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'step': '0.5',
                'placeholder': '0.0'
            }),
            'duration_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '59',
                'placeholder': '0'
            }),
            'reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': '3',
                'placeholder': 'أدخل سبب الغياب أو التأخير'
            }),
            'details': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': '2',
                'placeholder': 'تفاصيل إضافية (اختياري)'
            }),
            'deduction_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'step': '0.01',
                'placeholder': '0.00'
            }),
            'currency': forms.Select(attrs={
                'class': 'form-select'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': '2',
                'placeholder': 'ملاحظات إضافية (اختياري)'
            }),
            'attachment': forms.FileInput(attrs={
                'class': 'form-control'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جلب الموظفين النشطين فقط
        self.fields['employee'].queryset = Employee.objects.filter(
            status='ACTIVE'
        ).select_related('person').order_by('person__name')
        
        # جلب العملات النشطة
        self.fields['currency'].queryset = Currency.objects.filter(
            is_active=True
        ).order_by('name')

    def clean(self):
        cleaned_data = super().clean()
        absence_type = cleaned_data.get('absence_type')
        time_from = cleaned_data.get('time_from')
        time_to = cleaned_data.get('time_to')
        duration_hours = cleaned_data.get('duration_hours')
        duration_minutes = cleaned_data.get('duration_minutes')
        
        # التحقق من أن الوقت محدد للتأخير والانصراف المبكر
        if absence_type in ['LATE', 'EARLY_LEAVE', 'LATE_WITHOUT_PERMISSION']:
            if not time_from or not time_to:
                raise forms.ValidationError(
                    "يجب تحديد وقت البداية والنهاية للتأخير والانصراف المبكر"
                )
        
        # التحقق من أن المدة محسوبة بشكل صحيح
        if time_from and time_to:
            if time_from >= time_to:
                raise forms.ValidationError(
                    "وقت البداية يجب أن يكون قبل وقت النهاية"
                )
        
        return cleaned_data


class ContractForm(forms.ModelForm):
    class Meta:
        model = Contract
        fields = ['employee', 'contract_type', 'start_date', 'end_date', 'salary', 'is_active', 'notes', 'contract_file']

    def clean_contract_file(self):
        file = self.cleaned_data.get('contract_file')
        if file:
            valid_mime = [
                'application/pdf',
                'image/jpeg',
                'image/png',
            ]
            if hasattr(file, 'content_type') and file.content_type not in valid_mime:
                raise forms.ValidationError('يسمح فقط بملفات PDF أو صور JPG/PNG.')
        return file


class EmploymentForm(forms.Form):
    name = forms.CharField(label='الاسم الكامل', max_length=100, widget=forms.TextInput(attrs={'placeholder': 'مثال: أحمد محمد'}))
    national_id = forms.CharField(label='رقم الهوية', max_length=20, widget=forms.TextInput(attrs={'placeholder': 'مثال: 12345678901234'}))
    position = forms.ModelChoiceField(label='المنصب', queryset=Position.objects.filter(is_active=True), widget=forms.Select(attrs={'class': 'form-select'}))
    department = forms.ModelChoiceField(label='القسم', queryset=Department.objects.filter(is_active=True), widget=forms.Select(attrs={'class': 'form-select'}))
    hire_date = forms.DateField(label='تاريخ التعيين', widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}))
    salary = forms.DecimalField(label='الراتب الأساسي', min_value=1000, widget=forms.NumberInput(attrs={'placeholder': 'مثال: 5000', 'class': 'form-control'}))
    phone = forms.CharField(label='رقم الهاتف', max_length=20, widget=forms.TextInput(attrs={'placeholder': 'مثال: 01012345678'}))
    email = forms.EmailField(label='البريد الإلكتروني', widget=forms.EmailInput(attrs={'placeholder': 'مثال: <EMAIL>'}))
    address = forms.CharField(label='العنوان', widget=forms.Textarea(attrs={'rows': 2, 'placeholder': 'العنوان بالتفصيل'}))


class TransferForm(forms.ModelForm):
    class Meta:
        model = Transfer
        fields = [
            'employee', 'from_department', 'to_department', 'from_position', 'to_position',
            'transfer_type', 'transfer_date', 'effective_date', 'end_date', 'reason',
            'is_temporary', 'return_date', 'replacement_employee', 'notes', 'attachment'
        ]
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-select'}),
            'from_department': forms.Select(attrs={'class': 'form-select'}),
            'to_department': forms.Select(attrs={'class': 'form-select'}),
            'from_position': forms.Select(attrs={'class': 'form-select'}),
            'to_position': forms.Select(attrs={'class': 'form-select'}),
            'transfer_type': forms.Select(attrs={'class': 'form-select'}),
            'transfer_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'effective_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'سبب النقل...'}),
            'is_temporary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'return_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'replacement_employee': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية...'}),
            'attachment': forms.FileInput(attrs={'class': 'form-control'}),
        }
        labels = {
            'employee': 'الموظف',
            'from_department': 'القسم السابق',
            'to_department': 'القسم الجديد',
            'from_position': 'المنصب السابق',
            'to_position': 'المنصب الجديد',
            'transfer_type': 'نوع النقل',
            'transfer_date': 'تاريخ النقل',
            'effective_date': 'تاريخ سريان النقل',
            'end_date': 'تاريخ انتهاء النقل',
            'reason': 'سبب النقل',
            'is_temporary': 'نقل مؤقت',
            'return_date': 'تاريخ العودة',
            'replacement_employee': 'الموظف البديل',
            'notes': 'ملاحظات',
            'attachment': 'مرفقات',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعيين تاريخ اليوم كتاريخ نقل افتراضي
        if not self.instance.pk:
            from datetime import date
            self.fields['transfer_date'].initial = date.today()
            self.fields['effective_date'].initial = date.today()
        
        # تحديث queryset للموظفين النشطين فقط
        self.fields['employee'].queryset = Employee.objects.filter(status='ACTIVE').select_related('person')
        self.fields['replacement_employee'].queryset = Employee.objects.filter(status='ACTIVE').select_related('person')
        self.fields['from_department'].queryset = Department.objects.filter(is_active=True)
        self.fields['to_department'].queryset = Department.objects.filter(is_active=True)
        self.fields['from_position'].queryset = Position.objects.filter(is_active=True)
        self.fields['to_position'].queryset = Position.objects.filter(is_active=True)
    
    def clean(self):
        cleaned_data = super().clean()
        employee = cleaned_data.get('employee')
        from_department = cleaned_data.get('from_department')
        to_department = cleaned_data.get('to_department')
        from_position = cleaned_data.get('from_position')
        to_position = cleaned_data.get('to_position')
        transfer_date = cleaned_data.get('transfer_date')
        effective_date = cleaned_data.get('effective_date')
        end_date = cleaned_data.get('end_date')
        is_temporary = cleaned_data.get('is_temporary')
        return_date = cleaned_data.get('return_date')
        
        # التحقق من أن الموظف موجود في القسم السابق
        if employee and from_department:
            if employee.department != from_department:
                raise forms.ValidationError(
                    f"الموظف {employee.full_name} غير موجود في القسم {from_department.name}"
                )
        
        # التحقق من أن الموظف موجود في المنصب السابق
        if employee and from_position:
            if employee.position != from_position:
                raise forms.ValidationError(
                    f"الموظف {employee.full_name} غير موجود في المنصب {from_position.name}"
                )
        
        # التحقق من أن القسم والمنصب الجديدان مختلفان
        if from_department == to_department and from_position == to_position:
            raise forms.ValidationError("يجب أن يكون القسم أو المنصب الجديد مختلفاً عن السابق")
        
        # التحقق من التواريخ
        if transfer_date and effective_date:
            if effective_date < transfer_date:
                raise forms.ValidationError("تاريخ سريان النقل يجب أن يكون بعد أو يساوي تاريخ النقل")
        
        if end_date and effective_date:
            if end_date < effective_date:
                raise forms.ValidationError("تاريخ انتهاء النقل يجب أن يكون بعد تاريخ سريان النقل")
        
        # التحقق من النقل المؤقت
        if is_temporary and not return_date:
            raise forms.ValidationError("يجب تحديد تاريخ العودة للنقل المؤقت")
        
        if return_date and not is_temporary:
            raise forms.ValidationError("تاريخ العودة مخصص للنقل المؤقت فقط")
        
        return cleaned_data


class TerminationForm(forms.ModelForm):
    """نموذج إنهاء الخدمة"""
    
    class Meta:
        model = Termination
        fields = [
            'employee', 'termination_type', 'reason', 'notice_period',
            'last_working_day', 'termination_date'
        ]
        widgets = {
            'employee': forms.Select(attrs={
                'class': 'form-control',
                'placeholder': 'اختر الموظف'
            }),
            'termination_type': forms.Select(attrs={
                'class': 'form-control',
                'placeholder': 'نوع إنهاء الخدمة'
            }),
            'reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'سبب إنهاء الخدمة'
            }),
            'notice_period': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'فترة الإشعار بالأيام'
            }),
            'last_working_day': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'termination_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تصفية الموظفين النشطين فقط
        self.fields['employee'].queryset = Employee.objects.filter(
            is_active=True, 
            status='ACTIVE'
        ).select_related('person')
        
        # إضافة تسميات عربية
        self.fields['employee'].label = 'الموظف'
        self.fields['termination_type'].label = 'نوع إنهاء الخدمة'
        self.fields['reason'].label = 'سبب إنهاء الخدمة'
        self.fields['notice_period'].label = 'فترة الإشعار (بالأيام)'
        self.fields['last_working_day'].label = 'آخر يوم عمل'
        self.fields['termination_date'].label = 'تاريخ إنهاء الخدمة'


class SalaryAdditionForm(forms.ModelForm):
    employee = forms.ModelChoiceField(
        queryset=Employee.objects.all(),
        label="الموظف",
        widget=forms.Select(attrs={'class': 'form-select'}),
        required=True
    )

    class Meta:
        model = SalaryAddition
        fields = ['employee', 'addition_type', 'amount', 'start_date', 'end_date', 'is_active', 'description']
        widgets = {
            'addition_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }
        labels = {
            'employee': 'الموظف',
            'addition_type': 'نوع الإضافة',
            'amount': 'المبلغ',
            'start_date': 'تاريخ البداية',
            'end_date': 'تاريخ النهاية',
            'is_active': 'نشط',
            'description': 'الوصف',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص عرض اسم الموظف كما يظهر في صفحة العاملين
        self.fields['employee'].label_from_instance = lambda obj: getattr(obj, 'full_name', str(obj))
        self.fields['end_date'].required = False


class AllowanceDeductionForm(forms.ModelForm):
    deduction_type = forms.ModelChoiceField(queryset=DeductionType.objects.filter(is_active=True), label='نوع الخصم', widget=forms.Select(attrs={'class': 'form-select', 'required': True}))
    date = forms.DateField(label='تاريخ الخصم', widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date', 'required': True}))
    employee = forms.ModelChoiceField(queryset=Employee.objects.filter(status='ACTIVE'), label='الموظف', widget=forms.Select(attrs={'class': 'form-select', 'required': True}), required=True)

    class Meta:
        model = AllowanceDeduction
        fields = ['employee', 'deduction_type', 'amount', 'date', 'notes', 'is_active']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'step': '0.01', 'required': True}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب الخصم'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'employee': 'الموظف',
            'amount': 'المبلغ',
            'date': 'تاريخ الخصم',
            'notes': 'سبب الخصم',
            'is_active': 'نشط',
        }

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.type = 'deduction'
        if commit:
            instance.save()
        return instance


class AllowanceForm(forms.ModelForm):
    allowance_type = forms.ModelChoiceField(queryset=AllowanceType.objects.filter(is_active=True), label='نوع الإضافة', widget=forms.Select(attrs={'class': 'form-select', 'required': True}))
    date = forms.DateField(label='تاريخ الإضافة', widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date', 'required': True}))
    employee = forms.ModelChoiceField(queryset=Employee.objects.filter(status='ACTIVE'), label='الموظف', widget=forms.Select(attrs={'class': 'form-select', 'required': True}), required=True)

    class Meta:
        model = AllowanceDeduction
        fields = ['employee', 'allowance_type', 'amount', 'date', 'notes', 'is_active']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'step': '0.01', 'required': True}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب الإضافة'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'employee': 'الموظف',
            'amount': 'المبلغ',
            'date': 'تاريخ الإضافة',
            'notes': 'سبب الإضافة',
            'is_active': 'نشط',
        }

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.type = 'allowance'
        if commit:
            instance.save()
        return instance


class AllowanceTypeForm(forms.ModelForm):
    class Meta:
        model = AllowanceType
        fields = ['code', 'name', 'name_english', 'description', 'is_taxable', 'is_active']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'required': True}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'required': True}),
            'name_english': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'is_taxable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'code': 'الكود',
            'name': 'اسم النوع',
            'name_english': 'الاسم بالإنجليزية',
            'description': 'الوصف',
            'is_taxable': 'خاضعة للضريبة؟',
            'is_active': 'نشط',
        }

class DeductionTypeForm(forms.ModelForm):
    class Meta:
        model = DeductionType
        fields = ['code', 'name', 'name_english', 'description', 'is_statutory', 'is_active']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'required': True}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'required': True}),
            'name_english': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'is_statutory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'code': 'الكود',
            'name': 'اسم النوع',
            'name_english': 'الاسم بالإنجليزية',
            'description': 'الوصف',
            'is_statutory': 'خصم إلزامي (قانوني)؟',
            'is_active': 'نشط',
        }
