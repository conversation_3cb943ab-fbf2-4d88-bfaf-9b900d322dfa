# Generated by Django 5.2.2 on 2025-06-28 03:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WindowCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('code', models.CharField(choices=[('sales', 'نوافذ المبيعات'), ('purchases', 'نوافذ المشتريات'), ('inventory', 'نوافذ المخازن'), ('accounting', 'نوافذ المحاسبة'), ('persons', 'نوافذ الأشخاص'), ('assets', 'نوافذ الأصول الثابتة'), ('payroll', 'نوافذ الرواتب'), ('branches', 'نوافذ الفروع'), ('tools', 'نوافذ متنوعة')], max_length=20, unique=True, verbose_name='رمز الفئة')),
                ('icon', models.CharField(default='fas fa-window-maximize', max_length=50, verbose_name='أيقونة الفئة')),
                ('color', models.CharField(default='primary', max_length=20, verbose_name='لون الفئة')),
                ('order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'فئة النوافذ',
                'verbose_name_plural': 'فئات النوافذ',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WindowTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('window_type', models.CharField(choices=[('form', 'نموذج إدخال'), ('list', 'قائمة عرض'), ('report', 'تقرير'), ('tool', 'أداة'), ('calculator', 'آلة حاسبة'), ('calendar', 'تقويم'), ('notepad', 'مفكرة')], max_length=20, verbose_name='نوع النافذة')),
                ('html_template', models.TextField(verbose_name='قالب HTML')),
                ('css_styles', models.TextField(blank=True, null=True, verbose_name='أنماط CSS')),
                ('javascript_code', models.TextField(blank=True, null=True, verbose_name='كود JavaScript')),
                ('default_width', models.IntegerField(default=800, verbose_name='العرض الافتراضي')),
                ('default_height', models.IntegerField(default=600, verbose_name='الارتفاع الافتراضي')),
                ('is_resizable', models.BooleanField(default=True, verbose_name='قابل لتغيير الحجم')),
                ('is_draggable', models.BooleanField(default=True, verbose_name='قابل للسحب')),
                ('has_close_button', models.BooleanField(default=True, verbose_name='زر إغلاق')),
                ('has_minimize_button', models.BooleanField(default=True, verbose_name='زر تصغير')),
                ('has_maximize_button', models.BooleanField(default=True, verbose_name='زر تكبير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب النافذة',
                'verbose_name_plural': 'قوالب النوافذ',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WindowDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النافذة')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='رمز النافذة')),
                ('window_type', models.CharField(choices=[('form', 'نموذج إدخال'), ('list', 'قائمة عرض'), ('report', 'تقرير'), ('tool', 'أداة'), ('calculator', 'آلة حاسبة'), ('calendar', 'تقويم'), ('notepad', 'مفكرة')], default='form', max_length=20, verbose_name='نوع النافذة')),
                ('icon', models.CharField(default='fas fa-file', max_length=50, verbose_name='أيقونة النافذة')),
                ('url', models.CharField(blank=True, max_length=200, null=True, verbose_name='رابط النافذة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف النافذة')),
                ('shortcut_key', models.CharField(blank=True, max_length=20, null=True, verbose_name='اختصار لوحة المفاتيح')),
                ('order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('requires_permission', models.CharField(blank=True, max_length=100, null=True, verbose_name='صلاحية مطلوبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='windows.windowcategory', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'تعريف النافذة',
                'verbose_name_plural': 'تعريفات النوافذ',
                'ordering': ['category', 'order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='OpenWindow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=100, verbose_name='مفتاح الجلسة')),
                ('window_instance_id', models.CharField(max_length=50, verbose_name='معرف النافذة')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان النافذة')),
                ('url', models.CharField(max_length=500, verbose_name='رابط النافذة')),
                ('position_x', models.IntegerField(default=100, verbose_name='موضع X')),
                ('position_y', models.IntegerField(default=100, verbose_name='موضع Y')),
                ('width', models.IntegerField(default=800, verbose_name='العرض')),
                ('height', models.IntegerField(default=600, verbose_name='الارتفاع')),
                ('is_maximized', models.BooleanField(default=False, verbose_name='مكبرة')),
                ('is_minimized', models.BooleanField(default=False, verbose_name='مصغرة')),
                ('z_index', models.IntegerField(default=1000, verbose_name='ترتيب العمق')),
                ('opened_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت الفتح')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('window', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='windows.windowdefinition', verbose_name='النافذة')),
            ],
            options={
                'verbose_name': 'النافذة المفتوحة',
                'verbose_name_plural': 'النوافذ المفتوحة',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='UserWindowPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_favorite', models.BooleanField(default=False, verbose_name='مفضلة')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='مثبتة')),
                ('custom_shortcut', models.CharField(blank=True, max_length=20, null=True, verbose_name='اختصار مخصص')),
                ('last_used', models.DateTimeField(blank=True, null=True, verbose_name='آخر استخدام')),
                ('usage_count', models.IntegerField(default=0, verbose_name='عدد مرات الاستخدام')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('window', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='windows.windowdefinition', verbose_name='النافذة')),
            ],
            options={
                'verbose_name': 'تفضيلات النوافذ',
                'verbose_name_plural': 'تفضيلات النوافذ',
                'unique_together': {('user', 'window')},
            },
        ),
        migrations.CreateModel(
            name='QuickAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.IntegerField(default=0, verbose_name='الموضع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('window', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='windows.windowdefinition', verbose_name='النافذة')),
            ],
            options={
                'verbose_name': 'الوصول السريع',
                'verbose_name_plural': 'الوصول السريع',
                'ordering': ['position'],
                'unique_together': {('user', 'window')},
            },
        ),
    ]
