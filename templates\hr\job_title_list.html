{% extends 'base/base.html' %}
{% load static %}

{% block title %}المسميات الوظيفية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="{% url 'hr:dashboard' %}" class="btn btn-outline-secondary me-3" title="العودة للوحة التحكم">
                        <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
                    </a>
                    <h2 class="mb-0">
                        <i class="fas fa-briefcase text-primary me-2"></i>
                        المسميات الوظيفية
                    </h2>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'hr:job_title_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إضافة مسمى وظيفي جديد
                    </a>
                    <button class="btn btn-outline-success" onclick="exportTableToCSV('job_titles.csv')" title="تصدير إلى CSV">
                        <i class="fas fa-file-csv"></i>
                    </button>
                    <button class="btn btn-outline-dark" onclick="window.print()" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fs-2 fw-bold">{{ items.count }}</div>
                            <p class="mb-0">إجمالي المسميات الوظيفية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fs-2 fw-bold">{{ items.count }}</div>
                            <p class="mb-0">المسميات النشطة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fs-2 fw-bold">{{ departments_count }}</div>
                            <p class="mb-0">الأقسام المغطاة</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-sitemap"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fs-2 fw-bold">{{ employees_count }}</div>
                            <p class="mb-0">الموظفين العاملين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" class="row g-2 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">بحث بالاسم أو الكود</label>
                    <input type="text" name="q" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">القسم</label>
                    <select name="department" class="form-select">
                        <option value="">كل الأقسام</option>
                        {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:'s' %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex gap-2">
                    <button class="btn btn-outline-primary w-100" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="?" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Job Titles Table -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="jobTitlesTable">
                    <thead class="table-primary">
                        <tr>
                            <th>#</th>
                            <th>المسمى الوظيفي</th>
                            <th>الكود</th>
                            <th>القسم</th>
                            <th>عدد الموظفين</th>
                            <th>الحالة</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td class="fw-bold">{{ forloop.counter }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-briefcase text-primary me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ item.name }}</div>
                                        {% if item.description %}
                                            <small class="text-muted">{{ item.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ item.code }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ item.department.name }}</span>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ item.employees.count }}</span>
                            </td>
                            <td>
                                {% if item.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-warning text-dark">غير نشط</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'hr:position_detail' item.pk %}" class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'hr:position_edit' item.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" title="حذف" onclick="confirmDelete({{ item.pk }}, '{{ item.name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted py-5">
                                <i class="fas fa-briefcase fa-3x mb-3 text-muted"></i>
                                <h5 class="text-muted">لا توجد مسميات وظيفية</h5>
                                <p class="text-muted mb-3">ابدأ بإضافة أول مسمى وظيفي في النظام</p>
                                <a href="{% url 'hr:job_title_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة مسمى وظيفي جديد
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المسمى الوظيفي: <strong id="deleteItemName"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(itemId, itemName) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('confirmDeleteBtn').onclick = function() {
        deletePosition(itemId);
    };
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function deletePosition(positionId) {
    fetch(`/hr/positions/${positionId}/delete/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء الحذف: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الحذف');
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function exportTableToCSV(filename) {
    const table = document.getElementById('jobTitlesTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];
        
        for (let j = 0; j < cols.length; j++) {
            let text = cols[j].innerText.replace(/"/g, '""');
            csvRow.push('"' + text + '"');
        }
        
        csv.push(csvRow.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
</script>
{% endblock %}
