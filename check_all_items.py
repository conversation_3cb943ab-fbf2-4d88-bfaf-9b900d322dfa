#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit

def check_all_items():
    print("=== Checking all items in database ===")
    
    # Get all items (active and inactive)
    all_items = Item.objects.all().select_related('category', 'unit')
    active_items = Item.objects.filter(is_active=True).select_related('category', 'unit')
    inactive_items = Item.objects.filter(is_active=False).select_related('category', 'unit')
    
    print(f"Total items in database: {all_items.count()}")
    print(f"Active items: {active_items.count()}")
    print(f"Inactive items: {inactive_items.count()}")
    
    print("\n=== All Active Items ===")
    for i, item in enumerate(active_items, 1):
        try:
            category_name = item.category.name if item.category else "No Category"
            unit_name = item.unit.symbol if item.unit else "No Unit"
            print(f"{i:2d}. {item.code}: {item.name} | Cat: {category_name} | Unit: {unit_name}")
        except UnicodeEncodeError:
            print(f"{i:2d}. {item.code}: [Unicode Error] | Active: {item.is_active}")
        except Exception as e:
            print(f"{i:2d}. {item.code}: [Error: {e}] | Active: {item.is_active}")
    
    if inactive_items.exists():
        print("\n=== Inactive Items ===")
        for i, item in enumerate(inactive_items, 1):
            try:
                print(f"{i:2d}. {item.code}: {item.name} | Active: {item.is_active}")
            except UnicodeEncodeError:
                print(f"{i:2d}. {item.code}: [Unicode Error] | Active: {item.is_active}")
    
    # Check for items with missing relationships
    print("\n=== Items with missing relationships ===")
    items_no_category = Item.objects.filter(is_active=True, category__isnull=True)
    items_no_unit = Item.objects.filter(is_active=True, unit__isnull=True)
    
    if items_no_category.exists():
        print(f"Items without category: {items_no_category.count()}")
        for item in items_no_category:
            print(f"  - {item.code}: {item.name}")
    
    if items_no_unit.exists():
        print(f"Items without unit: {items_no_unit.count()}")
        for item in items_no_unit:
            print(f"  - {item.code}: {item.name}")
    
    # Check categories and units
    print(f"\nActive categories: {ItemCategory.objects.filter(is_active=True).count()}")
    print(f"Active units: {Unit.objects.filter(is_active=True).count()}")

if __name__ == "__main__":
    check_all_items()