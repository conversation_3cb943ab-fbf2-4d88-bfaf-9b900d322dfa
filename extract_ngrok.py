#!/usr/bin/env python3
"""
فك ضغط ngrok
Extract ngrok
"""

import zipfile
import os
from pathlib import Path

def extract_ngrok():
    """فك ضغط ngrok"""
    print("📂 فك ضغط ngrok...")
    
    zip_file = Path('ngrok/ngrok.exe')  # الملف المحمل
    ngrok_dir = Path('ngrok')
    
    try:
        # إعادة تسمية الملف
        zip_path = ngrok_dir / 'ngrok.zip'
        if zip_file.exists():
            zip_file.rename(zip_path)
            print("✅ تم إعادة تسمية الملف")
        
        # فك الضغط
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ngrok_dir)
        
        print("✅ تم فك ضغط ngrok")
        
        # حذف ملف الzip
        zip_path.unlink()
        
        # التحقق من وجود ngrok.exe
        ngrok_exe = ngrok_dir / 'ngrok.exe'
        if ngrok_exe.exists():
            print("✅ ngrok جاهز للاستخدام!")
            return True
        else:
            print("❌ لم يتم العثور على ngrok.exe")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    extract_ngrok()
