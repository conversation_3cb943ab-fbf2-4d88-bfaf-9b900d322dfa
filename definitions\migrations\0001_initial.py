# Generated by Django 5.2.2 on 2025-06-28 03:54

import definitions.models
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المجموعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('depreciation_method', models.CharField(choices=[('STRAIGHT_LINE', 'القسط الثابت'), ('DECLINING_BALANCE', 'الرصيد المتناقص'), ('UNITS_OF_PRODUCTION', 'وحدات الإنتاج'), ('SUM_OF_YEARS', 'مجموع سنوات الخدمة')], default='STRAIGHT_LINE', max_length=20, verbose_name='طريقة الاستهلاك')),
                ('default_useful_life', models.IntegerField(blank=True, null=True, verbose_name='العمر الافتراضي (سنوات)')),
                ('default_salvage_value_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة القيمة المتبقية (%)')),
                ('asset_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الأصل')),
                ('depreciation_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الاستهلاك')),
                ('accumulated_depreciation_account', models.CharField(blank=True, max_length=20, verbose_name='حساب مجمع الاستهلاك')),
                ('requires_insurance', models.BooleanField(default=False, verbose_name='يتطلب تأمين')),
                ('requires_maintenance', models.BooleanField(default=True, verbose_name='يتطلب صيانة')),
                ('is_depreciable', models.BooleanField(default=True, verbose_name='قابل للاستهلاك')),
                ('asset_category', models.CharField(choices=[('BUILDING', 'مباني'), ('MACHINERY', 'آلات ومعدات'), ('VEHICLE', 'مركبات'), ('FURNITURE', 'أثاث ومفروشات'), ('COMPUTER', 'أجهزة حاسوب'), ('LAND', 'أراضي'), ('INTANGIBLE', 'أصول معنوية'), ('OTHER', 'أخرى')], default='OTHER', max_length=20, verbose_name='فئة الأصل')),
                ('min_cost_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأدنى للتكلفة')),
                ('max_cost_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأقصى للتكلفة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.assetgroup', verbose_name='المجموعة الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مجموعة أصول',
                'verbose_name_plural': 'مجموعات الأصول',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='رمز العملة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العملة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10, verbose_name='سعر الصرف')),
                ('is_base_currency', models.BooleanField(default=False, verbose_name='العملة الأساسية')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'عملة',
                'verbose_name_plural': 'العملات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CompanySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('company_name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('company_name_english', models.CharField(blank=True, max_length=200, verbose_name='اسم الشركة بالإنجليزية')),
                ('logo', models.ImageField(blank=True, help_text='يفضل أن يكون الشعار بصيغة PNG أو JPG وبحجم 200x200 بكسل', null=True, upload_to=definitions.models.company_logo_upload_path, verbose_name='شعار الشركة')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='المدينة')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='المحافظة')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('tax_number', models.CharField(blank=True, max_length=20, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=20, verbose_name='السجل التجاري')),
                ('app_name', models.CharField(default='نظام الحسابات', max_length=100, verbose_name='اسم التطبيق')),
                ('app_version', models.CharField(default='1.0.0', max_length=20, verbose_name='إصدار التطبيق')),
                ('items_per_page', models.IntegerField(default=25, verbose_name='عدد العناصر في الصفحة')),
                ('date_format', models.CharField(choices=[('%d/%m/%Y', 'DD/MM/YYYY'), ('%m/%d/%Y', 'MM/DD/YYYY'), ('%Y-%m-%d', 'YYYY-MM-DD')], default='%d/%m/%Y', max_length=20, verbose_name='تنسيق التاريخ')),
                ('session_timeout_minutes', models.IntegerField(default=60, verbose_name='انتهاء الجلسة (دقيقة)')),
                ('password_min_length', models.IntegerField(default=8, verbose_name='الحد الأدنى لطول كلمة المرور')),
                ('auto_backup_enabled', models.BooleanField(default=False, verbose_name='تفعيل النسخ الاحتياطي التلقائي')),
                ('backup_frequency_days', models.IntegerField(default=7, verbose_name='تكرار النسخ الاحتياطي (أيام)')),
                ('email_notifications_enabled', models.BooleanField(default=True, verbose_name='تفعيل إشعارات البريد الإلكتروني')),
                ('sms_notifications_enabled', models.BooleanField(default=False, verbose_name='تفعيل إشعارات الرسائل النصية')),
                ('default_report_format', models.CharField(choices=[('PDF', 'PDF'), ('EXCEL', 'Excel'), ('CSV', 'CSV')], default='PDF', max_length=10, verbose_name='تنسيق التقارير الافتراضي')),
                ('print_logo_on_reports', models.BooleanField(default=True, verbose_name='طباعة الشعار في التقارير')),
                ('print_company_info', models.BooleanField(default=True, verbose_name='طباعة معلومات الشركة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('default_currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة الافتراضية')),
            ],
            options={
                'verbose_name': 'إعدادات الشركة',
                'verbose_name_plural': 'إعدادات الشركة',
            },
        ),
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود البنك')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البنك')),
                ('branch', models.CharField(blank=True, max_length=100, verbose_name='الفرع')),
                ('account_number', models.CharField(max_length=50, verbose_name='رقم الحساب')),
                ('account_name', models.CharField(max_length=100, verbose_name='اسم الحساب')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('contact_info', models.TextField(blank=True, verbose_name='معلومات الاتصال')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
            ],
            options={
                'verbose_name': 'بنك',
                'verbose_name_plural': 'البنوك',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CurrencyRateHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('rate', models.DecimalField(decimal_places=4, max_digits=10, verbose_name='السعر')),
                ('rate_type', models.CharField(choices=[('BUY', 'شراء'), ('SELL', 'بيع'), ('AVERAGE', 'متوسط'), ('OFFICIAL', 'رسمي')], default='AVERAGE', max_length=20, verbose_name='نوع السعر')),
                ('source', models.CharField(max_length=100, verbose_name='المصدر')),
                ('recorded_date', models.DateTimeField(verbose_name='تاريخ التسجيل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'تاريخ سعر العملة',
                'verbose_name_plural': 'تاريخ أسعار العملات',
                'ordering': ['-recorded_date'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود فئة المصروف')),
                ('name', models.CharField(max_length=100, verbose_name='اسم فئة المصروف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('category', models.CharField(choices=[('OPERATIONAL', 'مصروفات تشغيلية'), ('ADMINISTRATIVE', 'مصروفات إدارية'), ('SELLING', 'مصروفات بيعية'), ('FINANCIAL', 'مصروفات مالية'), ('CAPITAL', 'مصروفات رأسمالية'), ('OTHER', 'أخرى')], default='OPERATIONAL', max_length=20, verbose_name='تصنيف المصروف')),
                ('default_account', models.CharField(blank=True, max_length=20, verbose_name='الحساب الافتراضي')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='يتطلب موافقة')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأقصى للمبلغ')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'فئة مصروف',
                'verbose_name_plural': 'فئات المصروفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود بند المصروف')),
                ('name', models.CharField(max_length=100, verbose_name='اسم بند المصروف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('account_number', models.CharField(blank=True, max_length=20, verbose_name='رقم الحساب')),
                ('is_recurring', models.BooleanField(default=False, verbose_name='مصروف دوري')),
                ('recurring_period', models.CharField(blank=True, choices=[('DAILY', 'يومي'), ('WEEKLY', 'أسبوعي'), ('MONTHLY', 'شهري'), ('QUARTERLY', 'ربع سنوي'), ('YEARLY', 'سنوي')], max_length=20, verbose_name='فترة التكرار')),
                ('min_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأدنى للمبلغ')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأقصى للمبلغ')),
                ('requires_document', models.BooleanField(default=True, verbose_name='يتطلب مستند')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='يتطلب موافقة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('expense_category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.expensecategory', verbose_name='فئة المصروف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'بند مصروف',
                'verbose_name_plural': 'بنود المصروفات',
                'ordering': ['expense_category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ItemCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الفئة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='definitions.itemcategory', verbose_name='الفئة الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'فئة صنف',
                'verbose_name_plural': 'فئات الأصناف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الشخص/الجهة')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('name_english', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('person_type', models.CharField(choices=[('CUSTOMER', 'عميل'), ('SUPPLIER', 'مورد'), ('EMPLOYEE', 'موظف'), ('BOTH', 'عميل ومورد'), ('BANK', 'بنك'), ('GOVERNMENT', 'جهة حكومية'), ('PARTNER', 'شريك'), ('OTHER', 'أخرى')], default='CUSTOMER', max_length=20, verbose_name='نوع الشخص/الجهة')),
                ('entity_type', models.CharField(choices=[('INDIVIDUAL', 'فرد'), ('COMPANY', 'شركة'), ('INSTITUTION', 'مؤسسة'), ('GOVERNMENT', 'جهة حكومية')], default='INDIVIDUAL', max_length=20, verbose_name='نوع الكيان')),
                ('national_id', models.CharField(blank=True, max_length=20, verbose_name='رقم الهوية/السجل التجاري')),
                ('tax_number', models.CharField(blank=True, max_length=20, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=20, verbose_name='السجل التجاري')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='المدينة')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='المحافظة/المنطقة')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('credit_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='حد الائتمان')),
                ('payment_terms', models.IntegerField(blank=True, null=True, verbose_name='مدة السداد (أيام)')),
                ('account_receivable', models.CharField(blank=True, max_length=20, verbose_name='حساب المدينين')),
                ('account_payable', models.CharField(blank=True, max_length=20, verbose_name='حساب الدائنين')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول')),
                ('contact_title', models.CharField(blank=True, max_length=100, verbose_name='المنصب')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_active_customer', models.BooleanField(default=True, verbose_name='عميل نشط')),
                ('is_active_supplier', models.BooleanField(default=False, verbose_name='مورد نشط')),
                ('allow_credit', models.BooleanField(default=True, verbose_name='السماح بالائتمان')),
                ('registration_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسجيل')),
                ('last_transaction_date', models.DateField(blank=True, null=True, verbose_name='تاريخ آخر معاملة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة الافتراضية')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'شخص/جهة',
                'verbose_name_plural': 'الأشخاص والجهات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Printer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الطابعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الطابعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('brand', models.CharField(blank=True, max_length=50, verbose_name='العلامة التجارية')),
                ('model', models.CharField(blank=True, max_length=50, verbose_name='الموديل')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('printer_type', models.CharField(choices=[('THERMAL', 'حرارية'), ('INKJET', 'نفث الحبر'), ('LASER', 'ليزر'), ('DOT_MATRIX', 'نقطية'), ('LABEL', 'ملصقات'), ('RECEIPT', 'فواتير'), ('BARCODE', 'باركود'), ('PHOTO', 'صور'), ('OTHER', 'أخرى')], default='THERMAL', max_length=20, verbose_name='نوع الطابعة')),
                ('connection_type', models.CharField(choices=[('USB', 'USB'), ('NETWORK', 'شبكة'), ('BLUETOOTH', 'بلوتوث'), ('WIFI', 'واي فاي'), ('SERIAL', 'تسلسلي'), ('PARALLEL', 'متوازي')], default='USB', max_length=20, verbose_name='نوع الاتصال')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('port', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(65535)], verbose_name='رقم المنفذ')),
                ('paper_size', models.CharField(choices=[('A4', 'A4'), ('A5', 'A5'), ('LETTER', 'Letter'), ('RECEIPT_80MM', 'فاتورة 80 مم'), ('RECEIPT_58MM', 'فاتورة 58 مم'), ('LABEL_4X6', 'ملصق 4×6'), ('CUSTOM', 'مخصص')], default='A4', max_length=20, verbose_name='حجم الورق')),
                ('paper_width', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='عرض الورق (مم)')),
                ('paper_height', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='طول الورق (مم)')),
                ('dpi', models.IntegerField(blank=True, null=True, verbose_name='دقة الطباعة (DPI)')),
                ('print_speed', models.CharField(choices=[('SLOW', 'بطيء'), ('NORMAL', 'عادي'), ('FAST', 'سريع')], default='NORMAL', max_length=20, verbose_name='سرعة الطباعة')),
                ('color_support', models.BooleanField(default=False, verbose_name='دعم الألوان')),
                ('duplex_support', models.BooleanField(default=False, verbose_name='دعم الطباعة على الوجهين')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='القسم')),
                ('usage_type', models.CharField(choices=[('INVOICES', 'الفواتير'), ('REPORTS', 'التقارير'), ('LABELS', 'الملصقات'), ('RECEIPTS', 'الإيصالات'), ('BARCODES', 'الباركود'), ('DOCUMENTS', 'المستندات'), ('GENERAL', 'عام')], default='GENERAL', max_length=20, verbose_name='نوع الاستخدام')),
                ('is_default', models.BooleanField(default=False, verbose_name='الطابعة الافتراضية')),
                ('is_shared', models.BooleanField(default=False, verbose_name='طابعة مشتركة')),
                ('auto_cut', models.BooleanField(default=False, verbose_name='قطع تلقائي للورق')),
                ('cash_drawer', models.BooleanField(default=False, verbose_name='فتح درج النقد')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشراء')),
                ('warranty_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء الضمان')),
                ('last_maintenance', models.DateField(blank=True, null=True, verbose_name='آخر صيانة')),
                ('next_maintenance', models.DateField(blank=True, null=True, verbose_name='الصيانة القادمة')),
                ('total_pages_printed', models.IntegerField(default=0, verbose_name='إجمالي الصفحات المطبوعة')),
                ('pages_this_month', models.IntegerField(default=0, verbose_name='صفحات هذا الشهر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('responsible_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_printers', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'طابعة',
                'verbose_name_plural': 'الطابعات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductionStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المرحلة')),
                ('description', models.TextField(blank=True, verbose_name='وصف المرحلة')),
                ('order', models.PositiveIntegerField(default=1, verbose_name='الترتيب')),
                ('duration_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='المدة بالساعات')),
                ('cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة لكل ساعة')),
                ('required_skills', models.TextField(blank=True, verbose_name='المهارات المطلوبة')),
                ('equipment_needed', models.TextField(blank=True, verbose_name='المعدات المطلوبة')),
                ('quality_standards', models.TextField(blank=True, verbose_name='معايير الجودة')),
                ('safety_notes', models.TextField(blank=True, verbose_name='ملاحظات السلامة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مرحلة إنتاج',
                'verbose_name_plural': 'مراحل الإنتاج',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProfitCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود مركز الربحية')),
                ('name', models.CharField(max_length=100, verbose_name='اسم مركز الربحية')),
                ('name_english', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('level', models.IntegerField(default=1, verbose_name='المستوى')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('target_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='هدف الإيرادات السنوي')),
                ('target_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='هدف الربح السنوي')),
                ('target_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='هدف هامش الربح (%)')),
                ('evaluation_period', models.CharField(choices=[('MONTHLY', 'شهري'), ('QUARTERLY', 'ربع سنوي'), ('SEMI_ANNUAL', 'نصف سنوي'), ('ANNUAL', 'سنوي')], default='QUARTERLY', max_length=20, verbose_name='فترة التقييم')),
                ('revenue_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الإيرادات')),
                ('expense_account', models.CharField(blank=True, max_length=20, verbose_name='حساب المصروفات')),
                ('asset_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الأصول')),
                ('allocate_overhead', models.BooleanField(default=True, verbose_name='توزيع التكاليف الإضافية')),
                ('overhead_allocation_method', models.CharField(choices=[('REVENUE_BASED', 'على أساس الإيرادات'), ('EMPLOYEE_BASED', 'على أساس عدد الموظفين'), ('ASSET_BASED', 'على أساس الأصول'), ('EQUAL', 'توزيع متساوي'), ('CUSTOM', 'طريقة مخصصة')], default='REVENUE_BASED', max_length=20, verbose_name='طريقة توزيع التكاليف')),
                ('overhead_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة التكاليف الإضافية (%)')),
                ('include_in_reports', models.BooleanField(default=True, verbose_name='تضمين في التقارير')),
                ('consolidate_children', models.BooleanField(default=True, verbose_name='دمج البيانات من المراكز الفرعية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_profit_centers', to=settings.AUTH_USER_MODEL, verbose_name='المدير المسؤول')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='definitions.profitcenter', verbose_name='مركز الربحية الأب')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مركز ربحية',
                'verbose_name_plural': 'مراكز الربحية',
                'ordering': ['level', 'code', 'name'],
            },
        ),
        migrations.CreateModel(
            name='RevenueCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود فئة الإيراد')),
                ('name', models.CharField(max_length=100, verbose_name='اسم فئة الإيراد')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('category', models.CharField(choices=[('SALES', 'إيرادات مبيعات'), ('SERVICES', 'إيرادات خدمات'), ('INVESTMENT', 'إيرادات استثمارية'), ('FINANCIAL', 'إيرادات مالية'), ('RENTAL', 'إيرادات إيجارات'), ('OTHER', 'أخرى')], default='SALES', max_length=20, verbose_name='تصنيف الإيراد')),
                ('default_account', models.CharField(blank=True, max_length=20, verbose_name='الحساب الافتراضي')),
                ('is_taxable', models.BooleanField(default=True, verbose_name='خاضع للضريبة')),
                ('tax_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='معدل الضريبة (%)')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'فئة إيراد',
                'verbose_name_plural': 'فئات الإيرادات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='RevenueItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود بند الإيراد')),
                ('name', models.CharField(max_length=100, verbose_name='اسم بند الإيراد')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('account_number', models.CharField(blank=True, max_length=20, verbose_name='رقم الحساب')),
                ('is_recurring', models.BooleanField(default=False, verbose_name='إيراد دوري')),
                ('recurring_period', models.CharField(blank=True, choices=[('DAILY', 'يومي'), ('WEEKLY', 'أسبوعي'), ('MONTHLY', 'شهري'), ('QUARTERLY', 'ربع سنوي'), ('YEARLY', 'سنوي')], max_length=20, verbose_name='فترة التكرار')),
                ('is_taxable', models.BooleanField(default=True, verbose_name='خاضع للضريبة')),
                ('tax_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='معدل الضريبة (%)')),
                ('min_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأدنى للمبلغ')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد الأقصى للمبلغ')),
                ('requires_contract', models.BooleanField(default=False, verbose_name='يتطلب عقد')),
                ('commission_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='معدل العمولة (%)')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('revenue_category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.revenuecategory', verbose_name='فئة الإيراد')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'بند إيراد',
                'verbose_name_plural': 'بنود الإيرادات',
                'ordering': ['revenue_category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FinancialAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('alert_type', models.CharField(choices=[('EXPENSE_LIMIT', 'تجاوز حد المصروف'), ('RECURRING_DUE', 'مصروف دوري مستحق'), ('APPROVAL_REQUIRED', 'يتطلب موافقة'), ('DOCUMENT_MISSING', 'مستند مفقود'), ('REVENUE_TARGET', 'هدف إيراد'), ('TAX_CALCULATION', 'حساب ضريبة')], max_length=20, verbose_name='نوع التنبيه')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('priority', models.CharField(choices=[('LOW', 'منخفض'), ('MEDIUM', 'متوسط'), ('HIGH', 'عالي'), ('URGENT', 'عاجل')], default='MEDIUM', max_length=10, verbose_name='مستوى الأولوية')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('ACKNOWLEDGED', 'تم الاطلاع'), ('RESOLVED', 'تم الحل'), ('DISMISSED', 'تم التجاهل')], default='PENDING', max_length=15, verbose_name='حالة التنبيه')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='المبلغ')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('expense_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.expensecategory', verbose_name='فئة المصروف')),
                ('expense_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.expenseitem', verbose_name='بند المصروف')),
                ('target_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='financial_alerts', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم المستهدف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('revenue_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.revenuecategory', verbose_name='فئة الإيراد')),
                ('revenue_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.revenueitem', verbose_name='بند الإيراد')),
            ],
            options={
                'verbose_name': 'تنبيه مالي',
                'verbose_name_plural': 'التنبيهات المالية',
                'ordering': ['-created_at', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='Treasury',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الخزينة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الخزينة')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.currency', verbose_name='العملة')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_treasuries', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'خزينة',
                'verbose_name_plural': 'الخزائن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='كود الوحدة')),
                ('name', models.CharField(max_length=50, verbose_name='اسم الوحدة')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز الوحدة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'وحدة قياس',
                'verbose_name_plural': 'وحدات القياس',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الصنف')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الصنف')),
                ('item_type', models.CharField(choices=[('RAW_MATERIAL', 'مادة خام'), ('FINISHED_PRODUCT', 'منتج نهائي'), ('SEMI_FINISHED', 'منتج نصف مصنع'), ('CONSUMABLE', 'مادة استهلاكية'), ('SERVICE', 'خدمة')], default='RAW_MATERIAL', max_length=20, verbose_name='نوع الصنف')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='الباركود')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('cost_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('max_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('image', models.ImageField(blank=True, null=True, upload_to='items/', verbose_name='صورة الصنف')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('finished_product', models.ForeignKey(blank=True, help_text='المنتج النهائي الذي يمكن إنتاجه من هذه المادة الخام', limit_choices_to={'item_type': 'FINISHED_PRODUCT'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='raw_materials', to='definitions.item', verbose_name='المنتج النهائي')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.itemcategory', verbose_name='الفئة')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FinishedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('name_english', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('brand', models.CharField(blank=True, max_length=100, verbose_name='العلامة التجارية')),
                ('model', models.CharField(blank=True, max_length=100, verbose_name='الموديل')),
                ('version', models.CharField(blank=True, max_length=50, verbose_name='الإصدار')),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='الباركود')),
                ('sku', models.CharField(blank=True, max_length=100, verbose_name='رقم المنتج')),
                ('specifications', models.TextField(blank=True, verbose_name='المواصفات الفنية')),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن (كجم)')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('color', models.CharField(blank=True, max_length=50, verbose_name='اللون')),
                ('material', models.CharField(blank=True, max_length=100, verbose_name='المادة')),
                ('standard_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة المعيارية')),
                ('material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة المواد')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكاليف الإضافية')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر الجملة')),
                ('retail_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='سعر التجزئة')),
                ('min_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('max_stock', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='نقطة إعادة الطلب')),
                ('production_lead_time', models.IntegerField(blank=True, null=True, verbose_name='مدة الإنتاج (أيام)')),
                ('batch_size', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='حجم الدفعة')),
                ('quality_grade', models.CharField(blank=True, choices=[('A', 'ممتاز'), ('B', 'جيد جداً'), ('C', 'جيد'), ('D', 'مقبول')], max_length=20, verbose_name='درجة الجودة')),
                ('shelf_life_days', models.IntegerField(blank=True, null=True, verbose_name='مدة الصلاحية (أيام)')),
                ('certifications', models.TextField(blank=True, verbose_name='الشهادات والمعايير')),
                ('compliance_standards', models.TextField(blank=True, verbose_name='معايير الامتثال')),
                ('packaging_type', models.CharField(blank=True, max_length=100, verbose_name='نوع التعبئة')),
                ('package_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='وزن العبوة (كجم)')),
                ('package_dimensions', models.CharField(blank=True, max_length=100, verbose_name='أبعاد العبوة')),
                ('units_per_package', models.IntegerField(blank=True, null=True, verbose_name='الوحدات في العبوة')),
                ('inventory_account', models.CharField(blank=True, max_length=20, verbose_name='حساب المخزون')),
                ('cogs_account', models.CharField(blank=True, max_length=20, verbose_name='حساب تكلفة البضاعة المباعة')),
                ('revenue_account', models.CharField(blank=True, max_length=20, verbose_name='حساب الإيرادات')),
                ('is_manufactured', models.BooleanField(default=True, verbose_name='منتج مصنع')),
                ('is_sellable', models.BooleanField(default=True, verbose_name='قابل للبيع')),
                ('is_purchasable', models.BooleanField(default=False, verbose_name='قابل للشراء')),
                ('track_serial_numbers', models.BooleanField(default=False, verbose_name='تتبع الأرقام التسلسلية')),
                ('track_lot_numbers', models.BooleanField(default=False, verbose_name='تتبع أرقام الدفعات')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='صورة المنتج')),
                ('technical_drawing', models.FileField(blank=True, null=True, upload_to='products/drawings/', verbose_name='الرسم الفني')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.itemcategory', verbose_name='فئة المنتج')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'منتج تام',
                'verbose_name_plural': 'المنتجات التامة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المخزن')),
                ('location', models.TextField(blank=True, verbose_name='الموقع')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='السعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='مدير المخزن')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseZone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=20, verbose_name='كود المنطقة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المنطقة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='مكيف الهواء')),
                ('humidity_controlled', models.BooleanField(default=False, verbose_name='مراقب الرطوبة')),
                ('security_level', models.CharField(choices=[('LOW', 'منخفض'), ('MEDIUM', 'متوسط'), ('HIGH', 'عالي'), ('CRITICAL', 'حرج')], default='MEDIUM', max_length=20, verbose_name='مستوى الأمان')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'منطقة مخزن',
                'verbose_name_plural': 'مناطق المخازن',
                'ordering': ['warehouse', 'name'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('code', models.CharField(max_length=30, verbose_name='كود الموقع')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموقع')),
                ('aisle', models.CharField(blank=True, max_length=10, verbose_name='الممر')),
                ('rack', models.CharField(blank=True, max_length=10, verbose_name='الرف')),
                ('shelf', models.CharField(blank=True, max_length=10, verbose_name='الطبقة')),
                ('bin', models.CharField(blank=True, max_length=10, verbose_name='الصندوق')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعة')),
                ('max_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الحد الأقصى للوزن (كجم)')),
                ('is_available', models.BooleanField(default=True, verbose_name='متاح')),
                ('is_pickable', models.BooleanField(default=True, verbose_name='قابل للانتقاء')),
                ('is_receivable', models.BooleanField(default=True, verbose_name='قابل للاستقبال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('capacity_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='definitions.unit', verbose_name='وحدة السعة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
                ('zone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousezone', verbose_name='المنطقة')),
            ],
            options={
                'verbose_name': 'موقع مخزن',
                'verbose_name_plural': 'مواقع المخازن',
                'ordering': ['warehouse', 'aisle', 'rack', 'shelf', 'bin'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='EgyptianBankRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('bank_name', models.CharField(max_length=100, verbose_name='اسم البنك')),
                ('buy_rate', models.DecimalField(decimal_places=4, max_digits=10, verbose_name='سعر الشراء')),
                ('sell_rate', models.DecimalField(decimal_places=4, max_digits=10, verbose_name='سعر البيع')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('source_url', models.URLField(blank=True, verbose_name='مصدر البيانات')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currency', verbose_name='العملة')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'سعر صرف البنك',
                'verbose_name_plural': 'أسعار صرف البنوك',
                'ordering': ['bank_name', 'currency__code'],
                'unique_together': {('currency', 'bank_name')},
            },
        ),
        migrations.CreateModel(
            name='ItemLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('min_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى')),
                ('max_quantity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأقصى')),
                ('reorder_point', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='نقطة إعادة الطلب')),
                ('priority', models.IntegerField(default=1, verbose_name='الأولوية')),
                ('location_type', models.CharField(choices=[('PRIMARY', 'أساسي'), ('SECONDARY', 'ثانوي'), ('OVERFLOW', 'فائض'), ('PICKING', 'انتقاء'), ('RESERVE', 'احتياطي')], default='PRIMARY', max_length=20, verbose_name='نوع الموقع')),
                ('is_default', models.BooleanField(default=False, verbose_name='الموقع الافتراضي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_date', models.DateField(auto_now_add=True, verbose_name='تاريخ التخصيص')),
                ('last_movement_date', models.DateTimeField(blank=True, null=True, verbose_name='آخر حركة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.item', verbose_name='الصنف')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouse', verbose_name='المخزن')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehouselocation', verbose_name='الموقع')),
            ],
            options={
                'verbose_name': 'موقع صنف',
                'verbose_name_plural': 'مواقع الأصناف',
                'ordering': ['item', 'warehouse', 'priority'],
                'unique_together': {('item', 'warehouse', 'location')},
            },
        ),
    ]
