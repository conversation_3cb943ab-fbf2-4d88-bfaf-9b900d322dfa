@echo off
chcp 65001 >nul
REM ===================================================
REM Advanced Server Management System
REM نظام إدارة الخادم المتقدم
REM ===================================================

title Advanced Server Management - Osaric Accounts System

:main_menu
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 Advanced Server Management                 ║
echo ║                   Osaric Accounts System                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎯 Server Options:
echo ═══════════════════
echo 1. 🚀 Start Advanced Stable Server (Recommended)
echo 2. 🔧 Start Simple Stable Server
echo 3. 📊 Start Performance Monitor
echo 4. 💾 Backup Management
echo 5. 🔍 System Diagnostics
echo 6. 📈 View Server Statistics
echo 7. 🛠️  System Maintenance
echo 8. ⚙️  Configuration
echo 9. 📋 View Logs
echo 0. 🚪 Exit
echo.

set /p choice="Choose option (0-9): "

if "%choice%"=="1" goto advanced_server
if "%choice%"=="2" goto simple_server
if "%choice%"=="3" goto performance_monitor
if "%choice%"=="4" goto backup_menu
if "%choice%"=="5" goto diagnostics
if "%choice%"=="6" goto statistics
if "%choice%"=="7" goto maintenance
if "%choice%"=="8" goto configuration
if "%choice%"=="9" goto view_logs
if "%choice%"=="0" goto exit
goto invalid_choice

:advanced_server
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 Advanced Stable Server                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔥 Advanced Features:
echo • Auto-restart on crashes
echo • Health monitoring every 30 seconds
echo • Performance tracking and alerts
echo • Automatic backups every hour
echo • Memory and CPU monitoring
echo • Response time tracking
echo • Log rotation and management
echo • System resource monitoring
echo.
echo 🌐 Server will be available at: http://127.0.0.1:8000/
echo ⌨️ Press Ctrl+C to stop server safely
echo.
echo Starting Advanced Stable Server...
echo.
python advanced_stable_server.py
pause
goto main_menu

:simple_server
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔧 Simple Stable Server                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔥 Simple Features:
echo • Auto-restart on crashes
echo • Basic health monitoring
echo • Simple logging
echo • Lightweight operation
echo.
echo 🌐 Server will be available at: http://127.0.0.1:8000/
echo ⌨️ Press Ctrl+C to stop server safely
echo.
echo Starting Simple Stable Server...
echo.
python stable_server_fixed.py
pause
goto main_menu

:performance_monitor
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📊 Performance Monitor                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 📊 Start Real-time Monitoring
echo 2. 📈 View Performance Dashboard
echo 3. 📋 View Performance Summary (24h)
echo 4. ⚠️  View Recent Alerts
echo 5. 🔙 Back to Main Menu
echo.

set /p perf_choice="Choose option (1-5): "

if "%perf_choice%"=="1" (
    echo Starting Performance Monitor...
    python performance_monitor.py
) else if "%perf_choice%"=="2" (
    python performance_monitor.py dashboard
    pause
) else if "%perf_choice%"=="3" (
    python performance_monitor.py summary
    pause
) else if "%perf_choice%"=="4" (
    python performance_monitor.py alerts
    pause
) else if "%perf_choice%"=="5" (
    goto main_menu
) else (
    echo Invalid choice
    pause
)
goto performance_monitor

:backup_menu
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                💾 Backup Management                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 🗂️  Create Full System Backup
echo 2. 🗃️  Create Database Backup
echo 3. 📁 Create Media Files Backup
echo 4. 📄 Create Logs Backup
echo 5. 📋 List Available Backups
echo 6. 🔄 Restore from Backup
echo 7. 🧹 Cleanup Old Backups
echo 8. ⏰ Start Backup Scheduler
echo 9. 🔙 Back to Main Menu
echo.

set /p backup_choice="Choose option (1-9): "

if "%backup_choice%"=="1" (
    echo Creating full system backup...
    python auto_backup_system.py full
    pause
) else if "%backup_choice%"=="2" (
    echo Creating database backup...
    python auto_backup_system.py database
    pause
) else if "%backup_choice%"=="3" (
    echo Creating media files backup...
    python auto_backup_system.py media
    pause
) else if "%backup_choice%"=="4" (
    echo Creating logs backup...
    python auto_backup_system.py logs
    pause
) else if "%backup_choice%"=="5" (
    python auto_backup_system.py list
    pause
) else if "%backup_choice%"=="6" (
    echo.
    echo Available backups:
    python auto_backup_system.py list
    echo.
    set /p backup_path="Enter backup path to restore: "
    python auto_backup_system.py restore "%backup_path%"
    pause
) else if "%backup_choice%"=="7" (
    echo Cleaning up old backups...
    python auto_backup_system.py cleanup
    pause
) else if "%backup_choice%"=="8" (
    echo Starting backup scheduler...
    echo Press Ctrl+C to stop
    python auto_backup_system.py schedule
    pause
) else if "%backup_choice%"=="9" (
    goto main_menu
) else (
    echo Invalid choice
    pause
)
goto backup_menu

:diagnostics
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔍 System Diagnostics                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo Running system diagnostics...
echo.

REM Check Python
echo 🐍 Checking Python...
python --version
if errorlevel 1 (
    echo ❌ Python not found
) else (
    echo ✅ Python OK
)

REM Check Django
echo.
echo 🌐 Checking Django...
python -c "import django; print('Django version:', django.get_version())" 2>nul
if errorlevel 1 (
    echo ❌ Django not found
) else (
    echo ✅ Django OK
)

REM Check database
echo.
echo 🗃️  Checking Database...
python manage.py check --database default
if errorlevel 1 (
    echo ❌ Database issues found
) else (
    echo ✅ Database OK
)

REM Check migrations
echo.
echo 🔄 Checking Migrations...
python manage.py showmigrations --plan | findstr "\[ \]" >nul
if not errorlevel 1 (
    echo ⚠️  Unapplied migrations found
) else (
    echo ✅ Migrations OK
)

REM Check static files
echo.
echo 📁 Checking Static Files...
if exist "static" (
    echo ✅ Static directory exists
) else (
    echo ⚠️  Static directory not found
)

REM Check media files
echo.
echo 🖼️  Checking Media Files...
if exist "media" (
    echo ✅ Media directory exists
) else (
    echo ⚠️  Media directory not found
)

REM System resources
echo.
echo 💻 System Resources:
python -c "
import psutil
print(f'CPU Usage: {psutil.cpu_percent()}%%')
print(f'Memory Usage: {psutil.virtual_memory().percent}%%')
print(f'Disk Usage: {psutil.disk_usage(\".\").percent}%%')
" 2>nul

echo.
echo Diagnostics completed.
pause
goto main_menu

:statistics
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📈 Server Statistics                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if stats file exists
if exist "server_stats.json" (
    echo 📊 Server Statistics:
    echo.
    python -c "
import json
from datetime import datetime, timedelta
try:
    with open('server_stats.json', 'r') as f:
        stats = json.load(f)
    
    if stats.get('start_time'):
        uptime = int(float(stats.get('uptime_seconds', 0)))
        hours = uptime // 3600
        minutes = (uptime %% 3600) // 60
        print(f'Uptime: {hours}h {minutes}m')
    
    print(f'Total Restarts: {stats.get(\"total_restarts\", 0)}')
    print(f'Health Checks: {stats.get(\"health_checks\", 0)}')
    print(f'Failed Checks: {stats.get(\"failed_health_checks\", 0)}')
    print(f'Performance Alerts: {stats.get(\"performance_alerts\", 0)}')
    print(f'Last Backup: {stats.get(\"last_backup\", \"Never\")}')
    
except Exception as e:
    print(f'Error reading stats: {e}')
"
) else (
    echo ⚠️  No statistics available yet
    echo Start the Advanced Stable Server to generate statistics
)

echo.
echo 📊 Performance Dashboard:
python performance_monitor.py dashboard 2>nul

pause
goto main_menu

:maintenance
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🛠️  System Maintenance                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 🔄 Apply Database Migrations
echo 2. 🗂️  Collect Static Files
echo 3. 👤 Create Superuser
echo 4. 🧹 Clear Cache
echo 5. 📊 Optimize Database
echo 6. 🔧 Install/Update Requirements
echo 7. 🔙 Back to Main Menu
echo.

set /p maint_choice="Choose option (1-7): "

if "%maint_choice%"=="1" (
    echo Applying database migrations...
    python manage.py makemigrations
    python manage.py migrate
    pause
) else if "%maint_choice%"=="2" (
    echo Collecting static files...
    python manage.py collectstatic --noinput
    pause
) else if "%maint_choice%"=="3" (
    echo Creating superuser...
    python manage.py createsuperuser
    pause
) else if "%maint_choice%"=="4" (
    echo Clearing cache...
    python manage.py clear_cache 2>nul || echo Cache cleared manually
    pause
) else if "%maint_choice%"=="5" (
    echo Optimizing database...
    python -c "
import sqlite3
conn = sqlite3.connect('db.sqlite3')
conn.execute('VACUUM')
conn.execute('ANALYZE')
conn.close()
print('Database optimized')
"
    pause
) else if "%maint_choice%"=="6" (
    echo Installing/updating requirements...
    pip install -r requirements.txt
    pause
) else if "%maint_choice%"=="7" (
    goto main_menu
) else (
    echo Invalid choice
    pause
)
goto maintenance

:configuration
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                ⚙️  Configuration                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 🌐 Change Server Port
echo 2. 🏠 Change Server Host
echo 3. 📊 Configure Monitoring Intervals
echo 4. 💾 Configure Backup Settings
echo 5. 📄 View Current Configuration
echo 6. 🔙 Back to Main Menu
echo.

set /p config_choice="Choose option (1-6): "

if "%config_choice%"=="1" (
    set /p new_port="Enter new port (current: 8000): "
    echo Port configuration would be updated to: %new_port%
    echo Note: Restart server for changes to take effect
    pause
) else if "%config_choice%"=="2" (
    set /p new_host="Enter new host (current: 127.0.0.1): "
    echo Host configuration would be updated to: %new_host%
    echo Note: Restart server for changes to take effect
    pause
) else if "%config_choice%"=="3" (
    echo Current monitoring intervals:
    echo - Health check: 30 seconds
    echo - Performance check: 60 seconds
    echo - Backup: 3600 seconds (1 hour)
    pause
) else if "%config_choice%"=="4" (
    echo Current backup settings:
    echo - Max backups: 50
    echo - Compression: Enabled
    echo - Auto cleanup: Enabled
    pause
) else if "%config_choice%"=="5" (
    echo Current Configuration:
    echo =====================
    echo Server Host: 127.0.0.1
    echo Server Port: 8000
    echo Debug Mode: True
    echo Database: SQLite
    echo Static Files: Enabled
    echo Media Files: Enabled
    pause
) else if "%config_choice%"=="6" (
    goto main_menu
) else (
    echo Invalid choice
    pause
)
goto configuration

:view_logs
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📋 View Logs                                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1. 📄 View Server Log (Latest)
echo 2. 📊 View Performance Log
echo 3. 💾 View Backup Log
echo 4. 🗂️  List All Log Files
echo 5. 🧹 Clear Logs
echo 6. 🔙 Back to Main Menu
echo.

set /p log_choice="Choose option (1-6): "

if "%log_choice%"=="1" (
    if exist "logs\server.log" (
        echo Latest server log entries:
        echo ========================
        powershell "Get-Content logs\server.log | Select-Object -Last 50"
    ) else (
        echo No server log found
    )
    pause
) else if "%log_choice%"=="2" (
    if exist "performance.db" (
        echo Performance statistics available
        python performance_monitor.py dashboard
    ) else (
        echo No performance log found
    )
    pause
) else if "%log_choice%"=="3" (
    if exist "backups\backup.log" (
        echo Latest backup log entries:
        echo =========================
        powershell "Get-Content backups\backup.log | Select-Object -Last 20"
    ) else (
        echo No backup log found
    )
    pause
) else if "%log_choice%"=="4" (
    echo Available log files:
    echo ===================
    dir /b *.log 2>nul
    if exist "logs" dir /b logs\*.log 2>nul
    if exist "backups" dir /b backups\*.log 2>nul
    pause
) else if "%log_choice%"=="5" (
    echo Clearing logs...
    del *.log 2>nul
    if exist "logs" del logs\*.log 2>nul
    echo Logs cleared
    pause
) else if "%log_choice%"=="6" (
    goto main_menu
) else (
    echo Invalid choice
    pause
)
goto view_logs

:invalid_choice
echo ❌ Invalid choice. Please try again.
pause
goto main_menu

:exit
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                👋 Thank you for using                        ║
echo ║            Advanced Server Management System                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
exit /b 0