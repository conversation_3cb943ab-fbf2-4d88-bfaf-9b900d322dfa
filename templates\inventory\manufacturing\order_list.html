{% extends 'base/base.html' %}
{% load static %}

{% block title %}أوامر التصنيع{% endblock %}

{% block extra_css %}
<style>
    .order-card {
        transition: all 0.3s ease;
        border-left: 4px solid #007bff;
    }
    .order-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .order-card.pending {
        border-left-color: #ffc107;
    }
    .order-card.in-progress {
        border-left-color: #17a2b8;
    }
    .order-card.completed {
        border-left-color: #28a745;
    }
    .order-card.cancelled {
        border-left-color: #dc3545;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    .filter-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .stats-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .quick-actions {
        position: sticky;
        top: 20px;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0,123,255,0.1);
    }
    .search-box {
        border-radius: 20px;
        border: 2px solid #e9ecef;
        transition: all 0.3s;
    }
    .search-box:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    /* تحسينات للقوائم المنسدلة */
    .dropdown-menu {
        z-index: 1050 !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        border: 1px solid rgba(0,0,0,0.1) !important;
        border-radius: 8px !important;
    }

    .dropdown-item {
        padding: 8px 16px !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa !important;
        transform: translateX(-2px) !important;
    }

    .dropdown-item:active {
        background-color: #e9ecef !important;
    }

    .dropdown-divider {
        margin: 4px 0 !important;
        border-color: #dee2e6 !important;
    }

    /* تحسينات للأزرار */
    .btn-group.dropdown {
        position: relative !important;
    }

    .dropdown-toggle::after {
        margin-right: 0.5em !important;
    }

    /* تحسينات للتفاعل */
    .dropdown-toggle:focus {
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    }

    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .dropdown-menu {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            width: 90% !important;
            max-width: 300px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
        }
        
        .dropdown-item {
            padding: 12px 16px !important;
            font-size: 16px !important;
        }
    }

    /* تحسينات للتنبيهات */
    .alert {
        z-index: 9999 !important;
    }

    /* تحسينات للجدول */
    .table-responsive {
        overflow-x: auto !important;
    }

    .order-row {
        transition: all 0.3s ease !important;
    }

    .order-row:hover {
        background-color: #f8f9fa !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    /* تحسينات للأزرار */
    .btn-sm {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.875rem !important;
        border-radius: 0.375rem !important;
    }

    /* تحسينات للبطاقات */
    .card {
        border-radius: 12px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
        transition: all 0.3s ease !important;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.12) !important;
        transform: translateY(-2px) !important;
    }

    /* تحسينات للعناوين */
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border-radius: 12px 12px 0 0 !important;
        border: none !important;
    }

    /* تحسينات للفلترة */
    .quick-actions .btn {
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
    }

    .quick-actions .btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-industry text-primary me-2"></i>
                أوامر التصنيع
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'inventory:manufacturing_dashboard' %}">التصنيع</a></li>
                    <li class="breadcrumb-item active">أوامر التصنيع</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء أمر جديد
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-white text-uppercase mb-1">
                                إجمالي الأوامر
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-white">{{ orders.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ orders|dictsort:"status"|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                قيد التنفيذ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ orders|dictsort:"status"|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                مكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ orders|dictsort:"status"|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الفلاتر -->
        <div class="col-lg-9">
            <div class="card filter-card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-funnel me-2"></i>
                        تصفية النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label text-white">البحث</label>
                            {{ filter_form.search }}
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">الحالة</label>
                            {{ filter_form.status }}
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">الأولوية</label>
                            {{ filter_form.priority }}
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">المخزن</label>
                            {{ filter_form.warehouse }}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-white">من تاريخ</label>
                            {{ filter_form.date_from }}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-white">إلى تاريخ</label>
                            {{ filter_form.date_to }}
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">المشرف</label>
                            {{ filter_form.supervisor }}
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search me-2"></i>
                                تصفية
                            </button>
                            <a href="{% url 'inventory:manufacturing_order_list' %}" class="btn btn-outline-light">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الأوامر -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table me-2"></i>
                        قائمة أوامر التصنيع ({{ orders.count }} أمر)
                    </h6>
                </div>
                <div class="card-body">
                    {% if orders %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="ordersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رقم الأمر</th>
                                        <th>المنتج النهائي</th>
                                        <th>الكمية</th>
                                        <th>الحالة</th>
                                        <th>الأولوية</th>
                                        <th>التكلفة الإجمالية</th>
                                        <th>تاريخ الأمر</th>
                                        <th>المخزن</th>
                                        <th>المشرف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders %}
                                    <tr class="order-row" data-status="{{ order.status }}">
                                        <td>
                                            <strong>#{{ order.id }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-box text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ order.finished_product.name|default:"غير محدد" }}</strong>
                                                    {% if order.finished_product.code %}
                                                        <br><small class="text-muted">{{ order.finished_product.code }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ order.quantity_to_produce|default:0 }}</span>
                                        </td>
                                        <td>
                                            <span class="badge status-badge 
                                                {% if order.status == 'PENDING' %}bg-warning
                                                {% elif order.status == 'IN_PROGRESS' %}bg-info
                                                {% elif order.status == 'COMPLETED' %}bg-success
                                                {% elif order.status == 'CANCELLED' %}bg-danger
                                                {% else %}bg-secondary{% endif %}">
                                                {% if order.status == 'PENDING' %}
                                                    <i class="fas fa-clock me-1"></i>في الانتظار
                                                {% elif order.status == 'IN_PROGRESS' %}
                                                    <i class="fas fa-cogs me-1"></i>قيد التنفيذ
                                                {% elif order.status == 'COMPLETED' %}
                                                    <i class="fas fa-check me-1"></i>مكتمل
                                                {% elif order.status == 'CANCELLED' %}
                                                    <i class="fas fa-times me-1"></i>ملغي
                                                {% else %}
                                                    {{ order.get_status_display }}
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge priority-badge
                                                {% if order.priority == 'HIGH' %}bg-danger
                                                {% elif order.priority == 'MEDIUM' %}bg-warning
                                                {% else %}bg-success{% endif %}">
                                                {% if order.priority == 'HIGH' %}
                                                    <i class="fas fa-exclamation-triangle me-1"></i>عالية
                                                {% elif order.priority == 'MEDIUM' %}
                                                    <i class="fas fa-minus me-1"></i>متوسطة
                                                {% else %}
                                                    <i class="fas fa-arrow-down me-1"></i>منخفضة
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>{{ order.total_production_cost|default:0|floatformat:2 }} ج.م</strong>
                                        </td>
                                        <td>
                                            <div class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ order.order_date|date:"Y/m/d" }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-muted">
                                                <i class="fas fa-warehouse me-1"></i>
                                                {{ order.raw_materials_warehouse.name|default:"غير محدد" }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                {% if order.supervisor %}
                                                    {{ order.supervisor.get_full_name|default:order.supervisor.username|default:"غير محدد" }}
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'inventory:manufacturing_order_detail' order.pk %}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if order.status == 'DRAFT' %}
                                                    <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'inventory:plan_manufacturing_order' order.id %}" 
                                                       class="btn btn-sm btn-outline-info" title="تخطيط الأمر"
                                                       onclick="return confirm('هل أنت متأكد من تخطيط هذا الأمر؟')">
                                                        <i class="fas fa-calendar-check"></i>
                                                    </a>
                                                {% endif %}
                                                {% if order.status == 'PLANNED' %}
                                                    <a href="{% url 'inventory:manufacturing_order_edit' order.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'inventory:start_production' order.id %}" 
                                                       class="btn btn-sm btn-success" title="بدء التصنيع"
                                                       onclick="return confirm('هل أنت متأكد من بدء عملية التصنيع؟')">
                                                        <i class="fas fa-play"></i>
                                                    </a>
                                                {% endif %}
                                                {% if order.status == 'IN_PROGRESS' %}
                                                    <a href="{% url 'inventory:complete_production' order.id %}" 
                                                       class="btn btn-sm btn-info" title="إكمال التصنيع"
                                                       onclick="return confirm('هل أنت متأكد من إكمال عملية التصنيع؟')">
                                                        <i class="fas fa-check-double"></i>
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد أوامر تصنيع</h4>
                            <p class="text-muted">لم يتم إنشاء أي أوامر تصنيع حتى الآن</p>
                            <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i>
                                إنشاء أول أمر تصنيع
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="col-lg-3">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        الإجراءات السريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'inventory:manufacturing_order_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>
                            إنشاء أمر جديد
                        </a>
                        <a href="{% url 'inventory:manufacturing_dashboard' %}" class="btn btn-info">
                            <i class="fas fa-chart-line me-2"></i>
                            لوحة التحكم
                        </a>
                    </div>
                    
                    <!-- فلترة سريعة حسب الحالة -->
                    <hr class="my-3">
                    <h6 class="font-weight-bold text-primary mb-3">
                        <i class="fas fa-filter me-2"></i>
                        فلترة سريعة
                    </h6>
                    
                    <div class="d-grid gap-2">
                        <a href="?status=DRAFT" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-edit me-1"></i>
                            مسودة ({{ orders|dictsort:"status"|length }})
                        </a>
                        <a href="?status=PLANNED" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-calendar-check me-1"></i>
                            مخطط ({{ orders|dictsort:"status"|length }})
                        </a>
                        <a href="?status=IN_PROGRESS" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-cogs me-1"></i>
                            قيد التنفيذ ({{ orders|dictsort:"status"|length }})
                        </a>
                        <a href="?status=COMPLETED" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-check-circle me-1"></i>
                            مكتمل ({{ orders|dictsort:"status"|length }})
                        </a>
                        <a href="?status=CANCELLED" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-times-circle me-1"></i>
                            ملغي ({{ orders|dictsort:"status"|length }})
                        </a>
                        <a href="?" class="btn btn-outline-dark btn-sm">
                            <i class="fas fa-list me-1"></i>
                            جميع الأوامر
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// دالة الحصول على CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للصفوف
    const rows = document.querySelectorAll('.order-row');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>
{% endblock %}
