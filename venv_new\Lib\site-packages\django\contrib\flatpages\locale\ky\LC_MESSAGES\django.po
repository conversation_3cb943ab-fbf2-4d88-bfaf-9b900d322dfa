# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-23 06:23+0000\n"
"Last-Translator: Soyuzbek Orozbek uulu <<EMAIL>>\n"
"Language-Team: Kyrgyz (http://www.transifex.com/django/django/language/ky/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ky\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr "Дагы мүмкүнчүлүктөр"

msgid "Flat Pages"
msgstr "Туруктуу барактар"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Мисалы: “/about/contact/”. Башында жана аягында бөлчөк бар экендигин "
"текшериңиз."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Бул маани жалаң арип, сан, чекит, астсызык, сызык, бөлчөк же тилда камтый "
"алат."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Мисалы: “/about/contact”. Башында бөлчөк бар экендигин текшериңиз."

msgid "URL is missing a leading slash."
msgstr "URL башында бөлчөгү жок."

msgid "URL is missing a trailing slash."
msgstr "URL аягында бөлчөгү жок."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(url)s URLдүү барак %(site)sсайтында мурдатан эле бар."

msgid "title"
msgstr "наам"

msgid "content"
msgstr "мазмун"

msgid "enable comments"
msgstr "пикирлерди күйгүзүү"

msgid "template name"
msgstr "калып аты"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Мисалы: “flatpages/contact_page.html”. Эгер бул көрсөтүлбөсө, систем "
"“flatpages/default.html” колдоно берет."

msgid "registration required"
msgstr "катталуу талап кылынат."

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Эгер бул тандалса жалаң гана катталып кирген колдонуучулар бул баракты көрө "
"алышат."

msgid "sites"
msgstr "сайттар"

msgid "flat page"
msgstr "туруктуу барак"

msgid "flat pages"
msgstr "туруктуу барактар"
