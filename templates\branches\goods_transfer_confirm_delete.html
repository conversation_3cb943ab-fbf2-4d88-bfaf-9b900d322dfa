{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-trash text-danger me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'branches:home' %}">الفروع</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'branches:goods_transfer' %}">بضاعة مرحلة</a></li>
                            <li class="breadcrumb-item active">حذف التحويل</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <p class="mb-3">هل أنت متأكد من رغبتك في حذف تحويل البضاعة التالي؟</p>

                    <!-- Transfer Details -->
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-4"><strong>رقم التحويل:</strong></div>
                                <div class="col-sm-8">{{ transfer.transfer_number }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>الفرع:</strong></div>
                                <div class="col-sm-8">{{ transfer.branch.name }} ({{ transfer.branch.code }})</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>تاريخ التحويل:</strong></div>
                                <div class="col-sm-8">{{ transfer.transfer_date|date:"Y-m-d" }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>إجمالي القيمة:</strong></div>
                                <div class="col-sm-8">{{ transfer.total_amount|floatformat:2 }} جنيه</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>الحالة:</strong></div>
                                <div class="col-sm-8">
                                    {% if transfer.status == 'PENDING' %}
                                    <span class="badge bg-warning">معلق</span>
                                    {% elif transfer.status == 'APPROVED' %}
                                    <span class="badge bg-info">معتمد</span>
                                    {% elif transfer.status == 'TRANSFERRED' %}
                                    <span class="badge bg-primary">محول</span>
                                    {% elif transfer.status == 'RECEIVED' %}
                                    <span class="badge bg-success">مستلم</span>
                                    {% elif transfer.status == 'CANCELLED' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% if transfer.notes %}
                            <div class="row">
                                <div class="col-sm-4"><strong>ملاحظات:</strong></div>
                                <div class="col-sm-8">{{ transfer.notes }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'branches:goods_transfer' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
