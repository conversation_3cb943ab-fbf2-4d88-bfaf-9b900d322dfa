#!/usr/bin/env python3
"""
مراقب النظام المتقدم
Advanced System Monitor

يراقب أداء النظام والخادم ويرسل تنبيهات عند الحاجة
Monitors system performance and sends alerts when needed
"""

import os
import sys
import time
import psutil
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path

# إعداد اللوجز
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_monitor.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SystemMonitor:
    """مراقب النظام المتقدم"""
    
    def __init__(self):
        self.monitoring = False
        self.check_interval = 60  # فحص كل دقيقة
        self.alerts = []
        
        # حدود التنبيهات
        self.cpu_threshold = 80  # %
        self.memory_threshold = 85  # %
        self.disk_threshold = 90  # %
        self.response_time_threshold = 5  # ثواني
        
        # إحصائيات
        self.stats = {
            'uptime_start': datetime.now(),
            'total_checks': 0,
            'alerts_sent': 0,
            'max_cpu': 0,
            'max_memory': 0,
            'avg_response_time': 0
        }
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            # معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # معلومات القرص
            disk = psutil.disk_usage('/')
            
            # معلومات الشبكة
            network = psutil.net_io_counters()
            
            # العمليات
            processes = len(psutil.pids())
            
            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'frequency': cpu_freq.current if cpu_freq else 0
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                },
                'swap': {
                    'total': swap.total,
                    'used': swap.used,
                    'percent': swap.percent
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'processes': processes,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معلومات النظام: {e}")
            return None
    
    def check_server_response(self):
        """فحص استجابة الخادم"""
        try:
            import urllib.request
            import urllib.error
            from urllib.parse import urlparse
            
            url = "http://127.0.0.1:8000/"
            start_time = time.time()
            
            try:
                response = urllib.request.urlopen(url, timeout=10)
                response_time = time.time() - start_time
                
                if response.getcode() == 200:
                    return {
                        'status': 'success',
                        'response_time': response_time,
                        'status_code': response.getcode()
                    }
                else:
                    return {
                        'status': 'warning',
                        'response_time': response_time,
                        'status_code': response.getcode()
                    }
                    
            except urllib.error.URLError as e:
                return {
                    'status': 'error',
                    'response_time': time.time() - start_time,
                    'error': str(e)
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في فحص استجابة الخادم: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_django_processes(self):
        """فحص عمليات Django"""
        django_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'manage.py' in cmdline and 'runserver' in cmdline:
                        django_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_percent': proc.info['memory_percent']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"❌ خطأ في فحص عمليات Django: {e}")
        
        return django_processes
    
    def check_disk_space(self):
        """فحص مساحة القرص"""
        try:
            # فحص مساحة المشروع
            project_path = Path.cwd()
            disk_usage = psutil.disk_usage(project_path)
            
            # فحص مساحة قاعدة البيانات
            db_path = project_path / 'db.sqlite3'
            db_size = db_path.stat().st_size if db_path.exists() else 0
            
            # فحص مساحة اللوجز
            logs_path = project_path / 'logs'
            logs_size = sum(f.stat().st_size for f in logs_path.rglob('*') if f.is_file()) if logs_path.exists() else 0
            
            return {
                'total': disk_usage.total,
                'used': disk_usage.used,
                'free': disk_usage.free,
                'percent': (disk_usage.used / disk_usage.total) * 100,
                'db_size': db_size,
                'logs_size': logs_size
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص مساحة القرص: {e}")
            return None
    
    def send_alert(self, alert_type, message, severity='warning'):
        """إرسال تنبيه"""
        alert = {
            'timestamp': datetime.now(),
            'type': alert_type,
            'message': message,
            'severity': severity
        }
        
        self.alerts.append(alert)
        self.stats['alerts_sent'] += 1
        
        # تسجيل التنبيه
        if severity == 'critical':
            logger.critical(f"🚨 {alert_type}: {message}")
        elif severity == 'warning':
            logger.warning(f"⚠️ {alert_type}: {message}")
        else:
            logger.info(f"ℹ️ {alert_type}: {message}")
        
        # يمكن إضافة إرسال إيميل أو SMS هنا
        
    def analyze_system_health(self, system_info):
        """تحليل صحة النظام"""
        if not system_info:
            return
        
        # فحص المعالج
        if system_info['cpu']['percent'] > self.cpu_threshold:
            self.send_alert(
                'HIGH_CPU_USAGE',
                f"استخدام المعالج عالي: {system_info['cpu']['percent']:.1f}%",
                'warning'
            )
        
        # فحص الذاكرة
        if system_info['memory']['percent'] > self.memory_threshold:
            self.send_alert(
                'HIGH_MEMORY_USAGE',
                f"استخدام الذاكرة عالي: {system_info['memory']['percent']:.1f}%",
                'warning'
            )
        
        # فحص القرص
        if system_info['disk']['percent'] > self.disk_threshold:
            self.send_alert(
                'HIGH_DISK_USAGE',
                f"مساحة القرص ممتلئة: {system_info['disk']['percent']:.1f}%",
                'critical'
            )
        
        # تحديث الإحصائيات
        self.stats['max_cpu'] = max(self.stats['max_cpu'], system_info['cpu']['percent'])
        self.stats['max_memory'] = max(self.stats['max_memory'], system_info['memory']['percent'])
    
    def analyze_server_health(self, server_response):
        """تحليل صحة الخادم"""
        if not server_response:
            return
        
        if server_response['status'] == 'error':
            self.send_alert(
                'SERVER_DOWN',
                f"الخادم لا يستجيب: {server_response.get('error', 'خطأ غير معروف')}",
                'critical'
            )
        elif server_response['status'] == 'warning':
            self.send_alert(
                'SERVER_WARNING',
                f"الخادم يرد بكود خطأ: {server_response.get('status_code', 'غير معروف')}",
                'warning'
            )
        elif server_response.get('response_time', 0) > self.response_time_threshold:
            self.send_alert(
                'SLOW_RESPONSE',
                f"استجابة الخادم بطيئة: {server_response['response_time']:.2f} ثانية",
                'warning'
            )
    
    def generate_report(self):
        """إنشاء تقرير مفصل"""
        uptime = datetime.now() - self.stats['uptime_start']
        
        report = f"""
🔍 تقرير مراقبة النظام
{'=' * 50}

⏰ وقت التشغيل: {uptime}
🔢 إجمالي الفحوصات: {self.stats['total_checks']}
🚨 إجمالي التنبيهات: {self.stats['alerts_sent']}

📊 الإحصائيات:
• أقصى استخدام للمعالج: {self.stats['max_cpu']:.1f}%
• أقصى استخدام للذاكرة: {self.stats['max_memory']:.1f}%
• متوسط وقت الاستجابة: {self.stats['avg_response_time']:.2f}s

🚨 آخر 5 تنبيهات:
"""
        
        for alert in self.alerts[-5:]:
            report += f"• {alert['timestamp'].strftime('%H:%M:%S')} - {alert['type']}: {alert['message']}\n"
        
        return report
    
    def cleanup_old_logs(self):
        """تنظيف اللوجز القديمة"""
        try:
            logs_dir = Path('logs')
            if not logs_dir.exists():
                return
            
            # حذف اللوجز الأقدم من 7 أيام
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for log_file in logs_dir.glob('*.log'):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    logger.info(f"🗑️ تم حذف اللوج القديم: {log_file}")
                    
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف اللوجز: {e}")
    
    def monitor_loop(self):
        """حلقة المراقبة الرئيسية"""
        logger.info("🔍 بدء مراقبة النظام...")
        
        while self.monitoring:
            try:
                self.stats['total_checks'] += 1
                
                # فحص النظام
                system_info = self.get_system_info()
                if system_info:
                    self.analyze_system_health(system_info)
                    
                    # طباعة معلومات مختصرة
                    logger.info(
                        f"💻 CPU: {system_info['cpu']['percent']:.1f}% | "
                        f"🧠 RAM: {system_info['memory']['percent']:.1f}% | "
                        f"💾 Disk: {system_info['disk']['percent']:.1f}%"
                    )
                
                # فحص الخادم
                server_response = self.check_server_response()
                if server_response:
                    self.analyze_server_health(server_response)
                    
                    if server_response['status'] == 'success':
                        response_time = server_response.get('response_time', 0)
                        logger.info(f"🌐 Server: OK ({response_time:.2f}s)")
                        
                        # تحديث متوسط وقت الاستجابة
                        if self.stats['avg_response_time'] == 0:
                            self.stats['avg_response_time'] = response_time
                        else:
                            self.stats['avg_response_time'] = (
                                self.stats['avg_response_time'] * 0.9 + response_time * 0.1
                            )
                
                # فحص عمليات Django
                django_processes = self.check_django_processes()
                if django_processes:
                    logger.info(f"🐍 Django processes: {len(django_processes)}")
                else:
                    self.send_alert(
                        'NO_DJANGO_PROCESS',
                        "لا توجد عمليات Django قيد التشغيل",
                        'critical'
                    )
                
                # تنظيف اللوجز كل ساعة
                if self.stats['total_checks'] % 60 == 0:
                    self.cleanup_old_logs()
                
                # تقرير كل 10 دقائق
                if self.stats['total_checks'] % 10 == 0:
                    logger.info(self.generate_report())
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("⌨️ تم إيقاف المراقبة بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة المراقبة: {e}")
                time.sleep(self.check_interval)
    
    def start(self):
        """بدء المراقبة"""
        self.monitoring = True
        self.monitor_loop()
    
    def stop(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        logger.info("🛑 تم إيقاف مراقبة النظام")

def main():
    """الدالة الرئيسية"""
    print("🔍 مراقب النظام المتقدم")
    print("=" * 30)
    
    monitor = SystemMonitor()
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n⌨️ تم الضغط على Ctrl+C")
    finally:
        monitor.stop()
        print("👋 تم إنهاء مراقب النظام")

if __name__ == "__main__":
    main()
