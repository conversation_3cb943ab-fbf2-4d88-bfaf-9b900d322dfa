<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>مراحل الإنتاج</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body class="container-fluid mt-4">
    
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary">
                <i class="bi bi-diagram-3"></i>
                مراحل الإنتاج
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'definitions:home' %}">التعريفات</a></li>
                    <li class="breadcrumb-item active">مراحل الإنتاج</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                إضافة مرحلة جديدة
            </a>
        </div>
    </div>

    <!-- البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-search"></i>
                البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في اسم المرحلة أو الوصف..." 
                           value="{{ search }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المراحل -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-table"></i>
                قائمة مراحل الإنتاج ({{ stages.paginator.count }} مرحلة)
            </h5>
        </div>
        <div class="card-body">
            {% if stages %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم المرحلة</th>
                                <th>الوصف</th>
                                <th>المدة (ساعة)</th>
                                <th>التكلفة/ساعة</th>
                                <th>إجمالي التكلفة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage in stages %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">{{ stage.order }}</span>
                                </td>
                                <td>
                                    <strong>{{ stage.name }}</strong>
                                </td>
                                <td>
                                    {% if stage.description %}
                                        {{ stage.description|truncatechars:50 }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ stage.duration_hours }}</td>
                                <td>{{ stage.cost_per_hour }} ج.م</td>
                                <td>
                                    <strong class="text-success">{{ stage.total_cost }} ج.م</strong>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'definitions:production_stage_detail' stage.pk %}" 
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'definitions:production_stage_edit' stage.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'definitions:production_stage_delete' stage.pk %}" 
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه المرحلة؟')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- التصفح -->
                {% if stages.has_other_pages %}
                    <nav aria-label="صفحات النتائج">
                        <ul class="pagination justify-content-center">
                            {% if stages.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ stages.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    الصفحة {{ stages.number }} من {{ stages.paginator.num_pages }}
                                </span>
                            </li>

                            {% if stages.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ stages.next_page_number }}{% if search %}&search={{ search }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ stages.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}

            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-diagram-3 display-1 text-muted"></i>
                    <h4 class="text-muted">لا توجد مراحل إنتاج</h4>
                    {% if search %}
                        <p class="text-muted">لم يتم العثور على نتائج للبحث "{{ search }}"</p>
                        <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i>
                            عرض جميع المراحل
                        </a>
                    {% else %}
                        <p class="text-muted">لم يتم إنشاء أي مراحل إنتاج حتى الآن</p>
                        <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            إضافة أول مرحلة إنتاج
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
