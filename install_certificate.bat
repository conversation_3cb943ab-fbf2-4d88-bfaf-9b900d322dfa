@echo off
REM تثبيت شهادة SSL في Windows
REM Install SSL Certificate in Windows

echo ============================================================
echo 🔒 تثبيت شهادة SSL للخادم الأبدي
echo SSL Certificate Installation for Eternal Server
echo ============================================================
echo.

REM فحص إذا كان يعمل كـ Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ يعمل بصلاحيات Administrator
) else (
    echo ❌ يجب تشغيل السكريپت كـ Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM فحص وجود ملف الشهادة
if not exist "ssl\server.crt" (
    echo ❌ ملف الشهادة غير موجود: ssl\server.crt
    echo Creating SSL certificate first...
    python simple_ssl.py
    if not exist "ssl\server.crt" (
        echo ❌ فشل في إنشاء الشهادة
        pause
        exit /b 1
    )
)

echo 📄 تم العثور على ملف الشهادة: ssl\server.crt
echo.

REM تثبيت الشهادة في Trusted Root Certification Authorities
echo 🔧 تثبيت الشهادة في النظام...
certutil -addstore -f "Root" "ssl\server.crt"

if %errorLevel% == 0 (
    echo ✅ تم تثبيت الشهادة بنجاح!
    echo.
    echo 🎉 الآن يمكنك الوصول للموقع بأمان:
    echo    https://***************:8443/
    echo    https://localhost:8443/
    echo.
    echo 💡 لن تظهر تحذيرات الأمان بعد الآن
) else (
    echo ❌ فشل في تثبيت الشهادة
    echo.
    echo 🔧 حلول بديلة:
    echo 1. تشغيل السكريپت كـ Administrator
    echo 2. تثبيت الشهادة يدوياً:
    echo    - افتح ssl\server.crt
    echo    - اضغط Install Certificate
    echo    - اختر Local Machine
    echo    - اختر Trusted Root Certification Authorities
)

echo.
echo ============================================================
pause
