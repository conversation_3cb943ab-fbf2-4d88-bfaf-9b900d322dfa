{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-file-signature text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة عقود العمل للموظفين</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <a href="{% url 'hr:contract_create' %}" class="btn btn-success">إضافة عقد جديد</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Contract Types Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ permanent_count }}</h3>
                    <p class="mb-0">عقود دائمة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ temporary_count }}</h3>
                    <p class="mb-0">عقود مؤقتة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ training_count }}</h3>
                    <p class="mb-0">عقود تدريب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ ended_count }}</h3>
                    <p class="mb-0">عقود منتهية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>{{ title }}</h2>
        <div>
            <a href="{% url 'hr:contract_create' %}" class="btn btn-success">إضافة عقد جديد</a>
        </div>
    </div>
    <div class="mb-3">
        <a href="?status=active" class="btn btn-sm {% if status == 'active' %}btn-primary{% else %}btn-outline-primary{% endif %}">العقود النشطة</a>
        <a href="?status=ended" class="btn btn-sm {% if status == 'ended' %}btn-primary{% else %}btn-outline-primary{% endif %}">العقود المنتهية</a>
        <a href="?status=archived" class="btn btn-sm {% if status == 'archived' %}btn-primary{% else %}btn-outline-primary{% endif %}">العقود المؤرشفة</a>
        <a href="?status=all" class="btn btn-sm {% if status == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">الكل</a>
    </div>

    <!-- Contracts Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة عقود العمل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع العقد</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>القسم</th>
                                    <th>المنصب</th>
                                    <th>الراتب</th>
                                    <th>الحالة</th>
                                    <th>ملف العقد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contract in contracts %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>
                                        <i class="fas fa-user text-primary me-2"></i>
                                        {{ contract.employee.full_name }}
                                    </td>
                                    <td>
                                        {% if contract.type == 'دائم' %}
                                            <span class="badge bg-success">{{ contract.get_contract_type_display }}</span>
                                        {% elif contract.type == 'مؤقت' %}
                                            <span class="badge bg-warning">{{ contract.get_contract_type_display }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ contract.get_contract_type_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ contract.start_date }}</td>
                                    <td>
                                        {% if contract.end_date == '-' %}
                                            <span class="text-muted">غير محدد</span>
                                        {% else %}
                                            {{ contract.end_date }}
                                        {% endif %}
                                    </td>
                                    <td>{{ contract.department }}</td>
                                    <td>{{ contract.position }}</td>
                                    <td><strong>{{ contract.salary|floatformat:0 }} ج.م</strong></td>
                                    <td>
                                        {% if contract.is_active %}
                                            <span class="badge bg-success">{{ contract.get_status_display }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ contract.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if contract.contract_file %}
                                            <a href="{{ contract.contract_file.url }}" target="_blank" class="btn btn-sm btn-outline-info">تحميل</a>
                                        {% else %}
                                            —
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'hr:contract_edit' contract.pk %}" class="btn btn-sm btn-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{% url 'hr:contract_delete' contract.pk %}" method="post" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا العقد؟');">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            {% if not contract.archived %}
                                            <form action="{% url 'hr:contract_archive' contract.pk %}" method="post" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-warning" title="أرشفة" onclick="return confirm('هل تريد أرشفة العقد؟');">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                            </form>
                                            {% else %}
                                            <form action="{% url 'hr:contract_unarchive' contract.pk %}" method="post" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-success" title="إلغاء الأرشفة" onclick="return confirm('هل تريد إلغاء الأرشفة؟');">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                            <a href="{% url 'hr:contract_print' contract.pk %}" target="_blank" class="btn btn-sm btn-outline-info" title="طباعة التفاصيل">
                                                <i class="fas fa-print"></i> التفاصيل
                                            </a>
                                            <a href="{% url 'hr:contract_print_summary' contract.pk %}" target="_blank" class="btn btn-sm btn-outline-secondary" title="طباعة الملخص">
                                                <i class="fas fa-print"></i> الملخص
                                            </a>
                                            <a href="{% url 'hr:contract_create' %}?employee={{ contract.employee.pk }}&contract={{ contract.pk }}" class="btn btn-sm btn-outline-success" title="تجديد العقد">
                                                <i class="fas fa-redo"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-file-signature fa-3x mb-3 d-block"></i>
                                        لا توجد عقود للعرض
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Contract Modal -->
<div class="modal fade" id="addContractModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عقد عمل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <option value="1">أحمد محمد</option>
                                    <option value="2">فاطمة علي</option>
                                    <option value="3">محمد أحمد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع العقد</label>
                                <select class="form-select" required>
                                    <option value="">اختر نوع العقد</option>
                                    <option value="permanent">دائم</option>
                                    <option value="temporary">مؤقت</option>
                                    <option value="training">تدريب</option>
                                    <option value="part_time">دوام جزئي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ النهاية</label>
                                <input type="date" class="form-control">
                                <small class="text-muted">اتركه فارغاً للعقود الدائمة</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي (ج.م)</label>
                                <input type="number" class="form-control" min="1000" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب</label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">شروط العقد</label>
                        <textarea class="form-control" rows="4" placeholder="اكتب الشروط والأحكام الخاصة بالعقد"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">حفظ العقد</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
