{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">{{ title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:transfer_list' %}">النقل</a></li>
                        <li class="breadcrumb-item active">تفاصيل النقل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="mb-3">بيانات النقل</h5>
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th>الموظف</th>
                                <td>{{ transfer.employee }}</td>
                            </tr>
                            <tr>
                                <th>القسم الحالي</th>
                                <td>{{ transfer.from_department }}</td>
                            </tr>
                            <tr>
                                <th>القسم الجديد</th>
                                <td>{{ transfer.to_department }}</td>
                            </tr>
                            <tr>
                                <th>المنصب الحالي</th>
                                <td>{{ transfer.from_position }}</td>
                            </tr>
                            <tr>
                                <th>المنصب الجديد</th>
                                <td>{{ transfer.to_position }}</td>
                            </tr>
                            <tr>
                                <th>نوع النقل</th>
                                <td>{{ transfer.get_transfer_type_display }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ النقل</th>
                                <td>{{ transfer.transfer_date }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ التنفيذ</th>
                                <td>{{ transfer.effective_date }}</td>
                            </tr>
                            {% if transfer.end_date %}
                            <tr>
                                <th>تاريخ نهاية النقل</th>
                                <td>{{ transfer.end_date }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>سبب النقل</th>
                                <td>{{ transfer.reason }}</td>
                            </tr>
                            <tr>
                                <th>ملاحظات</th>
                                <td>{{ transfer.notes|default:'-' }}</td>
                            </tr>
                            <tr>
                                <th>الموظف البديل</th>
                                <td>{{ transfer.replacement_employee|default:'-' }}</td>
                            </tr>
                            <tr>
                                <th>الحالة</th>
                                <td>{{ transfer.get_status_display }}</td>
                            </tr>
                            <tr>
                                <th>أنشئ بواسطة</th>
                                <td>{% if transfer.created_by %}{{ transfer.created_by.username }}{% else %}-{% endif %}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الإنشاء</th>
                                <td>{{ transfer.created_at }}</td>
                            </tr>
                            <tr>
                                <th>آخر تحديث بواسطة</th>
                                <td>{% if transfer.updated_by %}{{ transfer.updated_by.username }}{% else %}-{% endif %}</td>
                            </tr>
                            <tr>
                                <th>تاريخ آخر تحديث</th>
                                <td>{{ transfer.updated_at }}</td>
                            </tr>
                            <tr>
                                <th>الموافقة بواسطة</th>
                                <td>{% if transfer.approved_by %}{{ transfer.approved_by.username }}{% else %}-{% endif %}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الموافقة</th>
                                <td>{{ transfer.approved_at|default:'-' }}</td>
                            </tr>
                            <tr>
                                <th>ملاحظات الموافقة/الرفض</th>
                                <td>{{ transfer.approval_notes|default:'-' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="mt-3">
                        <a href="{% url 'hr:transfer_list' %}" class="btn btn-secondary">عودة لقائمة النقل</a>
                        <a href="{% url 'hr:transfer_edit' transfer.id %}" class="btn btn-primary">تعديل</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 