"""
مراقب الخادم - يتحقق من حالة الخادم ويعيد تشغيله عند الحاجة
"""

import os
import sys
import time
import requests
import subprocess
import logging
from datetime import datetime
from pathlib import Path

# إعداد المسارات
PROJECT_DIR = Path(__file__).parent
LOG_FILE = PROJECT_DIR / "monitor.log"

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class ServerMonitor:
    """مراقب الخادم"""
    
    def __init__(self):
        self.server_url = "http://127.0.0.1:8000"
        self.check_interval = 60  # فحص كل دقيقة
        self.timeout = 10  # مهلة الاتصال
        self.max_failures = 3  # عدد الفشل المسموح قبل إعادة التشغيل
        self.failure_count = 0
        self.last_check = None
        self.server_process = None
        
    def check_server_health(self):
        """فحص صحة الخادم"""
        try:
            response = requests.get(
                self.server_url,
                timeout=self.timeout,
                allow_redirects=True
            )
            
            if response.status_code in [200, 302, 301]:
                return True, f"الخادم يعمل - كود الاستجابة: {response.status_code}"
            else:
                return False, f"استجابة غير متوقعة - كود: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, "فشل في الاتصال - الخادم غير متاح"
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال"
        except Exception as e:
            return False, f"خطأ في الفحص: {str(e)}"
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            logger.info("محاولة بدء تشغيل الخادم...")
            
            # إيقاف أي عملية سابقة
            self.stop_server()
            
            # تغيير المجلد الحالي
            os.chdir(PROJECT_DIR)
            
            # تشغيل الخادم
            cmd = [sys.executable, "manage.py", "runserver", "0.0.0.0:8000", "--noreload"]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"تم بدء الخادم بـ PID: {self.server_process.pid}")
            
            # انتظار قليل للتأكد من بدء التشغيل
            time.sleep(5)
            
            # فحص إذا كان الخادم بدأ بنجاح
            if self.server_process.poll() is None:
                return True
            else:
                logger.error("فشل في بدء الخادم")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في بدء الخادم: {str(e)}")
            return False
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            try:
                logger.info("إيقاف الخادم...")
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                logger.info("تم إيقاف الخادم")
            except subprocess.TimeoutExpired:
                logger.warning("فرض إيقاف الخادم...")
                self.server_process.kill()
            except Exception as e:
                logger.error(f"خطأ في إيقاف الخادم: {str(e)}")
            finally:
                self.server_process = None
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        logger.info("إعادة تشغيل الخادم...")
        self.stop_server()
        time.sleep(3)
        return self.start_server()
    
    def send_notification(self, message, level="INFO"):
        """إرسال إشعار (يمكن توسيعه لإرسال إيميل أو SMS)"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        notification = f"[{timestamp}] {level}: {message}"
        
        # كتابة في ملف الإشعارات
        notifications_file = PROJECT_DIR / "notifications.log"
        with open(notifications_file, "a", encoding="utf-8") as f:
            f.write(notification + "\n")
        
        logger.info(f"إشعار: {message}")
    
    def run_monitoring(self):
        """تشغيل المراقبة"""
        logger.info("بدء مراقبة الخادم...")
        
        # بدء الخادم إذا لم يكن يعمل
        is_healthy, message = self.check_server_health()
        if not is_healthy:
            logger.info("الخادم غير متاح، بدء التشغيل...")
            if not self.start_server():
                logger.error("فشل في بدء الخادم")
                return
        
        while True:
            try:
                self.last_check = datetime.now()
                is_healthy, message = self.check_server_health()
                
                if is_healthy:
                    if self.failure_count > 0:
                        logger.info("تم استعادة الخادم بنجاح")
                        self.send_notification("تم استعادة الخادم بنجاح", "SUCCESS")
                    
                    self.failure_count = 0
                    logger.debug(message)
                else:
                    self.failure_count += 1
                    logger.warning(f"فشل الفحص {self.failure_count}/{self.max_failures}: {message}")
                    
                    if self.failure_count >= self.max_failures:
                        logger.error("تم الوصول للحد الأقصى من الفشل، إعادة تشغيل الخادم...")
                        self.send_notification(f"إعادة تشغيل الخادم بعد {self.failure_count} محاولات فاشلة", "WARNING")
                        
                        if self.restart_server():
                            logger.info("تم إعادة تشغيل الخادم بنجاح")
                            self.failure_count = 0
                            
                            # انتظار قبل الفحص التالي
                            time.sleep(10)
                        else:
                            logger.error("فشل في إعادة تشغيل الخادم")
                            self.send_notification("فشل في إعادة تشغيل الخادم", "ERROR")
                
                # انتظار قبل الفحص التالي
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("تم طلب إيقاف المراقبة...")
                break
            except Exception as e:
                logger.error(f"خطأ في المراقبة: {str(e)}")
                time.sleep(30)  # انتظار أطول عند حدوث خطأ
        
        # إيقاف الخادم عند الخروج
        self.stop_server()
        logger.info("تم إيقاف مراقبة الخادم")
    
    def get_status(self):
        """الحصول على حالة الخادم"""
        is_healthy, message = self.check_server_health()
        
        status = {
            "healthy": is_healthy,
            "message": message,
            "last_check": self.last_check.isoformat() if self.last_check else None,
            "failure_count": self.failure_count,
            "server_url": self.server_url
        }
        
        return status


def main():
    """الدالة الرئيسية"""
    monitor = ServerMonitor()
    
    # التحقق من المعاملات
    if len(sys.argv) > 1:
        if sys.argv[1] == "status":
            status = monitor.get_status()
            print(f"حالة الخادم: {'صحي' if status['healthy'] else 'غير صحي'}")
            print(f"الرسالة: {status['message']}")
            print(f"عدد الفشل: {status['failure_count']}")
            return
        elif sys.argv[1] == "start":
            if monitor.start_server():
                print("تم بدء الخادم بنجاح")
            else:
                print("فشل في بدء الخادم")
            return
        elif sys.argv[1] == "stop":
            monitor.stop_server()
            print("تم إيقاف الخادم")
            return
        elif sys.argv[1] == "restart":
            if monitor.restart_server():
                print("تم إعادة تشغيل الخادم بنجاح")
            else:
                print("فشل في إعادة تشغيل الخادم")
            return
    
    # تشغيل المراقبة
    try:
        monitor.run_monitoring()
    except Exception as e:
        logger.error(f"خطأ في المراقب: {str(e)}")
    finally:
        monitor.stop_server()


if __name__ == "__main__":
    main()
