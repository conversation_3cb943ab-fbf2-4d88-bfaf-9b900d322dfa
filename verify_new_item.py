#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item

def verify_new_items():
    print("=== Verifying New Items ===")
    
    # Check for the new items we just created
    try:
        item1 = Item.objects.get(code='NEWITEM001')
        print(f"Found item: {item1.code} - {item1.name}")
        print(f"Active: {item1.is_active}")
        print(f"Item type: {item1.item_type}")
        print(f"Cost price: {item1.cost_price}")
        print(f"Selling price: {item1.selling_price}")
    except Item.DoesNotExist:
        print("NEWITEM001 not found")
    
    # Show total count
    total_items = Item.objects.count()
    active_items = Item.objects.filter(is_active=True).count()
    print(f"\nTotal items in database: {total_items}")
    print(f"Active items: {active_items}")

if __name__ == "__main__":
    verify_new_items()