#!/usr/bin/env python
"""
سكريبت لإضافة الأعمدة المفقودة في جدول أوامر التصنيع
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import connection
from django.db.utils import OperationalError

def add_missing_columns():
    """إضافة الأعمدة المفقودة في جدول أوامر التصنيع"""
    
    try:
        with connection.cursor() as cursor:
            print("🔍 فحص الأعمدة الموجودة...")
            
            # التحقق من وجود العمود actual_start_date
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='actual_start_date'
                """)
                has_actual_start_date = cursor.fetchone()[0] > 0
                
                if not has_actual_start_date:
                    print("➕ إضافة عمود actual_start_date...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN actual_start_date DATETIME NULL
                    """)
                    print("✅ تم إضافة عمود actual_start_date")
                else:
                    print("✅ عمود actual_start_date موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة actual_start_date: {e}")
            
            # التحقق من وجود العمود order_number
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='order_number'
                """)
                has_order_number = cursor.fetchone()[0] > 0
                
                if not has_order_number:
                    print("➕ إضافة عمود order_number...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN order_number VARCHAR(50) NULL
                    """)
                    print("✅ تم إضافة عمود order_number")
                else:
                    print("✅ عمود order_number موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة order_number: {e}")
            
            # التحقق من وجود العمود total_production_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_production_cost'
                """)
                has_total_production_cost = cursor.fetchone()[0] > 0
                
                if not has_total_production_cost:
                    print("➕ إضافة عمود total_production_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_production_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_production_cost")
                else:
                    print("✅ عمود total_production_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة total_production_cost: {e}")
            
            # التحقق من وجود العمود actual_completion_date
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='actual_completion_date'
                """)
                has_actual_completion_date = cursor.fetchone()[0] > 0
                
                if not has_actual_completion_date:
                    print("➕ إضافة عمود actual_completion_date...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN actual_completion_date DATETIME NULL
                    """)
                    print("✅ تم إضافة عمود actual_completion_date")
                else:
                    print("✅ عمود actual_completion_date موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة actual_completion_date: {e}")
            
            # التحقق من وجود عمود electricity_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='electricity_cost'
                """)
                has_electricity_cost = cursor.fetchone()[0] > 0
                if not has_electricity_cost:
                    print("➕ إضافة عمود electricity_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN electricity_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود electricity_cost")
                else:
                    print("✅ عمود electricity_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة electricity_cost: {e}")

            # التحقق من وجود عمود water_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='water_cost'
                """)
                has_water_cost = cursor.fetchone()[0] > 0
                if not has_water_cost:
                    print("➕ إضافة عمود water_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN water_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود water_cost")
                else:
                    print("✅ عمود water_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة water_cost: {e}")

            # التحقق من وجود عمود labor_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='labor_cost'
                """)
                has_labor_cost = cursor.fetchone()[0] > 0
                if not has_labor_cost:
                    print("➕ إضافة عمود labor_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN labor_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود labor_cost")
                else:
                    print("✅ عمود labor_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة labor_cost: {e}")

            # التحقق من وجود عمود other_costs
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='other_costs'
                """)
                has_other_costs = cursor.fetchone()[0] > 0
                if not has_other_costs:
                    print("➕ إضافة عمود other_costs...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN other_costs DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود other_costs")
                else:
                    print("✅ عمود other_costs موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة other_costs: {e}")
            
            # التحقق من وجود عمود transportation_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='transportation_cost'
                """)
                has_transportation_cost = cursor.fetchone()[0] > 0
                if not has_transportation_cost:
                    print("➕ إضافة عمود transportation_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN transportation_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود transportation_cost")
                else:
                    print("✅ عمود transportation_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة transportation_cost: {e}")

            # التحقق من وجود عمود fuel_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='fuel_cost'
                """)
                has_fuel_cost = cursor.fetchone()[0] > 0
                if not has_fuel_cost:
                    print("➕ إضافة عمود fuel_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN fuel_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود fuel_cost")
                else:
                    print("✅ عمود fuel_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة fuel_cost: {e}")

            # التحقق من وجود عمود maintenance_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='maintenance_cost'
                """)
                has_maintenance_cost = cursor.fetchone()[0] > 0
                if not has_maintenance_cost:
                    print("➕ إضافة عمود maintenance_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN maintenance_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود maintenance_cost")
                else:
                    print("✅ عمود maintenance_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ في إضافة maintenance_cost: {e}")
            
            # التحقق من وجود عمود total_material_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_material_cost'
                """)
                has_total_material_cost = cursor.fetchone()[0] > 0
                if not has_total_material_cost:
                    print("➕ إضافة عمود total_material_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_material_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_material_cost")
                else:
                    print("✅ عمود total_material_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_material_cost: {e}")

            # التحقق من وجود عمود total_labor_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_labor_cost'
                """)
                has_total_labor_cost = cursor.fetchone()[0] > 0
                if not has_total_labor_cost:
                    print("➕ إضافة عمود total_labor_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_labor_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_labor_cost")
                else:
                    print("✅ عمود total_labor_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_labor_cost: {e}")

            # التحقق من وجود عمود total_overhead_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_overhead_cost'
                """)
                has_total_overhead_cost = cursor.fetchone()[0] > 0
                if not has_total_overhead_cost:
                    print("➕ إضافة عمود total_overhead_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_overhead_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_overhead_cost")
                else:
                    print("✅ عمود total_overhead_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_overhead_cost: {e}")
            
            # التحقق من وجود عمود total_operating_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_operating_cost'
                """)
                has_total_operating_cost = cursor.fetchone()[0] > 0
                if not has_total_operating_cost:
                    print("➕ إضافة عمود total_operating_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_operating_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_operating_cost")
                else:
                    print("✅ عمود total_operating_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_operating_cost: {e}")

            # التحقق من وجود عمود total_direct_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_direct_cost'
                """)
                has_total_direct_cost = cursor.fetchone()[0] > 0
                if not has_total_direct_cost:
                    print("➕ إضافة عمود total_direct_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_direct_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_direct_cost")
                else:
                    print("✅ عمود total_direct_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_direct_cost: {e}")

            # التحقق من وجود عمود total_indirect_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='total_indirect_cost'
                """)
                has_total_indirect_cost = cursor.fetchone()[0] > 0
                if not has_total_indirect_cost:
                    print("➕ إضافة عمود total_indirect_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN total_indirect_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود total_indirect_cost")
                else:
                    print("✅ عمود total_indirect_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود total_indirect_cost: {e}")
            
            # التحقق من وجود عمود unit_cost
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='unit_cost'
                """)
                has_unit_cost = cursor.fetchone()[0] > 0
                if not has_unit_cost:
                    print("➕ إضافة عمود unit_cost...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN unit_cost DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود unit_cost")
                else:
                    print("✅ عمود unit_cost موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود unit_cost: {e}")

            # التحقق من وجود عمود quantity_required
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pragma_table_info('inventory_manufacturingorder') 
                    WHERE name='quantity_required'
                """)
                has_quantity_required = cursor.fetchone()[0] > 0
                if not has_quantity_required:
                    print("➕ إضافة عمود quantity_required...")
                    cursor.execute("""
                        ALTER TABLE inventory_manufacturingorder 
                        ADD COLUMN quantity_required DECIMAL(10,2) DEFAULT 0.00
                    """)
                    print("✅ تم إضافة عمود quantity_required")
                else:
                    print("✅ عمود quantity_required موجود بالفعل")
            except Exception as e:
                print(f"❌ خطأ أثناء إضافة عمود quantity_required: {e}")
            
            print("\n🎉 تم الانتهاء من فحص وإضافة الأعمدة!")
            
    except OperationalError as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("🚀 بدء إضافة الأعمدة المفقودة...")
    add_missing_columns()
    print("🏁 انتهى!") 