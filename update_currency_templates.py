#!/usr/bin/env python3
"""
Script لتحديث جميع templates لتغيير العملة من الريال السعودي إلى الجنيه المصري
Script to update all templates to change currency from SAR to EGP
"""

import os
import re
import glob

def update_currency_in_file(file_path):
    """تحديث العملة في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # تحديث الريال السعودي إلى الجنيه المصري
        updated_content = content.replace('ر.س', 'ج.م')
        updated_content = updated_content.replace('ريال', 'جنيه')
        updated_content = updated_content.replace('SAR', 'EGP')
        updated_content = updated_content.replace('Saudi Riyal', 'Egyptian Pound')
        updated_content = updated_content.replace('الريال السعودي', 'الجنيه المصري')
        
        # كتابة المحتوى المحدث
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"✅ تم تحديث: {file_path}")
            return True
        else:
            print(f"⏭️ لا يحتاج تحديث: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحديث {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🇪🇬 بدء تحديث العملة في جميع templates...")
    print("="*60)
    
    # البحث عن جميع ملفات HTML
    html_files = glob.glob('templates/**/*.html', recursive=True)
    
    updated_files = 0
    total_files = len(html_files)
    
    for file_path in html_files:
        if update_currency_in_file(file_path):
            updated_files += 1
    
    print("="*60)
    print(f"📊 النتائج:")
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   ✅ ملفات محدثة: {updated_files}")
    print(f"   ⏭️ ملفات لم تحتج تحديث: {total_files - updated_files}")
    
    if updated_files > 0:
        print("\n🎉 تم تحديث العملة بنجاح!")
        print("💡 تأكد من إعادة تشغيل الخادم لرؤية التغييرات")
    else:
        print("\n✨ جميع الملفات محدثة بالفعل!")

if __name__ == "__main__":
    main()
