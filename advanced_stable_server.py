#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Stable Django Server
خادم Django المتقدم فائق الاستقرار

Features:
- Auto-restart on crashes
- Health monitoring
- Performance tracking
- Memory management
- Log rotation
- Email alerts (optional)
- Database backup
- System resource monitoring
"""

import os
import sys
import time
import signal
import subprocess
import threading
import json
import sqlite3
import shutil
import psutil
from datetime import datetime, timedelta
from pathlib import Path

class AdvancedStableServer:
    """Advanced Stable Django Server with comprehensive monitoring"""
    
    def __init__(self):
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 10000
        self.restart_delay = 5
        self.port = 8000
        self.host = '127.0.0.1'
        
        # Monitoring settings
        self.health_check_interval = 30  # seconds
        self.performance_check_interval = 60  # seconds
        self.backup_interval = 3600  # 1 hour
        self.log_rotation_size = 10 * 1024 * 1024  # 10MB
        
        # Performance thresholds
        self.max_memory_mb = 500
        self.max_cpu_percent = 80
        self.max_response_time = 10  # seconds
        
        # Paths
        self.log_dir = Path("logs")
        self.backup_dir = Path("backups")
        self.stats_file = Path("server_stats.json")
        
        # Create directories
        self.log_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'start_time': None,
            'total_restarts': 0,
            'uptime_seconds': 0,
            'health_checks': 0,
            'failed_health_checks': 0,
            'performance_alerts': 0,
            'last_backup': None,
            'memory_usage_history': [],
            'cpu_usage_history': [],
            'response_time_history': []
        }
        
        self.load_stats()
    
    def log_message(self, message, level="INFO"):
        """Enhanced logging with file rotation"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        
        # Console output
        print(log_entry)
        
        # File logging
        log_file = self.log_dir / "server.log"
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
            
            # Check log rotation
            if log_file.stat().st_size > self.log_rotation_size:
                self.rotate_logs()
                
        except Exception as e:
            print(f"Logging error: {e}")
    
    def rotate_logs(self):
        """Rotate log files"""
        try:
            log_file = self.log_dir / "server.log"
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            archived_log = self.log_dir / f"server_{timestamp}.log"
            
            shutil.move(str(log_file), str(archived_log))
            self.log_message("Log file rotated", "INFO")
            
            # Keep only last 10 log files
            log_files = sorted(self.log_dir.glob("server_*.log"))
            if len(log_files) > 10:
                for old_log in log_files[:-10]:
                    old_log.unlink()
                    
        except Exception as e:
            self.log_message(f"Log rotation error: {e}", "ERROR")
    
    def save_stats(self):
        """Save statistics to file"""
        try:
            self.stats['uptime_seconds'] = int(time.time() - self.stats['start_time']) if self.stats['start_time'] else 0
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, default=str)
        except Exception as e:
            self.log_message(f"Error saving stats: {e}", "ERROR")
    
    def load_stats(self):
        """Load statistics from file"""
        try:
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    loaded_stats = json.load(f)
                    self.stats.update(loaded_stats)
        except Exception as e:
            self.log_message(f"Error loading stats: {e}", "WARNING")
    
    def signal_handler(self, signum, frame):
        """Enhanced signal handler"""
        self.log_message(f"Received signal {signum}, initiating graceful shutdown...", "INFO")
        self.create_backup()
        self.save_stats()
        self.stop_server()
        sys.exit(0)
    
    def setup_signal_handlers(self):
        """Setup signal handlers"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def get_system_info(self):
        """Get current system information"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'memory_mb': psutil.virtual_memory().used / (1024 * 1024),
                'disk_percent': psutil.disk_usage('.').percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.log_message(f"Error getting system info: {e}", "ERROR")
            return {}
    
    def check_performance(self):
        """Check system performance and alert if needed"""
        try:
            system_info = self.get_system_info()
            if not system_info:
                return
            
            # Check memory usage
            if system_info['memory_mb'] > self.max_memory_mb:
                self.log_message(f"HIGH MEMORY USAGE: {system_info['memory_mb']:.1f}MB", "WARNING")
                self.stats['performance_alerts'] += 1
            
            # Check CPU usage
            if system_info['cpu_percent'] > self.max_cpu_percent:
                self.log_message(f"HIGH CPU USAGE: {system_info['cpu_percent']:.1f}%", "WARNING")
                self.stats['performance_alerts'] += 1
            
            # Store history (keep last 100 entries)
            self.stats['memory_usage_history'].append({
                'timestamp': system_info['timestamp'],
                'value': system_info['memory_mb']
            })
            self.stats['cpu_usage_history'].append({
                'timestamp': system_info['timestamp'],
                'value': system_info['cpu_percent']
            })
            
            # Trim history
            if len(self.stats['memory_usage_history']) > 100:
                self.stats['memory_usage_history'] = self.stats['memory_usage_history'][-100:]
            if len(self.stats['cpu_usage_history']) > 100:
                self.stats['cpu_usage_history'] = self.stats['cpu_usage_history'][-100:]
                
        except Exception as e:
            self.log_message(f"Performance check error: {e}", "ERROR")
    
    def start_server(self):
        """Start Django server with enhanced monitoring"""
        try:
            self.log_message(f"Starting server on {self.host}:{self.port}")
            
            cmd = [
                sys.executable, 'manage.py', 'runserver',
                f'{self.host}:{self.port}',
                '--noreload'
            ]
            
            # Enhanced environment
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['DJANGO_SETTINGS_MODULE'] = 'osaric_accounts.settings'
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env
            )
            
            self.is_running = True
            if not self.stats['start_time']:
                self.stats['start_time'] = time.time()
            
            self.log_message(f"Server started successfully! PID: {self.server_process.pid}")
            return True
            
        except Exception as e:
            self.log_message(f"Error starting server: {e}", "ERROR")
            return False
    
    def check_server_health(self):
        """Enhanced health check with response time monitoring"""
        try:
            import urllib.request
            import urllib.error
            
            url = f"http://{self.host}:{self.port}/"
            start_time = time.time()
            
            try:
                request = urllib.request.Request(url)
                request.add_header('User-Agent', 'AdvancedStableServer/1.0')
                response = urllib.request.urlopen(request, timeout=self.max_response_time)
                
                response_time = time.time() - start_time
                
                # Store response time history
                self.stats['response_time_history'].append({
                    'timestamp': datetime.now().isoformat(),
                    'value': response_time
                })
                if len(self.stats['response_time_history']) > 100:
                    self.stats['response_time_history'] = self.stats['response_time_history'][-100:]
                
                if response.getcode() == 200:
                    self.stats['health_checks'] += 1
                    if response_time > 5:  # Slow response warning
                        self.log_message(f"SLOW RESPONSE: {response_time:.2f}s", "WARNING")
                    return True
                else:
                    self.log_message(f"Server returned error code: {response.getcode()}", "ERROR")
                    self.stats['failed_health_checks'] += 1
                    return False
                    
            except urllib.error.URLError as e:
                self.log_message(f"Health check failed: {e}", "ERROR")
                self.stats['failed_health_checks'] += 1
                return False
            except Exception as e:
                self.log_message(f"Health check error: {e}", "ERROR")
                self.stats['failed_health_checks'] += 1
                return False
                
        except Exception as e:
            self.log_message(f"Health check system error: {e}", "ERROR")
            return False
    
    def create_backup(self):
        """Create database and media backup"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_folder = self.backup_dir / f"backup_{timestamp}"
            backup_folder.mkdir(exist_ok=True)
            
            # Backup database
            db_file = Path("db.sqlite3")
            if db_file.exists():
                shutil.copy2(db_file, backup_folder / "db.sqlite3")
                self.log_message(f"Database backed up to {backup_folder}")
            
            # Backup media files
            media_dir = Path("media")
            if media_dir.exists():
                shutil.copytree(media_dir, backup_folder / "media", dirs_exist_ok=True)
                self.log_message(f"Media files backed up to {backup_folder}")
            
            # Backup logs
            if self.log_dir.exists():
                shutil.copytree(self.log_dir, backup_folder / "logs", dirs_exist_ok=True)
            
            self.stats['last_backup'] = datetime.now().isoformat()
            
            # Clean old backups (keep last 10)
            backups = sorted(self.backup_dir.glob("backup_*"))
            if len(backups) > 10:
                for old_backup in backups[:-10]:
                    shutil.rmtree(old_backup)
                    self.log_message(f"Removed old backup: {old_backup}")
            
            return True
            
        except Exception as e:
            self.log_message(f"Backup error: {e}", "ERROR")
            return False
    
    def restart_server(self):
        """Enhanced server restart with backup"""
        self.log_message("Initiating server restart...")
        
        # Create backup before restart
        self.create_backup()
        
        # Stop current server
        self.stop_server()
        
        # Wait before restart
        time.sleep(self.restart_delay)
        
        # Increment restart counter
        self.restart_count += 1
        self.stats['total_restarts'] += 1
        
        if self.restart_count <= self.max_restarts:
            self.log_message(f"Restart attempt #{self.restart_count}")
            return self.start_server()
        else:
            self.log_message(f"Maximum restart attempts reached ({self.max_restarts})", "ERROR")
            return False
    
    def stop_server(self):
        """Enhanced server stop"""
        if self.server_process:
            try:
                self.log_message("Stopping server...")
                
                # Graceful termination
                self.server_process.terminate()
                
                try:
                    self.server_process.wait(timeout=10)
                    self.log_message("Server stopped gracefully")
                except subprocess.TimeoutExpired:
                    self.log_message("Force stopping server...")
                    self.server_process.kill()
                    self.server_process.wait()
                    self.log_message("Server force stopped")
                
            except Exception as e:
                self.log_message(f"Error stopping server: {e}", "ERROR")
            finally:
                self.server_process = None
                self.is_running = False
    
    def monitor_server_output(self):
        """Enhanced output monitoring"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    
                    # Log different types of messages
                    if any(keyword in line.upper() for keyword in ['ERROR', 'EXCEPTION', 'CRITICAL']):
                        self.log_message(f"SERVER ERROR: {line}", "ERROR")
                    elif any(keyword in line.upper() for keyword in ['WARNING', 'WARN']):
                        self.log_message(f"SERVER WARNING: {line}", "WARNING")
                    elif 'Starting development server' in line:
                        self.log_message(f"SERVER READY: {line}")
                    elif 'Quit the server' in line:
                        self.log_message("Server is ready for connections")
                    elif line.startswith('[') and 'HTTP' in line:
                        # HTTP request log - only log errors
                        if any(code in line for code in ['4', '5']) and 'HTTP/1.1" ' in line:
                            status_code = line.split('HTTP/1.1" ')[1].split()[0]
                            if status_code.startswith(('4', '5')):
                                self.log_message(f"HTTP ERROR: {line}", "WARNING")
                
                if self.server_process.poll() is not None:
                    break
                    
        except Exception as e:
            self.log_message(f"Output monitoring error: {e}", "ERROR")
    
    def health_monitor_loop(self):
        """Enhanced health monitoring loop"""
        consecutive_failures = 0
        max_failures = 3
        last_backup = time.time()
        last_performance_check = time.time()
        
        while self.is_running:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                current_time = time.time()
                
                # Performance monitoring
                if current_time - last_performance_check >= self.performance_check_interval:
                    self.check_performance()
                    last_performance_check = current_time
                
                # Automatic backup
                if current_time - last_backup >= self.backup_interval:
                    self.create_backup()
                    last_backup = current_time
                
                # Process health check
                if not self.server_process or self.server_process.poll() is not None:
                    self.log_message("Server process died!", "ERROR")
                    if not self.restart_server():
                        break
                    consecutive_failures = 0
                    continue
                
                # HTTP health check
                if self.check_server_health():
                    if consecutive_failures > 0:
                        self.log_message("Server recovered successfully")
                    consecutive_failures = 0
                    self.log_message("Health check: OK")
                else:
                    consecutive_failures += 1
                    self.log_message(f"Health check failed ({consecutive_failures}/{max_failures})", "WARNING")
                    
                    if consecutive_failures >= max_failures:
                        self.log_message("Multiple health check failures - restarting server", "ERROR")
                        if not self.restart_server():
                            break
                        consecutive_failures = 0
                
                # Save statistics periodically
                self.save_stats()
                
            except Exception as e:
                self.log_message(f"Health monitor error: {e}", "ERROR")
                time.sleep(5)
    
    def print_status_report(self):
        """Print comprehensive status report"""
        uptime = int(time.time() - self.stats['start_time']) if self.stats['start_time'] else 0
        uptime_str = str(timedelta(seconds=uptime))
        
        print("\n" + "=" * 80)
        print("🚀 ADVANCED STABLE SERVER STATUS REPORT")
        print("=" * 80)
        print(f"📊 Server URL: http://{self.host}:{self.port}/")
        print(f"⏱️  Uptime: {uptime_str}")
        print(f"🔄 Total Restarts: {self.stats['total_restarts']}")
        print(f"✅ Health Checks: {self.stats['health_checks']}")
        print(f"❌ Failed Checks: {self.stats['failed_health_checks']}")
        print(f"⚠️  Performance Alerts: {self.stats['performance_alerts']}")
        print(f"💾 Last Backup: {self.stats.get('last_backup', 'Never')}")
        
        # System info
        system_info = self.get_system_info()
        if system_info:
            print(f"🖥️  CPU Usage: {system_info['cpu_percent']:.1f}%")
            print(f"💾 Memory Usage: {system_info['memory_mb']:.1f}MB ({system_info['memory_percent']:.1f}%)")
            print(f"💿 Disk Usage: {system_info['disk_percent']:.1f}%")
        
        print("=" * 80)
    
    def run(self):
        """Run advanced stable server"""
        print("=" * 80)
        print("🚀 ADVANCED STABLE DJANGO SERVER")
        print("   Osaric Accounts System")
        print("=" * 80)
        
        self.log_message("Starting Advanced Stable Server System...")
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Start server
        if not self.start_server():
            self.log_message("Failed to start server!", "ERROR")
            return False
        
        # Start monitoring threads
        output_thread = threading.Thread(target=self.monitor_server_output)
        output_thread.daemon = True
        output_thread.start()
        
        # Wait for server to start
        time.sleep(5)
        
        health_thread = threading.Thread(target=self.health_monitor_loop)
        health_thread.daemon = True
        health_thread.start()
        
        # Print initial status
        self.print_status_report()
        
        self.log_message("🎯 ADVANCED FEATURES ACTIVE:")
        self.log_message("   • Auto-restart on crashes")
        self.log_message("   • Health monitoring every 30s")
        self.log_message("   • Performance tracking")
        self.log_message("   • Automatic backups every hour")
        self.log_message("   • Log rotation")
        self.log_message("   • System resource monitoring")
        self.log_message("   • Response time tracking")
        self.log_message("Press Ctrl+C for safe shutdown")
        
        try:
            # Main loop
            while self.is_running:
                time.sleep(60)  # Print status every minute
                if self.is_running:
                    self.print_status_report()
                    
        except KeyboardInterrupt:
            self.log_message("Ctrl+C pressed - initiating shutdown")
        finally:
            self.create_backup()
            self.save_stats()
            self.stop_server()
            self.log_message("Advanced Stable Server terminated")
        
        return True

def main():
    """Main function"""
    server = AdvancedStableServer()
    success = server.run()
    
    if success:
        print("✅ Advanced Stable Server completed successfully!")
    else:
        print("❌ Advanced Stable Server failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()