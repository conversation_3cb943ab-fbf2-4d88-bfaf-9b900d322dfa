/**
 * مولد الباركود والـ QR Code
 * Barcode and QR Code Generator
 */

class BarcodeGenerator {
    constructor() {
        this.barcodeTypes = {
            'CODE128': 'Code 128',
            'CODE39': 'Code 39',
            'EAN13': 'EAN-13',
            'EAN8': 'EAN-8',
            'UPC': 'UPC-A',
            'ITF14': 'ITF-14'
        };
        
        this.qrCodeOptions = {
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.M
        };
    }

    // إنشاء باركود Code 128
    generateCode128(data, options = {}) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        const settings = {
            width: options.width || 200,
            height: options.height || 50,
            fontSize: options.fontSize || 12,
            showText: options.showText !== false,
            ...options
        };

        canvas.width = settings.width;
        canvas.height = settings.height;

        // رسم الباركود
        this.drawCode128(ctx, data, settings);
        
        return canvas.toDataURL();
    }

    // رسم Code 128
    drawCode128(ctx, data, settings) {
        const code128 = this.encodeCode128(data);
        const barWidth = settings.width / code128.length;
        
        ctx.fillStyle = '#000000';
        
        for (let i = 0; i < code128.length; i++) {
            if (code128[i] === '1') {
                ctx.fillRect(i * barWidth, 0, barWidth, settings.height - (settings.showText ? 20 : 0));
            }
        }
        
        if (settings.showText) {
            ctx.fillStyle = '#000000';
            ctx.font = `${settings.fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(data, settings.width / 2, settings.height - 5);
        }
    }

    // ترميز Code 128
    encodeCode128(data) {
        const code128Table = {
            '0': '11011001100', '1': '11001101100', '2': '11001100110', '3': '10010011000',
            '4': '10010001100', '5': '10001001100', '6': '10011001000', '7': '10011000100',
            '8': '10001100100', '9': '11001001000', 'A': '11001000100', 'B': '11000100100',
            'C': '10110011100', 'D': '10011011100', 'E': '10011001110', 'F': '10111001000',
            'G': '10011101000', 'H': '10011100100', 'I': '11001110010', 'J': '11001011100',
            'K': '11001001110', 'L': '11011100100', 'M': '11001110100', 'N': '11101101110',
            'O': '11101001100', 'P': '11100101100', 'Q': '11100100110', 'R': '11101100100',
            'S': '11100110100', 'T': '11100110010', 'U': '11011011000', 'V': '11011000110',
            'W': '11000110110', 'X': '10100011000', 'Y': '10001011000', 'Z': '10001000110',
            ' ': '10110001000', '!': '10001101000', '"': '10001100010', '#': '11010001000',
            '$': '11000101000', '%': '11000100010', '&': '10110111000', "'": '10110001110',
            '(': '10001101110', ')': '10111011000', '*': '10111000110', '+': '10001110110',
            ',': '11101110110', '-': '11010001110', '.': '11000101110', '/': '11011101000',
            ':': '11011100010', ';': '11011101110', '<': '11101011000', '=': '11101000110',
            '>': '11100010110', '?': '11101101000', '@': '11101100010', '[': '11100011010',
            '\\': '11101111010', ']': '11001000010', '^': '11110001010', '_': '10100110000',
            '`': '10100001100', 'a': '10010110000', 'b': '10010000110', 'c': '10000101100',
            'd': '10000100110', 'e': '10110010000', 'f': '10110000100', 'g': '10011010000',
            'h': '10011000010', 'i': '10000110100', 'j': '10000110010', 'k': '11000010010',
            'l': '11001010000', 'm': '11110111010', 'n': '11000010100', 'o': '10001111010',
            'p': '10100111100', 'q': '10010111100', 'r': '10010011110', 's': '10111100100',
            't': '10011110100', 'u': '10011110010', 'v': '11110100100', 'w': '11110010100',
            'x': '11110010010', 'y': '11011011110', 'z': '11011110110'
        };

        let encoded = '11010010000'; // Start Code B
        let checksum = 104; // Start Code B value

        for (let i = 0; i < data.length; i++) {
            const char = data[i];
            if (code128Table[char]) {
                encoded += code128Table[char];
                checksum += (char.charCodeAt(0) - 32) * (i + 1);
            }
        }

        // إضافة checksum
        const checksumChar = String.fromCharCode((checksum % 103) + 32);
        if (code128Table[checksumChar]) {
            encoded += code128Table[checksumChar];
        }

        encoded += '1100011101011'; // Stop pattern

        return encoded;
    }

    // إنشاء QR Code
    generateQRCode(data, options = {}) {
        const canvas = document.createElement('canvas');
        const settings = { ...this.qrCodeOptions, ...options };
        
        try {
            const qr = qrcode(0, settings.correctLevel);
            qr.addData(data);
            qr.make();
            
            const moduleCount = qr.getModuleCount();
            const cellSize = Math.floor(settings.width / moduleCount);
            
            canvas.width = cellSize * moduleCount;
            canvas.height = cellSize * moduleCount;
            
            const ctx = canvas.getContext('2d');
            
            for (let row = 0; row < moduleCount; row++) {
                for (let col = 0; col < moduleCount; col++) {
                    ctx.fillStyle = qr.isDark(row, col) ? settings.colorDark : settings.colorLight;
                    ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                }
            }
            
            return canvas.toDataURL();
        } catch (error) {
            console.error('خطأ في إنشاء QR Code:', error);
            return this.generateErrorImage('QR Code Error');
        }
    }

    // إنشاء باركود EAN-13
    generateEAN13(data, options = {}) {
        // التأكد من أن البيانات 13 رقم
        if (data.length !== 13 || !/^\d+$/.test(data)) {
            return this.generateErrorImage('EAN-13 يجب أن يكون 13 رقم');
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        const settings = {
            width: options.width || 200,
            height: options.height || 60,
            fontSize: options.fontSize || 10,
            showText: options.showText !== false,
            ...options
        };

        canvas.width = settings.width;
        canvas.height = settings.height;

        this.drawEAN13(ctx, data, settings);
        
        return canvas.toDataURL();
    }

    // رسم EAN-13
    drawEAN13(ctx, data, settings) {
        const ean13Patterns = {
            'L': ['0001101', '0011001', '0010011', '0111101', '0100011', '0110001', '0101111', '0111011', '0110111', '0001011'],
            'G': ['0100111', '0110011', '0011011', '0100001', '0011101', '0111001', '0000101', '0010001', '0001001', '0010111'],
            'R': ['1110010', '1100110', '1101100', '1000010', '1011100', '1001110', '1010000', '1000100', '1001000', '1110100']
        };

        const firstDigitPatterns = [
            'LLLLLL', 'LLGLGG', 'LLGGLG', 'LLGGGL', 'LGLLGG', 'LGGLLG', 'LGGGLL', 'LGLGLG', 'LGLGGL', 'LGGLGL'
        ];

        const firstDigit = parseInt(data[0]);
        const pattern = firstDigitPatterns[firstDigit];
        
        let encoded = '101'; // Start guard

        // الأرقام الستة الأولى
        for (let i = 1; i <= 6; i++) {
            const digit = parseInt(data[i]);
            const patternType = pattern[i - 1];
            encoded += ean13Patterns[patternType][digit];
        }

        encoded += '01010'; // Center guard

        // الأرقام الستة الأخيرة
        for (let i = 7; i <= 12; i++) {
            const digit = parseInt(data[i]);
            encoded += ean13Patterns['R'][digit];
        }

        encoded += '101'; // End guard

        // رسم الباركود
        const barWidth = settings.width / encoded.length;
        ctx.fillStyle = '#000000';
        
        for (let i = 0; i < encoded.length; i++) {
            if (encoded[i] === '1') {
                ctx.fillRect(i * barWidth, 0, barWidth, settings.height - (settings.showText ? 15 : 0));
            }
        }
        
        if (settings.showText) {
            ctx.fillStyle = '#000000';
            ctx.font = `${settings.fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(data, settings.width / 2, settings.height - 2);
        }
    }

    // إنشاء صورة خطأ
    generateErrorImage(message) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = 200;
        canvas.height = 60;
        
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.strokeStyle = '#dc3545';
        ctx.strokeRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = '#dc3545';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(message, canvas.width / 2, canvas.height / 2 + 4);
        
        return canvas.toDataURL();
    }

    // إنشاء باركود حسب النوع
    generateBarcode(type, data, options = {}) {
        switch (type) {
            case 'CODE128':
                return this.generateCode128(data, options);
            case 'EAN13':
                return this.generateEAN13(data, options);
            case 'QR':
                return this.generateQRCode(data, options);
            default:
                return this.generateCode128(data, options);
        }
    }

    // التحقق من صحة البيانات
    validateData(type, data) {
        switch (type) {
            case 'EAN13':
                return /^\d{13}$/.test(data);
            case 'EAN8':
                return /^\d{8}$/.test(data);
            case 'UPC':
                return /^\d{12}$/.test(data);
            case 'CODE39':
                return /^[A-Z0-9\-\.\$\/\+\%\*\s]+$/.test(data);
            case 'CODE128':
                return data.length > 0 && data.length <= 80;
            case 'QR':
                return data.length > 0 && data.length <= 2953;
            default:
                return true;
        }
    }

    // إنشاء باركود للفاتورة
    generateInvoiceBarcode(invoiceData, options = {}) {
        const settings = {
            type: 'CODE128',
            includeDate: true,
            includeAmount: false,
            prefix: 'INV',
            ...options
        };

        let barcodeData = settings.prefix + invoiceData.number;
        
        if (settings.includeDate) {
            const date = new Date(invoiceData.date);
            barcodeData += date.getFullYear().toString().substr(-2) + 
                          (date.getMonth() + 1).toString().padStart(2, '0') + 
                          date.getDate().toString().padStart(2, '0');
        }
        
        if (settings.includeAmount && invoiceData.total) {
            barcodeData += Math.round(invoiceData.total).toString();
        }

        return this.generateBarcode(settings.type, barcodeData, options);
    }

    // إنشاء QR Code للفاتورة
    generateInvoiceQRCode(invoiceData, options = {}) {
        const qrData = {
            invoice_number: invoiceData.number,
            date: invoiceData.date,
            total: invoiceData.total,
            customer: invoiceData.customer?.name || '',
            company: invoiceData.company?.name || ''
        };

        const qrString = JSON.stringify(qrData);
        return this.generateQRCode(qrString, options);
    }
}

// مكتبة QR Code بسيطة
const qrcode = function(typeNumber, errorCorrectLevel) {
    const QRCode = {
        CorrectLevel: { L: 1, M: 0, Q: 3, H: 2 }
    };
    
    let _typeNumber = typeNumber;
    let _errorCorrectLevel = errorCorrectLevel;
    let _modules = null;
    let _moduleCount = 0;
    let _dataCache = null;
    let _dataList = [];

    const _this = {};

    const makeImpl = function(test, maskPattern) {
        _moduleCount = _typeNumber * 4 + 17;
        _modules = function(moduleCount) {
            const modules = new Array(moduleCount);
            for (let row = 0; row < moduleCount; row++) {
                modules[row] = new Array(moduleCount);
                for (let col = 0; col < moduleCount; col++) {
                    modules[row][col] = null;
                }
            }
            return modules;
        }(_moduleCount);

        setupPositionProbePattern(0, 0);
        setupPositionProbePattern(_moduleCount - 7, 0);
        setupPositionProbePattern(0, _moduleCount - 7);
        setupPositionAdjustPattern();
        setupTimingPattern();
        setupTypeInfo(test, maskPattern);

        if (_typeNumber >= 7) {
            setupTypeNumber(test);
        }

        if (_dataCache == null) {
            _dataCache = createData(_typeNumber, _errorCorrectLevel, _dataList);
        }

        mapData(_dataCache, maskPattern);
    };

    const setupPositionProbePattern = function(row, col) {
        for (let r = -1; r <= 7; r++) {
            if (row + r <= -1 || _moduleCount <= row + r) continue;
            for (let c = -1; c <= 7; c++) {
                if (col + c <= -1 || _moduleCount <= col + c) continue;
                if ((0 <= r && r <= 6 && (c == 0 || c == 6)) ||
                    (0 <= c && c <= 6 && (r == 0 || r == 6)) ||
                    (2 <= r && r <= 4 && 2 <= c && c <= 4)) {
                    _modules[row + r][col + c] = true;
                } else {
                    _modules[row + r][col + c] = false;
                }
            }
        }
    };

    const getBestMaskPattern = function() {
        let minLostPoint = 0;
        let pattern = 0;
        for (let i = 0; i < 8; i++) {
            makeImpl(true, i);
            const lostPoint = QRUtil.getLostPoint(_this);
            if (i == 0 || minLostPoint > lostPoint) {
                minLostPoint = lostPoint;
                pattern = i;
            }
        }
        return pattern;
    };

    const createData = function(typeNumber, errorCorrectLevel, dataList) {
        const rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectLevel);
        const buffer = new QRBitBuffer();
        
        for (let i = 0; i < dataList.length; i++) {
            const data = dataList[i];
            buffer.put(data.mode, 4);
            buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, typeNumber));
            data.write(buffer);
        }

        let totalDataCount = 0;
        for (let i = 0; i < rsBlocks.length; i++) {
            totalDataCount += rsBlocks[i].dataCount;
        }

        if (buffer.getLengthInBits() > totalDataCount * 8) {
            throw new Error("code length overflow. (" + buffer.getLengthInBits() + ">" + totalDataCount * 8 + ")");
        }

        if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
            buffer.put(0, 4);
        }

        while (buffer.getLengthInBits() % 8 != 0) {
            buffer.putBit(false);
        }

        while (true) {
            if (buffer.getLengthInBits() >= totalDataCount * 8) {
                break;
            }
            buffer.put(QRCode.PAD0, 8);
            if (buffer.getLengthInBits() >= totalDataCount * 8) {
                break;
            }
            buffer.put(QRCode.PAD1, 8);
        }

        return createBytes(buffer, rsBlocks);
    };

    _this.addData = function(data) {
        const newData = new QR8bitByte(data);
        _dataList.push(newData);
        _dataCache = null;
    };

    _this.isDark = function(row, col) {
        if (row < 0 || _moduleCount <= row || col < 0 || _moduleCount <= col) {
            throw new Error(row + "," + col);
        }
        return _modules[row][col];
    };

    _this.getModuleCount = function() {
        return _moduleCount;
    };

    _this.make = function() {
        makeImpl(false, getBestMaskPattern());
    };

    return _this;
};

// تصدير المولد
window.BarcodeGenerator = BarcodeGenerator;
