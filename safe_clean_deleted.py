#!/usr/bin/env python
"""
Safe clean up deleted items from database
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from definitions.models import Item, ItemCategory, Unit
from django.db import transaction

def safe_clean_items():
    """Safely clean inactive items"""
    
    print("Safe Database Cleanup - Delete Inactive Items")
    print("=" * 50)
    
    # Clean inactive items
    print("\nCleaning inactive items...")
    inactive_items = Item.objects.filter(is_active=False)
    items_count = inactive_items.count()
    
    if items_count > 0:
        print(f"Found {items_count} inactive items:")
        
        deleted_count = 0
        for item in inactive_items:
            try:
                item_name = item.name
                item_code = item.code
                print(f"  Deleting: {item_name} ({item_code})")
                
                # Try to delete safely
                item.delete()
                deleted_count += 1
                
            except Exception as e:
                print(f"  Error deleting {item_name}: {str(e)}")
                continue
        
        print(f"\nSuccessfully deleted {deleted_count} out of {items_count} items")
    else:
        print("No inactive items found")
    
    # Clean inactive categories
    print("\nCleaning inactive categories...")
    inactive_categories = ItemCategory.objects.filter(is_active=False)
    categories_count = inactive_categories.count()
    
    if categories_count > 0:
        print(f"Found {categories_count} inactive categories:")
        
        deleted_count = 0
        for category in inactive_categories:
            try:
                category_name = category.name
                print(f"  Deleting: {category_name}")
                
                category.delete()
                deleted_count += 1
                
            except Exception as e:
                print(f"  Error deleting {category_name}: {str(e)}")
                continue
        
        print(f"\nSuccessfully deleted {deleted_count} out of {categories_count} categories")
    else:
        print("No inactive categories found")
    
    # Clean inactive units
    print("\nCleaning inactive units...")
    inactive_units = Unit.objects.filter(is_active=False)
    units_count = inactive_units.count()
    
    if units_count > 0:
        print(f"Found {units_count} inactive units:")
        
        deleted_count = 0
        for unit in inactive_units:
            try:
                unit_name = unit.name
                print(f"  Deleting: {unit_name}")
                
                unit.delete()
                deleted_count += 1
                
            except Exception as e:
                print(f"  Error deleting {unit_name}: {str(e)}")
                continue
        
        print(f"\nSuccessfully deleted {deleted_count} out of {units_count} units")
    else:
        print("No inactive units found")
    
    print("\nCleanup completed!")

if __name__ == '__main__':
    safe_clean_items()