#!/usr/bin/env python
"""
سكريبت للتحقق من حالة أوامر التصنيع الحالية
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.models import ManufacturingOrder, ManufacturingMaterial
from inventory.utils import validate_manufacturing_order

def check_manufacturing_orders():
    """التحقق من حالة أوامر التصنيع"""
    
    print("=" * 60)
    print("🔍 فحص حالة أوامر التصنيع")
    print("=" * 60)
    
    orders = ManufacturingOrder.objects.filter(is_active=True).order_by('-created_at')
    
    if not orders.exists():
        print("❌ لا توجد أوامر تصنيع في النظام")
        return
    
    print(f"📊 تم العثور على {orders.count()} أمر تصنيع")
    print()
    
    for order in orders:
        print(f"🔸 أمر التصنيع #{order.id}")
        print(f"   المنتج: {order.finished_product.name}")
        print(f"   الكمية: {order.quantity_to_produce}")
        print(f"   الحالة: {order.get_status_display()} ({order.status})")
        print(f"   تاريخ الإنشاء: {order.created_at}")
        
        # التحقق من المواد
        materials = order.materials.all()
        print(f"   المواد الخام: {materials.count()} مادة")
        
        for material in materials:
            print(f"     - {material.material.name}: {material.quantity_required} (متوفر: {material.available_quantity})")
        
        # التحقق من الأخطاء
        validation_errors = validate_manufacturing_order(order)
        if validation_errors:
            print(f"   ⚠️  أخطاء التحقق:")
            for error in validation_errors:
                print(f"     - {error}")
        else:
            print(f"   ✅ لا توجد أخطاء في التحقق")
        
        # التحقق من إمكانية عرض الأزرار
        print(f"   🔘 أزرار الإجراءات:")
        if order.status == 'DRAFT':
            print(f"     - زر التعديل: متاح")
            print(f"     - زر التخطيط: متاح")
            print(f"     - زر بدء التصنيع: غير متاح (يحتاج تخطيط)")
        elif order.status == 'PLANNED':
            print(f"     - زر التعديل: متاح")
            print(f"     - زر تحديث الكميات: متاح")
            if not validation_errors:
                print(f"     - زر بدء التصنيع: متاح")
            else:
                print(f"     - زر بدء التصنيع: غير متاح (يوجد أخطاء)")
        elif order.status == 'IN_PROGRESS':
            print(f"     - زر التعديل: متاح")
            print(f"     - زر إكمال التصنيع: متاح")
        elif order.status == 'COMPLETED':
            print(f"     - رسالة الإكمال: معروضة")
        
        print()
        print("-" * 40)
        print()

if __name__ == "__main__":
    check_manufacturing_orders() 