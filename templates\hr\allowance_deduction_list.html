{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-cogs fa-2x mb-2 text-info"></i>
                    <h5 class="card-title">إدارة أنواع الإضافات والخصومات</h5>
                    <p class="card-text">تعريف وتعديل أنواع الإضافات والخصومات المستخدمة في النظام.</p>
                    <a href="{% url 'hr:allowance_type_list' %}" class="btn btn-outline-success btn-sm me-2">أنواع الإضافات</a>
                    <a href="{% url 'hr:deduction_type_list' %}" class="btn btn-outline-danger btn-sm">أنواع الخصومات</a>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-cog text-success me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="text-muted mb-0">إدارة الإضافات والخصومات</p>
                </div>
                <div>
                    <a href="/hr/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول شامل لكل الإضافات والخصومات -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-primary shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة شاملة لكل الإضافات والخصومات</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>النوع</th>
                                    <th>نوع الإضافة/الخصم</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>ملاحظات</th>
                                    <th>الحالة</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for obj in allowances %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ obj.employee.full_name }}</td>
                                    <td><span class="badge bg-success">إضافة</span></td>
                                    <td>{{ obj.name }}</td>
                                    <td><span class="badge bg-success">{{ obj.amount }} ج.م</span></td>
                                    <td>{{ obj.date }}</td>
                                    <td>{{ obj.notes|default:'-' }}</td>
                                    <td>{% if obj.is_active %}<span class="badge bg-success">نشط</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</td>
                                    <td>
                                        <a href="{% url 'hr:allowance_deduction_delete' obj.pk %}" class="btn btn-sm btn-outline-danger" title="حذف"><i class="fas fa-trash"></i></a>
                                        <a href="{% url 'hr:allowance_deduction_edit' obj.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></a>
                                        <a href="{% url 'hr:allowance_deduction_print' obj.pk %}" class="btn btn-sm btn-outline-info" title="طباعة"><i class="fas fa-print"></i></a>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% for obj in deductions %}
                                <tr>
                                    <td>{{ forloop.counter|add:allowances|length }}</td>
                                    <td>{{ obj.employee.full_name }}</td>
                                    <td><span class="badge bg-danger">خصم</span></td>
                                    <td>{% if obj.type == 'allowance' and obj.allowance_type %}{{ obj.allowance_type.name }}{% elif obj.type == 'deduction' and obj.deduction_type %}{{ obj.deduction_type.name }}{% else %}-{% endif %}</td>
                                    <td><span class="badge bg-danger">{{ obj.amount }} ج.م</span></td>
                                    <td>{{ obj.date }}</td>
                                    <td>{{ obj.notes|default:'-' }}</td>
                                    <td>{% if obj.is_active %}<span class="badge bg-success">نشط</span>{% else %}<span class="badge bg-danger">غير نشط</span>{% endif %}</td>
                                    <td>
                                        <a href="{% url 'hr:allowance_deduction_delete' obj.pk %}" class="btn btn-sm btn-outline-danger" title="حذف"><i class="fas fa-trash"></i></a>
                                        <a href="{% url 'hr:allowance_deduction_edit' obj.pk %}" class="btn btn-sm btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></a>
                                        <a href="{% url 'hr:allowance_deduction_print' obj.pk %}" class="btn btn-sm btn-outline-info" title="طباعة"><i class="fas fa-print"></i></a>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if not allowances and not deductions %}
                                <tr><td colspan="9" class="text-center text-muted">لا توجد بيانات</td></tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
