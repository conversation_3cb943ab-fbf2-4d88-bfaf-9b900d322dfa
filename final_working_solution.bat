@echo off
title الحل النهائي الذي يعمل - Final Working Solution
color 0A

echo ============================================================
echo 🎯 الحل النهائي الذي يعمل بدون مشاكل
echo Final Working Solution - No Issues
echo ============================================================
echo.

echo 🛑 إيقاف جميع العمليات السابقة...
tasklist | findstr python.exe >nul
if %errorLevel% == 0 (
    echo وجدت عمليات Python - جاري الإيقاف...
    wmic process where "name='python.exe'" delete >nul 2>&1
    timeout /t 3 /nobreak >nul
    echo ✅ تم إيقاف العمليات السابقة
) else (
    echo ✅ لا توجد عمليات Python تعمل
)

echo.
echo 🔍 فحص ملفات Django...
if not exist "manage.py" (
    echo ❌ ملف manage.py غير موجود
    echo تأكد من وجودك في مجلد المشروع الصحيح
    pause
    exit /b 1
)
echo ✅ ملفات Django موجودة

echo.
echo 🚀 بدء الخادم البسيط...
echo Starting Simple Server...
echo.

REM تشغيل Django بأبسط طريقة ممكنة
echo 📋 تشغيل Django على المنفذ 8000...
start "خادم Django البسيط" python manage.py runserver 0.0.0.0:8000 --insecure

echo ⏳ انتظار بدء الخادم...
timeout /t 8 /nobreak >nul

REM فحص حالة الخادم
echo 🔍 فحص حالة الخادم...
netstat -an | findstr ":8000" >nul
if %errorLevel% == 0 (
    echo ✅ الخادم يعمل على المنفذ 8000
) else (
    echo ❌ الخادم لا يعمل - محاولة منفذ آخر...
    start "خادم Django البسيط" python manage.py runserver 0.0.0.0:8001 --insecure
    timeout /t 5 /nobreak >nul
    netstat -an | findstr ":8001" >nul
    if %errorLevel% == 0 (
        echo ✅ الخادم يعمل على المنفذ 8001
        set SERVER_PORT=8001
    ) else (
        echo ❌ فشل في تشغيل الخادم
        pause
        exit /b 1
    )
)

if not defined SERVER_PORT set SERVER_PORT=8000

echo.
echo ============================================================
echo 🎉 الخادم يعمل بنجاح!
echo Server is Running Successfully!
echo ============================================================
echo.

REM الحصول على عنوان IP
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4" ^| findstr "192.168"') do (
    set LOCAL_IP=%%a
    set LOCAL_IP=!LOCAL_IP: =!
)

if not defined LOCAL_IP (
    for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
        set LOCAL_IP=%%a
        set LOCAL_IP=!LOCAL_IP: =!
        goto :found_ip
    )
)
:found_ip

if not defined LOCAL_IP set LOCAL_IP=127.0.0.1

echo 🌐 معلومات الوصول:
echo    المنفذ: %SERVER_PORT%
echo    عنوان IP المحلي: %LOCAL_IP%
echo.

echo 🔗 الروابط:
echo    http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
echo    http://localhost:%SERVER_PORT%/dashboard/
echo.

echo 📱 للوصول من الهاتف:
echo    http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
echo.

echo 📋 خيارات:
echo 1. فتح الموقع في المتصفح
echo 2. عرض معلومات مفصلة
echo 3. إيقاف الخادم
echo 0. خروج (الخادم يبقى يعمل)
echo.

set /p choice="اختر رقم الخيار (1-3): "

if "%choice%"=="1" (
    echo 🌐 فتح الموقع في المتصفح...
    start http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
) else if "%choice%"=="2" (
    goto show_details
) else if "%choice%"=="3" (
    goto stop_server
) else if "%choice%"=="0" (
    goto exit_keep_running
) else (
    echo ❌ خيار غير صحيح
    pause
    goto menu
)

goto end

:show_details
echo.
echo ============================================================
echo 📊 معلومات مفصلة
echo Detailed Information
echo ============================================================
echo.

echo 🖥️ معلومات النظام:
echo    اسم الجهاز: %COMPUTERNAME%
echo    المستخدم: %USERNAME%
echo    نظام التشغيل: %OS%
echo.

echo 🌐 معلومات الشبكة:
echo    عنوان IP المحلي: %LOCAL_IP%
echo    منفذ الخادم: %SERVER_PORT%
echo.

echo 🚀 حالة الخادم:
netstat -an | findstr ":%SERVER_PORT%" >nul && echo    ✅ الخادم يعمل بنجاح || echo    ❌ الخادم لا يعمل

echo.
echo 🔗 جميع طرق الوصول:
echo    • من نفس الجهاز: http://localhost:%SERVER_PORT%/dashboard/
echo    • من الشبكة المحلية: http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
echo    • من الهاتف: http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
echo.

echo 💡 نصائح:
echo    • هذا خادم HTTP عادي (ليس HTTPS)
echo    • يعمل بدون مشاكل أو أخطاء
echo    • يمكن الوصول من أي جهاز في الشبكة
echo    • سريع ومستقر
echo.

pause
goto menu

:stop_server
echo.
echo 🛑 إيقاف الخادم...
wmic process where "name='python.exe'" delete >nul 2>&1
echo ✅ تم إيقاف الخادم
pause
goto end

:exit_keep_running
echo.
echo 💡 الخادم يبقى يعمل في الخلفية
echo Server keeps running in background
echo.
echo 🔗 للوصول: http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
echo.
pause
goto end

:menu
echo.
echo ============================================================
goto choice_menu

:choice_menu
echo 📋 خيارات:
echo 1. فتح الموقع في المتصفح
echo 2. عرض معلومات مفصلة
echo 3. إيقاف الخادم
echo 0. خروج (الخادم يبقى يعمل)
echo.
set /p choice="اختر رقم الخيار (1-3): "
goto process_choice

:process_choice
if "%choice%"=="1" (
    echo 🌐 فتح الموقع في المتصفح...
    start http://%LOCAL_IP%:%SERVER_PORT%/dashboard/
    goto menu
) else if "%choice%"=="2" (
    goto show_details
) else if "%choice%"=="3" (
    goto stop_server
) else if "%choice%"=="0" (
    goto exit_keep_running
) else (
    echo ❌ خيار غير صحيح
    pause
    goto menu
)

:end
echo.
echo 👋 شكراً لاستخدام النظام!
echo Thank you for using the system!
echo.
pause
exit /b 0
