#!/usr/bin/env python3
"""
Simple Test Manufacturing Order Forms
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from inventory.forms import ManufacturingOrderForm, ManufacturingOrderMaterialForm


def test_forms():
    """Test forms"""
    print("Testing Manufacturing Order Forms...")
    
    # Test main form
    form = ManufacturingOrderForm()
    
    # Check required fields exist
    required_fields = [
        'order_number', 'date', 'warehouse', 'product_item', 
        'quantity_to_produce', 'finished_goods_warehouse'
    ]
    
    missing_fields = []
    for field_name in required_fields:
        if field_name not in form.fields:
            missing_fields.append(field_name)
    
    if missing_fields:
        print(f"Missing required fields: {missing_fields}")
        return False
    
    # Check display fields exist
    display_fields = [
        'total_material_cost_display', 
        'total_cost_display', 
        'cost_per_unit_display'
    ]
    
    missing_display_fields = []
    for field_name in display_fields:
        if field_name not in form.fields:
            missing_display_fields.append(field_name)
    
    if missing_display_fields:
        print(f"Missing display fields: {missing_display_fields}")
        return False
    
    # Check readonly attributes
    for field_name in display_fields:
        widget = form.fields[field_name].widget
        if not widget.attrs.get('readonly'):
            print(f"Display field {field_name} should be readonly")
            return False
    
    # Test material form
    material_form = ManufacturingOrderMaterialForm()
    
    if 'total_cost_display' not in material_form.fields:
        print("Missing total_cost_display field in material form")
        return False
    
    # Check JavaScript events
    quantity_widget = form.fields['quantity_to_produce'].widget
    if 'onchange' not in quantity_widget.attrs:
        print("Missing onchange event for quantity_to_produce")
        return False
    
    # Check help texts
    help_text_fields = [
        'quantity_to_produce',
        'operating_expenses',
        'labor_cost',
        'overhead_cost'
    ]
    
    missing_help_texts = []
    for field_name in help_text_fields:
        if not form.fields[field_name].help_text:
            missing_help_texts.append(field_name)
    
    if missing_help_texts:
        print(f"Missing help texts for fields: {missing_help_texts}")
        return False
    
    print("All form tests passed!")
    return True


def main():
    """Main test function"""
    print("Starting Manufacturing Order Forms Test")
    print("=" * 50)
    
    try:
        if test_forms():
            print("\nSUCCESS: All tests passed!")
            print("\nManufacturing Order Forms are working correctly:")
            print("- All required fields are present")
            print("- Display fields are readonly")
            print("- JavaScript events are attached")
            print("- Help texts are provided")
            return True
        else:
            print("\nFAILED: Some tests failed")
            return False
    except Exception as e:
        print(f"\nERROR: Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)