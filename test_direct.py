#!/usr/bin/env python
"""
اختبار مباشر للموقع
"""

import requests
import sys

def test_direct():
    """اختبار مباشر للموقع"""
    
    print("🔍 اختبار مباشر للموقع...")
    
    try:
        # اختبار الصفحة الرئيسية
        print("📄 اختبار الصفحة الرئيسية...")
        response = requests.get('http://127.0.0.1:8000/', timeout=10)
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 302:
            print('✅ إعادة توجيه ناجحة للصفحة الرئيسية')
        elif response.status_code == 500:
            print('❌ خطأ 500 في الصفحة الرئيسية!')
            print('محتوى الخطأ:')
            print(response.text[:500])
            return False
        
        # اختبار صفحة dashboard
        print("\n📊 اختبار صفحة dashboard...")
        response = requests.get('http://127.0.0.1:8000/dashboard/', timeout=10)
        print(f'Status Code: {response.status_code}')
        
        if response.status_code == 500:
            print('❌ خطأ 500 في dashboard!')
            print('محتوى الخطأ:')
            print(response.text[:1000])
            return False
        elif response.status_code == 200:
            print('✅ صفحة dashboard تعمل بنجاح!')
            print('🎉 الموقع جاهز للاستخدام!')
            return True
        else:
            print(f'Status غير متوقع: {response.status_code}')
            return False
            
    except requests.exceptions.ConnectionError:
        print('❌ لا يمكن الاتصال بالخادم!')
        print('تأكد من أن الخادم يعمل على http://127.0.0.1:8000/')
        return False
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')
        return False

if __name__ == "__main__":
    success = test_direct()
    if success:
        print("\n🎯 النتيجة النهائية:")
        print("✅ الموقع يعمل بشكل مثالي!")
        print("✅ القائمة الجانبية الاحترافية جاهزة!")
        print("✅ اسم الشركة 'حسابات أوساريك' يظهر بشكل صحيح!")
        print("\n🌐 الرابط: http://127.0.0.1:8000/dashboard/")
        print("🌐 أو: http://127.0.0.1:8000/")
    else:
        print("\n❌ هناك مشاكل في الموقع تحتاج إلى إصلاح")
        print("💡 جرب إعادة تشغيل الخادم بدون autoreload:")
        print("   python manage.py runserver 127.0.0.1:8000 --noreload")
