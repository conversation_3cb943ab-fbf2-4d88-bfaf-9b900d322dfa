{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .form-header h4 {
        margin: 0;
        font-weight: 600;
    }
    
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .form-section {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }
    
    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .form-section h5 {
        color: #667eea;
        border-bottom: 3px solid #667eea;
        padding-bottom: 15px;
        margin-bottom: 25px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-section h5 i {
        background: rgba(102, 126, 234, 0.1);
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #667eea;
    }
    
    .form-group {
        margin-bottom: 20px;
        position: relative;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-label i {
        color: #667eea;
        font-size: 14px;
    }
    
    .required-field {
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        width: 100%;
        transition: all 0.3s ease;
        background: white;
        font-size: 14px;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
        transform: translateY(-1px);
    }
    
    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    
    .form-control.is-valid {
        border-color: #198754;
        box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    }
    
    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-top: 8px;
        padding: 8px 12px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 5px;
        border-left: 3px solid #667eea;
    }
    
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 8px;
        padding: 8px 12px;
        background: rgba(220, 53, 69, 0.05);
        border-radius: 5px;
        border-left: 3px solid #dc3545;
    }
    
    .action-buttons {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 25px;
        border-radius: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
    }
    
    .action-btn {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        text-decoration: none;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-submit:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
    
    .form-progress {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 12px;
    }
    
    .step-number.active {
        background: #667eea;
        color: white;
    }
    
    .step-number.completed {
        background: #198754;
        color: white;
    }
    
    .step-label {
        font-size: 12px;
        color: #6c757d;
        text-align: center;
    }
    
    .step-label.active {
        color: #667eea;
        font-weight: 600;
    }
    
    .step-label.completed {
        color: #198754;
        font-weight: 600;
    }
    
    .form-field-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
    }
    
    .form-control:focus + .form-field-icon {
        color: #667eea;
    }
    
    .form-control.is-invalid + .form-field-icon {
        color: #dc3545;
    }
    
    .form-control.is-valid + .form-field-icon {
        color: #198754;
    }
    
    .floating-label {
        position: relative;
    }
    
    .floating-label .form-control {
        padding-top: 20px;
        padding-bottom: 8px;
    }
    
    .floating-label .form-label {
        position: absolute;
        top: 12px;
        left: 15px;
        transition: all 0.3s ease;
        pointer-events: none;
        color: #6c757d;
    }
    
    .floating-label .form-control:focus + .form-label,
    .floating-label .form-control:not(:placeholder-shown) + .form-label {
        top: 5px;
        font-size: 12px;
        color: #667eea;
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }
        
        .action-btn {
            width: 100%;
        }
        
        .progress-steps {
            flex-direction: column;
            gap: 15px;
        }
        
        .form-section {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">
                    <i class="fas fa-user-times me-2" style="color: #667eea;"></i>
                    {{ title }}
                </h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'hr:dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'hr:termination_list' %}">إنهاء الخدمة</a></li>
                        <li class="breadcrumb-item active">
                            {% if termination %}تعديل{% else %}إضافة جديد{% endif %}
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="form-header">
                <h4>
                    <i class="fas fa-file-alt me-2"></i>
                    {% if termination %}تعديل طلب إنهاء الخدمة{% else %}إضافة طلب إنهاء خدمة جديد{% endif %}
                </h4>
                <p class="mb-0 mt-2">يرجى ملء جميع الحقول المطلوبة بعناية</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="form-container">
                <div class="card-body p-4">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <!-- مؤشر التقدم -->
                        <div class="form-progress">
                            <div class="progress-steps">
                                <div class="progress-step">
                                    <div class="step-number active">1</div>
                                    <div class="step-label active">معلومات الموظف</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-number">2</div>
                                    <div class="step-label">التواريخ</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-number">3</div>
                                    <div class="step-label">السبب</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-number">4</div>
                                    <div class="step-label">المراجعة</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- معلومات الموظف -->
                            <div class="col-md-6">
                                <div class="form-section">
                                    <h5>
                                        <i class="fas fa-user"></i>
                                        معلومات الموظف
                                    </h5>
                                    
                                    <div class="form-group">
                                        <label for="{{ form.employee.id_for_label }}" class="form-label">
                                            <i class="fas fa-user-circle"></i>
                                            {{ form.employee.label }} <span class="required-field">*</span>
                                        </label>
                                        {{ form.employee }}
                                        <i class="fas fa-chevron-down form-field-icon"></i>
                                        {% if form.employee.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.employee.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.termination_type.id_for_label }}" class="form-label">
                                            <i class="fas fa-tag"></i>
                                            {{ form.termination_type.label }} <span class="required-field">*</span>
                                        </label>
                                        {{ form.termination_type }}
                                        <i class="fas fa-chevron-down form-field-icon"></i>
                                        {% if form.termination_type.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.termination_type.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- التواريخ -->
                            <div class="col-md-6">
                                <div class="form-section">
                                    <h5>
                                        <i class="fas fa-calendar"></i>
                                        التواريخ
                                    </h5>
                                    
                                    <div class="form-group">
                                        <label for="{{ form.termination_date.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-alt"></i>
                                            {{ form.termination_date.label }} <span class="required-field">*</span>
                                        </label>
                                        {{ form.termination_date }}
                                        <i class="fas fa-calendar form-field-icon"></i>
                                        {% if form.termination_date.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.termination_date.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.last_working_day.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-check"></i>
                                            {{ form.last_working_day.label }} <span class="required-field">*</span>
                                        </label>
                                        {{ form.last_working_day }}
                                        <i class="fas fa-calendar form-field-icon"></i>
                                        {% if form.last_working_day.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.last_working_day.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.notice_period.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-day"></i>
                                            {{ form.notice_period.label }} <span class="required-field">*</span>
                                        </label>
                                        {{ form.notice_period }}
                                        <i class="fas fa-clock form-field-icon"></i>
                                        {% if form.notice_period.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.notice_period.errors.0 }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            عدد الأيام المطلوبة للإشعار قبل إنهاء الخدمة
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- السبب -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-comment"></i>
                                سبب إنهاء الخدمة
                            </h5>
                            
                            <div class="form-group">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">
                                    <i class="fas fa-edit"></i>
                                    {{ form.reason.label }} <span class="required-field">*</span>
                                </label>
                                {{ form.reason }}
                                <i class="fas fa-pen form-field-icon" style="top: 30px;"></i>
                                {% if form.reason.errors %}
                                    <div class="invalid-feedback">
                                        {{ form.reason.errors.0 }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    يرجى توضيح سبب إنهاء الخدمة بالتفصيل والوضوح
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="action-buttons">
                            <a href="{% url 'hr:termination_list' %}" class="action-btn btn-cancel">
                                <i class="fas fa-times"></i>إلغاء
                            </a>
                            <button type="submit" class="action-btn btn-submit">
                                <i class="fas fa-save"></i>
                                {% if termination %}تحديث{% else %}حفظ{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث آخر يوم عمل تلقائياً عند تغيير تاريخ إنهاء الخدمة
    $('#id_termination_date').change(function() {
        var terminationDate = new Date($(this).val());
        var noticePeriod = parseInt($('#id_notice_period').val()) || 30;
        
        var lastWorkingDay = new Date(terminationDate);
        lastWorkingDay.setDate(lastWorkingDay.getDate() - noticePeriod);
        
        $('#id_last_working_day').val(lastWorkingDay.toISOString().split('T')[0]);
        
        // تحديث مؤشر التقدم
        updateProgress(2);
    });
    
    // تحديث آخر يوم عمل عند تغيير فترة الإشعار
    $('#id_notice_period').change(function() {
        var terminationDate = $('#id_termination_date').val();
        if (terminationDate) {
            $('#id_termination_date').trigger('change');
        }
    });
    
    // تحديث مؤشر التقدم عند ملء السبب
    $('#id_reason').on('input', function() {
        if ($(this).val().length > 10) {
            updateProgress(3);
        }
    });
    
    // التحقق من صحة النموذج
    $('form').submit(function(e) {
        var isValid = true;
        
        // التحقق من الحقول المطلوبة
        $('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid').addClass('is-valid');
            }
        });
        
        if (isValid) {
            updateProgress(4);
        }
        
        if (!isValid) {
            e.preventDefault();
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        }
    });
    
    // تحديث مؤشر التقدم
    function updateProgress(step) {
        $('.progress-step').each(function(index) {
            var stepNumber = $(this).find('.step-number');
            var stepLabel = $(this).find('.step-label');
            
            if (index < step - 1) {
                stepNumber.removeClass('active').addClass('completed');
                stepLabel.removeClass('active').addClass('completed');
            } else if (index === step - 1) {
                stepNumber.removeClass('completed').addClass('active');
                stepLabel.removeClass('completed').addClass('active');
            } else {
                stepNumber.removeClass('active completed');
                stepLabel.removeClass('active completed');
            }
        });
    }
    
    // إظهار الإشعارات
    function showNotification(message, type) {
        var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.form-header').after(alertHtml);
        
        // إخفاء الإشعار تلقائياً بعد 5 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
    
    // تحسين تجربة المستخدم للنماذج
    $('.form-control').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        $(this).parent().removeClass('focused');
    });
    
    // إضافة تأثيرات بصرية للحقول
    $('.form-control').on('input', function() {
        if ($(this).val()) {
            $(this).addClass('is-valid');
        } else {
            $(this).removeClass('is-valid');
        }
    });
});
</script>
{% endblock %} 