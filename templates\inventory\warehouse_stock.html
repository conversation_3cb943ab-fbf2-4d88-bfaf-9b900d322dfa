{% extends 'base/base.html' %}
{% block title %}مخزون المخزن{% endblock %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0"><i class="fas fa-boxes me-2"></i>مخزون المخزن: {{ warehouse.name }}</h4>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <a href="{% url 'inventory:warehouse_dashboard' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للوحة التحكم
                </a>
            </div>
            
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>الصنف</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>متوسط التكلفة</th>
                        <th>إجمالي القيمة</th>
                        <th>آخر حركة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stock in stock_items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>
                            <strong>{{ stock.item.name }}</strong>
                            {% if stock.item.code %}
                                <br><small class="text-muted">كود: {{ stock.item.code }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-primary fs-6">{{ stock.quantity|floatformat:2 }}</span>
                        </td>
                        <td>{{ stock.item.unit.name }}</td>
                        <td>{{ stock.average_cost|floatformat:2 }} ج.م</td>
                        <td>{{ stock.total_value|floatformat:2 }} ج.م</td>
                        <td>
                            {% if stock.last_movement_date %}
                                {{ stock.last_movement_date|date:"Y/m/d" }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="fas fa-box-open fa-2x mb-2"></i>
                            <br>لا يوجد أصناف في هذا المخزن
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                {% if stock_items %}
                <tfoot class="table-info">
                    <tr>
                        <td colspan="2" class="text-end fw-bold">إجمالي الأصناف:</td>
                        <td class="fw-bold">{{ stock_items.count }}</td>
                        <td colspan="2" class="text-end fw-bold">إجمالي القيمة:</td>
                        <td class="fw-bold">{{ total_value|floatformat:2 }} ج.م</td>
                        <td></td>
                    </tr>
                </tfoot>
                {% endif %}
            </table>
        </div>
    </div>
</div>
{% endblock %}
