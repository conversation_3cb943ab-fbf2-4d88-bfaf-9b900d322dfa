/**
 * UI Enhancements for Manufacturing Order Form
 * تحسينات واجهة المستخدم لنموذج أمر الإنتاج
 */

/* تحسينات Bootstrap المخصصة */
.form-floating > label {
    font-weight: 600;
    color: #495057;
    padding-left: 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    opacity: 0.85;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: #667eea;
}

/* تحسين مظهر الحقول */
.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.8rem 1rem;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
    background: white;
    transform: translateY(-2px);
}

/* تحسين مظهر الأزرار */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* تحسين مظهر البطاقات */
.alert {
    border: none;
    border-radius: 15px;
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #0d47a1;
    border-left: 4px solid #2196f3;
}

.alert-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border-left: 4px solid #6c757d;
}

/* تحسين مظهر الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

/* تحسين مظهر الجدول */
.table-header div {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.75rem 0.5rem;
    margin: 0.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    margin-bottom: 0.5rem;
    padding: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.table-row:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateX(5px) translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر مؤشرات المخزون */
.stock-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.stock-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.stock-indicator:hover::before {
    left: 100%;
}

.stock-available {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.stock-low {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.stock-out {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

/* تحسين مظهر مجموعة الأزرار */
.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border-right: none;
}

.btn-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* تحسين مظهر النصوص المساعدة */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-text i {
    color: #007bff;
}

/* تحسين مظهر التقدم */
.progress-indicator {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 1rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* تحسين مظهر التنبيهات المخصصة */
.digital-alert {
    border-radius: 15px;
    border: none;
    padding: 1.25rem 1.5rem;
    margin: 1.5rem 0;
    font-weight: 500;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.digital-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

/* تحسين مظهر الحاسبة */
.cost-calculator {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.calculator-display {
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.display-row {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.display-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: rgba(255, 255, 255, 0.1);
    transition: width 0.3s ease;
}

.display-row:hover::before {
    width: 100%;
}

/* تحسين الاستجابة للهواتف */
@media (max-width: 768px) {
    .form-floating > label {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .table-row {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .stock-indicator {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }
}

/* تحسين إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسين الطباعة */
@media print {
    .btn, .form-text, .invalid-feedback {
        display: none !important;
    }
    
    .form-control, .form-select {
        border: 1px solid #000 !important;
        background: white !important;
        box-shadow: none !important;
    }
    
    .alert {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table-row {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
}

/* تحسين التركيز للوصول */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* تحسين مظهر التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: white;
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.loading-animation {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.loading-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-circle:nth-child(1) { animation-delay: -0.32s; }
.loading-circle:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingBounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.loading-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.loading-subtitle {
    color: #6c757d;
    margin-bottom: 2rem;
}

.loading-progress {
    background: #e9ecef;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.progress-bar-loading {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
}

.loading-steps {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.loading-steps .step {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.loading-steps .step.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
}

.loading-steps .step.completed {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

/* User Info Panel */
.user-info-panel {
    position: fixed;
    top: 20px;
    left: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 280px;
    transition: all 0.3s ease;
}

.user-info-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.user-avatar {
    font-size: 2.5rem;
    opacity: 0.9;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.user-session {
    font-size: 0.75rem;
    opacity: 0.7;
}

.user-actions .btn {
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.user-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Success Animation */
.success-animation i {
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Enhanced Modal Styles */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-footer {
    border-top: none;
    border-radius: 0 0 15px 15px;
}

/* Shortcuts Panel */
.shortcuts-panel {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    z-index: 1000;
    min-width: 280px;
    max-width: 350px;
}

.shortcuts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.shortcuts-header h6 {
    margin: 0;
    font-weight: 600;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.shortcut-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.shortcut-item kbd {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.shortcut-item span {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Status Bar */
.status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    backdrop-filter: blur(20px);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.status-left,
.status-center,
.status-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #495057;
}

.status-item i {
    font-size: 0.875rem;
}

.status-right .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Connection Indicator */
.connection-indicator {
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #495057;
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.connection-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: connectionPulse 2s infinite;
}

.connection-dot.online {
    background: #28a745;
}

.connection-dot.offline {
    background: #dc3545;
}

.connection-dot.connecting {
    background: #ffc107;
}

@keyframes connectionPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.connection-text {
    font-weight: 500;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .shortcuts-panel {
        position: fixed;
        bottom: 60px;
        left: 10px;
        right: 10px;
        min-width: auto;
        max-width: none;
    }

    .shortcuts-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 0.5rem;
    }

    .shortcut-item {
        padding: 0.5rem;
    }

    .shortcut-item kbd {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .shortcut-item span {
        font-size: 0.7rem;
    }

    .status-bar {
        padding: 0.375rem 0.5rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .status-left,
    .status-center,
    .status-right {
        gap: 0.5rem;
    }

    .status-item {
        font-size: 0.75rem;
    }

    .connection-indicator {
        position: relative;
        top: auto;
        right: auto;
        transform: none;
        margin: 0.5rem;
        align-self: flex-start;
    }

    .user-info-panel {
        position: relative;
        top: auto;
        left: auto;
        margin: 1rem;
        min-width: auto;
    }
}

/* Print Enhancements */
@media print {
    .shortcuts-panel,
    .status-bar,
    .connection-indicator,
    .user-info-panel {
        display: none !important;
    }

    body {
        padding-bottom: 0 !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .shortcuts-panel {
        border: 2px solid #000;
        background: #000;
        color: #fff;
    }

    .status-bar {
        border-top: 2px solid #000;
        background: #fff;
        color: #000;
    }

    .connection-indicator {
        border: 1px solid #000;
        background: #fff;
        color: #000;
    }
}
