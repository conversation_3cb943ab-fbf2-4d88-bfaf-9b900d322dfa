from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'accounts'

urlpatterns = [
    # تسجيل الدخول والخروج
    path('login/', views.custom_login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # الملف الشخصي
    path('profile/', views.profile, name='profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),
    path('profile/change-password/', views.change_password, name='change_password'),

    # إدارة المستخدمين
    path('users/', views.users_list, name='users_list'),
    path('users/create/', views.user_create, name='user_create'),
    path('users/<int:user_id>/', views.user_detail, name='user_detail'),
    path('users/<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('users/<int:user_id>/delete/', views.user_delete, name='user_delete'),

    # إدارة الصلاحيات
    path('users/<int:user_id>/permissions/', views.user_permissions, name='user_permissions'),
    path('users/<int:user_id>/reset-password/', views.reset_user_password, name='reset_user_password'),
    path('users/<int:user_id>/activities/', views.user_activities, name='user_activities'),
    path('permissions/bulk/', views.bulk_permissions, name='bulk_permissions'),
]
