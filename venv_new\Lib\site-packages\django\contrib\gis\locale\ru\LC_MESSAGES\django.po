# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2012,2014-2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-04 18:45+0000\n"
"Last-Translator: crazyzubr <<EMAIL>>, 2020\n"
"Language-Team: Russian (http://app.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "GIS"
msgstr "ГИС"

msgid "The base GIS field."
msgstr "Базовое ГИС-поле."

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr ""
"Базовое геометрическое поле. Соответствует типу OpenGIS Specification "
"Geometry."

msgid "Point"
msgstr "Точка"

msgid "Line string"
msgstr "Ломаная"

msgid "Polygon"
msgstr "Многоугольник"

msgid "Multi-point"
msgstr "Набор точек"

msgid "Multi-line string"
msgstr "Набор ломаных"

msgid "Multi polygon"
msgstr "Набор многоугольников"

msgid "Geometry collection"
msgstr "Набор геометрических объектов"

msgid "Extent Aggregate Field"
msgstr "Поле, агрегирующее площадь или объём"

msgid "Raster Field"
msgstr "Растровое поле"

msgid "No geometry value provided."
msgstr "Не указано значение геометрии."

msgid "Invalid geometry value."
msgstr "Неверное значение геометрии."

msgid "Invalid geometry type."
msgstr "Неверный тип геометрического объекта."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"Произошла ошибка во время преобразования геометрического объекта в SRID."

msgid "Delete all Features"
msgstr "Удалить все объекты"

msgid "Debugging window (serialized value)"
msgstr "Отладочное окно (сериализованные значения)"

msgid "No feeds are registered."
msgstr "Нет зарегистрированных фидов."

#, python-format
msgid "Slug %r isn’t registered."
msgstr "Слаг %r не зарегистрирован."
