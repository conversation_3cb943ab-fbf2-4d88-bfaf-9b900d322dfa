#!/usr/bin/env python3
"""
تحديث hash القائمة الجانبية الجديدة
"""

import hashlib
from datetime import datetime

def update_sidebar_hash():
    """تحديث hash القائمة الجانبية"""
    try:
        # حساب hash الحالي
        with open('templates/base/base.html', 'rb') as f:
            current_hash = hashlib.md5(f.read()).hexdigest()
        
        print(f'Hash الجديد: {current_hash}')
        
        # حفظ hash الجديد
        with open('sidebar_hash.txt', 'w', encoding='utf-8') as f:
            f.write(f'{current_hash}\n')
            f.write(f'Protection Date: {datetime.now()}\n')
            f.write('New sidebar with fixed header - Updated\n')
        
        print('✅ تم تحديث hash القائمة الجانبية الجديدة')
        return current_hash
        
    except Exception as e:
        print(f'❌ خطأ في تحديث hash: {e}')
        return None

if __name__ == "__main__":
    update_sidebar_hash()
