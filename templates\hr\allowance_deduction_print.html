{% extends 'base/base.html' %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow border-primary">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">طباعة {{ object.get_type_display }} الموظف</h4>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th>الموظف</th>
                            <td>{{ object.employee.full_name }}</td>
                        </tr>
                        <tr>
                            <th>النوع</th>
                            <td>{{ object.get_type_display }}</td>
                        </tr>
                        <tr>
                            <th>نوع الإضافة/الخصم</th>
                            <td>{% if object.type == 'allowance' and object.allowance_type %}{{ object.allowance_type.name }}{% elif object.type == 'deduction' and object.deduction_type %}{{ object.deduction_type.name }}{% else %}-{% endif %}</td>
                        </tr>
                        <tr>
                            <th>المبلغ</th>
                            <td>{{ object.amount }} ج.م</td>
                        </tr>
                        <tr>
                            <th>التاريخ</th>
                            <td>{{ object.date }}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ object.notes|default:'-' }}</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>{% if object.is_active %}نشط{% else %}غير نشط{% endif %}</td>
                        </tr>
                    </table>
                </div>
                <div class="card-footer text-center">
                    <button class="btn btn-primary" onclick="window.print()"><i class="fas fa-print me-2"></i>طباعة</button>
                    <a href="{% url 'hr:allowance_deduction_list' %}" class="btn btn-secondary ms-2">رجوع</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 