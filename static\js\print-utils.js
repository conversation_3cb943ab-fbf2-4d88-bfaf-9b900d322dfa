/**
 * مكتبة الطباعة المحسنة للتطبيق
 * Enhanced Print Utilities Library for the Application
 */

// دالة الطباعة العامة البسيطة والفعالة
function printPage(options = {}) {
    const config = {
        title: options.title || 'طباعة الصفحة',
        hideElements: options.hideElements || ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.breadcrumb'],
        ...options
    };

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');

    // الحصول على محتوى الصفحة الحالية
    const currentContent = document.documentElement.outerHTML;

    // CSS محسن للطباعة
    const printCSS = `
        <style>
            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Arial', sans-serif;
                    font-size: 12pt;
                    line-height: 1.4;
                    color: #000;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                    text-align: right;
                    background: white;
                }

                /* إخفاء العناصر غير المرغوب فيها */
                .btn, .form-control, .form-select, .dropdown, .navbar, .sidebar, .breadcrumb, .pagination, .modal, .alert-dismissible {
                    display: none !important;
                }

                .container, .container-fluid {
                    max-width: 100% !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                .card {
                    border: 1px solid #333 !important;
                    margin-bottom: 20px !important;
                    page-break-inside: avoid;
                    background: white !important;
                }

                .card-header {
                    background: #f0f0f0 !important;
                    color: #000 !important;
                    border-bottom: 1px solid #333 !important;
                    padding: 10px 15px !important;
                    font-weight: bold !important;
                }

                .table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 20px !important;
                    font-size: 10pt !important;
                }

                .table th {
                    background: #f0f0f0 !important;
                    color: #000 !important;
                    border: 1px solid #333 !important;
                    padding: 8px !important;
                    text-align: center !important;
                    font-weight: bold !important;
                }

                .table td {
                    border: 1px solid #333 !important;
                    padding: 8px !important;
                    text-align: center !important;
                }

                .badge {
                    background: #f0f0f0 !important;
                    color: #000 !important;
                    border: 1px solid #333 !important;
                    padding: 2px 6px !important;
                    border-radius: 3px !important;
                    font-size: 9pt !important;
                }

                h1, h2, h3, h4, h5, h6 {
                    color: #000 !important;
                    font-weight: bold !important;
                    margin-bottom: 10px !important;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border: 2px solid #000;
                    padding: 20px;
                    background: #f9f9f9;
                }

                .print-title {
                    font-size: 18pt;
                    font-weight: bold;
                    margin-bottom: 10px;
                }

                .print-date {
                    font-size: 12pt;
                    color: #666;
                }

                @page {
                    margin: 2cm;
                    size: A4;
                }

                .page-break {
                    page-break-before: always;
                }
            }
        </style>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>${config.title}</title>
            ${printCSS}
        </head>
        <body>
            <div class="print-header">
                <div class="print-title">نظام إدارة الموارد البشرية</div>
                <div class="print-title">${config.title}</div>
                <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            ${currentContent}
        </body>
        </html>
    `);

    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    setTimeout(() => {
        printWindow.print();
        setTimeout(() => {
            printWindow.close();
        }, 1000);
    }, 500);
}

// دالة طباعة التقارير المحسنة
function printReport(reportTitle, additionalCSS = '') {
    printPage({
        title: reportTitle,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.alert', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 11pt;
                    line-height: 1.5;
                    color: #2c3e50;
                    background: white;
                }

                .no-print { display: none !important; }
                .print-only { display: block !important; }

                .card {
                    border: 2px solid #34495e !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                    margin-bottom: 25px !important;
                    border-radius: 8px !important;
                    background: white !important;
                }

                .card-header {
                    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
                    color: white !important;
                    border-bottom: 2px solid #c0392b !important;
                    padding: 12px 20px !important;
                    font-weight: 600 !important;
                    font-size: 13pt !important;
                    text-align: center !important;
                }

                .table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 25px !important;
                    font-size: 10pt !important;
                }

                .table th {
                    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
                    color: white !important;
                    border: 1px solid #2c3e50 !important;
                    padding: 12px 8px !important;
                    text-align: center !important;
                    font-weight: 600 !important;
                }

                .table td {
                    border: 1px solid #bdc3c7 !important;
                    padding: 10px 8px !important;
                    text-align: center !important;
                }

                .table tbody tr:nth-child(even) {
                    background-color: #f8f9fa !important;
                }

                .page-break { page-break-before: always; }

                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50 !important;
                    text-align: center !important;
                    font-weight: 600 !important;
                    margin-bottom: 20px !important;
                }

                .badge {
                    border: 1px solid #2c3e50 !important;
                    padding: 4px 8px !important;
                    border-radius: 4px !important;
                    font-weight: 500 !important;
                }

                .progress {
                    border: 2px solid #34495e !important;
                    border-radius: 4px !important;
                    background: #ecf0f1 !important;
                }

                .chart-placeholder {
                    border: 3px dashed #34495e !important;
                    border-radius: 8px !important;
                    background: #f8f9fa !important;
                    padding: 20px !important;
                    text-align: center !important;
                }

                .stats-card {
                    border: 2px solid #3498db !important;
                    border-radius: 8px !important;
                    margin-bottom: 15px !important;
                    text-align: center !important;
                    padding: 15px !important;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                }

                .stats-number {
                    font-size: 18pt !important;
                    font-weight: 700 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 5px !important;
                }

                @page {
                    margin: 1.5cm;
                    size: A4;
                }

                ${additionalCSS}
            }
        `
    });
}

// دالة طباعة الفواتير المحسنة
function printInvoice(invoiceTitle) {
    printPage({
        title: invoiceTitle,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 11pt;
                    line-height: 1.5;
                    color: #2c3e50;
                    background: white;
                }

                .no-print { display: none !important; }
                .print-only { display: block !important; }

                .invoice-header {
                    text-align: center;
                    margin-bottom: 40px;
                    border: 3px solid #27ae60;
                    border-radius: 10px;
                    padding: 25px;
                    background: linear-gradient(135deg, #d5f4e6, #a9dfbf);
                }

                .invoice-details {
                    margin-bottom: 30px;
                    padding: 20px;
                    border: 2px solid #34495e;
                    border-radius: 8px;
                    background: #f8f9fa;
                }

                .table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 25px !important;
                    font-size: 10pt !important;
                }

                .table th {
                    background: linear-gradient(135deg, #27ae60, #229954) !important;
                    color: white !important;
                    border: 2px solid #229954 !important;
                    padding: 12px 10px !important;
                    text-align: center !important;
                    font-weight: 600 !important;
                    font-size: 11pt !important;
                }

                .table td {
                    border: 1px solid #27ae60 !important;
                    padding: 10px !important;
                    text-align: center !important;
                    background: white !important;
                }

                .table tbody tr:nth-child(even) {
                    background-color: #f8f9fa !important;
                }

                .table tfoot {
                    background: linear-gradient(135deg, #f39c12, #e67e22) !important;
                    color: white !important;
                    font-weight: 600 !important;
                }

                .invoice-total {
                    font-size: 16pt !important;
                    font-weight: 700 !important;
                    color: #27ae60 !important;
                    text-align: center !important;
                    padding: 15px !important;
                    border: 3px solid #27ae60 !important;
                    border-radius: 8px !important;
                    background: #d5f4e6 !important;
                    margin-top: 20px !important;
                }

                .company-logo {
                    max-width: 120px !important;
                    border: 2px solid #34495e !important;
                    border-radius: 8px !important;
                    padding: 5px !important;
                }

                h1, h2, h3 {
                    color: #2c3e50 !important;
                    font-weight: 600 !important;
                    text-align: center !important;
                }

                .invoice-number {
                    font-size: 14pt !important;
                    font-weight: 600 !important;
                    color: #e74c3c !important;
                    background: #fadbd8 !important;
                    padding: 10px !important;
                    border-radius: 5px !important;
                    border: 2px solid #e74c3c !important;
                }

                @page {
                    margin: 1.5cm;
                    size: A4;
                }
            }
        `
    });
}

// دالة طباعة قوائم البيانات المحسنة
function printDataList(listTitle) {
    printPage({
        title: listTitle,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.pagination', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 10pt;
                    line-height: 1.4;
                    color: #2c3e50;
                    background: white;
                }

                .no-print { display: none !important; }
                .print-only { display: block !important; }

                .table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 25px !important;
                    font-size: 9pt !important;
                }

                .table th {
                    background: linear-gradient(135deg, #8e44ad, #7d3c98) !important;
                    color: white !important;
                    border: 1px solid #7d3c98 !important;
                    padding: 10px 6px !important;
                    text-align: center !important;
                    font-weight: 600 !important;
                    font-size: 10pt !important;
                }

                .table td {
                    border: 1px solid #bdc3c7 !important;
                    padding: 8px 6px !important;
                    text-align: center !important;
                    background: white !important;
                }

                .table tbody tr:nth-child(even) {
                    background-color: #f8f9fa !important;
                }

                .card {
                    border: 2px solid #8e44ad !important;
                    margin-bottom: 25px !important;
                    border-radius: 8px !important;
                    background: white !important;
                    box-shadow: none !important;
                }

                .card-header {
                    background: linear-gradient(135deg, #8e44ad, #7d3c98) !important;
                    color: white !important;
                    padding: 12px 20px !important;
                    font-weight: 600 !important;
                    text-align: center !important;
                }

                .badge {
                    border: 1px solid #2c3e50 !important;
                    padding: 3px 6px !important;
                    border-radius: 3px !important;
                    font-weight: 500 !important;
                    font-size: 8pt !important;
                }

                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50 !important;
                    font-weight: 600 !important;
                    text-align: center !important;
                }

                @page {
                    margin: 1.2cm;
                    size: A4;
                }
            }
        `
    });
}

// دالة طباعة لوحة التحكم المحسنة
function printDashboard(dashboardTitle) {
    printPage({
        title: dashboardTitle,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 11pt;
                    line-height: 1.5;
                    color: #2c3e50;
                    background: white;
                }

                .no-print { display: none !important; }
                .print-only { display: block !important; }

                .card {
                    border: 2px solid #f39c12 !important;
                    margin-bottom: 25px !important;
                    border-radius: 10px !important;
                    background: white !important;
                    box-shadow: 0 3px 6px rgba(0,0,0,0.1) !important;
                }

                .card-header {
                    background: linear-gradient(135deg, #f39c12, #e67e22) !important;
                    color: white !important;
                    padding: 15px 20px !important;
                    font-weight: 600 !important;
                    text-align: center !important;
                    border-radius: 8px 8px 0 0 !important;
                }

                .stats-card {
                    border: 3px solid #3498db !important;
                    border-radius: 10px !important;
                    background: linear-gradient(135deg, #ebf3fd, #d6eaf8) !important;
                    text-align: center !important;
                    padding: 20px !important;
                    margin-bottom: 20px !important;
                }

                .stats-number {
                    font-size: 24pt !important;
                    font-weight: 700 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 10px !important;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
                }

                .stats-icon {
                    font-size: 20pt !important;
                    color: #3498db !important;
                    margin-bottom: 10px !important;
                }

                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50 !important;
                    text-align: center !important;
                    font-weight: 600 !important;
                    margin-bottom: 20px !important;
                }

                .row {
                    margin-bottom: 25px !important;
                    page-break-inside: avoid !important;
                }

                .col, .col-md-3, .col-md-4, .col-md-6, .col-lg-3, .col-lg-4 {
                    padding: 0 10px !important;
                    margin-bottom: 15px !important;
                }

                .chart-placeholder {
                    border: 3px dashed #34495e !important;
                    height: 200px !important;
                    border-radius: 8px !important;
                    background: #f8f9fa !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    color: #7f8c8d !important;
                    font-weight: 600 !important;
                }

                @page {
                    margin: 1.5cm;
                    size: A4;
                }
            }
        `
    });
}

// دالة إضافة أزرار الطباعة تلقائياً
function addPrintButtons() {
    // البحث عن العناصر التي تحتاج أزرار طباعة
    const containers = document.querySelectorAll('.card-header, .page-header, .section-header');
    
    containers.forEach(container => {
        // التحقق من عدم وجود زر طباعة مسبقاً
        if (!container.querySelector('.print-btn')) {
            const printBtn = document.createElement('button');
            printBtn.className = 'btn btn-success btn-sm print-btn ms-2';
            printBtn.innerHTML = '<i class="fas fa-print me-1"></i> طباعة';
            printBtn.onclick = () => {
                const pageTitle = document.querySelector('h1, h2, h3')?.textContent || 'طباعة الصفحة';
                printReport(pageTitle);
            };
            
            // إضافة الزر في المكان المناسب
            if (container.querySelector('.btn-group, .d-flex')) {
                container.querySelector('.btn-group, .d-flex').appendChild(printBtn);
            } else {
                container.appendChild(printBtn);
            }
        }
    });
}

// تشغيل إضافة أزرار الطباعة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أزرار الطباعة تلقائياً
    addPrintButtons();
    
    // إضافة اختصار لوحة المفاتيح للطباعة (Ctrl+P)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            const pageTitle = document.querySelector('h1, h2, h3')?.textContent || 'طباعة الصفحة';
            printReport(pageTitle);
        }
    });
});

// دالة طباعة تفاصيل الموظف
function printEmployeeDetails(employeeName) {
    printPage({
        title: `تفاصيل الموظف - ${employeeName}`,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 11pt;
                    line-height: 1.5;
                    color: #2c3e50;
                    background: white;
                }

                .card {
                    border: 2px solid #17a2b8 !important;
                    margin-bottom: 25px !important;
                    border-radius: 8px !important;
                    background: white !important;
                    page-break-inside: avoid !important;
                }

                .card-header {
                    background: linear-gradient(135deg, #17a2b8, #138496) !important;
                    color: white !important;
                    padding: 12px 20px !important;
                    font-weight: 600 !important;
                    border-radius: 6px 6px 0 0 !important;
                }

                .form-label {
                    font-weight: 600 !important;
                    color: #34495e !important;
                    margin-bottom: 5px !important;
                }

                .fw-bold {
                    font-weight: 600 !important;
                    color: #2c3e50 !important;
                }

                .text-primary { color: #17a2b8 !important; }
                .text-success { color: #27ae60 !important; }
                .text-danger { color: #e74c3c !important; }
                .text-muted { color: #7f8c8d !important; }

                .badge {
                    padding: 4px 8px !important;
                    border-radius: 4px !important;
                    font-weight: 500 !important;
                    font-size: 9pt !important;
                }

                .badge.bg-success { background-color: #27ae60 !important; color: white !important; }
                .badge.bg-warning { background-color: #f39c12 !important; color: #2c3e50 !important; }
                .badge.bg-danger { background-color: #e74c3c !important; color: white !important; }

                @page {
                    margin: 1.5cm;
                    size: A4;
                }
            }
        `
    });
}

// دالة طباعة تقرير مخصص مع إعدادات متقدمة
function printCustomReport(title, options = {}) {
    const defaultOptions = {
        orientation: 'portrait', // portrait أو landscape
        paperSize: 'A4',
        showHeader: true,
        showFooter: true,
        companyName: 'شركة [اسم الشركة]',
        reportDate: new Date().toLocaleDateString('ar-EG')
    };

    const config = { ...defaultOptions, ...options };

    printPage({
        title: title,
        hideElements: ['.btn', '.form-control', '.form-select', '.dropdown', '.navbar', '.sidebar', '.breadcrumb'],
        customCSS: `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Cairo', 'Arial', sans-serif !important;
                    direction: rtl;
                    font-size: 11pt;
                    line-height: 1.5;
                    color: #2c3e50;
                    background: white;
                }

                .custom-report-header {
                    text-align: center;
                    margin-bottom: 40px;
                    border: 3px solid #2c3e50;
                    border-radius: 15px;
                    padding: 30px;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    page-break-inside: avoid;
                }

                .report-title {
                    font-size: 22pt !important;
                    font-weight: 700 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 20px !important;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1) !important;
                }

                .report-subtitle {
                    font-size: 16pt !important;
                    color: #34495e !important;
                    margin-bottom: 15px !important;
                    font-weight: 500 !important;
                }

                .report-date {
                    font-size: 12pt !important;
                    color: #7f8c8d !important;
                    font-weight: 400 !important;
                }

                @page {
                    margin: ${config.orientation === 'landscape' ? '1cm 1.5cm' : '1.5cm'};
                    size: ${config.paperSize} ${config.orientation};
                }

                .page-number::after {
                    content: "صفحة " counter(page) " من " counter(pages);
                }
            }
        `
    });
}

// تصدير الدوال للاستخدام العام
window.printPage = printPage;
window.printReport = printReport;
window.printInvoice = printInvoice;
window.printDataList = printDataList;
window.printDashboard = printDashboard;
window.printEmployeeDetails = printEmployeeDetails;
window.printCustomReport = printCustomReport;
window.addPrintButtons = addPrintButtons;
