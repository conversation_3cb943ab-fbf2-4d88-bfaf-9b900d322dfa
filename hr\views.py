from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .models import Department, Position, SalarySystem, Employee, Qualification, Shift
from .forms import DepartmentForm, PositionForm, SalarySystemForm, EmployeeForm, EmployeeSalaryForm, AttendanceForm, OvertimeForm, ContractForm, EmploymentForm, SalaryAdditionForm, AllowanceDeductionForm, AllowanceForm
from .models import Attendance
from django.utils import timezone
from .models import AllowanceDeduction, Overtime
from datetime import datetime, timedelta
import calendar
import locale
from .models import Contract
from definitions.models import Person
from django.db import transaction
import re
from django.db import IntegrityError
import traceback
from .models import Transfer
from .models import Termination
from .forms import TerminationForm
from .models import SalaryAddition
from .models import AllowanceType, DeductionType
from .forms import AllowanceTypeForm, DeductionTypeForm


@login_required
def dashboard(request):
    """لوحة تحكم شؤون العاملين"""
    # إحصائيات عامة
    total_employees = Employee.objects.filter(is_active=True).count()
    active_employees = Employee.objects.filter(status='ACTIVE', is_active=True).count()
    total_departments = Department.objects.filter(is_active=True).count()
    total_positions = Position.objects.filter(is_active=True).count()
    total_salary_systems = SalarySystem.objects.filter(is_active=True).count()

    # الموظفين الجدد (آخر 30 يوم)
    from datetime import date, timedelta
    thirty_days_ago = date.today() - timedelta(days=30)
    new_employees = Employee.objects.filter(
        hire_date__gte=thirty_days_ago,
        is_active=True
    ).count()

    # توزيع الموظفين حسب الأقسام
    departments_stats = Department.objects.filter(is_active=True).annotate(
        employee_count=Count('employees', filter=Q(employees__is_active=True))
    ).order_by('-employee_count')[:5]

    context = {
        'total_employees': total_employees,
        'active_employees': active_employees,
        'total_departments': total_departments,
        'total_positions': total_positions,
        'total_salary_systems': total_salary_systems,
        'new_employees': new_employees,
        'departments_stats': departments_stats,
    }
    return render(request, 'hr/dashboard.html', context)


# أنظمة صرف المرتبات
@login_required
def salary_system_list(request):
    """قائمة أنظمة صرف المرتبات"""
    search_query = request.GET.get('search', '')
    system_type_filter = request.GET.get('system_type', '')

    salary_systems = SalarySystem.objects.filter(is_active=True)

    if search_query:
        salary_systems = salary_systems.filter(
            Q(code__icontains=search_query) |
            Q(name__icontains=search_query)
        )

    if system_type_filter:
        salary_systems = salary_systems.filter(system_type=system_type_filter)

    salary_systems = salary_systems.order_by('name')

    # Pagination
    paginator = Paginator(salary_systems, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'system_type_filter': system_type_filter,
        'system_types': SalarySystem.SYSTEM_TYPES,
    }
    return render(request, 'hr/salary_system_list.html', context)


@login_required
def salary_system_create(request):
    """إضافة نظام صرف مرتب جديد"""
    if request.method == 'POST':
        form = SalarySystemForm(request.POST)
        if form.is_valid():
            salary_system = form.save(commit=False)
            salary_system.created_by = request.user
            salary_system.save()
            messages.success(request, 'تم إضافة نظام صرف المرتب بنجاح')
            return redirect('hr:salary_system_list')
    else:
        form = SalarySystemForm()

    context = {
        'form': form,
        'title': 'إضافة نظام صرف مرتب جديد',
        'action': 'إضافة'
    }
    return render(request, 'hr/salary_system_form.html', context)


@login_required
def salary_system_detail(request, pk):
    """تفاصيل نظام صرف المرتب"""
    salary_system = get_object_or_404(SalarySystem, pk=pk)
    employees = salary_system.employees.filter(is_active=True)

    context = {
        'salary_system': salary_system,
        'employees': employees,
    }
    return render(request, 'hr/salary_system_detail.html', context)


@login_required
def salary_system_edit(request, pk):
    """تعديل نظام صرف المرتب"""
    salary_system = get_object_or_404(SalarySystem, pk=pk)

    if request.method == 'POST':
        form = SalarySystemForm(request.POST, instance=salary_system)
        if form.is_valid():
            salary_system = form.save(commit=False)
            salary_system.updated_by = request.user
            salary_system.save()
            messages.success(request, 'تم تحديث نظام صرف المرتب بنجاح')
            return redirect('hr:salary_system_detail', pk=salary_system.pk)
    else:
        form = SalarySystemForm(instance=salary_system)

    context = {
        'form': form,
        'salary_system': salary_system,
        'title': f'تعديل نظام صرف المرتب: {salary_system.name}',
        'action': 'تحديث'
    }
    return render(request, 'hr/salary_system_form.html', context)


@login_required
@require_http_methods(["POST"])
def salary_system_delete(request, pk):
    """حذف نظام صرف المرتب"""
    salary_system = get_object_or_404(SalarySystem, pk=pk)

    # التحقق من عدم وجود موظفين مرتبطين
    if salary_system.employees.exists():
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف نظام صرف المرتب لوجود موظفين مرتبطين به'
        })

    salary_system.is_active = False
    salary_system.updated_by = request.user
    salary_system.save()

    return JsonResponse({
        'success': True,
        'message': 'تم حذف نظام صرف المرتب بنجاح'
    })


# الأقسام
@login_required
def department_list(request):
    """قائمة الأقسام"""
    search_query = request.GET.get('search', '')

    departments = Department.objects.filter(is_active=True)

    if search_query:
        departments = departments.filter(
            Q(code__icontains=search_query) |
            Q(name__icontains=search_query) |
            Q(name_english__icontains=search_query)
        )

    departments = departments.order_by('name')

    # Pagination
    paginator = Paginator(departments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'hr/department_list.html', context)


@login_required
def department_create(request):
    """إضافة قسم جديد"""
    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            department = form.save(commit=False)
            department.created_by = request.user
            department.save()
            messages.success(request, 'تم إضافة القسم بنجاح')
            return redirect('hr:department_list')
    else:
        form = DepartmentForm()

    context = {
        'form': form,
        'title': 'إضافة قسم جديد',
        'action': 'إضافة'
    }
    return render(request, 'hr/department_form.html', context)


@login_required
def department_detail(request, pk):
    """تفاصيل القسم"""
    department = get_object_or_404(Department, pk=pk)
    employees = department.employees.filter(is_active=True)
    positions = department.positions.filter(is_active=True)
    sub_departments = department.sub_departments.filter(is_active=True)

    context = {
        'department': department,
        'employees': employees,
        'positions': positions,
        'sub_departments': sub_departments,
    }
    return render(request, 'hr/department_detail.html', context)


@login_required
def department_edit(request, pk):
    """تعديل القسم"""
    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            department = form.save(commit=False)
            department.updated_by = request.user
            department.save()
            messages.success(request, 'تم تحديث القسم بنجاح')
            return redirect('hr:department_detail', pk=department.pk)
    else:
        form = DepartmentForm(instance=department)

    context = {
        'form': form,
        'department': department,
        'title': f'تعديل القسم: {department.name}',
        'action': 'تحديث'
    }
    return render(request, 'hr/department_form.html', context)


@login_required
@require_http_methods(["POST"])
def department_delete(request, pk):
    """حذف القسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من عدم وجود موظفين أو مناصب مرتبطة
    if department.employees.exists() or department.positions.exists():
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف القسم لوجود موظفين أو مناصب مرتبطة به'
        })

    department.is_active = False
    department.updated_by = request.user
    department.save()

    return JsonResponse({
        'success': True,
        'message': 'تم حذف القسم بنجاح'
    })


# المناصب
@login_required
def position_list(request):
    """قائمة المناصب"""
    search_query = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')

    positions = Position.objects.filter(is_active=True).select_related('department')

    if search_query:
        positions = positions.filter(
            Q(code__icontains=search_query) |
            Q(name__icontains=search_query) |
            Q(name_english__icontains=search_query)
        )

    if department_filter:
        positions = positions.filter(department_id=department_filter)

    positions = positions.order_by('name')

    # Pagination
    paginator = Paginator(positions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # للفلترة
    departments = Department.objects.filter(is_active=True).order_by('name')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'department_filter': department_filter,
        'departments': departments,
    }
    return render(request, 'hr/position_list.html', context)


@login_required
def position_create(request):
    """إضافة منصب جديد"""
    if request.method == 'POST':
        form = PositionForm(request.POST)
        if form.is_valid():
            position = form.save(commit=False)
            position.created_by = request.user
            position.save()
            messages.success(request, 'تم إضافة المنصب بنجاح')
            return redirect('hr:position_list')
    else:
        form = PositionForm()

    context = {
        'form': form,
        'title': 'إضافة منصب جديد',
        'action': 'إضافة'
    }
    return render(request, 'hr/position_form.html', context)


@login_required
def position_detail(request, pk):
    """تفاصيل المنصب"""
    from .models import Position, Employee
    
    position = get_object_or_404(Position, pk=pk)
    employees = position.employees.filter(is_active=True).select_related(
        'person', 'department', 'position', 'salary_system'
    )

    context = {
        'position': position,
        'employees': employees,
    }
    return render(request, 'hr/position_detail.html', context)


@login_required
def position_edit(request, pk):
    """تعديل المنصب"""
    position = get_object_or_404(Position, pk=pk)

    if request.method == 'POST':
        form = PositionForm(request.POST, instance=position)
        if form.is_valid():
            position = form.save(commit=False)
            position.updated_by = request.user
            position.save()
            messages.success(request, 'تم تحديث المنصب بنجاح')
            return redirect('hr:position_detail', pk=position.pk)
    else:
        form = PositionForm(instance=position)

    context = {
        'form': form,
        'position': position,
        'title': f'تعديل المنصب: {position.name}',
        'action': 'تحديث'
    }
    return render(request, 'hr/position_form.html', context)


@login_required
@require_http_methods(["POST"])
def position_delete(request, pk):
    """حذف المنصب"""
    position = get_object_or_404(Position, pk=pk)

    # التحقق من عدم وجود موظفين مرتبطين
    if position.employees.exists():
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف المنصب لوجود موظفين مرتبطين به'
        })

    position.is_active = False
    position.updated_by = request.user
    position.save()

    return JsonResponse({
        'success': True,
        'message': 'تم حذف المنصب بنجاح'
    })


# الموظفين
@login_required
def employee_list(request):
    """قائمة العاملين"""
    from .models import Employee
    from django.core.paginator import Paginator

    # الحصول على جميع الموظفين النشطين
    employees = Employee.objects.filter(is_active=True).select_related(
        'person', 'department', 'position', 'salary_system'
    ).order_by('employee_number')

    # Pagination
    paginator = Paginator(employees, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    from .models import Department
    departments = Department.objects.filter(is_active=True).order_by('name')

    total_employees = Employee.objects.count()
    active_employees = Employee.objects.filter(is_active=True).count()
    inactive_employees = Employee.objects.filter(is_active=False).count()
    terminated_employees = Employee.objects.filter(status='TERMINATED').count()

    context = {
        'title': 'العاملين',
        'page_obj': page_obj,
        'search_query': '',
        'department_filter': '',
        'status_filter': '',
        'departments': departments,
        'status_choices': Employee.STATUS_CHOICES,
        'total_employees': total_employees,
        'active_employees': active_employees,
        'inactive_employees': inactive_employees,
        'terminated_employees': terminated_employees,
    }
    return render(request, 'hr/employee_list.html', context)


@login_required
def employee_create(request):
    """إضافة موظف جديد"""
    from .forms import EmployeeForm
    from definitions.models import Person
    from django.db import transaction
    import re
    
    if request.method == 'POST':
        form = EmployeeForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # إنشاء أو تحديث الشخص
                    name = form.cleaned_data['name']
                    national_id = form.cleaned_data['national_id']
                    birth_date = form.cleaned_data['birth_date']
                    phone = form.cleaned_data['phone']
                    mobile = form.cleaned_data['mobile']
                    email = form.cleaned_data['email']
                    address = form.cleaned_data['address']
                    city = form.cleaned_data['city']
                    
                    # البحث عن شخص موجود بنفس الرقم القومي
                    person = None
                    if national_id:
                        person = Person.objects.filter(national_id=national_id).first()
                    
                    if not person:
                        # إنشاء شخص جديد مع كود فريد
                        import time
                        timestamp = int(time.time())
                        new_code = f'EMP{timestamp}'
                        
                        # التأكد من عدم وجود كود مكرر
                        counter = 1
                        while Person.objects.filter(code=new_code).exists():
                            new_code = f'EMP{timestamp}_{counter}'
                            counter += 1
                        
                        person = Person.objects.create(
                            code=new_code,
                            name=name,
                            person_type='EMPLOYEE',
                            entity_type='INDIVIDUAL',
                            national_id=national_id,
                            phone=phone,
                            mobile=mobile,
                            email=email,
                            address=address,
                            city=city,
                            is_active_employee=True
                        )
                    else:
                        # تحديث بيانات الشخص الموجود
                        person.name = name
                        person.phone = phone
                        person.mobile = mobile
                        person.email = email
                        person.address = address
                        person.city = city
                        person.is_active_employee = True
                        person.save()
                    
                    # حفظ تاريخ الميلاد في ملاحظات الشخص إذا كان موجوداً
                    if birth_date:
                        if person.notes:
                            person.notes += f"\nتاريخ الميلاد: {birth_date}"
                        else:
                            person.notes = f"تاريخ الميلاد: {birth_date}"
                        person.save()
                    
                    # توليد رقم وظيفي تلقائياً إذا لم يتم إدخاله
                    employee_number = form.cleaned_data['employee_number']
                    if not employee_number:
                        import time
                        timestamp = int(time.time())
                        employee_number = f'EMP{timestamp}'
                        
                        # التأكد من عدم وجود رقم وظيفي مكرر
                        counter = 1
                        while Employee.objects.filter(employee_number=employee_number).exists():
                            employee_number = f'EMP{timestamp}_{counter}'
                            counter += 1
                    
                    # إنشاء الموظف
                    employee = Employee.objects.create(
                        person=person,
                        employee_number=employee_number,
                        department=form.cleaned_data['department'],
                        position=form.cleaned_data['position'],
                        salary_system=form.cleaned_data['salary_system'],
                        hire_date=form.cleaned_data['hire_date'],
                        contract_start_date=form.cleaned_data.get('contract_start_date'),
                        contract_end_date=form.cleaned_data.get('contract_end_date'),
                        current_salary=form.cleaned_data['current_salary'],
                        status=form.cleaned_data['status'],
                        is_active=True,  # تعيين الموظف كنشط
                        created_by=request.user
                    )
                    
                    # إنشاء سجل مرتب أساسي
                    from .models import EmployeeSalary
                    from decimal import Decimal
                    
                    current_salary = employee.current_salary
                    if current_salary is None or current_salary <= 0:
                        current_salary = Decimal('0')
                    
                    EmployeeSalary.objects.create(
                        employee=employee,
                        basic_salary=current_salary,
                        housing_allowance=Decimal('0'),
                        transportation_allowance=Decimal('0'),
                        food_allowance=Decimal('0'),
                        other_allowances=Decimal('0'),
                        social_insurance=Decimal('0'),
                        tax_deduction=Decimal('0'),
                        other_deductions=Decimal('0'),
                        overtime_hours=Decimal('0'),
                        overtime_rate=Decimal('1.5'),
                        effective_date=form.cleaned_data['hire_date'],
                        is_current=True,
                        created_by=request.user
                    )
                
                success_message = f'✅ تم إضافة الموظف {name} بنجاح!'
                if employee_number:
                    success_message += f' الرقم الوظيفي: {employee_number}'
                if national_id:
                    success_message += f' | الرقم القومي: {national_id}'
                messages.success(request, success_message)
                return redirect('hr:employee_list')
                
            except IntegrityError as e:
                error_msg = str(e)
                if 'definitions_person.code' in error_msg:
                    messages.error(request, 'خطأ: يوجد شخص بنفس الكود. يرجى المحاولة مرة أخرى.')
                elif 'definitions_person.national_id' in error_msg:
                    messages.error(request, 'خطأ: الرقم القومي مسجل بالفعل في النظام.')
                elif 'hr_employee.employee_number' in error_msg:
                    messages.error(request, 'خطأ: الرقم الوظيفي مسجل بالفعل. يرجى تغيير الرقم الوظيفي.')
                elif 'definitions_person.email' in error_msg:
                    messages.error(request, 'خطأ: البريد الإلكتروني مسجل بالفعل في النظام.')
                else:
                    messages.error(request, f'خطأ في قاعدة البيانات: {error_msg}')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الموظف: {str(e)}')
        else:
            # عرض الأخطاء بالتفصيل للتشخيص
            error_messages = []
            for field, errors in form.errors.items():
                for error in errors:
                    field_name = form.fields[field].label if field in form.fields else field
                    error_messages.append(f"{field_name}: {error}")
            
            if error_messages:
                messages.error(request, f'يرجى تصحيح الأخطاء التالية: {" | ".join(error_messages)}')
            else:
                messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        form = EmployeeForm()
    
    context = {
        'title': 'إضافة موظف جديد',
        'action': 'إضافة موظف جديد',
        'form': form,
    }
    return render(request, 'hr/employee_form.html', context)


@login_required
def employee_detail(request, pk):
    """تفاصيل الموظف"""
    employee = get_object_or_404(Employee, pk=pk)
    contracts = employee.contracts.all().order_by('-start_date')
    # باقي البيانات كما هي
    context = {
        'employee': employee,
        'contracts': contracts,
        # أضف أي بيانات أخرى كانت موجودة في السياق
    }
    return render(request, 'hr/employee_detail.html', context)


@login_required
def employee_edit(request, pk):
    """تعديل الموظف"""
    from .forms import EmployeeForm
    from definitions.models import Person
    from django.db import transaction
    
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # تحديث بيانات الشخص
                    person = employee.person
                    person.name = form.cleaned_data['name']
                    person.national_id = form.cleaned_data['national_id']
                    person.phone = form.cleaned_data['phone']
                    person.mobile = form.cleaned_data['mobile']
                    person.email = form.cleaned_data['email']
                    person.address = form.cleaned_data['address']
                    person.city = form.cleaned_data['city']
                    
                    # حفظ تاريخ الميلاد في ملاحظات الشخص إذا كان موجوداً
                    birth_date = form.cleaned_data['birth_date']
                    if birth_date:
                        # إزالة تاريخ الميلاد القديم من الملاحظات إذا كان موجوداً
                        if person.notes:
                            lines = person.notes.split('\n')
                            lines = [line for line in lines if not line.startswith('تاريخ الميلاد:')]
                            person.notes = '\n'.join(lines).strip()
                        
                        # إضافة تاريخ الميلاد الجديد
                        if person.notes:
                            person.notes += f"\nتاريخ الميلاد: {birth_date}"
                        else:
                            person.notes = f"تاريخ الميلاد: {birth_date}"
                    
                    person.save()
                    
                    # تحديث بيانات الموظف
                    employee.employee_number = form.cleaned_data['employee_number']
                    employee.department = form.cleaned_data['department']
                    employee.position = form.cleaned_data['position']
                    employee.salary_system = form.cleaned_data['salary_system']
                    employee.hire_date = form.cleaned_data['hire_date']
                    employee.contract_start_date = form.cleaned_data.get('contract_start_date')
                    employee.contract_end_date = form.cleaned_data.get('contract_end_date')
                    employee.current_salary = form.cleaned_data['current_salary']
                    employee.status = form.cleaned_data['status']
                    employee.updated_by = request.user
                    employee.save()
                    
                messages.success(request, f'تم تحديث بيانات الموظف {person.name} بنجاح')
                return redirect('hr:employee_list')
                
            except IntegrityError as e:
                error_msg = str(e)
                if 'definitions_person.national_id' in error_msg:
                    messages.error(request, 'خطأ: الرقم القومي مسجل بالفعل في النظام.')
                elif 'hr_employee.employee_number' in error_msg:
                    messages.error(request, 'خطأ: الرقم الوظيفي مسجل بالفعل. يرجى تغيير الرقم الوظيفي.')
                elif 'definitions_person.email' in error_msg:
                    messages.error(request, 'خطأ: البريد الإلكتروني مسجل بالفعل في النظام.')
                else:
                    messages.error(request, f'خطأ في قاعدة البيانات: {error_msg}')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث الموظف: {str(e)}')
        else:
            # عرض الأخطاء بالتفصيل للتشخيص
            error_messages = []
            for field, errors in form.errors.items():
                for error in errors:
                    field_name = form.fields[field].label if field in form.fields else field
                    error_messages.append(f"{field_name}: {error}")
            
            if error_messages:
                messages.error(request, f'يرجى تصحيح الأخطاء التالية: {" | ".join(error_messages)}')
            else:
                messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'title': f'تعديل الموظف: {employee.full_name}',
        'action': 'تحديث'
    }
    return render(request, 'hr/employee_form.html', context)


@login_required
@require_http_methods(["POST"])
def employee_delete(request, pk):
    """حذف الموظف فعليًا من قاعدة البيانات"""
    employee = get_object_or_404(Employee, pk=pk)
    try:
        employee.delete()
        return JsonResponse({
            'success': True,
            'message': 'تم حذف الموظف بنجاح'
        })
    except IntegrityError as e:
        return JsonResponse({
            'success': False,
            'message': f'لا يمكن حذف الموظف لوجود بيانات مرتبطة به. (IntegrityError: {str(e)})'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء الحذف: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=500)


# المسميات الوظيفية
@login_required
def job_title_list(request):
    """قائمة المسميات الوظيفية"""
    from .models import Position, Department, Employee
    from django.db import models
    
    # Get search and filter parameters
    search_query = request.GET.get('q', '')
    department_filter = request.GET.get('department', '')
    status_filter = request.GET.get('status', '')
    
    # Base queryset
    items = Position.objects.all()
    
    # Apply filters
    if search_query:
        items = items.filter(
            models.Q(name__icontains=search_query) |
            models.Q(code__icontains=search_query) |
            models.Q(description__icontains=search_query)
        )
    
    if department_filter:
        items = items.filter(department_id=department_filter)
    
    if status_filter == 'active':
        items = items.filter(is_active=True)
    elif status_filter == 'inactive':
        items = items.filter(is_active=False)
    
    # Order by name
    items = items.order_by('name')
    
    # Get additional context data
    departments = Department.objects.filter(is_active=True).order_by('name')
    departments_count = items.values('department').distinct().count()
    employees_count = Employee.objects.filter(is_active=True).count()
    
    context = {
        'title': 'المسميات الوظيفية',
        'items': items,
        'departments': departments,
        'departments_count': departments_count,
        'employees_count': employees_count,
        'search_query': search_query,
        'department_filter': department_filter,
        'status_filter': status_filter,
    }
    return render(request, 'hr/job_title_list.html', context)


@login_required
def job_title_create(request):
    """إضافة مسمى وظيفي جديد"""
    from .models import Position, Department
    from .forms import PositionForm
    
    if request.method == 'POST':
        form = PositionForm(request.POST)
        if form.is_valid():
            position = form.save(commit=False)
            position.created_by = request.user
            position.save()
            messages.success(request, 'تم إضافة المسمى الوظيفي بنجاح')
            return redirect('hr:job_title_list')
    else:
        form = PositionForm()

    context = {
        'form': form,
        'title': 'إضافة مسمى وظيفي جديد',
        'action': 'إضافة'
    }
    return render(request, 'hr/position_form.html', context)


# المؤهلات
@login_required
def qualification_list(request):
    """قائمة المؤهلات مع البحث والفلترة"""
    from .models import Qualification
    from django.core.paginator import Paginator
    from django.db.models import Q
    
    # الحصول على جميع المؤهلات
    qualifications = Qualification.objects.all()
    
    # البحث
    search_query = request.GET.get('q', '')
    if search_query:
        qualifications = qualifications.filter(
            Q(name__icontains=search_query) | 
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # فلترة المستوى
    level_filter = request.GET.get('level', '')
    if level_filter:
        qualifications = qualifications.filter(level=level_filter)
    
    # فلترة الحالة
    status_filter = request.GET.get('status', '')
    if status_filter == 'active':
        qualifications = qualifications.filter(is_active=True)
    elif status_filter == 'inactive':
        qualifications = qualifications.filter(is_active=False)
    
    # ترتيب النتائج
    qualifications = qualifications.order_by('name')
    
    # Pagination
    paginator = Paginator(qualifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # الإحصائيات
    total_qualifications = Qualification.objects.count()
    active_qualifications = Qualification.objects.filter(is_active=True).count()
    inactive_qualifications = Qualification.objects.filter(is_active=False).count()
    levels_count = Qualification.objects.values('level').distinct().count()
    
    context = {
        'title': 'المؤهلات',
        'items': page_obj,
        'page_obj': page_obj,
        'search_query': search_query,
        'level_filter': level_filter,
        'status_filter': status_filter,
        'total_qualifications': total_qualifications,
        'active_qualifications': active_qualifications,
        'inactive_qualifications': inactive_qualifications,
        'levels_count': levels_count,
    }
    return render(request, 'hr/qualification_list.html', context)


@login_required
def qualification_create(request):
    """إضافة مؤهل جديد"""
    from .models import Qualification
    
    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        level = request.POST.get('level', '')
        duration = request.POST.get('duration', '')
        is_active = request.POST.get('is_active') == 'on'
        
        if name and code:
            try:
                qualification = Qualification.objects.create(
                    name=name,
                    code=code,
                    description=description,
                    level=level,
                    duration=duration,
                    is_active=is_active,
                    created_by=request.user
                )
                messages.success(request, 'تم إضافة المؤهل بنجاح')
                return redirect('hr:qualification_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ المؤهل: {str(e)}')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
    
    context = {
        'title': 'إضافة مؤهل جديد'
    }
    return render(request, 'hr/qualification_form.html', context)


@login_required
def qualification_detail(request, pk):
    """عرض تفاصيل المؤهل"""
    from .models import Qualification
    try:
        qualification = Qualification.objects.get(pk=pk)
        context = {
            'title': f'تفاصيل المؤهل: {qualification.name}',
            'qualification': qualification
        }
        return render(request, 'hr/qualification_detail.html', context)
    except Qualification.DoesNotExist:
        messages.error(request, 'المؤهل غير موجود')
        return redirect('hr:qualification_list')


@login_required
def qualification_edit(request, pk):
    """تعديل المؤهل"""
    from .models import Qualification
    
    try:
        qualification = Qualification.objects.get(pk=pk)
    except Qualification.DoesNotExist:
        messages.error(request, 'المؤهل غير موجود')
        return redirect('hr:qualification_list')
    
    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        level = request.POST.get('level', '')
        duration = request.POST.get('duration', '')
        is_active = request.POST.get('is_active') == 'on'
        
        if name and code:
            try:
                qualification.name = name
                qualification.code = code
                qualification.description = description
                qualification.level = level
                qualification.duration = duration
                qualification.is_active = is_active
                qualification.save()
                messages.success(request, 'تم تحديث المؤهل بنجاح')
                return redirect('hr:qualification_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث المؤهل: {str(e)}')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
    
    context = {
        'title': f'تعديل المؤهل: {qualification.name}',
        'qualification': qualification
    }
    return render(request, 'hr/qualification_form.html', context)


@login_required
@require_http_methods(["POST"])
def qualification_delete(request, pk):
    """حذف المؤهل"""
    from .models import Qualification
    
    try:
        qualification = Qualification.objects.get(pk=pk)
        qualification_name = qualification.name
        qualification.delete()
        messages.success(request, f'تم حذف المؤهل "{qualification_name}" بنجاح')
    except Qualification.DoesNotExist:
        messages.error(request, 'المؤهل غير موجود')
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء حذف المؤهل: {str(e)}')
    
    return redirect('hr:qualification_list')


# الورديات
@login_required
def shift_list(request):
    """قائمة الورديات مع البحث والفلترة"""
    from .models import Shift
    from django.core.paginator import Paginator
    from django.db.models import Q
    
    # الحصول على جميع الورديات
    shifts = Shift.objects.all()
    
    # البحث
    search_query = request.GET.get('q', '')
    if search_query:
        shifts = shifts.filter(
            Q(name__icontains=search_query) | 
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # فلترة نوع الوردية
    shift_type_filter = request.GET.get('shift_type', '')
    if shift_type_filter:
        shifts = shifts.filter(shift_type=shift_type_filter)
    
    # فلترة الحالة
    status_filter = request.GET.get('status', '')
    if status_filter == 'active':
        shifts = shifts.filter(is_active=True)
    elif status_filter == 'inactive':
        shifts = shifts.filter(is_active=False)
    
    # ترتيب النتائج
    shifts = shifts.order_by('start_time')
    
    # Pagination
    paginator = Paginator(shifts, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # الإحصائيات
    total_shifts = Shift.objects.count()
    active_shifts = Shift.objects.filter(is_active=True).count()
    inactive_shifts = Shift.objects.filter(is_active=False).count()
    types_count = Shift.objects.values('shift_type').distinct().count()
    
    context = {
        'title': 'الورديات',
        'items': page_obj,
        'page_obj': page_obj,
        'search_query': search_query,
        'shift_type_filter': shift_type_filter,
        'status_filter': status_filter,
        'total_shifts': total_shifts,
        'active_shifts': active_shifts,
        'inactive_shifts': inactive_shifts,
        'types_count': types_count,
    }
    return render(request, 'hr/shift_list.html', context)


@login_required
def shift_create(request):
    """إضافة وردية جديدة"""
    from .models import Shift
    from datetime import datetime
    
    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        shift_type = request.POST.get('shift_type', 'MORNING')
        duration_hours = request.POST.get('duration_hours', 8.0)
        include_break = request.POST.get('include_break') == 'on'
        break_duration = request.POST.get('break_duration', 60)
        overtime_allowed = request.POST.get('overtime_allowed') == 'on'
        is_active = request.POST.get('is_active') == 'on'
        
        if name and code and start_time and end_time:
            try:
                # تحويل الوقت من string إلى time object
                start_time_obj = datetime.strptime(start_time, '%H:%M').time()
                end_time_obj = datetime.strptime(end_time, '%H:%M').time()
                
                shift = Shift.objects.create(
                    name=name,
                    code=code,
                    description=description,
                    start_time=start_time_obj,
                    end_time=end_time_obj,
                    shift_type=shift_type,
                    duration_hours=duration_hours,
                    include_break=include_break,
                    break_duration=break_duration,
                    overtime_allowed=overtime_allowed,
                    is_active=is_active,
                    created_by=request.user
                )
                messages.success(request, 'تم إضافة الوردية بنجاح')
                return redirect('hr:shift_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الوردية: {str(e)}')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
    
    context = {
        'title': 'إضافة وردية جديدة'
    }
    return render(request, 'hr/shift_form.html', context)


@login_required
def shift_detail(request, pk):
    """عرض تفاصيل الوردية"""
    from .models import Shift
    try:
        shift = Shift.objects.get(pk=pk)
        context = {
            'title': f'تفاصيل الوردية: {shift.name}',
            'shift': shift
        }
        return render(request, 'hr/shift_detail.html', context)
    except Shift.DoesNotExist:
        messages.error(request, 'الوردية غير موجودة')
        return redirect('hr:shift_list')


@login_required
def shift_edit(request, pk):
    """تعديل الوردية"""
    from .models import Shift
    from datetime import datetime
    
    try:
        shift = Shift.objects.get(pk=pk)
    except Shift.DoesNotExist:
        messages.error(request, 'الوردية غير موجودة')
        return redirect('hr:shift_list')
    
    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        shift_type = request.POST.get('shift_type', 'MORNING')
        duration_hours = request.POST.get('duration_hours', 8.0)
        include_break = request.POST.get('include_break') == 'on'
        break_duration = request.POST.get('break_duration', 60)
        overtime_allowed = request.POST.get('overtime_allowed') == 'on'
        is_active = request.POST.get('is_active') == 'on'
        
        if name and code and start_time and end_time:
            try:
                # تحويل الوقت من string إلى time object
                start_time_obj = datetime.strptime(start_time, '%H:%M').time()
                end_time_obj = datetime.strptime(end_time, '%H:%M').time()
                
                shift.name = name
                shift.code = code
                shift.description = description
                shift.start_time = start_time_obj
                shift.end_time = end_time_obj
                shift.shift_type = shift_type
                shift.duration_hours = duration_hours
                shift.include_break = include_break
                shift.break_duration = break_duration
                shift.overtime_allowed = overtime_allowed
                shift.is_active = is_active
                shift.save()
                
                messages.success(request, 'تم تحديث الوردية بنجاح')
                return redirect('hr:shift_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث الوردية: {str(e)}')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
    
    context = {
        'title': f'تعديل الوردية: {shift.name}',
        'shift': shift
    }
    return render(request, 'hr/shift_form.html', context)


@login_required
@require_http_methods(["POST"])
def shift_delete(request, pk):
    """حذف الوردية"""
    from .models import Shift
    
    try:
        shift = Shift.objects.get(pk=pk)
        shift_name = shift.name
        shift.delete()
        messages.success(request, f'تم حذف الوردية "{shift_name}" بنجاح')
    except Shift.DoesNotExist:
        messages.error(request, 'الوردية غير موجودة')
    except Exception as e:
        messages.error(request, f'حدث خطأ أثناء حذف الوردية: {str(e)}')
    
    return redirect('hr:shift_list')


# الإضافات والخصومات
@login_required
def allowance_deduction_list(request):
    """قائمة الإضافات والخصومات"""
    allowances = AllowanceDeduction.objects.filter(type='allowance', is_active=True).order_by('-date')
    deductions = AllowanceDeduction.objects.filter(type='deduction', is_active=True).order_by('-date')
    context = {
        'title': 'الإضافات والخصومات',
        'allowances': allowances,
        'deductions': deductions
    }
    return render(request, 'hr/allowance_deduction_list.html', context)


@login_required
def allowance_create(request):
    """إضافة إضافة جديدة"""
    if request.method == 'POST':
        form = AllowanceForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الإضافة بنجاح')
            return redirect('hr:allowance_deduction_list')
    else:
        form = AllowanceForm()
    context = {
        'title': 'إضافة إضافة جديدة',
        'form': form,
    }
    return render(request, 'hr/allowance_deduction_form.html', context)


@login_required
def deduction_create(request):
    """إضافة خصم جديد"""
    if request.method == 'POST':
        form = AllowanceDeductionForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الخصم بنجاح')
            return redirect('hr:allowance_deduction_list')
    else:
        form = AllowanceDeductionForm()
    context = {
        'title': 'إضافة خصم جديد',
        'form': form,
    }
    return render(request, 'hr/allowance_deduction_form.html', context)


@login_required
def allowance_deduction_delete(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk)
    if request.method == 'POST':
        obj.delete()
        messages.success(request, 'تم حذف السجل بنجاح')
        return redirect('hr:allowance_deduction_list')
    context = {
        'object': obj,
        'title': 'تأكيد حذف الإضافة/الخصم'
    }
    return render(request, 'hr/allowance_deduction_delete.html', context)


# الحضور والانصراف
@login_required
def attendance_list(request):
    """تسجيل الحضور والانصراف"""
    today = timezone.localdate()
    if request.method == 'POST':
        form = AttendanceForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('hr:attendance_list')
    else:
        form = AttendanceForm(initial={'date': today})
    today_attendance = Attendance.objects.filter(date=today).select_related('employee__person')

    # حساب ملخص الحضور
    present_count = 0
    late_count = 0
    absent_count = 0
    total_count = today_attendance.count()
    for att in today_attendance:
        # استخدم attendance_state إذا كان موجودًا، وإلا status
        state = att.attendance_state or att.status
        if state in ['REGULAR', 'حاضر']:
            present_count += 1
        elif state in ['LATE', 'متأخر']:
            late_count += 1
        elif state in ['ABSENT', 'غائب']:
            absent_count += 1
        # "مكتمل" يمكن اعتباره حاضر أو منفصل حسب رغبتك

    context = {
        'title': 'تسجيل الحضور والانصراف',
        'form': form,
        'today_attendance': today_attendance,
        'present_count': present_count,
        'late_count': late_count,
        'absent_count': absent_count,
        'total_count': total_count,
    }
    return render(request, 'hr/attendance_list.html', context)


@login_required
def attendance_report(request):
    """تقرير الحضور والانصراف"""
    employees = Employee.objects.all()
    report_data = []
    for emp in employees:
        attendances = Attendance.objects.filter(employee=emp)
        present_days = attendances.filter(Q(attendance_state='REGULAR') | Q(attendance_state__isnull=True)).count()
        absent_days = attendances.filter(attendance_state='ABSENT').count()
        late_days = attendances.filter(attendance_state='LATE').count()
        total_days = attendances.count()
        attendance_rate = int((present_days / total_days) * 100) if total_days else 0
        report_data.append({
            'employee': emp.full_name,
            'present_days': present_days,
            'absent_days': absent_days,
            'late_days': late_days,
            'attendance_rate': attendance_rate,
        })
    total_present = sum(r['present_days'] for r in report_data)
    total_absent = sum(r['absent_days'] for r in report_data)
    total_late = sum(r['late_days'] for r in report_data)
    total_employees = len(report_data)
    total_days = total_present + total_absent + total_late
    attendance_percent = int((total_present / total_days) * 100) if total_days else 0
    context = {
        'title': 'تقرير الحضور والانصراف',
        'report_data': report_data,
        'total_present': total_present,
        'total_absent': total_absent,
        'total_late': total_late,
        'attendance_percent': attendance_percent,
    }
    return render(request, 'hr/attendance_report.html', context)


# العمل الإضافي
@login_required
def overtime_list(request):
    """ساعات العمل الإضافي"""
    overtime_records = Overtime.objects.all().select_related('employee__person').order_by('-date')
    
    # حساب الإحصائيات
    total_hours = sum(o.hours for o in overtime_records)
    total_amount = sum(o.total_amount for o in overtime_records)
    employee_count = overtime_records.values('employee').distinct().count()
    avg_rate = total_amount / total_hours if total_hours > 0 else 0

    context = {
        'title': 'ساعات العمل الإضافي',
        'overtime_records': overtime_records,
        'total_hours': total_hours,
        'total_amount': total_amount,
        'employee_count': employee_count,
        'avg_rate': avg_rate,
    }
    return render(request, 'hr/overtime_list.html', context)


@login_required
def overtime_create(request):
    """إضافة عمل إضافي جديد"""
    if request.method == 'POST':
        form = OvertimeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة العمل الإضافي بنجاح')
            return redirect('hr:overtime_list')
    else:
        form = OvertimeForm()
    
    context = {
        'title': 'إضافة عمل إضافي جديد',
        'form': form,
    }
    return render(request, 'hr/overtime_form.html', context)


@login_required
def overtime_edit(request, pk):
    """تعديل عمل إضافي"""
    overtime = get_object_or_404(Overtime, pk=pk)
    if request.method == 'POST':
        form = OvertimeForm(request.POST, instance=overtime)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل العمل الإضافي بنجاح')
            return redirect('hr:overtime_list')
    else:
        form = OvertimeForm(instance=overtime)
    
    context = {
        'title': 'تعديل عمل إضافي',
        'form': form,
        'overtime': overtime,
    }
    return render(request, 'hr/overtime_form.html', context)


@login_required
@require_http_methods(["POST"])
def overtime_delete(request, pk):
    """حذف عمل إضافي"""
    overtime = get_object_or_404(Overtime, pk=pk)
    overtime.delete()
    messages.success(request, 'تم حذف العمل الإضافي بنجاح')
    return redirect('hr:overtime_list')


# طلبات الإجازات
@login_required
def leave_request_list(request):
    """طلبات الإجازات"""
    from .models import LeaveRequest, Employee, LeaveType
    
    # جلب طلبات الإجازات مع البيانات المرتبطة
    leave_requests = LeaveRequest.objects.select_related(
        'employee__person', 'leave_type', 'approved_by', 'created_by'
    ).order_by('-created_at')
    
    # إحصائيات سريعة
    total_requests = leave_requests.count()
    pending_requests = leave_requests.filter(status='PENDING').count()
    approved_requests = leave_requests.filter(status='APPROVED').count()
    rejected_requests = leave_requests.filter(status='REJECTED').count()
    
    context = {
        'title': 'طلبات الإجازات',
        'leave_requests': leave_requests,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'rejected_requests': rejected_requests,
        'employees': Employee.objects.filter(status='ACTIVE').select_related('person'),
        'leave_types': LeaveType.objects.filter(is_active=True),
    }
    return render(request, 'hr/leave_request_list.html', context)


# الغياب والتأخير
@login_required
def absence_list(request):
    """الغياب والتأخير"""
    from .models import AbsenceRecord
    from .forms import AbsenceRecordForm
    
    # جلب سجلات الغياب والتأخير
    absence_records = AbsenceRecord.objects.select_related(
        'employee__person', 'currency', 'created_by', 'approved_by'
    ).order_by('-date', '-created_at')
    
    # إحصائيات
    total_absences = absence_records.filter(absence_type='ABSENCE').count()
    total_lates = absence_records.filter(absence_type='LATE').count()
    total_early_leaves = absence_records.filter(absence_type='EARLY_LEAVE').count()
    total_violations = absence_records.count()
    
    # معالجة النموذج
    if request.method == 'POST':
        form = AbsenceRecordForm(request.POST, request.FILES)
        if form.is_valid():
            absence_record = form.save(commit=False)
            absence_record.created_by = request.user
            absence_record.save()
            messages.success(request, 'تم تسجيل الغياب/التأخير بنجاح')
            return redirect('hr:absence_list')
    else:
        form = AbsenceRecordForm()
    
    context = {
        'title': 'الغياب والتأخير',
        'absence_records': absence_records,
        'form': form,
        'total_absences': total_absences,
        'total_lates': total_lates,
        'total_early_leaves': total_early_leaves,
        'total_violations': total_violations,
    }
    return render(request, 'hr/absence_list.html', context)


@login_required
def absence_create(request):
    """إنشاء سجل غياب/تأخير جديد"""
    from .forms import AbsenceRecordForm
    
    if request.method == 'POST':
        form = AbsenceRecordForm(request.POST, request.FILES)
        if form.is_valid():
            absence_record = form.save(commit=False)
            absence_record.created_by = request.user
            absence_record.save()
            messages.success(request, 'تم تسجيل الغياب/التأخير بنجاح')
            return redirect('hr:absence_list')
    else:
        form = AbsenceRecordForm()
    
    context = {
        'title': 'تسجيل غياب/تأخير جديد',
        'form': form,
    }
    return render(request, 'hr/absence_form.html', context)


@login_required
def absence_detail(request, pk):
    """تفاصيل سجل الغياب/التأخير"""
    from .models import AbsenceRecord
    
    try:
        absence_record = AbsenceRecord.objects.select_related(
            'employee__person', 'currency', 'created_by', 'approved_by'
        ).get(pk=pk)
    except AbsenceRecord.DoesNotExist:
        messages.error(request, 'سجل الغياب/التأخير غير موجود')
        return redirect('hr:absence_list')
    
    context = {
        'title': f'تفاصيل الغياب/التأخير - {absence_record.employee.full_name}',
        'absence_record': absence_record,
    }
    return render(request, 'hr/absence_detail.html', context)


@login_required
def absence_edit(request, pk):
    """تعديل سجل الغياب/التأخير"""
    from .models import AbsenceRecord
    from .forms import AbsenceRecordForm
    
    try:
        absence_record = AbsenceRecord.objects.get(pk=pk)
    except AbsenceRecord.DoesNotExist:
        messages.error(request, 'سجل الغياب/التأخير غير موجود')
        return redirect('hr:absence_list')
    
    if request.method == 'POST':
        form = AbsenceRecordForm(request.POST, request.FILES, instance=absence_record)
        if form.is_valid():
            absence_record = form.save(commit=False)
            absence_record.updated_by = request.user
            absence_record.save()
            messages.success(request, 'تم تحديث سجل الغياب/التأخير بنجاح')
            return redirect('hr:absence_list')
    else:
        form = AbsenceRecordForm(instance=absence_record)
    
    context = {
        'title': f'تعديل الغياب/التأخير - {absence_record.employee.full_name}',
        'form': form,
        'absence_record': absence_record,
    }
    return render(request, 'hr/absence_form.html', context)


@login_required
@require_http_methods(["POST"])
def absence_delete(request, pk):
    """حذف سجل الغياب/التأخير"""
    from .models import AbsenceRecord
    
    try:
        absence_record = AbsenceRecord.objects.get(pk=pk)
        employee_name = absence_record.employee.full_name
        absence_record.delete()
        messages.success(request, f'تم حذف سجل الغياب/التأخير للموظف {employee_name} بنجاح')
    except AbsenceRecord.DoesNotExist:
        messages.error(request, 'سجل الغياب/التأخير غير موجود')
    
    return redirect('hr:absence_list')


@login_required
def absence_approve(request, pk):
    """اعتماد سجل الغياب/التأخير"""
    from .models import AbsenceRecord
    from django.utils import timezone
    
    try:
        absence_record = AbsenceRecord.objects.get(pk=pk)
        if absence_record.status == 'PENDING':
            absence_record.status = 'APPROVED'
            absence_record.approved_by = request.user
            absence_record.approved_at = timezone.now()
            absence_record.save()
            messages.success(request, 'تم اعتماد سجل الغياب/التأخير بنجاح')
        else:
            messages.warning(request, 'سجل الغياب/التأخير معتمد بالفعل')
    except AbsenceRecord.DoesNotExist:
        messages.error(request, 'سجل الغياب/التأخير غير موجود')
    
    return redirect('hr:absence_list')


@login_required
def absence_reject(request, pk):
    """رفض سجل الغياب/التأخير"""
    from .models import AbsenceRecord
    from django.utils import timezone
    
    try:
        absence_record = AbsenceRecord.objects.get(pk=pk)
        if absence_record.status == 'PENDING':
            absence_record.status = 'REJECTED'
            absence_record.approved_by = request.user
            absence_record.approved_at = timezone.now()
            absence_record.save()
            messages.success(request, 'تم رفض سجل الغياب/التأخير بنجاح')
        else:
            messages.warning(request, 'سجل الغياب/التأخير معتمد بالفعل')
    except AbsenceRecord.DoesNotExist:
        messages.error(request, 'سجل الغياب/التأخير غير موجود')
    
    return redirect('hr:absence_list')


# إدارة الإجازات
@login_required
def leave_type_list(request):
    """أنواع الإجازات"""
    from .models import LeaveType
    
    leave_types = LeaveType.objects.all().order_by('name')
    
    context = {
        'title': 'أنواع الإجازات',
        'leave_types': leave_types,
    }
    return render(request, 'hr/leave_type_list.html', context)


@login_required
def leave_balance_list(request):
    """أرصدة الإجازات"""
    from .models import LeaveBalance, Employee
    
    # جلب أرصدة الإجازات للعام الحالي
    current_year = datetime.now().year
    leave_balances = LeaveBalance.objects.filter(
        year=current_year
    ).select_related('employee__person', 'leave_type').order_by('employee__person__name')
    
    # جلب جميع الموظفين النشطين
    employees = Employee.objects.filter(status='ACTIVE').select_related('person')
    
    # حساب المجاميع
    total_days_sum = sum(lb.total_days for lb in leave_balances)
    used_days_sum = sum(lb.used_days for lb in leave_balances)
    remaining_days_sum = sum(lb.remaining_days for lb in leave_balances)
    context = {
        'leave_balances': leave_balances,
        'employees': employees,
        'total_days_sum': total_days_sum,
        'used_days_sum': used_days_sum,
        'remaining_days_sum': remaining_days_sum,
        'title': 'أرصدة الإجازات',
    }
    return render(request, 'hr/leave_balance_list.html', context)


@login_required
def leave_approval_list(request):
    """اعتماد الإجازات"""
    from .models import LeaveRequest
    
    # جلب الطلبات قيد المراجعة
    pending_requests = LeaveRequest.objects.filter(
        status='PENDING'
    ).select_related('employee__person', 'leave_type', 'created_by').order_by('created_at')
    
    # جلب جميع الطلبات للمراجعة
    all_requests = LeaveRequest.objects.select_related(
        'employee__person', 'leave_type', 'approved_by', 'created_by'
    ).order_by('-created_at')
    
    approved_count = all_requests.filter(status='APPROVED').count()
    rejected_count = all_requests.filter(status='REJECTED').count()
    context = {
        'title': 'اعتماد الإجازات',
        'pending_requests': pending_requests,
        'all_requests': all_requests,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
    }
    return render(request, 'hr/leave_approval_list.html', context)


@login_required
def leave_calendar(request):
    """تقويم الإجازات"""
    from .models import LeaveCalendar, LeaveRequest
    from datetime import datetime, timedelta
    
    # قراءة الشهر والسنة من GET
    month = request.GET.get('month')
    year = request.GET.get('year')
    today = datetime.now().date()
    if month and year:
        try:
            month = int(month)
            year = int(year)
            current_date = today.replace(year=year, month=month, day=1)
        except Exception:
            current_date = today.replace(day=1)
    else:
        current_date = today.replace(day=1)
    start_date = current_date
    if current_date.month == 12:
        end_date = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
    else:
        end_date = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)
    # جلب أحداث التقويم للفترة المحددة
    calendar_events = LeaveCalendar.objects.filter(
        event_date__range=[start_date, end_date]
    ).select_related('employee__person', 'leave_request__leave_type').order_by('event_date')
    approved_leaves = LeaveRequest.objects.filter(
        status='APPROVED',
        from_date__lte=end_date,
        to_date__gte=start_date
    ).select_related('employee__person', 'leave_type')
    # بناء أسابيع الشهر
    month_calendar = calendar.Calendar(firstweekday=6)
    month_days = list(month_calendar.itermonthdates(current_date.year, current_date.month))
    calendar_weeks = []
    week = []
    for day in month_days:
        in_month = (day.month == current_date.month)
        is_weekend = (day.weekday() in [4, 5])
        leaves = []
        for leave in approved_leaves:
            if leave.from_date <= day <= leave.to_date:
                leaves.append({
                    'employee_name': leave.employee.full_name,
                    'badge_class': 'bg-success' if leave.leave_type.name == 'إجازة سنوية' else 'bg-info',
                })
        week.append({
            'day': day.day,
            'in_month': in_month,
            'is_weekend': is_weekend,
            'leaves': leaves,
        })
        if len(week) == 7:
            calendar_weeks.append(week)
            week = []
    # اسم الشهر والسنة
    arabic_months = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل', 5: 'مايو', 6: 'يونيو',
        7: 'يوليو', 8: 'أغسطس', 9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }
    month_name = arabic_months.get(current_date.month, str(current_date.month))
    context = {
        'title': 'تقويم الإجازات',
        'calendar_events': calendar_events,
        'approved_leaves': approved_leaves,
        'calendar_weeks': calendar_weeks,
        'start_date': start_date,
        'end_date': end_date,
        'current_month': current_date.month,
        'current_year': current_date.year,
        'month_name': month_name,
        'prev_month': (current_date.month - 1) if current_date.month > 1 else 12,
        'prev_year': (current_date.year if current_date.month > 1 else current_date.year - 1),
        'next_month': (current_date.month + 1) if current_date.month < 12 else 1,
        'next_year': (current_date.year if current_date.month < 12 else current_date.year + 1),
        'today_month': today.month,
        'today_year': today.year,
    }
    return render(request, 'hr/leave_calendar.html', context)


# العقود والتعيينات
@login_required
def contract_list(request):
    status = request.GET.get('status', 'active')
    contracts = Contract.objects.select_related('employee')
    if status == 'active':
        contracts = contracts.filter(is_active=True, archived=False)
    elif status == 'ended':
        contracts = contracts.filter(is_active=False, archived=False)
    elif status == 'archived':
        contracts = contracts.filter(archived=True)
    # else: الكل
    contracts = contracts.order_by('-start_date')
    return render(request, 'hr/contract_list.html', {'contracts': contracts, 'title': 'عقود العمل', 'status': status})


@login_required
def contract_create(request):
    employee_id = request.GET.get('employee')
    renew_contract_id = request.GET.get('contract')
    initial = {}
    form_kwargs = {}
    renew_message = None
    old_contract = None
    # تعبئة الموظف تلقائياً
    if employee_id:
        initial['employee'] = employee_id
    # تعبئة بيانات العقد القديم تلقائياً عند التجديد
    if renew_contract_id:
        try:
            old_contract = Contract.objects.get(pk=renew_contract_id)
            initial.update({
                'employee': old_contract.employee_id,
                'contract_type': old_contract.contract_type,
                'salary': old_contract.salary,
                'notes': old_contract.notes,
                'is_active': True,
            })
            renew_message = f"أنت تقوم الآن بتجديد عقد سابق للموظف: {old_contract.employee.full_name} (نوع العقد: {old_contract.get_contract_type_display()}, بداية: {old_contract.start_date}, نهاية: {old_contract.end_date or '-'}). يمكنك تعديل البيانات قبل الحفظ."
        except Contract.DoesNotExist:
            pass
    if initial:
        form_kwargs['initial'] = initial
    if request.method == 'POST':
        form = ContractForm(request.POST, request.FILES, **form_kwargs)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة العقد بنجاح')
            return redirect('hr:contract_list')
    else:
        form = ContractForm(**form_kwargs)
        if employee_id:
            form.fields['employee'].queryset = form.fields['employee'].queryset.filter(pk=employee_id)
            form.fields['employee'].empty_label = None
            form.fields['employee'].disabled = True
    return render(request, 'hr/contract_form.html', {'form': form, 'title': 'إضافة عقد جديد', 'renew_message': renew_message, 'old_contract': old_contract})


@login_required
@require_http_methods(["GET", "POST"])
def contract_edit(request, pk):
    from django.shortcuts import redirect
    from django.contrib import messages
    try:
        contract = Contract.objects.get(pk=pk)
    except Contract.DoesNotExist:
        messages.warning(request, 'العقد المطلوب غير موجود أو تم حذفه مسبقاً.')
        return redirect('hr:contract_list')
    if request.method == 'POST':
        form = ContractForm(request.POST, request.FILES, instance=contract)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل بيانات العقد بنجاح')
            return redirect('hr:contract_list')
    else:
        form = ContractForm(instance=contract)
    return render(request, 'hr/contract_form.html', {'form': form, 'title': 'تعديل بيانات العقد'})


@login_required
@require_http_methods(["POST"])
def contract_delete(request, pk):
    from django.shortcuts import redirect
    from django.contrib import messages
    try:
        contract = Contract.objects.get(pk=pk)
        contract.delete()
        messages.success(request, 'تم حذف العقد بنجاح')
    except Contract.DoesNotExist:
        messages.warning(request, 'العقد المطلوب غير موجود أو تم حذفه مسبقاً.')
    return redirect('hr:contract_list')


@login_required
@require_http_methods(["POST"])
def contract_archive(request, pk):
    contract = get_object_or_404(Contract, pk=pk)
    contract.archived = True
    contract.save()
    messages.success(request, 'تم أرشفة العقد بنجاح')
    return redirect('hr:contract_list')


@login_required
@require_http_methods(["POST"])
def contract_unarchive(request, pk):
    contract = get_object_or_404(Contract, pk=pk)
    contract.archived = False
    contract.save()
    messages.success(request, 'تم إلغاء الأرشفة بنجاح')
    return redirect('hr:contract_list')


@login_required
def employment_list(request):
    """التعيينات الجديدة"""
    from .models import Employee
    from django.utils import timezone
    from datetime import timedelta
    today = timezone.now().date()
    recent_period = today - timedelta(days=90)
    # الموظفون المعينون خلال آخر 90 يوم (النشطون فقط)
    new_hires_qs = Employee.objects.select_related('person', 'department', 'position').filter(hire_date__gte=recent_period, is_active=True)
    new_hires = []
    review_count = 0
    hired_count = 0
    probation_count = 0
    rejected_count = 0
    for emp in new_hires_qs:
        # الحالة بناءً على status
        if emp.status == 'ACTIVE':
            status = 'تم التعيين'
            hired_count += 1
        elif emp.status == 'SUSPENDED':
            status = 'فترة تجريبية'
            probation_count += 1
        elif emp.status == 'INACTIVE':
            status = 'قيد المراجعة'
            review_count += 1
        elif emp.status == 'TERMINATED':
            status = 'مرفوض'
            rejected_count += 1
        else:
            status = emp.get_status_display()
        new_hires.append({
            'id': emp.id,
            'name': emp.full_name,
            'position': emp.position.name if emp.position else '',
            'department': emp.department.name if emp.department else '',
            'start_date': emp.hire_date,
            'salary': emp.current_salary,
            'status': status,
        })
    context = {
        'title': 'التعيينات الجديدة',
        'new_hires': new_hires,
        'review_count': review_count,
        'hired_count': hired_count,
        'probation_count': probation_count,
        'rejected_count': rejected_count,
        'form': EmploymentForm(),
    }
    return render(request, 'hr/employment_list.html', context)


@login_required
def promotion_list(request):
    """الترقيات"""
    from .models import Employee, Position, Promotion
    from datetime import date, timedelta
    
    # جلب الترقيات من قاعدة البيانات
    promotions = Promotion.objects.select_related(
        'employee__person', 'from_position', 'to_position', 'approved_by'
    ).order_by('-promotion_date')
    
    # تحويل البيانات إلى قائمة للعرض
    promotion_list = []
    for promotion in promotions:
        promotion_list.append({
            'id': promotion.id,
            'employee': promotion.employee.full_name,
            'from_position': promotion.from_position.name if promotion.from_position else '',
            'to_position': promotion.to_position.name if promotion.to_position else '',
            'date': promotion.promotion_date,
            'salary_increase': promotion.salary_increase,
            'salary_increase_percentage': promotion.salary_increase_percentage,
            'reason': promotion.reason,
            'status': promotion.get_status_display(),
            'promotion': promotion,  # للوصول للبيانات الكاملة
        })
    
    # حساب الموظفين المؤهلين للترقية
    eligible_employees = []
    current_date = date.today()
    
    for emp in Employee.objects.select_related('person', 'position', 'department').filter(status='ACTIVE'):
        if emp.hire_date:
            # حساب سنوات الخبرة
            years_of_experience = (current_date - emp.hire_date).days / 365.25
            
            # معايير الأهلية للترقية (يمكن تعديلها حسب السياسة)
            is_eligible = False
            promotion_reason = ""
            
            if years_of_experience >= 2:  # على الأقل سنتين خبرة
                is_eligible = True
                promotion_reason = "سنوات خبرة كافية"
            elif years_of_experience >= 1.5:  # سنة ونصف
                is_eligible = True
                promotion_reason = "أداء متميز"
            
            if is_eligible:
                # حساب تقييم الأداء (افتراضي - يمكن ربطه بنظام تقييم حقيقي)
                performance_rating = "ممتاز" if years_of_experience >= 2 else "جيد جداً"
                
                eligible_employees.append({
                    'id': emp.id,
                    'name': emp.full_name,
                    'current_position': emp.position.name if emp.position else 'غير محدد',
                    'department': emp.department.name if emp.department else 'غير محدد',
                    'years_experience': round(years_of_experience, 1),
                    'performance_rating': performance_rating,
                    'last_promotion': 'لم يتم' if years_of_experience < 2 else f'{int(years_of_experience - 1)} سنوات',
                    'promotion_reason': promotion_reason,
                    'hire_date': emp.hire_date,
                })
                print(f"DEBUG: Added eligible employee - ID: {emp.id}, Name: {emp.full_name}")
    
    # ترتيب الموظفين المؤهلين حسب سنوات الخبرة (الأكثر أولوية)
    eligible_employees.sort(key=lambda x: x['years_experience'], reverse=True)
    
    # الإحصائيات
    total_promotions = len(promotion_list)
    pending_reviews = len([p for p in promotion_list if p['promotion'].status == 'PENDING'])
    approved_promotions = len([p for p in promotion_list if p['promotion'].status == 'APPROVED'])
    rejected_promotions = len([p for p in promotion_list if p['promotion'].status == 'REJECTED'])
    eligible_count = len(eligible_employees)
    promotion_rate = round((total_promotions / max(Employee.objects.count(), 1)) * 100, 1)
    
    context = {
        'title': 'الترقيات',
        'promotions': promotion_list,
        'eligible_employees': eligible_employees,
        'total_promotions': total_promotions,
        'pending_reviews': pending_reviews,
        'approved_promotions': approved_promotions,
        'rejected_promotions': rejected_promotions,
        'eligible_count': eligible_count,
        'promotion_rate': promotion_rate,
    }
    return render(request, 'hr/promotion_list.html', context)


@login_required
def promotion_create(request):
    """إنشاء ترقية جديدة"""
    from .models import Employee, Position, Promotion
    from django.contrib import messages
    
    if request.method == 'POST':
        try:
            employee_id = request.POST.get('employee')
            from_position_id = request.POST.get('from_position')
            to_position_id = request.POST.get('to_position')
            from_salary = request.POST.get('from_salary')
            to_salary = request.POST.get('to_salary')
            promotion_date = request.POST.get('promotion_date')
            effective_date = request.POST.get('effective_date')
            reason = request.POST.get('reason')
            notes = request.POST.get('notes')
            
            # التحقق من البيانات
            if not all([employee_id, to_position_id, to_salary, promotion_date]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect('hr:promotion_create')
            
            employee = Employee.objects.get(id=employee_id)
            to_position = Position.objects.get(id=to_position_id)
            
            # إنشاء الترقية
            promotion = Promotion.objects.create(
                employee=employee,
                from_position=employee.position if employee.position else to_position,
                to_position=to_position,
                from_salary=from_salary or employee.salary or 0,
                to_salary=to_salary,
                promotion_date=promotion_date,
                effective_date=effective_date or promotion_date,
                reason=reason,
                notes=notes,
                status='PENDING',
                created_by=request.user
            )
            
            messages.success(request, 'تم إنشاء الترقية بنجاح')
            return redirect('hr:promotion_list')
            
        except (Employee.DoesNotExist, Position.DoesNotExist):
            messages.error(request, 'بيانات غير صحيحة')
        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')
    
    # عرض النموذج
    employees = Employee.objects.filter(status='ACTIVE').select_related('person', 'position')
    positions = Position.objects.all()
    
    context = {
        'title': 'إنشاء ترقية جديدة',
        'employees': employees,
        'positions': positions,
    }
    return render(request, 'hr/promotion_form.html', context)


@login_required
def promotion_detail(request, pk):
    """تفاصيل الترقية"""
    from .models import Promotion
    
    try:
        promotion = Promotion.objects.select_related(
            'employee__person', 'from_position', 'to_position', 
            'approved_by', 'created_by'
        ).get(id=pk)
    except Promotion.DoesNotExist:
        messages.error(request, 'الترقية غير موجودة')
        return redirect('hr:promotion_list')
    
    context = {
        'title': f'تفاصيل ترقية - {promotion.employee.full_name}',
        'promotion': promotion,
    }
    return render(request, 'hr/promotion_detail.html', context)


@login_required
def promotion_edit(request, pk):
    """تعديل الترقية"""
    from .models import Promotion, Employee, Position
    from django.contrib import messages
    
    try:
        promotion = Promotion.objects.get(id=pk)
    except Promotion.DoesNotExist:
        messages.error(request, 'الترقية غير موجودة')
        return redirect('hr:promotion_list')
    
    if request.method == 'POST':
        try:
            from_position_id = request.POST.get('from_position')
            to_position_id = request.POST.get('to_position')
            from_salary = request.POST.get('from_salary')
            to_salary = request.POST.get('to_salary')
            promotion_date = request.POST.get('promotion_date')
            effective_date = request.POST.get('effective_date')
            reason = request.POST.get('reason')
            notes = request.POST.get('notes')
            status = request.POST.get('status')
            
            # التحقق من البيانات
            if not all([to_position_id, to_salary, promotion_date]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect('hr:promotion_edit', pk=pk)
            
            to_position = Position.objects.get(id=to_position_id)
            from_position = Position.objects.get(id=from_position_id) if from_position_id else promotion.from_position
            
            # تحديث الترقية
            promotion.from_position = from_position
            promotion.to_position = to_position
            promotion.from_salary = from_salary or promotion.from_salary
            promotion.to_salary = to_salary
            promotion.promotion_date = promotion_date
            promotion.effective_date = effective_date or promotion_date
            promotion.reason = reason
            promotion.notes = notes
            promotion.status = status or promotion.status
            
            # إذا تم الموافقة، تحديث بيانات الموظف
            if status == 'APPROVED' and promotion.status != 'APPROVED':
                promotion.approved_by = request.user
                promotion.approved_at = timezone.now()
                
                # تحديث منصب وراتب الموظف
                promotion.employee.position = to_position
                promotion.employee.salary = to_salary
                promotion.employee.save()
            
            promotion.save()
            
            messages.success(request, 'تم تحديث الترقية بنجاح')
            return redirect('hr:promotion_list')
            
        except Position.DoesNotExist:
            messages.error(request, 'المنصب غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')
    
    # عرض النموذج
    employees = Employee.objects.filter(status='ACTIVE').select_related('person', 'position')
    positions = Position.objects.all()
    
    context = {
        'title': f'تعديل ترقية - {promotion.employee.full_name}',
        'promotion': promotion,
        'employees': employees,
        'positions': positions,
    }
    return render(request, 'hr/promotion_form.html', context)


@login_required
@require_http_methods(["POST"])
def promotion_delete(request, pk):
    """حذف الترقية"""
    from .models import Promotion
    from django.contrib import messages
    
    try:
        promotion = Promotion.objects.get(id=pk)
        promotion.delete()
        messages.success(request, 'تم حذف الترقية بنجاح')
    except Promotion.DoesNotExist:
        messages.error(request, 'الترقية غير موجودة')
    except Exception as e:
        messages.error(request, f'حدث خطأ: {str(e)}')
    
    return redirect('hr:promotion_list')


@login_required
@require_http_methods(["POST"])
def promotion_approve(request, pk):
    """الموافقة على الترقية"""
    from .models import Promotion
    from django.contrib import messages
    
    try:
        promotion = Promotion.objects.get(id=pk)
        if promotion.status == 'PENDING':
            promotion.status = 'APPROVED'
            promotion.approved_by = request.user
            promotion.approved_at = timezone.now()
            
            # تحديث منصب وراتب الموظف
            promotion.employee.position = promotion.to_position
            promotion.employee.salary = promotion.to_salary
            promotion.employee.save()
            
            promotion.save()
            messages.success(request, 'تمت الموافقة على الترقية بنجاح')
        else:
            messages.warning(request, 'الترقية ليست في حالة انتظار')
    except Promotion.DoesNotExist:
        messages.error(request, 'الترقية غير موجودة')
    except Exception as e:
        messages.error(request, f'حدث خطأ: {str(e)}')
    
    return redirect('hr:promotion_list')


@login_required
@require_http_methods(["POST"])
def promotion_reject(request, pk):
    """رفض الترقية"""
    from .models import Promotion
    from django.contrib import messages
    
    try:
        promotion = Promotion.objects.get(id=pk)
        if promotion.status == 'PENDING':
            promotion.status = 'REJECTED'
            promotion.approved_by = request.user
            promotion.approved_at = timezone.now()
            promotion.approval_notes = request.POST.get('rejection_reason', '')
            promotion.save()
            messages.success(request, 'تم رفض الترقية')
        else:
            messages.warning(request, 'الترقية ليست في حالة انتظار')
    except Promotion.DoesNotExist:
        messages.error(request, 'الترقية غير موجودة')
    except Exception as e:
        messages.error(request, f'حدث خطأ: {str(e)}')
    
    return redirect('hr:promotion_list')


@login_required
def transfer_list(request):
    """النقل والتحويل"""
    from .models import Transfer, Employee, Department, Position
    
    # جلب عمليات النقل مع البيانات المرتبطة
    transfer_list = Transfer.objects.select_related(
        'employee__person', 'employee__department', 'employee__position',
        'from_department', 'to_department', 'from_position', 'to_position',
        'approved_by', 'created_by'
    ).order_by('-transfer_date', '-created_at')
    
    # جلب الموظفين المؤهلين للنقل (نشطين)
    eligible_employees = Employee.objects.filter(
        status='ACTIVE'
    ).select_related('person', 'department', 'position')
    
    # الإحصائيات
    total_transfers = len(transfer_list)
    pending_reviews = len([t for t in transfer_list if t.status == 'PENDING'])
    approved_transfers = len([t for t in transfer_list if t.status == 'APPROVED'])
    rejected_transfers = len([t for t in transfer_list if t.status == 'REJECTED'])
    completed_transfers = len([t for t in transfer_list if t.status == 'COMPLETED'])
    internal_transfers = len([t for t in transfer_list if t.transfer_type == 'INTERNAL'])
    external_transfers = len([t for t in transfer_list if t.transfer_type == 'EXTERNAL'])
    eligible_count = len(eligible_employees)
    transfer_rate = round((total_transfers / max(Employee.objects.count(), 1)) * 100, 1)
    
    # تجميع البيانات للعرض
    transfers = []
    for transfer in transfer_list:
        transfers.append({
            'transfer': transfer,
            'employee_name': transfer.employee.full_name,
            'employee_number': transfer.employee.employee_number,
            'from_dept': transfer.from_department.name if transfer.from_department else '',
            'to_dept': transfer.to_department.name if transfer.to_department else '',
            'from_pos': transfer.from_position.name if transfer.from_position else '',
            'to_pos': transfer.to_position.name if transfer.to_position else '',
            'transfer_type': transfer.transfer_type_display,
            'transfer_date': transfer.transfer_date,
            'effective_date': transfer.effective_date,
            'status': transfer.status,
            'status_badge': transfer.status_badge_class,
            'reason': transfer.reason[:50] + '...' if len(transfer.reason) > 50 else transfer.reason,
        })
    
    context = {
        'title': 'النقل والتحويل',
        'transfers': transfers,
        'eligible_employees': eligible_employees,
        'departments': Department.objects.filter(is_active=True),
        'positions': Position.objects.filter(is_active=True),
        'total_transfers': total_transfers,
        'pending_reviews': pending_reviews,
        'approved_transfers': approved_transfers,
        'rejected_transfers': rejected_transfers,
        'completed_transfers': completed_transfers,
        'internal_transfers': internal_transfers,
        'external_transfers': external_transfers,
        'eligible_count': eligible_count,
        'transfer_rate': transfer_rate,
    }
    return render(request, 'hr/transfer_list.html', context)


@login_required
def transfer_create(request):
    """إنشاء نقل جديد"""
    from .models import Transfer, Employee, Department, Position
    from .forms import TransferForm
    
    if request.method == 'POST':
        form = TransferForm(request.POST, request.FILES)
        if form.is_valid():
            transfer = form.save(commit=False)
            transfer.created_by = request.user
            transfer.save()
            messages.success(request, 'تم إنشاء طلب النقل بنجاح')
            return redirect('hr:transfer_list')
    else:
        form = TransferForm()
    
    context = {
        'title': 'إنشاء نقل جديد',
        'form': form,
        'employees': Employee.objects.filter(status='ACTIVE').select_related('person', 'department', 'position'),
        'departments': Department.objects.filter(is_active=True),
        'positions': Position.objects.filter(is_active=True),
    }
    return render(request, 'hr/transfer_form.html', context)


@login_required
def transfer_detail(request, pk):
    """تفاصيل النقل"""
    from .models import Transfer
    
    transfer = get_object_or_404(Transfer.objects.select_related(
        'employee__person', 'employee__department', 'employee__position',
        'from_department', 'to_department', 'from_position', 'to_position',
        'approved_by', 'created_by', 'updated_by', 'replacement_employee__person'
    ), pk=pk)
    
    context = {
        'title': f'تفاصيل النقل - {transfer.employee.full_name}',
        'transfer': transfer,
    }
    return render(request, 'hr/transfer_detail.html', context)


@login_required
def transfer_edit(request, pk):
    """تعديل النقل"""
    from .models import Transfer
    from .forms import TransferForm
    
    transfer = get_object_or_404(Transfer, pk=pk)
    
    if request.method == 'POST':
        form = TransferForm(request.POST, request.FILES, instance=transfer)
        if form.is_valid():
            transfer = form.save(commit=False)
            transfer.updated_by = request.user
            transfer.save()
            messages.success(request, 'تم تحديث النقل بنجاح')
            return redirect('hr:transfer_list')
    else:
        form = TransferForm(instance=transfer)
    
    context = {
        'title': 'تعديل النقل',
        'form': form,
        'transfer': transfer,
        'employees': Employee.objects.filter(status='ACTIVE').select_related('person', 'department', 'position'),
        'departments': Department.objects.filter(is_active=True),
        'positions': Position.objects.filter(is_active=True),
    }
    return render(request, 'hr/transfer_form.html', context)


@login_required
@require_http_methods(["POST"])
def transfer_delete(request, pk):
    """حذف النقل"""
    from .models import Transfer
    
    transfer = get_object_or_404(Transfer, pk=pk)
    transfer.delete()
    messages.success(request, 'تم حذف النقل بنجاح')
    return redirect('hr:transfer_list')


@login_required
@require_http_methods(["POST"])
def transfer_approve(request, pk):
    """الموافقة على النقل"""
    from .models import Transfer
    
    transfer = get_object_or_404(Transfer, pk=pk)
    
    if transfer.status == 'PENDING':
        transfer.status = 'APPROVED'
        transfer.approved_by = request.user
        transfer.approved_at = timezone.now()
        transfer.approval_notes = request.POST.get('approval_notes', '')
        transfer._current_user = request.user
        transfer.save()
        
        # تحديث بيانات الموظف
        transfer._update_employee_data()
        
        messages.success(request, 'تمت الموافقة على النقل بنجاح')
    else:
        messages.error(request, 'لا يمكن الموافقة على هذا النقل')
    
    return redirect('hr:transfer_list')


@login_required
@require_http_methods(["POST"])
def transfer_reject(request, pk):
    """رفض النقل"""
    from .models import Transfer
    
    transfer = get_object_or_404(Transfer, pk=pk)
    
    if transfer.status == 'PENDING':
        transfer.status = 'REJECTED'
        transfer.approved_by = request.user
        transfer.approved_at = timezone.now()
        transfer.approval_notes = request.POST.get('rejection_notes', '')
        transfer._current_user = request.user
        transfer.save()
        
        messages.success(request, 'تم رفض النقل بنجاح')
    else:
        messages.error(request, 'لا يمكن رفض هذا النقل')
    
    return redirect('hr:transfer_list')


@login_required
def termination_list(request):
    """قائمة إنهاء الخدمة"""
    terminations = Termination.objects.select_related(
        'employee__person', 'employee__department', 'employee__position'
    ).order_by('-created_at')
    
    # إحصائيات
    total_terminations = terminations.count()
    pending_terminations = terminations.filter(status='PENDING').count()
    approved_terminations = terminations.filter(status='APPROVED').count()
    rejected_terminations = terminations.filter(status='REJECTED').count()
    completed_terminations = terminations.filter(status='COMPLETED').count()
    
    # إحصائيات حسب النوع
    resignation_count = terminations.filter(termination_type='RESIGNATION').count()
    dismissal_count = terminations.filter(termination_type='DISMISSAL').count()
    retirement_count = terminations.filter(termination_type='RETIREMENT').count()
    
    # قائمة الموظفين النشطين للـ modal
    employees = Employee.objects.filter(
        is_active=True, 
        status='ACTIVE'
    ).select_related('person', 'department').order_by('person__name')
    
    context = {
        'terminations': terminations,
        'employees': employees,
        'total_terminations': total_terminations,
        'pending_terminations': pending_terminations,
        'approved_terminations': approved_terminations,
        'rejected_terminations': rejected_terminations,
        'completed_terminations': completed_terminations,
        'resignation_count': resignation_count,
        'dismissal_count': dismissal_count,
        'retirement_count': retirement_count,
        'title': 'إنهاء الخدمة',
    }
    
    return render(request, 'hr/termination_list.html', context)


@login_required
def termination_create(request):
    """إنشاء طلب إنهاء خدمة جديد"""
    if request.method == 'POST':
        form = TerminationForm(request.POST)
        if form.is_valid():
            termination = form.save(commit=False)
            termination.created_by = request.user
            termination.save()
            messages.success(request, 'تم إنشاء طلب إنهاء الخدمة بنجاح')
            return redirect('hr:termination_list')
    else:
        form = TerminationForm()
    
    context = {
        'form': form,
        'title': 'إنشاء طلب إنهاء خدمة جديد',
    }
    
    return render(request, 'hr/termination_form.html', context)


@login_required
def termination_detail(request, pk):
    """تفاصيل طلب إنهاء الخدمة"""
    try:
        termination = Termination.objects.select_related(
            'employee__person', 'employee__department', 'employee__position',
            'approved_by', 'created_by'
        ).get(pk=pk)
    except Termination.DoesNotExist:
        messages.error(request, 'طلب إنهاء الخدمة غير موجود')
        return redirect('hr:termination_list')
    
    context = {
        'termination': termination,
        'title': f'تفاصيل إنهاء الخدمة - {termination.employee}',
    }
    
    return render(request, 'hr/termination_detail.html', context)


@login_required
def termination_edit(request, pk):
    """تعديل طلب إنهاء الخدمة"""
    try:
        termination = Termination.objects.get(pk=pk)
    except Termination.DoesNotExist:
        messages.error(request, 'طلب إنهاء الخدمة غير موجود')
        return redirect('hr:termination_list')
    
    if request.method == 'POST':
        form = TerminationForm(request.POST, instance=termination)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث طلب إنهاء الخدمة بنجاح')
            return redirect('hr:termination_detail', pk=termination.pk)
    else:
        form = TerminationForm(instance=termination)
    
    context = {
        'form': form,
        'termination': termination,
        'title': f'تعديل إنهاء الخدمة - {termination.employee}',
    }
    
    return render(request, 'hr/termination_form.html', context)


@login_required
def termination_delete(request, pk):
    """حذف طلب إنهاء الخدمة"""
    try:
        termination = Termination.objects.get(pk=pk)
    except Termination.DoesNotExist:
        messages.error(request, 'طلب إنهاء الخدمة غير موجود')
        return redirect('hr:termination_list')
    
    if request.method == 'POST':
        termination.delete()
        messages.success(request, 'تم حذف طلب إنهاء الخدمة بنجاح')
        return redirect('hr:termination_list')
    
    context = {
        'termination': termination,
        'title': f'حذف إنهاء الخدمة - {termination.employee}',
    }
    
    return render(request, 'hr/termination_confirm_delete.html', context)


@login_required
def termination_approve(request, pk):
    """الموافقة على طلب إنهاء الخدمة"""
    try:
        termination = Termination.objects.get(pk=pk)
    except Termination.DoesNotExist:
        messages.error(request, 'طلب إنهاء الخدمة غير موجود')
        return redirect('hr:termination_list')
    
    if not termination.can_be_approved():
        messages.error(request, 'لا يمكن الموافقة على هذا الطلب')
        return redirect('hr:termination_detail', pk=termination.pk)
    
    if request.method == 'POST':
        approval_notes = request.POST.get('approval_notes', '')
        termination.status = 'APPROVED'
        termination.approved_by = request.user
        termination.approved_date = timezone.now()
        termination.approval_notes = approval_notes
        termination.save()
        
        # تحديث حالة الموظف
        employee = termination.employee
        employee.status = 'TERMINATED'
        employee.termination_date = termination.termination_date
        employee.save()
        
        messages.success(request, 'تمت الموافقة على طلب إنهاء الخدمة بنجاح')
        return redirect('hr:termination_detail', pk=termination.pk)
    
    context = {
        'termination': termination,
        'title': f'الموافقة على إنهاء الخدمة - {termination.employee}',
    }
    
    return render(request, 'hr/termination_approve.html', context)


@login_required
def termination_reject(request, pk):
    """رفض طلب إنهاء الخدمة"""
    try:
        termination = Termination.objects.get(pk=pk)
    except Termination.DoesNotExist:
        messages.error(request, 'طلب إنهاء الخدمة غير موجود')
        return redirect('hr:termination_list')
    
    if not termination.can_be_rejected():
        messages.error(request, 'لا يمكن رفض هذا الطلب')
        return redirect('hr:termination_detail', pk=termination.pk)
    
    if request.method == 'POST':
        approval_notes = request.POST.get('approval_notes', '')
        termination.status = 'REJECTED'
        termination.approved_by = request.user
        termination.approved_date = timezone.now()
        termination.approval_notes = approval_notes
        termination.save()
        
        messages.success(request, 'تم رفض طلب إنهاء الخدمة')
        return redirect('hr:termination_detail', pk=termination.pk)
    
    context = {
        'termination': termination,
        'title': f'رفض إنهاء الخدمة - {termination.employee}',
    }
    
    return render(request, 'hr/termination_reject.html', context)


# المرتبات
@login_required
def salary_addition_list(request):
    """الإضافات للمرتبات"""
    from django.db.models import Sum, Avg
    from django.utils import timezone
    from django.shortcuts import redirect
    from django.contrib import messages

    # معالجة إضافة جديدة إذا كان الطلب POST
    if request.method == 'POST':
        form = SalaryAdditionForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تمت إضافة الإضافة بنجاح.')
            return redirect('hr:salary_addition_list')
        else:
            messages.error(request, 'حدث خطأ في البيانات المدخلة. يرجى التحقق والمحاولة مرة أخرى.')
    else:
        form = SalaryAdditionForm()

    # جلب الإضافات النشطة
    additions = SalaryAddition.objects.filter(is_active=True).select_related(
        'employee__person', 'employee__department'
    ).order_by('-created_at')
    
    # حساب الإحصائيات
    total_additions = additions.count()
    total_amount = additions.aggregate(Sum('amount'))['amount__sum'] or 0
    
    # تجميع الإضافات حسب النوع
    additions_by_type = {}
    for addition in additions:
        type_name = addition.get_addition_type_display()
        if type_name not in additions_by_type:
            additions_by_type[type_name] = {'count': 0, 'total': 0}
        additions_by_type[type_name]['count'] += 1
        additions_by_type[type_name]['total'] += addition.amount
    
    # تحضير البيانات للعرض
    addition_data = []
    for addition in additions:
        addition_data.append({
            'addition': addition,
            'employee_name': addition.employee.full_name,
            'employee_number': addition.employee.employee_number,
            'department': addition.employee.department.name if addition.employee.department else 'غير محدد',
            'type_display': addition.get_addition_type_display(),
            'amount': addition.amount,
            'start_date': addition.start_date,
            'end_date': addition.end_date,
            'is_current': addition.is_current,
        })
    
    # حساب الإحصائيات الحقيقية للبطاقات
    from django.db.models import Sum, Avg, Count
    total_allowances = additions.aggregate(Sum('amount'))['amount__sum'] or 0
    avg_allowance = additions.aggregate(Avg('amount'))['amount__avg'] or 0
    total_employees = additions.values('employee').distinct().count()
    current_month = timezone.now().strftime('%B %Y')

    # ملخص أنواع الإضافات
    addition_types_summary = additions.values('addition_type').annotate(
        total=Sum('amount'), count=Count('id')
    )
    # تحويل رموز الأنواع إلى نصوص عربية
    type_display_map = dict(SalaryAddition.ADDITION_TYPES)
    for t in addition_types_summary:
        t['type_display'] = type_display_map.get(t['addition_type'], t['addition_type'])

    context = {
        'title': 'الإضافات للمرتبات',
        'addition_data': addition_data,
        'total_additions': total_additions,
        'total_amount': total_amount,
        'additions_by_type': additions_by_type,
        'form': form,
        'total_allowances': total_allowances,
        'avg_allowance': avg_allowance,
        'total_employees': total_employees,
        'current_month': current_month,
        'addition_types_summary': addition_types_summary,
    }
    return render(request, 'hr/salary_addition_list.html', context)


@login_required
def salary_deduction_list(request):
    """الخصومات من المرتبات"""
    # جلب الخصومات النشطة فقط
    deductions = AllowanceDeduction.objects.filter(type='deduction', is_active=True).order_by('-date')

    # إضافة خصم جديد
    if request.method == 'POST':
        form = AllowanceDeductionForm(request.POST)
        if form.is_valid():
            form.save()
            from django.contrib import messages
            messages.success(request, 'تم إضافة الخصم بنجاح.')
            return redirect('hr:salary_deduction_list')
    else:
        form = AllowanceDeductionForm()

    # حساب الإحصائيات
    total_deductions = deductions.aggregate(Sum('amount'))['amount__sum'] or 0
    avg_deduction = deductions.aggregate(Avg('amount'))['amount__avg'] or 0
    count_deductions = deductions.count()
    current_month = timezone.now().strftime('%B %Y')

    # ملخص الأنواع
    deduction_types_summary = deductions.values('name').annotate(
        total=Sum('amount'), count=Count('id')
    )

    context = {
        'title': 'الخصومات من المرتبات',
        'deductions': deductions,
        'total_deductions': total_deductions,
        'avg_deduction': avg_deduction,
        'count_deductions': count_deductions,
        'current_month': current_month,
        'deduction_types_summary': deduction_types_summary,
        'form': form,
    }
    return render(request, 'hr/salary_deduction_list.html', context)


@login_required
def salary_calculation(request):
    """حساب المرتب"""
    from .models import EmployeeSalary
    from django.db.models import Sum, Avg
    
    # جلب المرتبات الحالية للموظفين النشطين
    current_salaries = EmployeeSalary.objects.filter(
        employee__status='ACTIVE',
        is_current=True
    ).select_related('employee__person', 'employee__department')
    
    # حساب الإحصائيات
    total_employees = current_salaries.count()
    total_basic_salary = current_salaries.aggregate(Sum('basic_salary'))['basic_salary__sum'] or 0
    total_allowances = current_salaries.aggregate(Sum('total_allowances'))['total_allowances__sum'] or 0
    total_deductions = current_salaries.aggregate(Sum('total_deductions'))['total_deductions__sum'] or 0
    total_net_salary = current_salaries.aggregate(Sum('net_salary'))['net_salary__sum'] or 0
    avg_salary = current_salaries.aggregate(Avg('net_salary'))['net_salary__avg'] or 0
    
    # تحضير بيانات المرتبات
    salary_calculations = []
    for salary in current_salaries:
        salary_calculations.append({
            'employee': salary.employee.full_name,
            'employee_id': salary.employee.employee_number,
            'department': salary.employee.department.name if salary.employee.department else 'غير محدد',
            'basic_salary': float(salary.basic_salary),
            'additions': float(salary.total_allowances),
            'deductions': float(salary.total_deductions),
            'net_salary': float(salary.net_salary),
            'effective_date': salary.effective_date,
        })
    
    context = {
        'title': 'حساب المرتب',
        'salary_calculations': salary_calculations,
        'statistics': {
            'total_employees': total_employees,
            'total_basic_salary': total_basic_salary,
            'total_allowances': total_allowances,
            'total_deductions': total_deductions,
            'total_net_salary': total_net_salary,
            'avg_salary': avg_salary,
        }
    }
    return render(request, 'hr/salary_calculation.html', context)


# التدريب والتطوير
@login_required
def training_program_list(request):
    """البرامج التدريبية"""
    context = {
        'title': 'البرامج التدريبية',
        'programs': [
            {'name': 'دورة إدارة المشاريع', 'duration': '40 ساعة', 'start_date': '2024-03-01', 'trainer': 'د. أحمد علي'},
            {'name': 'دورة المحاسبة المتقدمة', 'duration': '30 ساعة', 'start_date': '2024-03-15', 'trainer': 'أ. فاطمة محمد'},
            {'name': 'دورة خدمة العملاء', 'duration': '20 ساعة', 'start_date': '2024-04-01', 'trainer': 'أ. محمد أحمد'},
        ]
    }
    return render(request, 'hr/training_program_list.html', context)


@login_required
def training_enrollment_list(request):
    """تسجيل المتدربين"""
    context = {
        'title': 'تسجيل المتدربين',
        'enrollments': [
            {'employee': 'أحمد محمد', 'program': 'دورة إدارة المشاريع', 'status': 'مسجل', 'date': '2024-02-15'},
            {'employee': 'فاطمة علي', 'program': 'دورة المحاسبة المتقدمة', 'status': 'مكتمل', 'date': '2024-02-01'},
            {'employee': 'محمد أحمد', 'program': 'دورة خدمة العملاء', 'status': 'قيد التسجيل', 'date': '2024-02-20'},
        ]
    }
    return render(request, 'hr/training_enrollment_list.html', context)


@login_required
def training_evaluation_list(request):
    """تقييم التدريب"""
    context = {
        'title': 'تقييم التدريب',
        'evaluations': [
            {'employee': 'أحمد محمد', 'program': 'دورة إدارة المشاريع', 'score': 85, 'feedback': 'ممتاز'},
            {'employee': 'فاطمة علي', 'program': 'دورة المحاسبة المتقدمة', 'score': 92, 'feedback': 'ممتاز جداً'},
            {'employee': 'محمد أحمد', 'program': 'دورة خدمة العملاء', 'score': 78, 'feedback': 'جيد جداً'},
        ]
    }
    return render(request, 'hr/training_evaluation_list.html', context)


@login_required
def training_certificate_list(request):
    """شهادات التدريب"""
    context = {
        'title': 'شهادات التدريب',
        'certificates': [
            {'employee': 'أحمد محمد', 'program': 'دورة إدارة المشاريع', 'issue_date': '2024-02-28', 'certificate_no': 'CERT001'},
            {'employee': 'فاطمة علي', 'program': 'دورة المحاسبة المتقدمة', 'issue_date': '2024-02-15', 'certificate_no': 'CERT002'},
        ]
    }
    return render(request, 'hr/training_certificate_list.html', context)


# تقييم الأداء
@login_required
def performance_criteria_list(request):
    """معايير التقييم"""
    context = {
        'title': 'معايير التقييم',
        'criteria': [
            {'name': 'جودة العمل', 'weight': 30, 'description': 'مستوى الجودة في إنجاز المهام'},
            {'name': 'الالتزام بالمواعيد', 'weight': 25, 'description': 'الحضور والانصراف في الوقت المحدد'},
            {'name': 'التعاون', 'weight': 20, 'description': 'التعاون مع الزملاء والفريق'},
            {'name': 'المبادرة', 'weight': 25, 'description': 'اتخاذ المبادرات وحل المشاكل'},
        ]
    }
    return render(request, 'hr/performance_criteria_list.html', context)


@login_required
def performance_evaluation_list(request):
    """تقييمات الأداء"""
    context = {
        'title': 'تقييمات الأداء',
        'evaluations': [
            {'employee': 'أحمد محمد', 'period': 'Q1 2024', 'score': 85, 'rating': 'ممتاز', 'evaluator': 'مدير القسم'},
            {'employee': 'فاطمة علي', 'period': 'Q1 2024', 'score': 92, 'rating': 'ممتاز جداً', 'evaluator': 'مدير القسم'},
            {'employee': 'محمد أحمد', 'period': 'Q1 2024', 'score': 78, 'rating': 'جيد جداً', 'evaluator': 'مدير القسم'},
        ]
    }
    return render(request, 'hr/performance_evaluation_list.html', context)


@login_required
def performance_report_list(request):
    """تقارير الأداء"""
    context = {
        'title': 'تقارير الأداء',
        'reports': [
            {'title': 'تقرير أداء الربع الأول 2024', 'date': '2024-03-31', 'employees': 25, 'avg_score': 83.5},
            {'title': 'تقرير أداء قسم المحاسبة', 'date': '2024-03-15', 'employees': 8, 'avg_score': 87.2},
            {'title': 'تقرير أداء قسم المبيعات', 'date': '2024-03-10', 'employees': 12, 'avg_score': 79.8},
        ]
    }
    return render(request, 'hr/performance_report_list.html', context)


@login_required
def performance_improvement_plan_list(request):
    """خطط تحسين الأداء"""
    context = {
        'title': 'خطط تحسين الأداء',
        'plans': [
            {'employee': 'علي أحمد', 'area': 'إدارة الوقت', 'target_date': '2024-06-30', 'status': 'قيد التنفيذ'},
            {'employee': 'نور محمد', 'area': 'مهارات التواصل', 'target_date': '2024-05-31', 'status': 'مكتمل'},
        ]
    }
    return render(request, 'hr/performance_improvement_plan_list.html', context)


# التقارير المتخصصة
@login_required
def employee_report(request):
    """تقرير بالعاملين"""
    context = {
        'title': 'تقرير بالعاملين',
        'employees': [
            {'name': 'أحمد محمد', 'department': 'المحاسبة', 'position': 'محاسب', 'hire_date': '2023-01-01', 'status': 'نشط'},
            {'name': 'فاطمة علي', 'department': 'الموارد البشرية', 'position': 'أخصائي موارد بشرية', 'hire_date': '2023-06-01', 'status': 'نشط'},
            {'name': 'محمد أحمد', 'department': 'المبيعات', 'position': 'مندوب مبيعات', 'hire_date': '2022-03-01', 'status': 'نشط'},
        ]
    }
    return render(request, 'hr/employee_report.html', context)


@login_required
def hr_journal(request):
    """يوميات شؤون العاملين"""
    context = {
        'title': 'يوميات شؤون العاملين',
        'journal_entries': [
            {'date': '2024-02-01', 'type': 'تعيين', 'description': 'تعيين سارة أحمد في قسم المحاسبة'},
            {'date': '2024-02-02', 'type': 'ترقية', 'description': 'ترقية أحمد محمد إلى منصب مشرف'},
            {'date': '2024-02-03', 'type': 'إجازة', 'description': 'اعتماد إجازة فاطمة علي لمدة 3 أيام'},
        ]
    }
    return render(request, 'hr/hr_journal.html', context)


@login_required
def salary_report(request):
    """تقارير المرتبات"""
    from .models import EmployeeSalary
    from django.db.models import Sum, Avg
    from django.utils import timezone
    
    # جلب المرتبات الحالية للموظفين النشطين
    current_salaries = EmployeeSalary.objects.filter(
        employee__status='ACTIVE',
        is_current=True
    ).select_related('employee__person', 'employee__department')
    
    # حساب الإحصائيات
    total_employees = current_salaries.count()
    total_basic_salary = current_salaries.aggregate(Sum('basic_salary'))['basic_salary__sum'] or 0
    total_allowances = current_salaries.aggregate(Sum('total_allowances'))['total_allowances__sum'] or 0
    total_deductions = current_salaries.aggregate(Sum('total_deductions'))['total_deductions__sum'] or 0
    total_net_salary = current_salaries.aggregate(Sum('net_salary'))['net_salary__sum'] or 0
    avg_salary = current_salaries.aggregate(Avg('net_salary'))['net_salary__avg'] or 0
    
    # تحضير بيانات المرتبات
    salary_data = []
    for salary in current_salaries:
        salary_data.append({
            'employee_name': salary.employee.full_name,
            'employee_id': salary.employee.employee_number,
            'department': salary.employee.department.name if salary.employee.department else 'غير محدد',
            'basic_salary': float(salary.basic_salary),
            'allowances': float(salary.total_allowances),
            'deductions': float(salary.total_deductions),
            'net_salary': float(salary.net_salary),
            'last_payment_date': salary.effective_date.strftime('%Y-%m-%d') if salary.effective_date else 'غير محدد',
        })
    
    # ترتيب البيانات حسب صافي المرتب (تنازلي)
    salary_data.sort(key=lambda x: x['net_salary'], reverse=True)

    context = {
        'title': 'تقارير المرتبات',
        'salary_data': salary_data,
        'statistics': {
            'total_employees': total_employees,
            'total_salary_cost': f"{total_net_salary:,.0f} ج.م",
            'avg_salary': f"{avg_salary:,.0f} ج.م",
            'total_allowances': f"{total_allowances:,.0f} ج.م",
            'total_deductions': f"{total_deductions:,.0f} ج.م",
            'total_basic_salary': f"{total_basic_salary:,.0f} ج.م",
        }
    }
    return render(request, 'hr/salary_report.html', context)


@login_required
def payroll_list(request):
    """كشف المرتبات"""
    from .models import EmployeeSalary
    from django.db.models import Sum, Avg
    from django.utils import timezone
    
    # جلب المرتبات الحالية للموظفين النشطين
    current_salaries = EmployeeSalary.objects.filter(
        employee__status='ACTIVE',
        is_current=True
    ).select_related('employee__person', 'employee__department')
    
    # حساب الإحصائيات
    total_employees = current_salaries.count()
    total_basic_salary = current_salaries.aggregate(Sum('basic_salary'))['basic_salary__sum'] or 0
    total_allowances = current_salaries.aggregate(Sum('total_allowances'))['total_allowances__sum'] or 0
    total_deductions = current_salaries.aggregate(Sum('total_deductions'))['total_deductions__sum'] or 0
    total_net_salary = current_salaries.aggregate(Sum('net_salary'))['net_salary__sum'] or 0
    
    # تحضير بيانات كشف المرتبات
    payrolls = []
    for salary in current_salaries:
        # تحديد حالة المرتب بناءً على تاريخ السريان
        if salary.effective_date:
            days_since_effective = (timezone.now().date() - salary.effective_date).days
            if days_since_effective <= 30:
                status = 'مدفوع'
            elif days_since_effective <= 60:
                status = 'معتمد'
            else:
                status = 'معلق'
        else:
            status = 'معلق'
        
        payrolls.append({
            'employee': salary.employee.full_name,
            'employee_id': salary.employee.employee_number,
            'department': salary.employee.department.name if salary.employee.department else 'غير محدد',
            'basic_salary': float(salary.basic_salary),
            'additions': float(salary.total_allowances),
            'deductions': float(salary.total_deductions),
            'net_salary': float(salary.net_salary),
            'status': status,
            'effective_date': salary.effective_date.strftime('%Y-%m-%d') if salary.effective_date else 'غير محدد',
        })
    
    # ترتيب البيانات حسب صافي المرتب (تنازلي)
    payrolls.sort(key=lambda x: x['net_salary'], reverse=True)

    context = {
        'title': 'كشف المرتبات',
        'payrolls': payrolls,
        'statistics': {
            'total_employees': total_employees,
            'total_basic_salary': total_basic_salary,
            'total_allowances': total_allowances,
            'total_deductions': total_deductions,
            'total_net_salary': total_net_salary,
        }
    }
    return render(request, 'hr/payroll_list.html', context)


@login_required
def loan_list(request):
    """السلف والقروض"""
    loans = [
        {'employee': 'أحمد محمد', 'type': 'سلفة شخصية', 'amount': 5000, 'paid': 2000, 'remaining': 3000, 'monthly': 500, 'status': 'نشط'},
        {'employee': 'فاطمة علي', 'type': 'قرض', 'amount': 10000, 'paid': 7500, 'remaining': 2500, 'monthly': 750, 'status': 'نشط'},
        {'employee': 'محمد أحمد', 'type': 'سلفة طارئة', 'amount': 2000, 'paid': 1200, 'remaining': 800, 'monthly': 300, 'status': 'نشط'},
        {'employee': 'سارة علي', 'type': 'سلفة شخصية', 'amount': 3000, 'paid': 3000, 'remaining': 0, 'monthly': 0, 'status': 'مكتمل'},
        {'employee': 'علي محمد', 'type': 'قرض', 'amount': 8000, 'paid': 4000, 'remaining': 4000, 'monthly': 800, 'status': 'نشط'},
    ]

    context = {
        'title': 'السلف والقروض',
        'loans': loans,
    }
    return render(request, 'hr/loan_list.html', context)


@login_required
def training_program_list(request):
    """البرامج التدريبية"""
    training_programs = [
        {'name': 'القيادة والإدارة', 'category': 'إداري', 'trainer': 'د. أحمد سالم', 'duration': '5 أيام', 'start_date': '2024-02-01', 'end_date': '2024-02-05', 'participants': 25, 'status': 'نشط'},
        {'name': 'مهارات التواصل', 'category': 'مهارات شخصية', 'trainer': 'أ. فاطمة محمد', 'duration': '3 أيام', 'start_date': '2024-02-10', 'end_date': '2024-02-12', 'participants': 18, 'status': 'مجدول'},
        {'name': 'Excel المتقدم', 'category': 'تقني', 'trainer': 'م. محمد علي', 'duration': '4 أيام', 'start_date': '2024-01-15', 'end_date': '2024-01-18', 'participants': 15, 'status': 'مكتمل'},
        {'name': 'إدارة المشاريع', 'category': 'إداري', 'trainer': 'د. سارة أحمد', 'duration': '6 أيام', 'start_date': '2024-02-15', 'end_date': '2024-02-20', 'participants': 20, 'status': 'مجدول'},
        {'name': 'الأمن السيبراني', 'category': 'تقني', 'trainer': 'م. علي محمود', 'duration': '7 أيام', 'start_date': '2024-01-20', 'end_date': '2024-01-26', 'participants': 12, 'status': 'نشط'},
    ]

    context = {
        'title': 'البرامج التدريبية',
        'training_programs': training_programs,
    }
    return render(request, 'hr/training_program_list.html', context)


@login_required
def training_enrollment_list(request):
    """تسجيل المتدربين"""
    enrollments = [
        {'employee': 'أحمد محمد', 'position': 'مدير', 'department': 'الموارد البشرية', 'program': 'القيادة والإدارة', 'enrollment_date': '2024-01-25', 'start_date': '2024-02-01', 'completion': 85, 'status': 'قيد التدريب'},
        {'employee': 'فاطمة علي', 'position': 'محاسب', 'department': 'المالية', 'program': 'مهارات التواصل', 'enrollment_date': '2024-01-20', 'start_date': '2024-02-10', 'completion': 100, 'status': 'مكتمل'},
        {'employee': 'محمد أحمد', 'position': 'مطور', 'department': 'تقنية المعلومات', 'program': 'Excel المتقدم', 'enrollment_date': '2024-01-10', 'start_date': '2024-01-15', 'completion': 100, 'status': 'مكتمل'},
        {'employee': 'سارة علي', 'position': 'مندوب مبيعات', 'department': 'المبيعات', 'program': 'إدارة المشاريع', 'enrollment_date': '2024-02-01', 'start_date': '2024-02-15', 'completion': 0, 'status': 'مسجل'},
        {'employee': 'علي محمد', 'position': 'مشرف', 'department': 'الإنتاج', 'program': 'الأمن السيبراني', 'enrollment_date': '2024-01-15', 'start_date': '2024-01-20', 'completion': 60, 'status': 'قيد التدريب'},
    ]

    context = {
        'title': 'تسجيل المتدربين',
        'enrollments': enrollments,
    }
    return render(request, 'hr/training_enrollment_list.html', context)


@login_required
def training_evaluation_list(request):
    """تقييم التدريب"""
    evaluations = [
        {'program': 'القيادة والإدارة', 'trainee': 'أحمد محمد', 'trainer': 'د. أحمد سالم', 'date': '2024-02-05', 'overall_rating': 5, 'content_rating': 5, 'trainer_rating': 4},
        {'program': 'مهارات التواصل', 'trainee': 'فاطمة علي', 'trainer': 'أ. فاطمة محمد', 'date': '2024-02-12', 'overall_rating': 4, 'content_rating': 4, 'trainer_rating': 5},
        {'program': 'Excel المتقدم', 'trainee': 'محمد أحمد', 'trainer': 'م. محمد علي', 'date': '2024-01-18', 'overall_rating': 4, 'content_rating': 5, 'trainer_rating': 4},
        {'program': 'الأمن السيبراني', 'trainee': 'علي محمد', 'trainer': 'م. علي محمود', 'date': '2024-01-26', 'overall_rating': 5, 'content_rating': 4, 'trainer_rating': 5},
        {'program': 'القيادة والإدارة', 'trainee': 'سارة أحمد', 'trainer': 'د. أحمد سالم', 'date': '2024-01-30', 'overall_rating': 3, 'content_rating': 3, 'trainer_rating': 4},
    ]

    context = {
        'title': 'تقييم التدريب',
        'evaluations': evaluations,
    }
    return render(request, 'hr/training_evaluation_list.html', context)


@login_required
def training_certificate_list(request):
    """شهادات التدريب"""
    certificates = [
        {'number': 'CERT-2024-001', 'employee': 'أحمد محمد', 'department': 'الموارد البشرية', 'program': 'القيادة والإدارة', 'duration': '5 أيام', 'completion_date': '2024-02-05', 'issue_date': '2024-02-06', 'grade': 95, 'status': 'مُصدرة'},
        {'number': 'CERT-2024-002', 'employee': 'فاطمة علي', 'department': 'المالية', 'program': 'مهارات التواصل', 'duration': '3 أيام', 'completion_date': '2024-02-12', 'issue_date': '2024-02-13', 'grade': 88, 'status': 'مُصدرة'},
        {'number': 'CERT-2024-003', 'employee': 'محمد أحمد', 'department': 'تقنية المعلومات', 'program': 'Excel المتقدم', 'duration': '4 أيام', 'completion_date': '2024-01-18', 'issue_date': '2024-01-19', 'grade': 92, 'status': 'مُصدرة'},
        {'number': 'CERT-2024-004', 'employee': 'علي محمد', 'department': 'الإنتاج', 'program': 'الأمن السيبراني', 'duration': '7 أيام', 'completion_date': '2024-01-26', 'issue_date': '2024-01-27', 'grade': 85, 'status': 'مُصدرة'},
        {'number': 'CERT-2024-005', 'employee': 'سارة أحمد', 'department': 'المبيعات', 'program': 'إدارة المشاريع', 'duration': '6 أيام', 'completion_date': '2024-02-20', 'issue_date': '', 'grade': 78, 'status': 'قيد الإصدار'},
    ]

    context = {
        'title': 'شهادات التدريب',
        'certificates': certificates,
    }
    return render(request, 'hr/training_certificate_list.html', context)


@login_required
def evaluation_criteria_list(request):
    """معايير التقييم"""
    evaluation_criteria = [
        {'name': 'جودة العمل', 'description': 'مستوى الدقة والإتقان في أداء المهام', 'category': 'الأداء الوظيفي', 'weight': 15, 'measurement': 'نسبة مئوية', 'minimum': 70, 'target': 90},
        {'name': 'الالتزام بالمواعيد', 'description': 'الانضباط في تسليم المهام في الوقت المحدد', 'category': 'الأداء الوظيفي', 'weight': 10, 'measurement': 'نسبة مئوية', 'minimum': 80, 'target': 95},
        {'name': 'الإنتاجية', 'description': 'كمية العمل المنجز مقارنة بالمعايير المحددة', 'category': 'الأداء الوظيفي', 'weight': 15, 'measurement': 'نسبة مئوية', 'minimum': 75, 'target': 100},
        {'name': 'التعاون مع الفريق', 'description': 'القدرة على العمل بفعالية مع أعضاء الفريق', 'category': 'العمل الجماعي', 'weight': 12, 'measurement': 'تقييم (1-5)', 'minimum': 3, 'target': 5},
        {'name': 'مشاركة المعرفة', 'description': 'الاستعداد لمشاركة المعرفة والخبرات مع الآخرين', 'category': 'العمل الجماعي', 'weight': 8, 'measurement': 'تقييم (1-5)', 'minimum': 3, 'target': 4},
        {'name': 'حل المشكلات', 'description': 'القدرة على إيجاد حلول إبداعية للمشكلات', 'category': 'الإبداع والابتكار', 'weight': 10, 'measurement': 'تقييم (1-5)', 'minimum': 3, 'target': 4},
        {'name': 'تطوير العمليات', 'description': 'اقتراح تحسينات على العمليات الحالية', 'category': 'الإبداع والابتكار', 'weight': 8, 'measurement': 'عدد الاقتراحات', 'minimum': 2, 'target': 5},
        {'name': 'الالتزام المهني', 'description': 'الالتزام بأخلاقيات العمل والسلوك المهني', 'category': 'السلوك المهني', 'weight': 10, 'measurement': 'تقييم (1-5)', 'minimum': 4, 'target': 5},
        {'name': 'التطوير الذاتي', 'description': 'الاهتمام بتطوير المهارات والمعرفة الشخصية', 'category': 'السلوك المهني', 'weight': 7, 'measurement': 'ساعات التدريب', 'minimum': 20, 'target': 40},
    ]

    context = {
        'title': 'معايير التقييم',
        'evaluation_criteria': evaluation_criteria,
    }
    return render(request, 'hr/evaluation_criteria_list.html', context)


@login_required
def performance_evaluation_list(request):
    """تقييمات الأداء"""
    evaluations = [
        {'employee': 'أحمد محمد', 'position': 'مدير المشاريع', 'department': 'تقنية المعلومات', 'evaluator': 'مدير التقنية', 'period': 'Q1 2024', 'rating': 5, 'score': 95, 'status': 'مكتمل'},
        {'employee': 'فاطمة علي', 'position': 'محاسب أول', 'department': 'المالية', 'evaluator': 'مدير المالية', 'period': 'Q1 2024', 'rating': 5, 'score': 92, 'status': 'مكتمل'},
        {'employee': 'محمد أحمد', 'position': 'مطور أول', 'department': 'تقنية المعلومات', 'evaluator': 'مدير التقنية', 'period': 'Q1 2024', 'rating': 5, 'score': 90, 'status': 'مكتمل'},
        {'employee': 'سارة علي', 'position': 'مندوب مبيعات', 'department': 'المبيعات', 'evaluator': 'مدير المبيعات', 'period': 'Q1 2024', 'rating': 4, 'score': 85, 'status': 'قيد المراجعة'},
        {'employee': 'علي محمد', 'position': 'مشرف إنتاج', 'department': 'الإنتاج', 'evaluator': 'مدير الإنتاج', 'period': 'Q1 2024', 'rating': 4, 'score': 82, 'status': 'مكتمل'},
        {'employee': 'نور أحمد', 'position': 'موظف خدمة عملاء', 'department': 'خدمة العملاء', 'evaluator': 'مدير الخدمة', 'period': 'Q1 2024', 'rating': 4, 'score': 80, 'status': 'قيد المراجعة'},
        {'employee': 'خالد علي', 'position': 'محاسب', 'department': 'المالية', 'evaluator': 'مدير المالية', 'period': 'Q1 2024', 'rating': 4, 'score': 78, 'status': 'مكتمل'},
        {'employee': 'مريم محمد', 'position': 'مصمم جرافيك', 'department': 'التسويق', 'evaluator': 'مدير التسويق', 'period': 'Q1 2024', 'rating': 4, 'score': 75, 'status': 'مكتمل'},
    ]

    context = {
        'title': 'تقييمات الأداء',
        'evaluations': evaluations,
    }
    return render(request, 'hr/performance_evaluation_list.html', context)


@login_required
def performance_report_list(request):
    """تقارير الأداء"""
    department_performance = [
        {'name': 'تقنية المعلومات', 'employees': 15, 'avg_rating': 4, 'avg_score': 88, 'goal_achievement': 92, 'top_performers': 8, 'need_development': 2},
        {'name': 'المالية', 'employees': 12, 'avg_rating': 4, 'avg_score': 85, 'goal_achievement': 88, 'top_performers': 6, 'need_development': 1},
        {'name': 'المبيعات', 'employees': 10, 'avg_rating': 4, 'avg_score': 82, 'goal_achievement': 85, 'top_performers': 4, 'need_development': 2},
        {'name': 'الموارد البشرية', 'employees': 8, 'avg_rating': 4, 'avg_score': 80, 'goal_achievement': 83, 'top_performers': 3, 'need_development': 1},
        {'name': 'التسويق', 'employees': 6, 'avg_rating': 3, 'avg_score': 75, 'goal_achievement': 78, 'top_performers': 2, 'need_development': 2},
        {'name': 'الإنتاج', 'employees': 20, 'avg_rating': 3, 'avg_score': 72, 'goal_achievement': 75, 'top_performers': 5, 'need_development': 5},
    ]

    context = {
        'title': 'تقارير الأداء',
        'department_performance': department_performance,
    }
    return render(request, 'hr/performance_report_list.html', context)


@login_required
def development_plan_list(request):
    """خطط تحسين الأداء"""
    development_plans = [
        {'employee': 'يوسف أحمد', 'department': 'المبيعات', 'area': 'مهارات التواصل', 'goal': 'تحسين مهارات التواصل مع العملاء', 'start_date': '2024-01-15', 'end_date': '2024-03-15', 'progress': 65, 'status': 'نشط'},
        {'employee': 'هدى علي', 'department': 'خدمة العملاء', 'area': 'إدارة الوقت', 'goal': 'تطوير مهارات إدارة الوقت والأولويات', 'start_date': '2024-01-20', 'end_date': '2024-04-20', 'progress': 45, 'status': 'نشط'},
        {'employee': 'طارق محمد', 'department': 'الإنتاج', 'area': 'المهارات التقنية', 'goal': 'تعلم تقنيات الإنتاج الحديثة', 'start_date': '2024-02-01', 'end_date': '2024-05-01', 'progress': 30, 'status': 'نشط'},
        {'employee': 'ليلى أحمد', 'department': 'التسويق', 'area': 'مهارات القيادة', 'goal': 'تطوير مهارات القيادة والإشراف', 'start_date': '2024-01-10', 'end_date': '2024-06-10', 'progress': 55, 'status': 'نشط'},
        {'employee': 'محمد أحمد', 'department': 'تقنية المعلومات', 'area': 'المهارات التقنية', 'goal': 'تعلم Python المتقدم', 'start_date': '2024-01-05', 'end_date': '2024-02-15', 'progress': 75, 'status': 'نشط'},
        {'employee': 'سارة علي', 'department': 'المبيعات', 'area': 'مهارات القيادة', 'goal': 'إدارة المشاريع PMP', 'start_date': '2024-01-12', 'end_date': '2024-02-20', 'progress': 60, 'status': 'نشط'},
        {'employee': 'علي محمد', 'department': 'الإنتاج', 'area': 'المهارات التقنية', 'goal': 'تطبيق مشروع تحسين العمليات', 'start_date': '2024-01-08', 'end_date': '2024-02-28', 'progress': 90, 'status': 'نشط'},
        {'employee': 'أحمد محمد', 'department': 'تقنية المعلومات', 'area': 'مهارات القيادة', 'goal': 'برنامج تطوير مهارات القيادة', 'start_date': '2023-12-01', 'end_date': '2024-01-25', 'progress': 100, 'status': 'مكتمل'},
        {'employee': 'فاطمة علي', 'department': 'المالية', 'area': 'مهارات التواصل', 'goal': 'دورة مهارات التواصل المتقدمة', 'start_date': '2023-12-15', 'end_date': '2024-01-22', 'progress': 100, 'status': 'مكتمل'},
    ]

    context = {
        'title': 'خطط تحسين الأداء',
        'development_plans': development_plans,
    }
    return render(request, 'hr/development_plan_list.html', context)


@login_required
def employee_report(request):
    """تقرير بالعاملين"""
    employees = [
        {'name': 'أحمد محمد علي', 'employee_id': 'EMP001', 'national_id': '12345678901234', 'department': 'تقنية المعلومات', 'position': 'مدير المشاريع', 'hire_date': '2020-01-15', 'basic_salary': 15000, 'employment_type': 'دائم', 'status': 'نشط', 'phone': '01234567890', 'email': '<EMAIL>'},
        {'name': 'فاطمة علي محمد', 'employee_id': 'EMP002', 'national_id': '12345678901235', 'department': 'المالية', 'position': 'محاسب أول', 'hire_date': '2019-03-20', 'basic_salary': 12000, 'employment_type': 'دائم', 'status': 'نشط', 'phone': '01234567891', 'email': '<EMAIL>'},
        {'name': 'محمد أحمد سالم', 'employee_id': 'EMP003', 'national_id': '12345678901236', 'department': 'تقنية المعلومات', 'position': 'مطور أول', 'hire_date': '2021-06-10', 'basic_salary': 10000, 'employment_type': 'دائم', 'status': 'نشط', 'phone': '01234567892', 'email': '<EMAIL>'},
        {'name': 'سارة علي أحمد', 'employee_id': 'EMP004', 'national_id': '12345678901237', 'department': 'المبيعات', 'position': 'مندوب مبيعات', 'hire_date': '2022-02-01', 'basic_salary': 8000, 'employment_type': 'تعاقد', 'status': 'نشط', 'phone': '01234567893', 'email': '<EMAIL>'},
        {'name': 'علي محمد حسن', 'employee_id': 'EMP005', 'national_id': '12345678901238', 'department': 'الإنتاج', 'position': 'مشرف إنتاج', 'hire_date': '2018-09-15', 'basic_salary': 9000, 'employment_type': 'دائم', 'status': 'في إجازة', 'phone': '01234567894', 'email': '<EMAIL>'},
    ]

    department_summary = [
        {'name': 'تقنية المعلومات', 'employee_count': 15, 'avg_salary': 12000, 'total_salary': 180000},
        {'name': 'المالية', 'employee_count': 8, 'avg_salary': 10000, 'total_salary': 80000},
        {'name': 'المبيعات', 'employee_count': 12, 'avg_salary': 8500, 'total_salary': 102000},
        {'name': 'الإنتاج', 'employee_count': 20, 'avg_salary': 7000, 'total_salary': 140000},
        {'name': 'الموارد البشرية', 'employee_count': 5, 'avg_salary': 11000, 'total_salary': 55000},
    ]

    context = {
        'title': 'تقرير بالعاملين',
        'employees': employees,
        'department_summary': department_summary,
        'total_employees': 60,
        'active_employees': 55,
        'departments_count': 5,
        'positions_count': 25,
    }
    return render(request, 'hr/employee_report.html', context)


@login_required
def employee_diary(request):
    """يوميات شؤون العاملين"""
    diary_entries = [
        {'type': 'توظيف', 'title': 'توظيف موظف جديد', 'employee': 'أحمد محمد', 'description': 'تم توظيف أحمد محمد في منصب مطور في قسم تقنية المعلومات', 'date': '2024-01-25', 'time': '09:00', 'department': 'تقنية المعلومات', 'responsible': 'مدير الموارد البشرية', 'notes': 'بدء العمل في 1 فبراير 2024'},
        {'type': 'ترقية', 'title': 'ترقية موظف', 'employee': 'فاطمة علي', 'description': 'تم ترقية فاطمة علي من محاسب إلى محاسب أول', 'date': '2024-01-24', 'time': '10:30', 'department': 'المالية', 'responsible': 'مدير المالية', 'notes': 'زيادة راتب 15%'},
        {'type': 'إجازة', 'title': 'طلب إجازة', 'employee': 'محمد أحمد', 'description': 'طلب إجازة سنوية لمدة أسبوعين', 'date': '2024-01-23', 'time': '14:15', 'department': 'تقنية المعلومات', 'responsible': 'مدير المشاريع', 'notes': 'إجازة مستحقة'},
        {'type': 'تدريب', 'title': 'التحاق بدورة تدريبية', 'employee': 'سارة علي', 'description': 'التحاق بدورة مهارات المبيعات المتقدمة', 'date': '2024-01-22', 'time': '11:00', 'department': 'المبيعات', 'responsible': 'مدير المبيعات', 'notes': 'مدة الدورة 3 أيام'},
        {'type': 'نقل', 'title': 'نقل موظف', 'employee': 'علي محمد', 'description': 'نقل من قسم الإنتاج إلى قسم الجودة', 'date': '2024-01-21', 'time': '13:45', 'department': 'الجودة', 'responsible': 'مدير العمليات', 'notes': 'نقل بناء على طلب الموظف'},
    ]

    context = {
        'title': 'يوميات شؤون العاملين',
        'diary_entries': diary_entries,
        'today_entries': 3,
        'week_entries': 8,
        'pending_actions': 5,
        'total_entries': 125,
    }
    return render(request, 'hr/employee_diary.html', context)


@login_required
def salary_report(request):
    """تقارير المرتبات"""
    from .models import EmployeeSalary
    from django.db.models import Sum, Avg
    from django.utils import timezone
    
    # جلب المرتبات الحالية للموظفين النشطين
    current_salaries = EmployeeSalary.objects.filter(
        employee__status='ACTIVE',
        is_current=True
    ).select_related('employee__person', 'employee__department')
    
    # حساب الإحصائيات
    total_employees = current_salaries.count()
    total_basic_salary = current_salaries.aggregate(Sum('basic_salary'))['basic_salary__sum'] or 0
    total_allowances = current_salaries.aggregate(Sum('total_allowances'))['total_allowances__sum'] or 0
    total_deductions = current_salaries.aggregate(Sum('total_deductions'))['total_deductions__sum'] or 0
    total_net_salary = current_salaries.aggregate(Sum('net_salary'))['net_salary__sum'] or 0
    avg_salary = current_salaries.aggregate(Avg('net_salary'))['net_salary__avg'] or 0
    
    # تحضير بيانات المرتبات
    salary_data = []
    for salary in current_salaries:
        salary_data.append({
            'employee_name': salary.employee.full_name,
            'employee_id': salary.employee.employee_number,
            'department': salary.employee.department.name if salary.employee.department else 'غير محدد',
            'basic_salary': float(salary.basic_salary),
            'allowances': float(salary.total_allowances),
            'deductions': float(salary.total_deductions),
            'net_salary': float(salary.net_salary),
            'last_payment_date': salary.effective_date.strftime('%Y-%m-%d') if salary.effective_date else 'غير محدد',
        })
    
    # ترتيب البيانات حسب صافي المرتب (تنازلي)
    salary_data.sort(key=lambda x: x['net_salary'], reverse=True)

    context = {
        'title': 'تقارير المرتبات',
        'salary_data': salary_data,
        'statistics': {
            'total_employees': total_employees,
            'total_salary_cost': f"{total_net_salary:,.0f} ج.م",
            'avg_salary': f"{avg_salary:,.0f} ج.م",
            'total_allowances': f"{total_allowances:,.0f} ج.م",
            'total_deductions': f"{total_deductions:,.0f} ج.م",
            'total_basic_salary': f"{total_basic_salary:,.0f} ج.م",
        }
    }
    return render(request, 'hr/salary_report.html', context)


@login_required
def attendance_summary(request):
    """تقرير ملخص الحضور"""
    attendance_data = []  # حذف جميع البيانات التجريبية
    department_attendance = []  # حذف جميع البيانات التجريبية
    context = {
        'title': 'تقرير ملخص الحضور',
        'attendance_data': attendance_data,
        'department_attendance': department_attendance,
        'total_present': 0,
        'total_absent': 0,
        'total_late': 0,
        'attendance_rate': 0,
    }
    return render(request, 'hr/attendance_summary.html', context)


@login_required
def attendance_edit(request, pk):
    attendance = get_object_or_404(Attendance, pk=pk)
    if request.method == 'POST':
        form = AttendanceForm(request.POST, instance=attendance)
        if form.is_valid():
            form.save()
            return redirect('hr:attendance_list')
    else:
        form = AttendanceForm(instance=attendance)
    context = {
        'form': form,
        'attendance': attendance,
        'title': 'تعديل سجل الحضور والانصراف',
    }
    return render(request, 'hr/attendance_edit.html', context)


@login_required
def attendance_delete(request, pk):
    attendance = get_object_or_404(Attendance, pk=pk)
    if request.method == 'POST':
        attendance.delete()
        return redirect('hr:attendance_list')
    context = {
        'attendance': attendance,
        'title': 'حذف سجل الحضور والانصراف',
    }
    return render(request, 'hr/attendance_delete.html', context)


# إدارة طلبات الإجازات
@login_required
def leave_request_create(request):
    """إنشاء طلب إجازة جديد"""
    from .models import LeaveRequest, Employee, LeaveType
    from django.utils import timezone
    
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            employee_id = request.POST.get('employee')
            leave_type_id = request.POST.get('leave_type')
            from_date = request.POST.get('from_date')
            to_date = request.POST.get('to_date')
            reason = request.POST.get('reason')
            is_emergency = request.POST.get('is_emergency') == 'on'
            
            print(f"DEBUG: employee_id={employee_id}, leave_type_id={leave_type_id}")
            print(f"DEBUG: from_date={from_date}, to_date={to_date}")
            print(f"DEBUG: reason={reason}, is_emergency={is_emergency}")
            
            # التحقق من صحة البيانات
            if not all([employee_id, leave_type_id, from_date, to_date, reason]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect('hr:leave_request_list')
            
            # جلب الكائنات
            try:
                employee = Employee.objects.get(id=employee_id)
                leave_type = LeaveType.objects.get(id=leave_type_id)
            except (Employee.DoesNotExist, LeaveType.DoesNotExist) as e:
                messages.error(request, f'بيانات غير صحيحة: {str(e)}')
                return redirect('hr:leave_request_list')
            
            # إنشاء طلب الإجازة
            leave_request = LeaveRequest.objects.create(
                employee=employee,
                leave_type=leave_type,
                from_date=from_date,
                to_date=to_date,
                reason=reason,
                is_emergency=is_emergency,
                created_by=request.user
            )
            
            # معالجة المرفقات
            if 'attachment' in request.FILES:
                leave_request.attachment = request.FILES['attachment']
                leave_request.save()
            
            print(f"DEBUG: تم إنشاء طلب الإجازة بنجاح - ID: {leave_request.id}")
            messages.success(request, f'تم إنشاء طلب الإجازة بنجاح للموظف {employee.full_name}')
            return redirect('hr:leave_request_list')
            
        except Exception as e:
            print(f"DEBUG: خطأ في إنشاء طلب الإجازة: {str(e)}")
            messages.error(request, f'حدث خطأ أثناء إنشاء الطلب: {str(e)}')
            return redirect('hr:leave_request_list')
    
    # إذا لم يكن POST، عرض النموذج
    context = {
        'title': 'إنشاء طلب إجازة جديد',
        'employees': Employee.objects.filter(status='ACTIVE').select_related('person'),
        'leave_types': LeaveType.objects.filter(is_active=True),
    }
    return render(request, 'hr/leave_request_create.html', context)


@login_required
def leave_request_detail(request, pk):
    """عرض تفاصيل طلب الإجازة"""
    from .models import LeaveRequest
    
    try:
        leave_request = LeaveRequest.objects.select_related(
            'employee__person', 'leave_type', 'approved_by', 'created_by'
        ).get(id=pk)
        
        context = {
            'title': f'تفاصيل طلب الإجازة - {leave_request.employee.full_name}',
            'leave_request': leave_request,
        }
        return render(request, 'hr/leave_request_detail.html', context)
        
    except LeaveRequest.DoesNotExist:
        messages.error(request, 'طلب الإجازة غير موجود')
        return redirect('hr:leave_request_list')


@login_required
def leave_request_edit(request, pk):
    """تعديل طلب الإجازة"""
    from .models import LeaveRequest, Employee, LeaveType
    
    try:
        leave_request = LeaveRequest.objects.get(id=pk)
        
        if request.method == 'POST':
            # جلب البيانات من النموذج
            employee_id = request.POST.get('employee')
            leave_type_id = request.POST.get('leave_type')
            from_date = request.POST.get('from_date')
            to_date = request.POST.get('to_date')
            reason = request.POST.get('reason')
            is_emergency = request.POST.get('is_emergency') == 'on'
            
            # التحقق من صحة البيانات
            if not all([employee_id, leave_type_id, from_date, to_date, reason]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return redirect('hr:leave_request_edit', pk=pk)
            
            # جلب الكائنات
            employee = Employee.objects.get(id=employee_id)
            leave_type = LeaveType.objects.get(id=leave_type_id)
            
            # تحديث طلب الإجازة
            leave_request.employee = employee
            leave_request.leave_type = leave_type
            leave_request.from_date = from_date
            leave_request.to_date = to_date
            leave_request.reason = reason
            leave_request.is_emergency = is_emergency
            leave_request.save()
            
            # معالجة المرفقات
            if 'attachment' in request.FILES:
                leave_request.attachment = request.FILES['attachment']
                leave_request.save()
            
            messages.success(request, 'تم تحديث طلب الإجازة بنجاح')
            return redirect('hr:leave_request_detail', pk=pk)
        
        # عرض نموذج التعديل
        context = {
            'title': f'تعديل طلب الإجازة - {leave_request.employee.full_name}',
            'leave_request': leave_request,
            'employees': Employee.objects.filter(status='ACTIVE').select_related('person'),
            'leave_types': LeaveType.objects.filter(is_active=True),
        }
        return render(request, 'hr/leave_request_edit.html', context)
        
    except LeaveRequest.DoesNotExist:
        messages.error(request, 'طلب الإجازة غير موجود')
        return redirect('hr:leave_request_list')


@login_required
def leave_request_approve(request, pk):
    """اعتماد طلب الإجازة"""
    from .models import LeaveRequest
    from django.utils import timezone
    
    try:
        leave_request = LeaveRequest.objects.get(id=pk)
        
        if leave_request.status != 'PENDING':
            messages.error(request, 'لا يمكن اعتماد طلب غير قيد المراجعة')
            return redirect('hr:leave_request_list')
        
        # اعتماد الطلب
        leave_request.status = 'APPROVED'
        leave_request.approved_by = request.user
        leave_request.approved_at = timezone.now()
        leave_request.approval_notes = request.POST.get('approval_notes', 'تم الاعتماد')
        leave_request.save()
        
        messages.success(request, 'تم اعتماد طلب الإجازة بنجاح')
        
    except LeaveRequest.DoesNotExist:
        messages.error(request, 'طلب الإجازة غير موجود')
    
    return redirect('hr:leave_request_list')


@login_required
def leave_request_reject(request, pk):
    """رفض طلب الإجازة"""
    from .models import LeaveRequest
    from django.utils import timezone
    
    try:
        leave_request = LeaveRequest.objects.get(id=pk)
        
        if leave_request.status != 'PENDING':
            messages.error(request, 'لا يمكن رفض طلب غير قيد المراجعة')
            return redirect('hr:leave_request_list')
        
        # رفض الطلب
        leave_request.status = 'REJECTED'
        leave_request.approved_by = request.user
        leave_request.approved_at = timezone.now()
        leave_request.approval_notes = request.POST.get('rejection_notes', 'تم الرفض')
        leave_request.save()
        
        messages.success(request, 'تم رفض طلب الإجازة')
        
    except LeaveRequest.DoesNotExist:
        messages.error(request, 'طلب الإجازة غير موجود')
    
    return redirect('hr:leave_request_list')


@login_required
@require_http_methods(["POST"])
def leave_request_delete(request, pk):
    """حذف طلب الإجازة"""
    from .models import LeaveRequest
    
    try:
        leave_request = LeaveRequest.objects.get(id=pk)
        
        # التحقق من صلاحية الحذف
        if leave_request.status == 'APPROVED':
            messages.error(request, 'لا يمكن حذف طلب معتمد')
            return redirect('hr:leave_request_list')
        
        leave_request.delete()
        messages.success(request, 'تم حذف طلب الإجازة بنجاح')
        
    except LeaveRequest.DoesNotExist:
        messages.error(request, 'طلب الإجازة غير موجود')
    
    return redirect('hr:leave_request_list')


# إدارة أنواع الإجازات
@login_required
def leave_type_create(request):
    """إنشاء نوع إجازة جديد"""
    from .models import LeaveType
    
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_english = request.POST.get('name_english', '')
            description = request.POST.get('description', '')
            category = request.POST.get('category')
            allowed_days = request.POST.get('allowed_days', 0)
            is_paid = request.POST.get('is_paid') == 'on'
            requires_approval = request.POST.get('requires_approval') == 'on'
            can_carry_forward = request.POST.get('can_carry_forward') == 'on'
            max_carry_forward_days = request.POST.get('max_carry_forward_days', 0)
            color = request.POST.get('color', '#007bff')
            
            # التحقق من صحة البيانات
            if not all([code, name, category]):
                messages.error(request, 'يرجى ملء الحقول المطلوبة')
                return redirect('hr:leave_type_list')
            
            # إنشاء نوع الإجازة
            leave_type = LeaveType.objects.create(
                code=code,
                name=name,
                name_english=name_english,
                description=description,
                category=category,
                allowed_days=allowed_days,
                is_paid=is_paid,
                requires_approval=requires_approval,
                can_carry_forward=can_carry_forward,
                max_carry_forward_days=max_carry_forward_days,
                color=color
            )
            
            messages.success(request, 'تم إنشاء نوع الإجازة بنجاح')
            return redirect('hr:leave_type_list')
            
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء نوع الإجازة: {str(e)}')
    
    return redirect('hr:leave_type_list')


@login_required
def leave_type_edit(request, pk):
    """تعديل نوع الإجازة"""
    from .models import LeaveType
    
    try:
        leave_type = LeaveType.objects.get(id=pk)
        
        if request.method == 'POST':
            # جلب البيانات من النموذج
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_english = request.POST.get('name_english', '')
            description = request.POST.get('description', '')
            category = request.POST.get('category')
            allowed_days = request.POST.get('allowed_days', 0)
            is_paid = request.POST.get('is_paid') == 'on'
            requires_approval = request.POST.get('requires_approval') == 'on'
            can_carry_forward = request.POST.get('can_carry_forward') == 'on'
            max_carry_forward_days = request.POST.get('max_carry_forward_days', 0)
            color = request.POST.get('color', '#007bff')
            is_active = request.POST.get('is_active') == 'on'
            
            # التحقق من صحة البيانات
            if not all([code, name, category]):
                messages.error(request, 'يرجى ملء الحقول المطلوبة')
                return redirect('hr:leave_type_edit', pk=pk)
            
            # تحديث نوع الإجازة
            leave_type.code = code
            leave_type.name = name
            leave_type.name_english = name_english
            leave_type.description = description
            leave_type.category = category
            leave_type.allowed_days = allowed_days
            leave_type.is_paid = is_paid
            leave_type.requires_approval = requires_approval
            leave_type.can_carry_forward = can_carry_forward
            leave_type.max_carry_forward_days = max_carry_forward_days
            leave_type.color = color
            leave_type.is_active = is_active
            leave_type.save()
            
            messages.success(request, 'تم تحديث نوع الإجازة بنجاح')
            return redirect('hr:leave_type_list')
        
        # عرض نموذج التعديل
        context = {
            'title': f'تعديل نوع الإجازة - {leave_type.name}',
            'leave_type': leave_type,
        }
        return render(request, 'hr/leave_type_edit.html', context)
        
    except LeaveType.DoesNotExist:
        messages.error(request, 'نوع الإجازة غير موجود')
        return redirect('hr:leave_type_list')


@login_required
@require_http_methods(["POST"])
def leave_type_delete(request, pk):
    """حذف نوع الإجازة"""
    from .models import LeaveType
    
    try:
        leave_type = LeaveType.objects.get(id=pk)
        
        # التحقق من عدم وجود طلبات مرتبطة
        if leave_type.requests.exists():
            messages.error(request, 'لا يمكن حذف نوع إجازة مرتبط بطلبات')
            return redirect('hr:leave_type_list')
        
        leave_type.delete()
        messages.success(request, 'تم حذف نوع الإجازة بنجاح')
        
    except LeaveType.DoesNotExist:
        messages.error(request, 'نوع الإجازة غير موجود')
    
    return redirect('hr:leave_type_list')


# إدارة أرصدة الإجازات
@login_required
def leave_balance_create(request):
    """إنشاء رصيد إجازة جديد"""
    from .models import LeaveBalance, Employee, LeaveType
    current_year = datetime.now().year
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            employee_id = request.POST.get('employee')
            leave_type_id = request.POST.get('leave_type')
            year = int(request.POST.get('year')) if request.POST.get('year') else None
            total_days = int(request.POST.get('total_days', 0)) if request.POST.get('total_days') else 0
            used_days = int(request.POST.get('used_days', 0)) if request.POST.get('used_days') else 0
            carried_forward_days = int(request.POST.get('carried_forward_days', 0)) if request.POST.get('carried_forward_days') else 0
            notes = request.POST.get('notes', '')
            # التحقق من صحة البيانات
            if not all([employee_id, leave_type_id, year]):
                messages.error(request, 'يرجى ملء الحقول المطلوبة')
                return redirect('hr:leave_balance_create')
            # جلب الكائنات
            employee = Employee.objects.get(id=employee_id)
            leave_type = LeaveType.objects.get(id=leave_type_id)
            # إنشاء رصيد الإجازة
            leave_balance = LeaveBalance.objects.create(
                employee=employee,
                leave_type=leave_type,
                year=year,
                total_days=total_days,
                used_days=used_days,
                carried_forward_days=carried_forward_days,
                notes=notes
            )
            messages.success(request, 'تم إنشاء رصيد الإجازة بنجاح')
            return redirect('hr:leave_balance_list')
        except (Employee.DoesNotExist, LeaveType.DoesNotExist):
            messages.error(request, 'بيانات غير صحيحة')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء رصيد الإجازة: {str(e)}')
    # عرض النموذج
    context = {
        'title': 'إضافة رصيد إجازة جديد',
        'employees': Employee.objects.filter(status='ACTIVE').select_related('person'),
        'leave_types': LeaveType.objects.filter(is_active=True),
        'current_year': current_year,
        'leave_balance': None,
    }
    return render(request, 'hr/leave_balance_form.html', context)


@login_required
def leave_balance_edit(request, pk):
    """تعديل رصيد الإجازة"""
    from .models import LeaveBalance, Employee, LeaveType
    try:
        leave_balance = LeaveBalance.objects.get(id=pk)
        if request.method == 'POST':
            # جلب البيانات من النموذج
            employee_id = request.POST.get('employee')
            leave_type_id = request.POST.get('leave_type')
            year = int(request.POST.get('year')) if request.POST.get('year') else None
            total_days = int(request.POST.get('total_days', 0)) if request.POST.get('total_days') else 0
            used_days = int(request.POST.get('used_days', 0)) if request.POST.get('used_days') else 0
            carried_forward_days = int(request.POST.get('carried_forward_days', 0)) if request.POST.get('carried_forward_days') else 0
            notes = request.POST.get('notes', '')
            # التحقق من صحة البيانات
            if not all([employee_id, leave_type_id, year]):
                messages.error(request, 'يرجى ملء الحقول المطلوبة')
                return redirect('hr:leave_balance_edit', pk=pk)
            # جلب الكائنات
            employee = Employee.objects.get(id=employee_id)
            leave_type = LeaveType.objects.get(id=leave_type_id)
            # تحديث رصيد الإجازة
            leave_balance.employee = employee
            leave_balance.leave_type = leave_type
            leave_balance.year = year
            leave_balance.total_days = total_days
            leave_balance.used_days = used_days
            leave_balance.carried_forward_days = carried_forward_days
            leave_balance.notes = notes
            leave_balance.save()
            messages.success(request, 'تم تحديث رصيد الإجازة بنجاح')
            return redirect('hr:leave_balance_list')
        # عرض النموذج
        context = {
            'title': f'تعديل رصيد الإجازة - {leave_balance.employee.full_name}',
            'leave_balance': leave_balance,
            'employees': Employee.objects.filter(status='ACTIVE').select_related('person'),
            'leave_types': LeaveType.objects.filter(is_active=True),
            'current_year': leave_balance.year,
        }
        return render(request, 'hr/leave_balance_form.html', context)
    except LeaveBalance.DoesNotExist:
        messages.error(request, 'رصيد الإجازة غير موجود')
        return redirect('hr:leave_balance_list')


@login_required
@require_http_methods(["POST"])
def leave_balance_delete(request, pk):
    """حذف رصيد الإجازة"""
    from .models import LeaveBalance
    try:
        leave_balance = LeaveBalance.objects.get(id=pk)
        leave_balance.delete()
        messages.success(request, 'تم حذف رصيد الإجازة بنجاح')
    except LeaveBalance.DoesNotExist:
        messages.error(request, 'رصيد الإجازة غير موجود')
    return redirect('hr:leave_balance_list')


@login_required
def contract_print(request, pk):
    contract = get_object_or_404(Contract, pk=pk)
    return render(request, 'hr/contract_print.html', {'contract': contract, 'title': 'طباعة تفاصيل العقد'})

@login_required
def contract_print_summary(request, pk):
    contract = get_object_or_404(Contract, pk=pk)
    return render(request, 'hr/contract_print_summary.html', {'contract': contract, 'title': 'طباعة ملخص العقد'})

@login_required
@require_http_methods(["POST", "GET"])
def employment_create(request):
    from definitions.models import Person
    from .models import Employee, Department, Position, SalarySystem
    from django.db import transaction
    import re
    if request.method == 'POST':
        form = EmploymentForm(request.POST)
        if form.is_valid():
            name = form.cleaned_data['name']
            national_id = form.cleaned_data['national_id']
            position = form.cleaned_data['position']
            department = form.cleaned_data['department']
            hire_date = form.cleaned_data['hire_date']
            salary = form.cleaned_data['salary']
            phone = form.cleaned_data['phone']
            email = form.cleaned_data['email']
            address = form.cleaned_data['address']

            # البحث عن شخص موجود بنفس رقم الهوية أو الاسم
            person = None
            if national_id:
                person = Person.objects.filter(national_id=national_id).first()
            if not person:
                # fallback: البحث بالاسم
                person = Person.objects.filter(name=name).first()
            if not person:
                # توليد كود تلقائي للشخص
                last_person = Person.objects.filter(code__regex=r'^EMP\\d+$').order_by('-code').first()
                if last_person and re.match(r'^EMP(\\d+)$', last_person.code):
                    last_num = int(re.match(r'^EMP(\\d+)$', last_person.code).group(1))
                else:
                    last_num = 0
                new_code = f'EMP{last_num+1:03d}'
                person = Person.objects.create(
                    code=new_code,
                    name=name,
                    person_type='EMPLOYEE',
                    entity_type='INDIVIDUAL',
                    national_id=national_id,
                    phone=phone,
                    email=email,
                    address=address,
                    is_active_employee=True
                )
            else:
                # تحديث بيانات الشخص إذا لزم الأمر
                updated = False
                if not person.national_id and national_id:
                    person.national_id = national_id
                    updated = True
                if not person.phone and phone:
                    person.phone = phone
                    updated = True
                if not person.email and email:
                    person.email = email
                    updated = True
                if not person.address and address:
                    person.address = address
                    updated = True
                if not person.is_active_employee:
                    person.is_active_employee = True
                    updated = True
                if updated:
                    person.save()

            # توليد رقم موظف تلقائي (أول رقم متاح)
            existing_numbers = set(Employee.objects.values_list('employee_number', flat=True))
            i = 1
            while True:
                candidate = f'EMP{i:03d}'
                if candidate not in existing_numbers:
                    new_emp_number = candidate
                    break
                i += 1

            # اختيار نظام المرتب الافتراضي (أو يمكن تعديله لاحقًا)
            salary_system = SalarySystem.objects.first()

            # إنشاء الموظف
            with transaction.atomic():
                employee = Employee.objects.create(
                    person=person,
                    employee_number=new_emp_number,
                    department=department,
                    position=position,
                    salary_system=salary_system,
                    hire_date=hire_date,
                    current_salary=salary,
                    status='ACTIVE'
                )
            messages.success(request, f'✅ تم تعيين الموظف الجديد بنجاح! يمكنك الآن متابعة ملفه أو إضافة عقد عمل له.')
            return redirect('hr:employment_list')
    else:
        form = EmploymentForm()
    return render(request, 'hr/employment_form.html', {'form': form, 'title': 'تعيين موظف جديد'})

@login_required
def salary_addition_create(request):
    """إنشاء إضافة جديدة للمرتب"""
    if request.method == 'POST':
        form = SalaryAdditionForm(request.POST)
        if form.is_valid():
            addition = form.save(commit=False)
            addition.created_by = request.user
            addition.save()
            messages.success(request, 'تم إنشاء الإضافة بنجاح')
            return redirect('hr:salary_addition_list')
    else:
        form = SalaryAdditionForm()
    
    context = {
        'title': 'إضافة جديدة للمرتب',
        'form': form,
    }
    return render(request, 'hr/salary_addition_form.html', context)

@login_required
def salary_addition_detail(request, pk):
    """تفاصيل إضافة المرتب"""
    try:
        addition = SalaryAddition.objects.get(pk=pk)
    except SalaryAddition.DoesNotExist:
        messages.error(request, 'الإضافة غير موجودة')
        return redirect('hr:salary_addition_list')
    
    context = {
        'title': f'تفاصيل الإضافة - {addition.employee.full_name}',
        'addition': addition,
    }
    return render(request, 'hr/salary_addition_detail.html', context)

@login_required
def salary_addition_edit(request, pk):
    """تعديل إضافة المرتب"""
    try:
        addition = SalaryAddition.objects.get(pk=pk)
    except SalaryAddition.DoesNotExist:
        messages.error(request, 'الإضافة غير موجودة')
        return redirect('hr:salary_addition_list')
    
    if request.method == 'POST':
        form = SalaryAdditionForm(request.POST, instance=addition)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الإضافة بنجاح')
            return redirect('hr:salary_addition_list')
    else:
        form = SalaryAdditionForm(instance=addition)
    
    context = {
        'title': f'تعديل الإضافة - {addition.employee.full_name}',
        'form': form,
        'addition': addition,
    }
    return render(request, 'hr/salary_addition_form.html', context)

@login_required
def salary_addition_delete(request, pk):
    """حذف إضافة المرتب"""
    try:
        addition = SalaryAddition.objects.get(pk=pk)
    except SalaryAddition.DoesNotExist:
        messages.error(request, 'الإضافة غير موجودة')
        return redirect('hr:salary_addition_list')
    
    if request.method == 'POST':
        employee_name = addition.employee.full_name
        addition.delete()
        messages.success(request, f'تم حذف إضافة {employee_name} بنجاح')
        return redirect('hr:salary_addition_list')
    
    context = {
        'title': f'حذف الإضافة - {addition.employee.full_name}',
        'addition': addition,
    }
    return render(request, 'hr/salary_addition_delete.html', context)

@login_required
def salary_deduction_detail(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk, type='deduction')
    return render(request, 'hr/salary_deduction_detail.html', {'object': obj, 'title': 'تفاصيل الخصم'})

@login_required
def salary_deduction_edit(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk, type='deduction')
    if request.method == 'POST':
        form = AllowanceDeductionForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل بيانات الخصم بنجاح')
            return redirect('hr:salary_deduction_list')
    else:
        form = AllowanceDeductionForm(instance=obj)
    return render(request, 'hr/salary_deduction_form.html', {'form': form, 'title': 'تعديل الخصم'})

@login_required
def salary_deduction_print(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk, type='deduction')
    return render(request, 'hr/salary_deduction_print.html', {'object': obj, 'title': 'طباعة الخصم'})

# أنواع الإضافات
@login_required
def allowance_type_list(request):
    types = AllowanceType.objects.all().order_by('name')
    return render(request, 'hr/allowance_type_list.html', {'types': types, 'title': 'أنواع الإضافات'})

@login_required
def allowance_type_create(request):
    if request.method == 'POST':
        form = AllowanceTypeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة نوع الإضافة بنجاح')
            return redirect('hr:allowance_type_list')
    else:
        form = AllowanceTypeForm()
    return render(request, 'hr/allowance_type_form.html', {'form': form, 'title': 'إضافة نوع إضافة'})

@login_required
def allowance_type_edit(request, pk):
    obj = get_object_or_404(AllowanceType, pk=pk)
    if request.method == 'POST':
        form = AllowanceTypeForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل نوع الإضافة بنجاح')
            return redirect('hr:allowance_type_list')
    else:
        form = AllowanceTypeForm(instance=obj)
    return render(request, 'hr/allowance_type_form.html', {'form': form, 'title': 'تعديل نوع إضافة'})

@login_required
def allowance_type_delete(request, pk):
    obj = get_object_or_404(AllowanceType, pk=pk)
    if request.method == 'POST':
        obj.delete()
        messages.success(request, 'تم حذف نوع الإضافة بنجاح')
        return redirect('hr:allowance_type_list')
    return render(request, 'hr/allowance_type_delete.html', {'object': obj, 'title': 'حذف نوع إضافة'})

# أنواع الخصومات
@login_required
def deduction_type_list(request):
    types = DeductionType.objects.all().order_by('name')
    return render(request, 'hr/deduction_type_list.html', {'types': types, 'title': 'أنواع الخصومات'})

@login_required
def deduction_type_create(request):
    if request.method == 'POST':
        form = DeductionTypeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة نوع الخصم بنجاح')
            return redirect('hr:deduction_type_list')
    else:
        form = DeductionTypeForm()
    return render(request, 'hr/deduction_type_form.html', {'form': form, 'title': 'إضافة نوع خصم'})

@login_required
def deduction_type_edit(request, pk):
    obj = get_object_or_404(DeductionType, pk=pk)
    if request.method == 'POST':
        form = DeductionTypeForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل نوع الخصم بنجاح')
            return redirect('hr:deduction_type_list')
    else:
        form = DeductionTypeForm(instance=obj)
    return render(request, 'hr/deduction_type_form.html', {'form': form, 'title': 'تعديل نوع خصم'})

@login_required
def deduction_type_delete(request, pk):
    obj = get_object_or_404(DeductionType, pk=pk)
    if request.method == 'POST':
        obj.delete()
        messages.success(request, 'تم حذف نوع الخصم بنجاح')
        return redirect('hr:deduction_type_list')
    return render(request, 'hr/deduction_type_delete.html', {'object': obj, 'title': 'حذف نوع خصم'})

@login_required
def allowance_deduction_edit(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk)
    if obj.type == 'allowance':
        FormClass = AllowanceForm
        title = 'تعديل الإضافة'
    else:
        FormClass = AllowanceDeductionForm
        title = 'تعديل الخصم'
    if request.method == 'POST':
        form = FormClass(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تعديل السجل بنجاح')
            return redirect('hr:allowance_deduction_list')
    else:
        form = FormClass(instance=obj)
    return render(request, 'hr/allowance_deduction_form.html', {'form': form, 'title': title})

@login_required
def allowance_deduction_print(request, pk):
    obj = get_object_or_404(AllowanceDeduction, pk=pk)
    return render(request, 'hr/allowance_deduction_print.html', {'object': obj, 'title': 'طباعة الإضافة/الخصم'})


