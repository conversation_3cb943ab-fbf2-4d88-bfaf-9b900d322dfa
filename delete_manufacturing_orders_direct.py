#!/usr/bin/env python
"""
سكريبت لحذف جميع أوامر التصنيع من قاعدة البيانات مباشرة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric_accounts.settings')
django.setup()

from django.db import transaction
from inventory.models import ManufacturingOrder, ManufacturingMaterial, ManufacturingStep

def delete_all_manufacturing_orders():
    """حذف جميع أوامر التصنيع والبيانات المرتبطة بها"""
    
    print("=" * 60)
    print("🚨 بدء عملية حذف جميع أوامر التصنيع")
    print("=" * 60)
    
    # إحصائيات قبل الحذف
    total_orders = ManufacturingOrder.objects.count()
    total_materials = ManufacturingMaterial.objects.count()
    total_steps = ManufacturingStep.objects.count()
    
    print(f"📊 الإحصائيات الحالية:")
    print(f"   - عدد أوامر التصنيع: {total_orders}")
    print(f"   - عدد المواد الخام: {total_materials}")
    print(f"   - عدد خطوات التصنيع: {total_steps}")
    print()
    
    if total_orders == 0:
        print("✅ لا توجد أوامر تصنيع للحذف")
        return
    
    try:
        with transaction.atomic():
            print("🔄 بدء عملية الحذف...")
            
            # حذف خطوات التصنيع أولاً
            deleted_steps = ManufacturingStep.objects.all().delete()
            print(f"✅ تم حذف {deleted_steps[0]} خطوة تصنيع")
            
            # حذف المواد الخام
            deleted_materials = ManufacturingMaterial.objects.all().delete()
            print(f"✅ تم حذف {deleted_materials[0]} مادة خام")
            
            # حذف أوامر التصنيع
            deleted_orders = ManufacturingOrder.objects.all().delete()
            print(f"✅ تم حذف {deleted_orders[0]} أمر تصنيع")
            
            print()
            print("🎉 تم حذف جميع أوامر التصنيع بنجاح!")
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء الحذف: {e}")
        print("تم التراجع عن جميع التغييرات")
        return

if __name__ == '__main__':
    delete_all_manufacturing_orders() 