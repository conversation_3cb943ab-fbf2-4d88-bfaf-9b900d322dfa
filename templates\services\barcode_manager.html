{% extends 'base/base.html' %}
{% load static %}

{% block title %}إدارة الباركود والـ QR Code{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/print-design.css' %}">
<style>
    .barcode-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }

    .barcode-preview {
        background: white;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        min-height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .barcode-preview:hover {
        border-color: #667eea;
        background: #f8f9ff;
    }

    .barcode-preview img {
        max-width: 100%;
        height: auto;
        border-radius: 5px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .barcode-type-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }

    .barcode-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #667eea;
    }

    .barcode-type-card.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
    }

    .barcode-settings {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .settings-section {
        margin-bottom: 25px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .settings-section h6 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .settings-section h6 i {
        color: #667eea;
        margin-left: 8px;
    }

    .btn-generate {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .invoice-templates {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-top: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .template-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .template-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-qrcode me-3"></i>
                        إدارة الباركود والـ QR Code
                    </h4>
                    <p class="mb-0 mt-2">إنشاء وتخصيص الباركود ورموز QR للفواتير والمستندات</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- أنواع الباركود -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        أنواع الباركود
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="barcode-type-card active" data-type="CODE128">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-barcode text-primary fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">Code 128</h6>
                                <small class="text-muted">الأكثر شيوعاً للفواتير</small>
                            </div>
                        </div>
                    </div>

                    <div class="barcode-type-card" data-type="EAN13">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-barcode text-success fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">EAN-13</h6>
                                <small class="text-muted">للمنتجات التجارية</small>
                            </div>
                        </div>
                    </div>

                    <div class="barcode-type-card" data-type="QR">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-qrcode text-warning fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">QR Code</h6>
                                <small class="text-muted">للبيانات المتقدمة</small>
                            </div>
                        </div>
                    </div>

                    <div class="barcode-type-card" data-type="CODE39">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-barcode text-danger fa-2x me-3"></i>
                            <div>
                                <h6 class="mb-1">Code 39</h6>
                                <small class="text-muted">للنصوص والأرقام</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الباركود -->
        <div class="col-lg-6">
            <div class="barcode-settings">
                <h5 class="mb-4">
                    <i class="fas fa-cogs text-primary me-2"></i>
                    إعدادات الباركود
                </h5>

                <!-- البيانات الأساسية -->
                <div class="settings-section">
                    <h6><i class="fas fa-database"></i>البيانات الأساسية</h6>
                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">النص أو البيانات</label>
                            <input type="text" class="form-control" id="barcodeData" placeholder="أدخل البيانات المراد ترميزها" value="INV-2024-001">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">البادئة</label>
                            <select class="form-select" id="barcodePrefix">
                                <option value="INV">INV - فاتورة</option>
                                <option value="PUR">PUR - مشتريات</option>
                                <option value="REC">REC - إيصال</option>
                                <option value="ORD">ORD - طلب</option>
                                <option value="">بدون بادئة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- إعدادات المظهر -->
                <div class="settings-section">
                    <h6><i class="fas fa-paint-brush"></i>إعدادات المظهر</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">العرض (px)</label>
                            <input type="number" class="form-control" id="barcodeWidth" value="200" min="100" max="500">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الارتفاع (px)</label>
                            <input type="number" class="form-control" id="barcodeHeight" value="60" min="30" max="200">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">حجم الخط</label>
                            <input type="number" class="form-control" id="barcodeFontSize" value="12" min="8" max="20">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showBarcodeText" checked>
                                <label class="form-check-label" for="showBarcodeText">
                                    إظهار النص أسفل الباركود
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeBorder">
                                <label class="form-check-label" for="includeBorder">
                                    إضافة حدود
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الفاتورة -->
                <div class="settings-section" id="invoiceSettings">
                    <h6><i class="fas fa-file-invoice"></i>إعدادات الفاتورة</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeDate" checked>
                                <label class="form-check-label" for="includeDate">
                                    تضمين التاريخ
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeAmount">
                                <label class="form-check-label" for="includeAmount">
                                    تضمين المبلغ
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeCustomer">
                                <label class="form-check-label" for="includeCustomer">
                                    تضمين العميل
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center">
                    <button class="btn btn-generate me-3" onclick="generateBarcode()">
                        <i class="fas fa-magic me-2"></i>
                        إنشاء الباركود
                    </button>
                    <button class="btn btn-outline-primary me-3" onclick="downloadBarcode()">
                        <i class="fas fa-download me-2"></i>
                        تحميل
                    </button>
                    <button class="btn btn-outline-success" onclick="addToInvoice()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة للفاتورة
                    </button>
                </div>
            </div>
        </div>

        <!-- معاينة الباركود -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة الباركود
                    </h6>
                </div>
                <div class="card-body">
                    <div class="barcode-preview" id="barcodePreview">
                        <div class="text-muted">
                            <i class="fas fa-qrcode fa-3x mb-3"></i>
                            <p>اضغط "إنشاء الباركود" لرؤية المعاينة</p>
                        </div>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted d-block">معلومات الباركود:</small>
                        <div id="barcodeInfo" class="mt-2">
                            <span class="badge bg-primary">النوع: Code 128</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قوالب الفواتير -->
    <div class="invoice-templates">
        <h5 class="mb-4">
            <i class="fas fa-file-invoice text-primary me-2"></i>
            قوالب الفواتير مع الباركود
        </h5>

        <div class="row">
            <div class="col-md-4">
                <div class="template-card">
                    <h6>فاتورة مبيعات مع باركود</h6>
                    <p class="text-muted small">قالب فاتورة مبيعات يتضمن باركود تلقائي</p>
                    <button class="btn btn-primary btn-sm" onclick="useInvoiceTemplate('sales')">
                        <i class="fas fa-file-alt me-1"></i>
                        استخدام القالب
                    </button>
                </div>
            </div>

            <div class="col-md-4">
                <div class="template-card">
                    <h6>فاتورة مشتريات مع QR</h6>
                    <p class="text-muted small">قالب فاتورة مشتريات مع رمز QR متقدم</p>
                    <button class="btn btn-success btn-sm" onclick="useInvoiceTemplate('purchase')">
                        <i class="fas fa-shopping-cart me-1"></i>
                        استخدام القالب
                    </button>
                </div>
            </div>

            <div class="col-md-4">
                <div class="template-card">
                    <h6>إيصال مع باركود مخصص</h6>
                    <p class="text-muted small">قالب إيصال مع باركود قابل للتخصيص</p>
                    <button class="btn btn-warning btn-sm" onclick="useInvoiceTemplate('receipt')">
                        <i class="fas fa-receipt me-1"></i>
                        استخدام القالب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/barcode-generator.js' %}"></script>
<script>
// متغيرات عامة
let barcodeGenerator;
let currentBarcodeType = 'CODE128';
let currentBarcodeImage = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    barcodeGenerator = new BarcodeGenerator();
    initializeBarcodeManager();
    generateBarcode(); // إنشاء باركود افتراضي

    // تحديث العداد من localStorage عند تحميل الصفحة
    syncBarcodeCounterOnLoad();
});

// مزامنة العداد عند تحميل الصفحة
function syncBarcodeCounterOnLoad() {
    try {
        const localCount = parseInt(localStorage.getItem('total_barcodes_generated') || '0');
        if (localCount > 0) {
            // إرسال العدد للخادم للمزامنة
            fetch('/services/ajax/update-barcode-count/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                },
                body: JSON.stringify({
                    count: localCount
                })
            }).catch(error => {
                console.log('تعذر مزامنة العداد مع الخادم:', error);
            });
        }
    } catch (error) {
        console.error('خطأ في مزامنة عداد الباركود:', error);
    }
}

// تهيئة مدير الباركود
function initializeBarcodeManager() {
    // ربط أحداث اختيار نوع الباركود
    document.querySelectorAll('.barcode-type-card').forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.barcode-type-card').forEach(c => c.classList.remove('active'));

            // تحديد البطاقة المختارة
            this.classList.add('active');
            currentBarcodeType = this.dataset.type;

            // تحديث واجهة الإعدادات
            updateSettingsInterface();

            // إنشاء باركود جديد
            generateBarcode();
        });
    });

    // ربط أحداث تغيير الإعدادات
    const settingsInputs = ['barcodeData', 'barcodePrefix', 'barcodeWidth', 'barcodeHeight', 'barcodeFontSize'];
    settingsInputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', generateBarcode);
        }
    });

    // ربط أحداث checkbox
    const checkboxes = ['showBarcodeText', 'includeBorder', 'includeDate', 'includeAmount', 'includeCustomer'];
    checkboxes.forEach(checkboxId => {
        const element = document.getElementById(checkboxId);
        if (element) {
            element.addEventListener('change', generateBarcode);
        }
    });
}

// تحديث واجهة الإعدادات حسب نوع الباركود
function updateSettingsInterface() {
    const invoiceSettings = document.getElementById('invoiceSettings');
    const barcodeDataInput = document.getElementById('barcodeData');

    // إظهار/إخفاء إعدادات الفاتورة
    if (currentBarcodeType === 'QR') {
        invoiceSettings.style.display = 'block';
    } else {
        invoiceSettings.style.display = 'none';
    }

    // تحديث placeholder حسب النوع
    switch (currentBarcodeType) {
        case 'EAN13':
            barcodeDataInput.placeholder = 'أدخل 13 رقم (مثال: 1234567890123)';
            barcodeDataInput.value = '1234567890123';
            break;
        case 'EAN8':
            barcodeDataInput.placeholder = 'أدخل 8 أرقام (مثال: 12345678)';
            barcodeDataInput.value = '12345678';
            break;
        case 'QR':
            barcodeDataInput.placeholder = 'أدخل النص أو البيانات للرمز';
            barcodeDataInput.value = 'https://example.com/invoice/INV-2024-001';
            break;
        default:
            barcodeDataInput.placeholder = 'أدخل البيانات المراد ترميزها';
            barcodeDataInput.value = 'INV-2024-001';
    }
}

// إنشاء الباركود
function generateBarcode() {
    try {
        const data = getBarcodeData();
        const options = getBarcodeOptions();

        // التحقق من صحة البيانات
        if (!barcodeGenerator.validateData(currentBarcodeType, data)) {
            showBarcodeError('البيانات المدخلة غير صحيحة لهذا النوع من الباركود');
            return;
        }

        // إنشاء الباركود
        const barcodeImage = barcodeGenerator.generateBarcode(currentBarcodeType, data, options);
        currentBarcodeImage = barcodeImage;

        // عرض الباركود
        displayBarcode(barcodeImage, data);

        // تحديث معلومات الباركود
        updateBarcodeInfo(data, options);

    } catch (error) {
        console.error('خطأ في إنشاء الباركود:', error);
        showBarcodeError('حدث خطأ في إنشاء الباركود');
    }
}

// الحصول على بيانات الباركود
function getBarcodeData() {
    let data = document.getElementById('barcodeData').value.trim();
    const prefix = document.getElementById('barcodePrefix').value;

    // إضافة البادئة إذا لم تكن موجودة
    if (prefix && !data.startsWith(prefix)) {
        data = prefix + '-' + data;
    }

    // للـ QR Code، إضافة بيانات إضافية إذا كانت مفعلة
    if (currentBarcodeType === 'QR') {
        const includeDate = document.getElementById('includeDate').checked;
        const includeAmount = document.getElementById('includeAmount').checked;
        const includeCustomer = document.getElementById('includeCustomer').checked;

        if (includeDate || includeAmount || includeCustomer) {
            const qrData = {
                invoice: data,
                generated: new Date().toISOString()
            };

            if (includeDate) {
                qrData.date = new Date().toLocaleDateString('ar-EG');
            }

            if (includeAmount) {
                qrData.amount = '1500.00 ج.م';
            }

            if (includeCustomer) {
                qrData.customer = 'شركة العميل المثال';
            }

            data = JSON.stringify(qrData);
        }
    }

    return data;
}

// الحصول على خيارات الباركود
function getBarcodeOptions() {
    return {
        width: parseInt(document.getElementById('barcodeWidth').value) || 200,
        height: parseInt(document.getElementById('barcodeHeight').value) || 60,
        fontSize: parseInt(document.getElementById('barcodeFontSize').value) || 12,
        showText: document.getElementById('showBarcodeText').checked,
        includeBorder: document.getElementById('includeBorder').checked
    };
}

// عرض الباركود
function displayBarcode(barcodeImage, data) {
    const preview = document.getElementById('barcodePreview');
    preview.innerHTML = `
        <div>
            <img src="${barcodeImage}" alt="باركود" class="img-fluid">
            <div class="mt-2">
                <small class="text-muted">البيانات: ${data.length > 50 ? data.substring(0, 50) + '...' : data}</small>
            </div>
        </div>
    `;
}

// عرض خطأ الباركود
function showBarcodeError(message) {
    const preview = document.getElementById('barcodePreview');
    preview.innerHTML = `
        <div class="text-danger">
            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
            <p>${message}</p>
        </div>
    `;
}

// تحديث معلومات الباركود
function updateBarcodeInfo(data, options) {
    const info = document.getElementById('barcodeInfo');
    const typeNames = {
        'CODE128': 'Code 128',
        'EAN13': 'EAN-13',
        'EAN8': 'EAN-8',
        'QR': 'QR Code',
        'CODE39': 'Code 39'
    };

    info.innerHTML = `
        <span class="badge bg-primary me-1">النوع: ${typeNames[currentBarcodeType]}</span>
        <span class="badge bg-info me-1">الحجم: ${options.width}×${options.height}</span>
        <span class="badge bg-success">الطول: ${data.length} حرف</span>
    `;
}

// تحميل الباركود
function downloadBarcode() {
    if (!currentBarcodeImage) {
        alert('يرجى إنشاء باركود أولاً');
        return;
    }

    const link = document.createElement('a');
    link.download = `barcode_${currentBarcodeType}_${new Date().getTime()}.png`;
    link.href = currentBarcodeImage;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // تحديث عداد الباركود
    updateBarcodeCounter();

    // إظهار رسالة نجاح
    showSuccessMessage('تم تحميل الباركود بنجاح');
}

// إضافة الباركود للفاتورة
function addToInvoice() {
    if (!currentBarcodeImage) {
        alert('يرجى إنشاء باركود أولاً');
        return;
    }

    // حفظ الباركود في localStorage للاستخدام في الفاتورة
    const barcodeData = {
        type: currentBarcodeType,
        image: currentBarcodeImage,
        data: getBarcodeData(),
        options: getBarcodeOptions(),
        timestamp: new Date().getTime()
    };

    localStorage.setItem('invoice_barcode', JSON.stringify(barcodeData));

    // تحديث عداد الباركود
    updateBarcodeCounter();

    showSuccessMessage('تم إضافة الباركود للفاتورة. يمكنك الآن استخدامه في تصميم الطباعة.');
}

// تحديث عداد الباركود
function updateBarcodeCounter() {
    try {
        // الحصول على العدد الحالي
        let currentCount = parseInt(localStorage.getItem('total_barcodes_generated') || '0');

        // زيادة العدد
        currentCount++;

        // حفظ العدد الجديد
        localStorage.setItem('total_barcodes_generated', currentCount.toString());

        // إرسال التحديث للخادم (اختياري)
        fetch('/services/ajax/update-barcode-count/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: JSON.stringify({
                count: currentCount
            })
        }).catch(error => {
            console.log('تعذر تحديث العداد على الخادم:', error);
        });

    } catch (error) {
        console.error('خطأ في تحديث عداد الباركود:', error);
    }
}

// استخدام قالب فاتورة
function useInvoiceTemplate(type) {
    const templates = {
        sales: {
            title: 'فاتورة مبيعات',
            prefix: 'INV',
            barcodeType: 'CODE128',
            data: 'INV-2024-001'
        },
        purchase: {
            title: 'فاتورة مشتريات',
            prefix: 'PUR',
            barcodeType: 'QR',
            data: 'PUR-2024-001'
        },
        receipt: {
            title: 'إيصال',
            prefix: 'REC',
            barcodeType: 'CODE128',
            data: 'REC-2024-001'
        }
    };

    const template = templates[type];
    if (template) {
        // تحديث النوع
        currentBarcodeType = template.barcodeType;

        // تحديث الواجهة
        document.querySelectorAll('.barcode-type-card').forEach(card => {
            card.classList.remove('active');
            if (card.dataset.type === template.barcodeType) {
                card.classList.add('active');
            }
        });

        // تحديث البيانات
        document.getElementById('barcodeData').value = template.data;
        document.getElementById('barcodePrefix').value = template.prefix;

        // تحديث الإعدادات وإنشاء الباركود
        updateSettingsInterface();
        generateBarcode();

        showSuccessMessage(`تم تحميل قالب ${template.title} بنجاح`);
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 4000);
}

// دوال إضافية للتكامل مع نظام الطباعة
function getBarcodeForInvoice() {
    const saved = localStorage.getItem('invoice_barcode');
    return saved ? JSON.parse(saved) : null;
}

function clearInvoiceBarcode() {
    localStorage.removeItem('invoice_barcode');
}
</script>
{% endblock %}